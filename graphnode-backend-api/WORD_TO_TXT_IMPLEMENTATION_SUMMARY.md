# Word Document to TXT Conversion Implementation

## Overview
Successfully implemented comprehensive support for `.doc` and `.docx` file uploads and conversion to `.txt` format in the code-generation file upload functionality.

## Changes Made

### 1. Updated File Type Support
- **File**: `app/routes/file_route.py` (line 51)
- **Change**: Added `'doc'` and `'docx'` to `ALLOWED_EXTENSIONS`
- **Before**: `{'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'py', 'js', 'json', 'yaml', 'yml'}`
- **After**: `{'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'py', 'js', 'json', 'yaml', 'yml', 'doc', 'docx'}`

### 2. Created Word Document Processing Utilities
- **File**: `app/utils/file_utils/word_utils.py` (new file)
- **Functions**:
  - `extract_text_from_docx()` - Extracts text from .docx files using python-docx
  - `extract_text_from_doc()` - Extracts text from .doc files using antiword/catdoc with fallbacks
  - `extract_text_from_word_document()` - Main function that handles both formats
  - `convert_word_to_txt()` - Converts Word documents to .txt files
  - `convert_word_to_txt_bytes()` - Converts Word documents to .txt content as bytes

### 3. Updated Dependencies
- **File**: `requirements.txt`
- **Added**: `docx2txt` - Additional library for .docx text extraction as fallback

### 4. Enhanced Text Extraction Endpoints
- **Files**: `app/routes/file_route.py`
- **Endpoints Updated**:
  - `/v1/extract_text/` - Now supports .doc and .docx files
  - `/extract_text_discussion` - Now supports .doc and .docx files

### 5. New Conversion Endpoints
- **File**: `app/routes/file_route.py`
- **New Endpoints**:
  - `POST /file/convert-word-to-txt` - Converts Word documents and returns .txt file download
  - `POST /file/convert-word-to-txt-json` - Converts Word documents and returns JSON with extracted text

### 6. Enhanced Upload Attachment Endpoint
- **File**: `app/routes/file_route.py`
- **Endpoint**: `POST /file/upload-attachment`
- **New Parameter**: `convert_word_to_txt: bool = Form(False)`
- **Functionality**: Optionally converts uploaded Word documents to .txt files automatically

## API Usage Examples

### 1. Convert Word Document to TXT (Download)
```bash
curl -X POST "http://localhost:8000/file/convert-word-to-txt" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@document.docx"
```

### 2. Convert Word Document to TXT (JSON Response)
```bash
curl -X POST "http://localhost:8000/file/convert-word-to-txt-json" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@document.docx"
```

### 3. Upload Attachment with TXT Conversion
```bash
curl -X POST "http://localhost:8000/file/upload-attachment" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@document.docx" \
  -F "project_id=123" \
  -F "convert_word_to_txt=true"
```

## Features

### Text Extraction Capabilities
- **Paragraphs**: Extracts all paragraph text
- **Tables**: Extracts table content with pipe-separated values
- **Formatting**: Preserves basic text structure
- **Encoding**: UTF-8 encoding for proper character support

### File Format Support
- **.docx files**: Full support using python-docx library
- **.doc files**: Support using external tools (antiword/catdoc) with fallbacks
- **Error Handling**: Graceful fallback mechanisms for different extraction methods

### Conversion Options
1. **Extract text only** - Get text content as string
2. **Convert to .txt bytes** - Get .txt file content as bytes
3. **Convert to .txt file** - Save extracted text to .txt file
4. **Automatic conversion** - Convert during upload process

### Response Formats
- **File Download**: Direct .txt file download
- **JSON Response**: Structured response with metadata
- **Upload Response**: Enhanced response with conversion status

## Error Handling
- File size validation (10MB limit)
- File type validation
- Extraction failure handling
- Graceful degradation for unsupported .doc files
- Detailed error messages and logging

## Testing
- **Test File**: `test_word_to_txt_conversion.py`
- **Test Coverage**:
  - Text extraction from .docx files
  - Conversion to .txt bytes
  - Conversion to .txt files
  - File cleanup and validation

## Dependencies
- `python-docx` - Primary library for .docx processing
- `docx2txt` - Fallback library for .docx processing
- `antiword` or `catdoc` - External tools for .doc processing (optional)

## File Structure
```
app/
├── routes/
│   └── file_route.py (updated)
├── utils/
│   └── file_utils/
│       └── word_utils.py (new)
requirements.txt (updated)
test_word_to_txt_conversion.py (new)
```

## Benefits
1. **Seamless Integration**: Works with existing file upload infrastructure
2. **Multiple Options**: Various ways to convert Word documents to TXT
3. **Robust Error Handling**: Handles edge cases and failures gracefully
4. **Backward Compatibility**: Doesn't break existing functionality
5. **Comprehensive Support**: Handles both .doc and .docx formats
6. **Automatic Conversion**: Optional automatic conversion during upload

## Next Steps
For production deployment:
1. Install `antiword` or `catdoc` system packages for better .doc support
2. Test with various Word document formats and sizes
3. Monitor conversion performance and add caching if needed
4. Consider adding progress tracking for large document conversions
