#!/bin/bash

# Configuration
NAMESPACE="duploservices-k-dev01"
SERVICE_LABEL="service=codegen"
NGINX_POD_LABEL="app=nginx"

# Function to delete nginx configuration for a specific deployment
delete_nginx_config() {
    local deployment_name="$1"
    local project_id=$(echo "$deployment_name" | cut -d"-" -f2)
    
    echo "Cleaning up nginx configuration for deployment: $deployment_name"
    
    # Get nginx pod
    local nginx_pod=$(kubectl get pods -n "$NAMESPACE" -l "$NGINX_POD_LABEL" -o name | head -1 | cut -d'/' -f2)
    
    if [[ -n "$nginx_pod" ]]; then
        echo "Using nginx pod: $nginx_pod"
        # Remove the custom nginx configuration file
        kubectl exec -n "$NAMESPACE" "$nginx_pod" -- rm -f "/etc/nginx/conf.d/custom_${project_id}.conf" 2>/dev/null || true
        echo "Nginx configuration cleaned up for project: $project_id"
    else
        echo "Warning: No nginx pod found with label: $NGINX_POD_LABEL"
    fi
}

# Function to delete resources for a specific deployment
delete_deployment_resources() {
    local deployment_name="$1"
    local project_id=$(echo "$deployment_name" | cut -d"-" -f2)
    
    echo "Deleting resources for deployment: $deployment_name"
    
    # Define resource names
    local service_internal="internal-$deployment_name"
    local service_clusterip="internal-clusterip-$deployment_name"
    local pvc_name="pvc-$deployment_name"
    local configmap_name="pod-status-$deployment_name"
    
    # Delete nginx configuration first
    delete_nginx_config "$deployment_name"
    
    # Delete all resources in parallel
    {
        echo "  Deleting deployment: $deployment_name"
        kubectl delete deployment "$deployment_name" -n "$NAMESPACE" --ignore-not-found &
    } &
    
    {
        echo "  Deleting internal service: $service_internal"
        kubectl delete service "$service_internal" -n "$NAMESPACE" --ignore-not-found &
    } &
    
    {
        echo "  Deleting clusterip service: $service_clusterip"
        kubectl delete service "$service_clusterip" -n "$NAMESPACE" --ignore-not-found &
    } &
    
    {
        echo "  Deleting PVC: $pvc_name"
        kubectl delete pvc "$pvc_name" -n "$NAMESPACE" --ignore-not-found &
    } &
    
    {
        echo "  Deleting ConfigMap: $configmap_name"
        kubectl delete configmap "$configmap_name" -n "$NAMESPACE" --ignore-not-found &
    } &
    
    # Wait for all parallel operations to complete
    wait
    
    echo "✓ Completed deletion of resources for deployment: $deployment_name"
}

# Function to get deployment name from pod name
get_deployment_from_pod() {
    local pod_name="$1"
    echo "$pod_name" | cut -d "-" -f1,2
}

# Function to delete specific pod and its resources
delete_specific_pod() {
    local target_pod="$1"
    
    echo "Searching for pod: $target_pod"
    
    # Check if pod exists
    if ! kubectl get pod "$target_pod" -n "$NAMESPACE" &>/dev/null; then
        echo "Error: Pod '$target_pod' not found in namespace '$NAMESPACE'"
        exit 1
    fi
    
    # Verify pod has the correct label
    local pod_labels=$(kubectl get pod "$target_pod" -n "$NAMESPACE" -o jsonpath='{.metadata.labels}')
    if [[ "$pod_labels" != *"service=codegen"* ]]; then
        echo "Warning: Pod '$target_pod' does not have the expected label '$SERVICE_LABEL'"
        echo "Proceeding anyway..."
    fi
    
    # Get deployment name from pod name
    local deployment_name=$(get_deployment_from_pod "$target_pod")
    
    echo "Identified deployment: $deployment_name"
    echo "Deleting resources for pod: $target_pod"
    
    # Delete all resources associated with this deployment
    delete_deployment_resources "$deployment_name"
    
    echo "✓ Successfully processed deletion for pod: $target_pod"
}

# Function to delete all pods with the service label
delete_all_pods() {
    echo "Deleting ALL pods with label: $SERVICE_LABEL"
    
    # Get all pods at once
    local all_pods=$(kubectl get pods -l "$SERVICE_LABEL" -n "$NAMESPACE" --no-headers | awk '{print $1}')
    
    if [[ -z "$all_pods" ]]; then
        echo "No pods found with label: $SERVICE_LABEL"
        exit 0
    fi
    
    echo "Found pods: $(echo $all_pods | tr '\n' ' ')"
    
    # Extract unique deployment names
    local deployment_names=()
    while IFS= read -r pod_name; do
        [[ -z "$pod_name" ]] && continue
        local deployment_name=$(get_deployment_from_pod "$pod_name")
        deployment_names+=("$deployment_name")
    done <<< "$all_pods"
    
    # Remove duplicates
    local unique_deployments=($(printf '%s\n' "${deployment_names[@]}" | sort -u))
    
    echo "Unique deployments to delete: ${unique_deployments[*]}"
    echo "Processing ${#unique_deployments[@]} deployments..."
    
    # Process each deployment
    for deployment in "${unique_deployments[@]}"; do
        delete_deployment_resources "$deployment"
    done
    
    echo "✓ Successfully processed deletion for all ${#unique_deployments[@]} deployments"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Delete Kubernetes resources for codegen deployments.

OPTIONS:
    -p, --pod POD_NAME     Delete resources for a specific pod
    -a, --all             Delete resources for all pods with label '$SERVICE_LABEL'
    -h, --help            Show this help message

EXAMPLES:
    $0 --all                           # Delete all codegen deployments
    $0 --pod codegen-abc123-xyz789     # Delete specific pod and its resources
    $0 -p my-pod-name                  # Delete specific pod (short form)

NOTES:
    - The script will automatically clean up nginx configurations
    - All associated resources (services, PVCs, ConfigMaps) will be deleted
    - Operations are performed in parallel for better performance
    - Namespace: $NAMESPACE
EOF
}

# Main script logic
main() {
    local pod_name=""
    local delete_all=false
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -p|--pod)
                pod_name="$2"
                shift 2
                ;;
            -a|--all)
                delete_all=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                echo "Error: Unknown option '$1'"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Validate arguments
    if [[ -n "$pod_name" && "$delete_all" == true ]]; then
        echo "Error: Cannot specify both --pod and --all options"
        show_usage
        exit 1
    fi
    
    if [[ -z "$pod_name" && "$delete_all" == false ]]; then
        echo "Error: Must specify either --pod POD_NAME or --all"
        show_usage
        exit 1
    fi
    
    # Execute based on options
    if [[ -n "$pod_name" ]]; then
        delete_specific_pod "$pod_name"
    elif [[ "$delete_all" == true ]]; then
        delete_all_pods
    fi
    
    echo "🎉 Operation completed successfully!"
}

# Run main function with all arguments
main "$@"
