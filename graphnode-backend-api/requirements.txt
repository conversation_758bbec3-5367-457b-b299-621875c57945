py2neo==2021.2.4
pymongo==4.8.0
requests==2.32.3
pinecone-client==5.0.1
# openai
# python-dotenv==1.0.1
# pydantic-settings
# pytest==8.3.2
PyYAML==6.0.2
jinja2==3.1.6
annotated-types==0.7.0
anyio
boto3==1.38.0
click==8.1.7
exceptiongroup==1.1.3
fastapi
h11==0.14.0
idna==3.7
jmespath==1.0.1
pydantic_core
# pydantic
websockets
python-dateutil==2.9.0.post0
six==1.16.0
sniffio==1.3.1
starlette
typing_extensions==4.12.2
urllib3==2.2.2
uvicorn==0.30.3
billiard==4.2.1
celery==5.4.0
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
kombu==5.4.0
redis==5.0.8
tzdata==2024.1
vine==5.1.0
wcwidth==0.2.13
pyjwt==2.9.0
cryptography==43.0.0
GitPython
litellm
pytest-mock
# psutil==6.0.0
bcrypt==4.2.0
passlib==1.7.4
# pytest-asyncio
cachetools==5.3.3
# datadog-api-client==2.28.0
ddtrace==2.12.1
pdfplumber
pytesseract==0.3.13
openpyxl==3.1.5
pandas==2.2.2
scikit-learn==1.6.1
PyGithub==2.4.0
# private package
aider
# Code generation agent 
code-generation-core-agent==0.0.330

llm-wrapper==0.0.3
# knowledge_graph_generator==0.1.0
pygments
mistune
weasyprint
fuzzywuzzy==0.18.0
python-Levenshtein==0.26.1
motor
pycryptodome
python-gitlab
authlib
pymilvus==2.5.0
pymupdf
nest-asyncio
google-auth
google-cloud-aiplatform
ipython==9.0.2
mermaid-py==0.7.1
anthropic
stripe==11.6.0
sse-starlette
python-multipart
async_timeout
pydantic[email]
kubernetes
uvloop
py7zr==0.22.0
rarfile
firecrawl==2.5.4
PyPDF2
opentelemetry-api
opentelemetry-sdk 
opentelemetry-instrumentation-fastapi 
opentelemetry-instrumentation-requests 
opentelemetry-exporter-jaeger-thrift 
opentelemetry-instrumentation-sqlalchemy 
opentelemetry-instrumentation-pymongo
python-docx
lxml
flower