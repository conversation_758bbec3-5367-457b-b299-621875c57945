# graphnode-backend-api
<!-- PYTHON VERSION >= 3.11 is needed to ensure all functionalities are working -->

## Setup virtual Environment:
```bash
python3 -m venv pyvenv
```

## Activate the virtural Environment:

```bash
source pyvenv/bin/activate
```


## Install the requirements:

```bash
pip3 install -r requirements.txt
```
## Run locally

```bash
uvicorn app.main:app --reload 
```

To see the Swagger documentation, visit:
http://localhost:8000/docs

# Running celery (task) worker
For local dev we are going to use celery and Redis as transport

To start Redis we can use docker-compose:
```bash
docker compose up -d redis
```

Once started, put
```bash
CELERY_BROKER_URL=redis://localhost:6379/0
```
to your .env file

Note that you will have to add the following line to your /etc/hosts so that your backend can see `redis` hostname:
```bash
127.0.0.1 redis

```

# setting up the redis server

# Create a simple working Redis instance
```
sudo mkdir -p /tmp/redis
sudo chown redis:redis /tmp/redis
```
# Start Redis with minimal config
```
sudo -u redis redis-server --port 6379 --bind 127.0.0.1 --dir /tmp/redis --daemonize yes
```
# Test connection
```
redis-cli
CONFIG SET requirepass "RedisKAVia\$IDpass"
AUTH "RedisKAVia\$IDpass"
CONFIG REWRITE
exit

redis-cli -a 'RedisKAVia$IDpass' ping
```

There are two options for running celery worker:
1. Running the worker natively:
```bash
celery -A app.tasks worker --loglevel=INFO
```

2. Running the worker with watchmedo to auto-restart with dev changes: 

```bash
LOCAL_DEBUG=True watchmedo auto-restart -d app/ -p '*.py' -- celery -A app.tasks worker --loglevel=INFO
 ```

3. Run the worker in docker container
```bash
docker compose up -d --build worker
```

## Run unit tests
Running unit tests in docker container:
```bash
docker-compose -f docker-compose.test.yml run  --build unit_test
```

## Use Package

```
aws codeartifact login --tool pip --domain kavia --domain-owner 058264095463 --repository kavia
```

## Use code query server

```
python3 -m uvicorn app.code_query_app:app --port 8000
```

## running codegeneration in local_debug mode 
```
STAGE=develop PYTHONPATH=$(pwd) LOCAL_DEBUG=TRUE uvicorn app.main:app --workers 4 --loop uvloop --reload --host 0.0.0.0
```

## copy k8 logs to localhost

```
kubectl cp duploservices-kavia-beta/{pod-id}:/app/data/T0000/{project_id}/logs ./logs
```

# local development vscode server

```
mkdir -p /tmp/kavia/workspace

docker run --rm \
  --name code-server \
  -p 8080:8080 \
  -v "/tmp/kavia/workspace:/tmp/kavia/workspace" \
  -u "$(id -u):$(id -g)" \
  codercom/code-server:latest \
  --auth none \
  --bind-addr 0.0.0.0:8080 \
  /tmp/kavia/workspace

```

## Deploy CodeGen - Deprecated
NOTE: stage can able dev, qa, pre_prod

python3.11 deploy-codegen.py --deploy-by-ip {stage} {ip}

python3.11 deploy-codegen.py --deploy-ami {stage} {ami-id}



## Run Docker Container Manually
```
docker run --rm -d -p 8080:8080 -p 8088:8088 -p 3001:3000 -e LOCAL_UID=1000 -e LOCAL_GID=1000 -v /tmp:/tmp  --name kavia_default_container_custom kavia_default_container_image
```


## Access duplo jit in wsl 
1. get the token from this URL https://duplo.cloud.kavia.ai/app/user/profile
2. Pase the url in the below command 
```
duplo-jit k8s --plan dev --host https://duplo.cloud.kavia.ai/ --token "YOUR_TOKEN"
```

## Run Code Generation Separately

To run code generation in a separate container:

```
docker run -ti --rm -p 8085:8080 -p 8090:8088    -e LOCAL_UID=$(id -u) -e LOCAL_GID=$(id -g)   -v /tmp:/tmp   kavia_dev_container
```