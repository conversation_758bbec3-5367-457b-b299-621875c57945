from app.core.Settings import settings
import uuid
from app.models.user_model import LLMModel

from app.core.code_generation import (Controller, TaskReporter, CodeMaintenanceAgent, LLMInterface, 
                                      get_codegeneration_path, get_prompt_directory, WebSocketClient, yaml, pkg_resources, get_codegeneration_path )
import asyncio
import os
from dotenv import load_dotenv
load_dotenv(".env.develop")

def test_code_gen_agent():
    task_id = f"test_session-{uuid.uuid4()}"
    micro_agents_yaml_path = pkg_resources.resource_filename(
            "code_generation_core_agent.agents.micro", 
            "cm_micro_agents.yaml"
        )
    micro_agents_config = None


    with open(micro_agents_yaml_path, 'r') as file:
        micro_agents_config = yaml.safe_load(file)
        
    llm = LLMInterface(
    llm_api_key=settings.OPENAI_API_KEY,
    session_dir=get_codegeneration_path("CodeMaintenance",task_id),
instance_name="kavia-backend")

    agent = CodeMaintenanceAgent(
        llm,
        execution_base_path=get_codegeneration_path("CodeMaintenance",task_id),
        micro_agents_config=micro_agents_config,
        model_name=LLMModel.bedrock_claude_3_5_sonnet.value,
        prompts_base_path=get_prompt_directory("CodeMaintenance")
    )
    code_maintenance_agent = CodeMaintenanceAgent(
        llm,
        execution_base_path=get_codegeneration_path("CodeMaintenance",task_id),
        micro_agents_config=micro_agents_config,
        model_name=LLMModel.bedrock_claude_3_5_sonnet.value,
        prompts_base_path=get_prompt_directory("CodeMaintenance")
    )
    controller = Controller(task_id, agent=code_maintenance_agent)
    reporter = TaskReporter(task_id, agent=code_maintenance_agent)

    asyncio.run(agent.process_work_item(
    agent_name="CodeMaintenance",
    use_retriever=False,
    control=controller,
    status_reporter=reporter,
    chat_interface=None,
    previous_context=None,
    is_interactive=True,
    wait_for_user_input=True,
    git_links=["https://github.com/githubtraining/hellogitworld.git"])
    )

if __name__ == "__main__":
    test_code_gen_agent()


