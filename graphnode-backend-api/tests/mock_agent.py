# mock_agent.py

import asyncio
import json
import threading
import time
import uuid
from datetime import datetime
from queue import Queue, Empty
from typing import Optional, Dict, Any
from collections import OrderedDict

from app.core.constants import TaskStatus, TASKS_COLLECTION_NAME
from app.core.websocket.client import WebSocket<PERSON>lient, ws_thread
from app.models.code_generation_model import Message
from code_generation_core_agent.agents.task_execution_agent import TaskExecutionReporter, TaskExecutionControl
from code_generation_core_agent.agents.tools.AgentPreProcessor import FunctionCallDescription
from mock_preview_controller import MockPreviewController
from app.core.Settings import settings


class MockController(TaskExecutionControl):
    def __init__(self, task_id):
        self.task_id = task_id
        self._paused = False
        self._stopped = False
        self.ws_client = WebSocketClient(task_id,uri=settings.WEBSOCKET_URI)
        self._handler_running = False
        self._input_buffer = Queue()
        self.mock_preview_controller = MockPreviewController(task_id, ws_client=self.ws_client)
    def initialize(self):
        if self.ws_client.connect():
            self.ws_client.add_message_handler(self._handle_message)
            self.ws_client.start_message_handler()
            return True
        return False

    def pause(self):
        self._paused = True
        if self.ws_client and self.ws_client.connected:
            self.ws_client.send_message("status_update", {"status": TaskStatus.PAUSED})

    def resume(self):
        self._paused = False
        if self.ws_client and self.ws_client.connected:
            self.ws_client.send_message("status_update", {"status": TaskStatus.RUNNING})

    def stop(self):
        self._stopped = True
        if self.ws_client and self.ws_client.connected:
            self.ws_client.send_message("status_update", {"status": TaskStatus.STOPPED})

    def reset(self):
        self._paused = False
        self._stopped = False
        if self.ws_client and self.ws_client.connected:
            self.ws_client.send_message("status_update", {"status": TaskStatus.RUNNING})

    async def _handle_message(self, message):
        try:
            command = message.get('type')
            if command == 'user_input':
                user_input = message.get('input')
                if user_input:
                    self._input_buffer.put(user_input)
            elif command in ['pause', 'resume', 'stop', 'reset']:
                getattr(self, command)()
            else:
                self.mock_preview_controller.handle_command(command, message)
        except Exception as e:
            print(f"Error handling message: {e}")

    def check_status(self):
        if self._stopped:
            raise StopIteration("Mock agent stopped")
        if self._paused:
            while self._paused and not self._stopped:
                time.sleep(0.1)

class MockReporter(TaskExecutionReporter):
    def __init__(self, task_id):
        self.task_id = task_id
        self.ws_client = WebSocketClient(task_id, uri=settings.WEBSOCKET_URI)
        self.is_user_input_requested = False
        self._user_input_queue = Queue()
        self._ws_task = None
        self._notification_task = None
        self._last_user_input = None

    def initialize(self):
        try:
            connected = self.ws_client.connect()
            if connected:
                self.ws_client.add_message_handler(self._handle_message)
                self.ws_client.start_message_handler()
            return connected
        except Exception as e:
            print(f"Failed to initialize reporter WebSocket: {e}")
            return False

    async def _handle_message(self, data):
        """Handle incoming messages"""
        try:
            if data.get('type') == 'user_input':
                user_input = data.get('input')
                if self.is_user_input_requested:
                    self._user_input_queue.put(user_input)
                else:
                    self._last_user_input = user_input
        except Exception as e:
            print(f"Error handling message in reporter: {e}")

    async def get_user_input(self, prompt):
        """Enhanced user input handling"""
        try:
            self.send_agent_message(prompt)
            self.is_user_input_requested = True

            try:
                if self._last_user_input:
                    user_input = self._last_user_input
                    self._last_user_input = None
                    return user_input

                start_time = time.time()
                while time.time() - start_time < 1800:  # 30 minutes timeout
                    try:
                        user_input = self._user_input_queue.get(timeout=1.0)
                        if user_input:
                            return user_input
                    except Empty:
                        continue

                raise TimeoutError("User input timeout reached")

            finally:
                self.is_user_input_requested = False

        except Exception as e:
            print(f"Error getting user input: {e}")
            self.is_user_input_requested = False
            raise

    async def _notify_waiting_for_input(self):
        """Periodically notify clients we're waiting for input"""
        while self.is_user_input_requested:
            try:
                self.ws_client.send_message("status_update", {
                    "type": "waiting_for_input",
                    "status": "waiting"
                })
                await asyncio.sleep(1)
            except Exception as e:
                print(f"Error sending waiting notification: {e}")
                await asyncio.sleep(1)
    
    def app_state_callback(self, url, state=None):
        return super().app_state_callback(url, state)
    
    def send_code_panel_update(self, stream_id, content, metadata=None):
        return super().send_code_panel_update(stream_id, content, metadata)
    
    def progress_callback(self, status, total_tasks, latest_result, request_context):
        try:
            new_step = self.collect_step_information(request_context)
            self.ws_client.send_message("progress_update", {
                "status": status,
                "total_tasks": total_tasks,
                "latest_result": latest_result,
                "step": new_step if new_step else None
            })
        except Exception as e:
            print(f"Error in progress_callback: {e}")

    def terminal_output_callback(self, output):
        try:
            self.ws_client.send_message("terminal_output", {"output": output})
        except Exception as e:
            print(f"Error in terminal_output_callback: {e}")

    def browser_output_callback(self, image):
        try:
            self.ws_client.send_message("browser_output", {"output": image})
        except Exception as e:
            print(f"Error in browser_output_callback: {e}")

    def cost_update_callback(self, agent_costs, total_cost):
        try:
            self.ws_client.send_message("cost_update", {
                "agents_cost": agent_costs,
                "total_cost": total_cost
            })
        except Exception as e:
            print(f"Error in cost_update_callback: {e}")

    def function_call_callback(self, call_description: FunctionCallDescription):
        try:
            call_dict = self._convert_to_serializable(call_description)
            self.ws_client.send_message("function_call", {"call": call_dict})
        except Exception as e:
            print(f"Error in function_call_callback: {e}")

    def task_start_callback(self, task_description):
        try:
            self.ws_client.send_message("task_start", {"description": task_description})
        except Exception as e:
            print(f"Error in task_start_callback: {e}")

    def user_query_callback(self, questions):
        try:
            self.ws_client.send_message("user_query", {"questions": questions})
        except Exception as e:
            print(f"Error in user_query_callback: {e}")

    def send_agent_message(self, message):
        try:
            message_obj = Message(content=message, sender="AI")
            self.ws_client.send_message("agent_message", message_obj.to_dict())
        except Exception as e:
            print(f"Error in send_agent_message: {e}")

    @staticmethod
    def _convert_to_serializable(obj):
        if isinstance(obj, OrderedDict):
            return dict(obj)
        elif hasattr(obj, '__dict__'):
            return {k: MockReporter._convert_to_serializable(v) 
                   for k, v in obj.__dict__.items() 
                   if not k.startswith('_')}
        return obj

    def collect_step_information(self, request_context):
        try:
            context = json.loads(request_context) if request_context else {}
            
            if 'last_step' in context:
                last_step = context['last_step']
                if isinstance(last_step, str):
                    try:
                        last_step = json.loads(last_step)
                    except json.JSONDecodeError:
                        print(f"Error decoding last_step JSON: {last_step}")
                        last_step = {}
                
                if isinstance(last_step, dict):
                    step_title = last_step.get('action', 'Unknown Action')
                    agent = last_step.get('agent', 'Unknown Agent')
                    action = last_step.get('action', 'Unknown Action')
                    details = json.dumps(last_step.get('request_details', {}), indent=4)
                    
                    new_step = {
                        'title': step_title,
                        'agent': agent,
                        'action': action,
                        'details': details,
                        'context': context
                    }
                    
                    return new_step
     
            return {}
        except json.JSONDecodeError as e:
            print(f"Error decoding JSON in progress_callback: {e}")
            return {}

    def send_mock_updates(self):
        """Send periodic mock updates"""
        base64_images = [
                "test_image_1_base64_string",  
                "test_image_2_base64_string",
                "test_image_3_base64_string",
        ] 
        toggle=False
        while True:
            try:
                time.sleep(5)
                
                # Mock terminal output
                self.terminal_output_callback(
                    f"Mock terminal output: Processing task at {datetime.now().isoformat()}"
                )
                
                # Mock function call - Fixed FunctionCallDescription
                mock_call = {
                    "function_name": "write_file",
                    "reason": "Creating mock component file",
                    "observations": "File created successfully",
                    "arguments": {
                        "file_path": "/mock/path/Component.tsx",
                        "content": "// Mock component code"
                    },
                    "root_cause_analysis": "Implementation required for functionality"
                }
                self.function_call_callback(mock_call)
                
                # Mock browser output
                image_to_send = base64_images[0] if toggle else base64_images[1]
                toggle = not toggle
                self.browser_output_callback(image_to_send)
                
                # Mock progress update
                self.progress_callback(
                    status="Mock task in progress",
                    total_tasks=[{
                        "step": "Implementing mock component",
                        "status": "in_progress",
                        "micro_agent": "MockAgent",
                        "details": "Writing mock component code"
                    }],
                    latest_result=json.dumps({
                        "code_changes": [{
                            "file": "Component.tsx",
                            "changes": "Added mock features"
                        }]
                    }),
                    request_context=json.dumps({
                        "current_task": "Mock implementation",
                        "progress": 50,
                        "last_step": {
                            "micro_agent": "MockAgent",
                            "action": "write_file",
                            "request_details": {
                                "file_path": "/mock/path/Component.tsx"
                            }
                        }
                    })
                )
                
                self.task_start_callback(
                    "Starting with Mock agent"
                )
                # Mock cost update
                self.cost_update_callback(
                    agent_costs={"MockAgent": 0.05},
                    total_cost=0.05
                )
                
                # Mock agent message
                self.send_agent_message(f"Hello Processing mock task at {datetime.now().isoformat()}")
                
            except Exception as e:
                print(f"Error sending mock updates: {e}")
                import traceback
                traceback.print_exc()
                time.sleep(1)

class MockAgent:
    def __init__(self, task_id=None):
        self.task_id = task_id or str(uuid.uuid4())
        self.controller = MockController(self.task_id)
        self.reporter = MockReporter(self.task_id)
        self.update_thread = None

    async def start(self):
        """Start the mock agent"""
        self.controller.initialize()
        self.reporter.initialize()

        # Start periodic updates
        self.update_thread = threading.Thread(
            target=self.reporter.send_mock_updates,
            daemon=True
        )
        self.update_thread.start()

        try:
            while not self.controller._stopped:
                self.controller.check_status()
                await asyncio.sleep(1)
        except StopIteration:
            pass
        finally:
            if self.controller.ws_client:
                self.controller.ws_client.disconnect()
            if self.reporter.ws_client:
                self.reporter.ws_client.disconnect()

def execute_mock_agent(project_details=None, work_item_details=None, task_id=None):
    """Entry point to run the mock agent"""
    try:
        mock_agent = MockAgent(task_id)
        asyncio.run(mock_agent.start())
        return mock_agent.task_id
    except Exception as e:
        print(f"Error in mock agent execution: {e}")
        raise

if __name__ == "__main__":
    task_id = "task123"
    execute_mock_agent(
        project_details={"name": "Mock Project"},
        work_item_details={"component": "Mock Component"},
        task_id=task_id
    )