from app.utils.aws import secrets_loader
import os
import litellm
import json

vertex_secret = secrets_loader.get_vertex_secret()
model_name = "gemini-2.5-pro-preview-05-06"

VERTEX_PROJECT_ID="vertex-ai-sandbox-447902"
VERTEX_LOCATION_ID="us-central1"
kwargs = {}


litellm.vertex_project = VERTEX_PROJECT_ID
litellm.vertex_location = VERTEX_LOCATION_ID
kwargs["vertex_credentials"] = vertex_secret
kwargs["stream"] = True

if isinstance(vertex_secret, str):
    vertex_secret = json.loads(vertex_secret)
messages = [
    {
        "role": "user",
        "content": "What is your name? Are you gemini 2.5 pro?",


        
    }
]
response_stream = litellm.completion(
            model=model_name,
            messages=messages,
            **kwargs,
        )
print("Response Stream", response_stream)
print("response content", response_stream.choices[0].message.content)
