from app.utils.docker_utils import ensure_docker_container_running




if __name__ == "__main__":
    # Test the function with a specific container name
    container_name = "kavia_default_container_custom"
    try:
        result = ensure_docker_container_running(container_name)
        print(f"Container '{container_name}' is running: {result}")
    except Exception as e:
        print(f"Error: {str(e)}")