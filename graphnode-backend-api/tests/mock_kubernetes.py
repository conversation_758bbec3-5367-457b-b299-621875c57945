from app.core.kubernetes_monitor import KubernetesAvailablePodsManager

print("Testing KubernetesAvailablePodsManager...")
kubernetes_pods_manager = KubernetesAvailablePodsManager("dev", "duploservices-k-dev01", True)

print("Fetching available codegen pods...")
# Properly unpack the tuple return value
available_pods, count = kubernetes_pods_manager.get_available_codegen_pods()


print(f"Available Pods Count: {count}")
selected_pod = available_pods[0] if available_pods else None
if selected_pod:
    print(f"Available Pod: {selected_pod}")
