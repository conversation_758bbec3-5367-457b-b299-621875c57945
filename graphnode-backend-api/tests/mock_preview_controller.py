import asyncio
import json
import time
from datetime import datetime
from typing import Dict, Any, Optional
from enum import Enum
from app.core.websocket import websocket_manager
from app.core.websocket.client import WebSocketClient
from app.core.Settings import settings
class MockPreviewState(Enum):
    """Mock preview states"""
    NOT_STARTED = "not_started"
    BUILDING = "building"
    RUNNING = "running"
    STOPPED = "stopped"
    FAILED = "failed"

class MockPreviewContainer:
    """Mock container representation"""
    def __init__(self, name: str, container_type: str, framework: str = "react", port: int = 3000):
        self.name = name
        self.container_type = container_type
        self.framework = framework
        self.port = port
        self.status = MockPreviewState.NOT_STARTED.value
        self.url = f"http://localhost:{port}"
        self.api_route = f"http://localhost:{port}/api" if container_type == "backend" else None
        self.error = None
        self.timestamp = datetime.now().isoformat()
        
    def to_dict(self) -> dict:
        return {
            "name": self.name,
            "container_type": self.container_type,
            "framework": self.framework,
            "status": self.status,
            "url": self.url,
            "api_route": self.api_route,
            "error": self.error,
            "timestamp": self.timestamp,
            "port": self.port
        }

class MockPreviewManager:
    """Async mock preview manager"""
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, '_initialized'):
            self.containers: Dict[str, MockPreviewContainer] = {}
            self.current_container_name: Optional[str] = None
            self._build_tasks: Dict[str, asyncio.Task] = {}
            self._initialized = True
            
    def register_container(self, name: str, container_type: str, framework: str, 
                          config: dict, workspace: str, depends_on: list = None):
        """Register a mock container"""
        port = config.get("ports", [3000])[0] if "ports" in config else 3000
        self.containers[name] = MockPreviewContainer(name, container_type, framework, port)
        
    def set_current_container(self, name: str):
        """Set current container"""
        if name in self.containers:
            self.current_container_name = name
            
    def get_current_container(self) -> Optional[MockPreviewContainer]:
        """Get current container"""
        if self.current_container_name:
            return self.containers.get(self.current_container_name)
        return None
    
    def get_preview_url(self) -> Optional[str]:
        """Get preview URL of current container"""
        container = self.get_current_container()
        return container.url if container else None
    
    def get_preview_status(self) -> dict:
        """Get preview status of current container"""
        container = self.get_current_container()
        if container:
            return {
                "status": container.status,
                "timestamp": container.timestamp,
                "error": container.error
            }
        return {"status": MockPreviewState.NOT_STARTED.value}
    
    def get_all_statuses(self) -> dict:
        """Get status of all containers"""
        return {name: container.to_dict() for name, container in self.containers.items()}
    
    async def run_application(self) -> dict:
        """Run all containers"""
        result = {}
        tasks = []
        for name in self.containers:
            task = asyncio.create_task(self.run_container(name))
            tasks.append(task)
            result[name] = {"status": MockPreviewState.BUILDING.value}
        await asyncio.gather(*tasks)
        return {"message": "All containers started", "containers": result}
    
    async def run_container(self, name: str, restart: bool = False) -> dict:
        """Run a specific container"""
        if name not in self.containers:
            return {"status": "error", "message": f"Container {name} not found"}
            
        container = self.containers[name]
        
        # Cancel existing build task if restarting

        container.status = MockPreviewState.STOPPED.value
        
            
        # Start build task
        task = asyncio.create_task(self._mock_build_container(container))
        self._build_tasks[name] = task
        
        return {"status": MockPreviewState.BUILDING.value, "message": f"Container {name} is building"}
    
    
    async def run_all_containers(self) -> dict:
        """Run all registered containers"""
        results = []
        tasks = []
        for name in self.containers:
            task = asyncio.create_task(self.run_container(name))
            tasks.append((name, task))
        
        for name, task in tasks:
            result = await task
            results.append({"container": name, **result})
        return {"status": "success", "results": results}
    
    async def _mock_build_container(self, container: MockPreviewContainer):
        """Simulate container build process"""
        try:
            container.status = MockPreviewState.BUILDING.value
            container.timestamp = datetime.now().isoformat()
                
            # Simulate build time
            build_time = 3 if container.container_type == "frontend" else 2
            await asyncio.sleep(build_time)
            
            # 90% success rate for mock
            import random
            if random.random() > 0.1:
                container.status = MockPreviewState.RUNNING.value
                container.error = None
            else:
                container.status = MockPreviewState.FAILED.value
                container.error = "Mock build failed"
                    
            container.timestamp = datetime.now().isoformat()
            
        except asyncio.CancelledError:
            container.status = MockPreviewState.STOPPED.value
            container.timestamp = datetime.now().isoformat()
            raise
        except Exception as e:
            container.status = MockPreviewState.FAILED.value
            container.error = str(e)
            container.timestamp = datetime.now().isoformat()

class MockPreviewController:
    """Async mock preview controller"""
    
    def __init__(self, task_id: str, ws_client=None):
        self.task_id = task_id
        self.ws_client = ws_client
        self.preview_manager = MockPreviewManager()
        self._command_handlers = {
            'get_preview_url': self._handle_get_preview_url,
            'restart_preview': self._handle_restart_preview,
            'get_all_containers': self._handle_get_all_containers,
            'run_all_containers': self._handle_run_all_containers,
            'get_all_status': self._handle_get_all_status,
            'restart_container': self._handle_restart_container,
            'stop': self._handle_stop
        }
        self._setup_mock_containers()
        self.stopped = False
        self._monitor_tasks = set()
    
    def initialize(self):
        self.ws_client.add_message_handler(self.handle_message)
        self.ws_client.start_message_handler()

    async def _handle_message(self, message):
        try:
            print("Incoming message")
            command = message.get('type')
            self.handle_command(command, message)
        except Exception as e:
            print(f"Error handling message: {e}")

    async def _handle_stop(self, input_data: Dict[str, Any]) -> dict:
        """Handle stop command"""
        self.stopped = True
        # Cancel all monitor tasks
        for task in self._monitor_tasks:
            task.cancel()
        if self.ws_client:
            self.ws_client.send_message("preview_stopped", {
                "status": "stopped",
                "message": "Preview controller stopped",
                "timestamp": datetime.now().isoformat()
            })
        return {"status": "success", "message": "Preview controller stopped"}
    
    def _setup_mock_containers(self):
        """Set up default mock containers"""
        # Frontend container
        self.preview_manager.register_container(
            name="frontend",
            container_type="frontend",
            framework="react",
            config={"ports": [3000]},
            workspace="frontend"
        )
        
        # Backend container
        self.preview_manager.register_container(
            name="backend",
            container_type="backend",
            framework="fastapi",
            config={"ports": [8000]},
            workspace="backend"
        )
        
        # Set frontend as current
        self.preview_manager.set_current_container("frontend")
        
    async def handle_command(self, command: str, input_data: Dict[str, Any] = None) -> dict:
        """Handle preview commands"""
        handler = self._command_handlers.get(command)
        if handler:
            try:
                if asyncio.iscoroutinefunction(handler):
                    return await handler(input_data or {})
                else:
                    return await asyncio.get_event_loop().run_in_executor(
                        None, handler, input_data or {}
                    )
            except Exception as e:
                error_msg = f"Error handling command {command}: {e}"
                print(error_msg)
                if self.ws_client:
                    self.ws_client.send_message("preview_error", {"message": error_msg})
                return {"status": "error", "message": error_msg}
        return {"status": "error", "message": f"Unknown command: {command}"}
    
    async def handle_message(self, message: Dict[str, Any]) -> dict:
        """Handle incoming WebSocket messages"""
        command = message.get('type')
        input_data = message
        
        if not command:
            return {"status": "error", "message": "No command type provided"}
        
        return await self.handle_command(command, input_data)

    async def _handle_get_preview_url(self, input_data: Dict[str, Any]) -> dict:
        """Get current preview URL"""
        url = self.preview_manager.get_preview_url()
        status = self.preview_manager.get_preview_status()
        
        response = {
            "url": url,
            "status": status.get("status", "not_started"),
            "timestamp": status.get("timestamp"),
            "available": url is not None
        }
        
        if self.ws_client:
            self.ws_client.send_message("preview_status", response)
            
        return response
    
    async def _handle_restart_preview(self, input_data: Dict[str, Any]) -> dict:
        """Restart current container"""
        container = self.preview_manager.get_current_container()
        if not container:
            return {"status": "error", "message": "No current container set"}
            
        # Start restart task
        task = asyncio.create_task(self._restart_preview_async(container.name))
        self._monitor_tasks.add(task)
        task.add_done_callback(self._monitor_tasks.discard)
        
        if self.ws_client:
            self.ws_client.send_message("preview_restart_response", {
                "status": "starting",
                "message": "Preview restart initiated",
                "timestamp": datetime.now().isoformat()
            })
            
        return {"status": "success", "message": "Restart initiated"}
    
    async def _restart_preview_async(self, container_name: str):
        """Async restart handler"""
        try:
            result = await self.preview_manager.run_container(container_name, restart=True)
            
            # Wait for build to complete
            container = self.preview_manager.containers[container_name]
            max_wait = 10
            while container.status == MockPreviewState.BUILDING.value and max_wait > 0:
                await asyncio.sleep(0.5)
                max_wait -= 1
                
            if self.ws_client:
                self.ws_client.send_message("preview_restart_response", {
                    "status": "completed",
                    "result": container.to_dict(),
                    "message": f"Preview restarted - Status: {container.status}",
                    "timestamp": datetime.now().isoformat()
                })
                
        except Exception as e:
            if self.ws_client:
                self.ws_client.send_message("preview_error", {
                    "message": f"Error restarting preview: {str(e)}"
                })
    
    async def _handle_get_all_containers(self, input_data: Dict[str, Any]) -> dict:
        """Get all containers"""
        containers = list(self.preview_manager.containers.values())
        containers_list = [c.to_dict() for c in containers]
        
        response = {
            "status": "success",
            "containers": containers_list,
            "timestamp": datetime.now().isoformat()
        }
        
        if self.ws_client:
            self.ws_client.send_message("containers", response)
            
        return response
    
    async def _handle_run_all_containers(self, input_data: Dict[str, Any]) -> dict:
        """Run all containers"""
        if self.ws_client:
            self.ws_client.send_message("run_all_containers_response", {
                "status": "starting",
                "message": "Running all containers initiated",
                "timestamp": datetime.now().isoformat()
            })
            
        # Monitor in background
        task = asyncio.create_task(self._monitor_all_containers())
        self._monitor_tasks.add(task)
        task.add_done_callback(self._monitor_tasks.discard)
        
        result = await self.preview_manager.run_all_containers()
        return result
    
    async def _monitor_all_containers(self):
        """Monitor all containers until they finish building"""
        try:
            await asyncio.sleep(5)  # Simulate build time
            
            statuses = self.preview_manager.get_all_statuses()
            if self.ws_client:
                self.ws_client.send_message("run_all_containers_response", {
                    "status": "completed",
                    "result": statuses,
                    "message": "All containers processed",
                    "timestamp": datetime.now().isoformat()
                })
        except Exception as e:
            if self.ws_client:
                self.ws_client.send_message("preview_error", {
                    "message": f"Error monitoring containers: {str(e)}"
                })
    
    async def _handle_get_all_status(self, input_data: Dict[str, Any]) -> dict:
        """Get status of all containers"""
        statuses = self.preview_manager.get_all_statuses()
        
        response = {
            "status": "success",
            "container_statuses": statuses,
            "timestamp": datetime.now().isoformat()
        }
        
        if self.ws_client:
            self.ws_client.send_message("container_statuses", response)
            
        return response
    
    async def _handle_restart_container(self, input_data: Dict[str, Any]) -> dict:
        """Restart specific container"""
        container_name = input_data.get("container_name")
        if not container_name:
            
            self.ws_client.send_message ("container_status", {"status": "error", "message": "Container name required", "input_data":input_data})
            
        result = await self.preview_manager.run_container(container_name, restart=True)
        

        self.ws_client.send_message("container_status", {
            "status": "success",
            "container_status": result,
            "timestamp": datetime.now().isoformat()
        })
            
        return result
    
    def get_current_status(self) -> Dict[str, Any]:
        """Get comprehensive preview status"""
        container = self.preview_manager.get_current_container()
        if not container:
            return {"status": "error", "message": "No current container"}
            
        return {
            "status": container.status,
            "url": container.url,
            "available": container.status == MockPreviewState.RUNNING.value,
            "timestamp": container.timestamp,
            "build_in_progress": container.status == MockPreviewState.BUILDING.value,
            "framework": container.framework,
            "platform": container.container_type
        }

# Usage with asyncio
async def main():
    task_id = "task123"
    print(task_id )
    ws_client = websocket_manager.create_session(task_id, uri=settings.WEBSOCKET_URI)
    ws_client.connect()
    print("WS")
    controller = MockPreviewController(task_id=task_id, ws_client=ws_client)
    controller.initialize()
    
    while not controller.stopped:
        await asyncio.sleep(10)

if __name__ == "__main__":
    asyncio.run(main())