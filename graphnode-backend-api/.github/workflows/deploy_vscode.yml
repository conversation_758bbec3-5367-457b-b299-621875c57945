name: Codegenservice Deployment
on:
  push:
    branches:
      - developtest
  workflow_dispatch:

env:
  DUPLO_HOST: https://duplo.cloud.kavia.ai
  DUPLO_TOKEN: ${{ secrets.DUPLO_TOKEN }}
  DUPLO_TENANT: kavia-dev
  REPO_NAME: vscode-server

jobs:
  build:
    if: contains(github.event.head_commit.message, 'vscodedeploy')
    runs-on: ubuntu-latest
    outputs:
      backend_image: ${{ steps.build-and-push-codegenservice.outputs.image }}:${{ github.sha }}
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Duplo Setup
      uses: duplocloud/actions@main

    - name: Build and Push codegenservice Docker Image
      uses: duplocloud/actions/build-image@main
      id: build-and-push-codegenservice
      with:
        push: true
        repo: ${{ env.REPO_NAME }}
        dockerfile: Dockerfile.vscode
        platforms: linux/amd64
        cache: false
        build-args: |
          AWS_ACCESS_KEY_ID=${{ secrets.DUPLO_AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY=${{ secrets.DUPLO_AWS_SECRET_ACCESS_KEY }}
        tags: ${{ github.sha }}
  
  deploy:
    if: contains(github.event.head_commit.message, 'vscodedeploy')
    runs-on: ubuntu-latest
    needs:
    - build
    steps: 
    - name: Checkout code
      uses: actions/checkout@v3
    - name: Duplo Setup
      uses: duplocloud/actions@main
      with:
        admin: true
    - name: Create Kubeconfig
      run: |
        aws eks update-kubeconfig \
        --name duploinfra-dev \
        --region us-west-2
        
#    - name: Configure AWS credentials
#      uses: aws-actions/configure-aws-credentials@v1
#      with:
#        aws-access-key-id: ${{ secrets.DUPLO_AWS_ACCESS_KEY_ID }}
#        aws-secret-access-key: ${{ secrets.DUPLO_AWS_SECRET_ACCESS_KEY }}
#        aws-region: us-west-2
        
#    - name: Login to Amazon ECR
#      id: login-ecr
#      uses: aws-actions/amazon-ecr-login@v1

    - name: Deploy vscode DEV environment
      env:
        IMAGE: "${{ needs.build.outputs.backend_image }}"
      run: |
        echo "Image being used: $IMAGE"
        GIT_SHA="${{ github.sha }}"
        echo $GIT_SHA
        GIT_SHA="${{ github.sha }}"
        kubectl get pods -l pod-status=used -n duploservices-kavia-dev --no-headers | while read -r line; do
          ORIGINAL_POD_NAME=$(echo "$line" | awk '{print $1}')
          STATUS=$(echo "$line" | awk '{print $3}')
          echo $ORIGINAL_POD_NAME
          echo $STATUS
           if [[ "$STATUS" == "Running" ]]; then
             BASE_NAME=$(echo "$ORIGINAL_POD_NAME" | sed -E 's/-[a-z0-9]+-[a-z0-9]+$//')
             echo "Updating deployment: $BASE_NAME"
             kubectl set image deployment/$BASE_NAME vscode-server=$IMAGE -n duploservices-kavia-dev
             for i in {1..10}; do
               NEW_POD_NAME=$(kubectl get pods -n duploservices-kavia-dev -l app=$BASE_NAME --sort-by=.metadata.creationTimestamp -o jsonpath="{.items[-1:].metadata.name}")
               echo "Checking for new pod: $NEW_POD_NAME"
               if [[ -n "$NEW_POD_NAME" ]]; then
                 NEW_STATUS=$(kubectl get pod $NEW_POD_NAME -n duploservices-kavia-dev -o jsonpath="{.status.phase}")
                 if [[ "$NEW_STATUS" == "Running" || "$NEW_STATUS" == "Pending" ]]; then
                   break
                 fi
               fi
               sleep 1
             done   
             kubectl label pod $NEW_POD_NAME pod-status=used --overwrite -n duploservices-kavia-dev
             echo "Labeled new pod: $NEW_POD_NAME"
           else
             echo "Skipping update for pod: $POD_NAME (Status: $STATUS)"
          fi
        done

        echo "INFO: Now updating the ready state pods"
        kubectl get pods -l pod-status=available -n duploservices-kavia-dev --no-headers | while read -r line; do
          ORIGINAL_POD_NAME=$(echo "$line" | awk '{print $1}')
          STATUS=$(echo "$line" | awk '{print $3}')
          echo $ORIGINAL_POD_NAME
          echo $STATUS
          if [[ "$STATUS" == "Running" ]]; then
            BASE_NAME=$(echo "$ORIGINAL_POD_NAME" | sed -E 's/-[a-z0-9]+-[a-z0-9]+$//')
            echo "Pod: $ORIGINAL_POD_NAME is Running."
            echo "Base Name: $BASE_NAME"
            echo "Updating deployment: $BASE_NAME"
            kubectl set image deployment/$BASE_NAME vscode-server=$IMAGE -n duploservices-kavia-dev
          else    
            echo "Skipping update for pod: $POD_NAME (Status: $STATUS)"
          fi
        done
    - name: update image-id for codegen in duploctl
      env:
        IMAGE: "${{ needs.build.outputs.backend_image }}"
      run: |
        echo "Image being used: $IMAGE"
        GIT_SHA="${{ github.sha }}"
        echo $GIT_SHA
        NEW_IMAGE_TAG=${IMAGE##*:}
        echo $NEW_IMAGE_TAG
        echo "finding config map"
        duploctl configmap find codegenservicedeployment4 -o yaml > /tmp/test7.yaml
        cat /tmp/test7.yaml
        echo "defining image prefix"
        IMAGE_PREFIX="127214169382.dkr.ecr.us-west-2.amazonaws.com/vscode-server"
        echo "using sed"
        sed -i -E "s|(${IMAGE_PREFIX}):[a-zA-Z0-9._-]+|\1:${NEW_IMAGE_TAG}|g" /tmp/test7.yaml
        echo "opening final file"
        cat /tmp/test7.yaml
        echo "updating existing config map"
        duploctl configmap update codegenservicedeployment4 -f /tmp/test7.yaml
