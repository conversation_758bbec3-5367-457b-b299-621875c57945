#!/usr/bin/env python3
import json
import uuid
import os
import fcntl
import threading
import time
from datetime import datetime

class TaskQueue:
    def __init__(self, queue_path="/home/<USER>/workspace/.queue"):
        if os.environ.get("LOCAL_DEBUG"):
            self.queue_path = "/tmp/kavia/workspace/.queue"
        else:
            self.queue_path = queue_path
        self._ensure_queue_directory()
        # Add per-task locks to prevent race conditions
        self._task_locks = {}
        self._locks_lock = threading.Lock()
        
    def _get_task_lock(self, task_id):
        """Get or create a lock for a specific task"""
        with self._locks_lock:
            if task_id not in self._task_locks:
                self._task_locks[task_id] = threading.RLock()
            return self._task_locks[task_id]
    
    def _ensure_queue_directory(self):
        """Ensure the queue directory exists"""
        print(f"Ensuring queue directory exists: {self.queue_path}")
        os.makedirs(self.queue_path, exist_ok=True)
    
    def _get_task_file(self, task_id):
        """Get the JSON file path for a specific task"""
        return os.path.join(self.queue_path, f"task_{task_id}.json")
    
    def _read_task_data(self, task_file):
        """Safely read task data from JSON file"""
        if not os.path.exists(task_file):
            return []
        
        try:
            with open(task_file, 'r') as f:
                fcntl.flock(f.fileno(), fcntl.LOCK_SH)  # Shared lock for reading
                content = f.read().strip()
                if not content:
                    return []
                return json.loads(content)
        except (json.JSONDecodeError, FileNotFoundError):
            return []
    
    def _write_task_data(self, task_file, data_list):
        """Safely write task data to JSON file with unique temp file name"""
        # Create unique temp file name to avoid race conditions
        temp_file = f"{task_file}.tmp.{uuid.uuid4().hex[:8]}.{threading.current_thread().ident}"
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # Ensure directory exists
                os.makedirs(os.path.dirname(task_file), exist_ok=True)
                
                # Write to unique temp file
                with open(temp_file, 'w') as f:
                    fcntl.flock(f.fileno(), fcntl.LOCK_EX)  # Exclusive lock for writing
                    json.dump(data_list, f, indent=2)
                
                # Verify temp file exists before rename
                if not os.path.exists(temp_file):
                    raise FileNotFoundError(f"Temp file {temp_file} does not exist after creation")
                
                # Atomic move
                os.rename(temp_file, task_file)
                return
                
            except Exception as e:
                print(f"Attempt {attempt + 1} failed for writing task data: {e}")
                
                # Clean up temp file if it exists
                if os.path.exists(temp_file):
                    try:
                        os.remove(temp_file)
                    except:
                        pass
                
                if attempt == max_retries - 1:
                    raise
                else:
                    # Brief delay before retry
                    time.sleep(0.1 * (attempt + 1))
    
    def enqueue(self, task_id, data):
        """Enqueue data for a specific task ID with thread safety"""
        if not isinstance(data, (list, dict, str, int, float, bool)) and data is not None:
            raise ValueError("Data must be JSON serializable")

        # Use task-specific lock to prevent race conditions
        task_lock = self._get_task_lock(task_id)
        
        with task_lock:
            task_file = self._get_task_file(task_id)
            
            # Create data entry with timestamp
            entry = {
                "data": data,
                "timestamp": datetime.now().isoformat(),
                "id": str(uuid.uuid4())[:8]  # Short ID for tracking
            }
            
            # Read existing data
            existing_data = self._read_task_data(task_file)
            
            # Append new entry
            existing_data.append(entry)
            
            # Write back
            self._write_task_data(task_file, existing_data)
            
            print(f"✅ Enqueued data for task_id: {task_id}")
            print(f"   Data: {data}")
            print(f"   Queue length: {len(existing_data)} items")
            return True
    
    def dequeue(self, task_id):
        """Dequeue one data entry for a specific task ID with thread safety"""
        task_lock = self._get_task_lock(task_id)
        
        with task_lock:
            task_file = self._get_task_file(task_id)
            existing_data = self._read_task_data(task_file)
            
            if not existing_data:
                return None
            
            # Get the first entry
            entry = existing_data.pop(0)
            
            # Write back the remaining data
            if existing_data:
                self._write_task_data(task_file, existing_data)
            else:
                # Remove file if empty
                if os.path.exists(task_file):
                    os.remove(task_file)
            
            return {
                "id": entry["id"],
                "task_id": task_id,
                "data": entry["data"],
                "timestamp": entry["timestamp"]
            }
    
    def peek(self, task_id, count=1):
        """Peek at the next data entries without removing them"""
        task_file = self._get_task_file(task_id)
        existing_data = self._read_task_data(task_file)
        
        if not existing_data:
            return []
        
        return existing_data[:count]
    
    def get_queue_length(self, task_id):
        """Get the number of pending items for a task"""
        task_file = self._get_task_file(task_id)
        existing_data = self._read_task_data(task_file)
        return len(existing_data)
    
    def list_active_tasks(self):
        """List all tasks with pending data"""
        tasks = []
        for filename in os.listdir(self.queue_path):
            if filename.startswith("task_") and filename.endswith(".json"):
                task_id = filename[5:-5]  # Remove "task_" prefix and ".json" suffix
                task_file = os.path.join(self.queue_path, filename)
                data = self._read_task_data(task_file)
                if data:  # Only include tasks with data
                    tasks.append({
                        "task_id": task_id,
                        "count": len(data),
                        "last_updated": max(item.get("timestamp", "") for item in data) if data else ""
                    })
        return tasks
    
    def clear_task(self, task_id):
        """Clear all data for a specific task with thread safety"""
        task_lock = self._get_task_lock(task_id)
        
        with task_lock:
            task_file = self._get_task_file(task_id)
            if os.path.exists(task_file):
                os.remove(task_file)
                return True
            return False
    
    def clear_all_tasks(self):
        """Clear all task data"""
        cleared_count = 0
        for filename in os.listdir(self.queue_path):
            if filename.startswith("task_") and filename.endswith(".json"):
                task_file = os.path.join(self.queue_path, filename)
                os.remove(task_file)
                cleared_count += 1
        return cleared_count
    
    def generate_task_id(self):
        """Generate a random task ID"""
        return str(uuid.uuid4())
    
    def task_exists(self, task_id):
        """Check if a task has any pending data"""
        task_file = self._get_task_file(task_id)
        return os.path.exists(task_file) and len(self._read_task_data(task_file)) > 0 