import asyncio
from fastapi import HTT<PERSON>Exception
from jinja2 import Environment, FileSystemLoader
import json
from app.agents.agent import Agent
from app.models.node_model import ConfigureNodeRequest
from app.discussions.discussion_util import  conduct_discussion,create_root_node_properties
from app.discussions.discussion_factory import DiscussionFactory
from app.utils.node_utils import get_node_type
from app.connection.establish_db_connection import get_node_db
from app.connection.llm_init import get_llm_interface
from app.discussions.discussion import Discussion
from app.discussions.types.architecture_design_details import ArchitectureDesignDetails
from app.telemetry.logger_config import get_logger, set_task_id
from app.core.task_framework import TaskStatus


class SoftwareDesignerAgent(Agent):
    def __init__(self, name, node_id, node_type, root_node_type, discussion_type, logging_context, levels, semaphore, supervisor=None):
        super().__init__(name)
        self.current_step = 0
        self.supervisor = supervisor
        self.llm_interface = get_llm_interface()
        self.node_id = node_id
        self.node_type = node_type
        self.root_node_type = root_node_type
        self.discussion_type = discussion_type
        self.db = get_node_db()
        self.processed_nodes = 0
        self.update_logger_agent = get_logger(__name__)

        semaphore = asyncio.Semaphore(10)
        self.levels = levels
        self.architecture_root_id = None
        if logging_context:
            self.logging_context = logging_context
            self.mongo_handler = logging_context.get('mongo_handler')
            self.task_id = logging_context.get('task_id')
            set_task_id(self.task_id)
        else:
            self.logging_context = {}
            self.task_id = None
            self.mongo_handler = None
        self.queue: asyncio.Queue = asyncio.Queue()

    async def process_work_item(self, work_item_dict):
        try:
            self.current_work_item = work_item_dict
            self.update_logger_agent.info(f"Processing WorkItem: {work_item_dict.entry} for node {work_item_dict.node_id}")
            if work_item_dict.entry == "traverse_architecture":
                await self.traverse_architecture(work_item_dict.node_id, work_item_dict.project_id)
            elif work_item_dict.entry == "interface_definition_all":
                await self.interface_definition_all(work_item_dict.node_id, work_item_dict.node_type)
            elif work_item_dict.entry == "create_design":
                await self.create_design(work_item_dict.node_id, work_item_dict.node_type, work_item_dict.project_id, work_item_dict.levels)
            elif work_item_dict.entry == "create_specification":
                await self.create_specification(work_item_dict.node_id, work_item_dict.node_type)
            elif work_item_dict.entry == "interface_definition":
                await self.interface_definition(work_item_dict.node_id, work_item_dict.node_type)
            elif work_item_dict.entry == "create_software_architecture_docs":
                await self.create_software_architecture_docs(work_item_dict.node_id, work_item_dict.node_type)
            elif work_item_dict.entry == "create_behavior":
                await self.create_behavior(work_item_dict.node_id, work_item_dict.node_type)
            elif work_item_dict.entry == "create_interaction_diagrams":
                await self.create_interaction_diagrams(work_item_dict.node_id, work_item_dict.node_type)
            elif work_item_dict.entry == "create_class":
                await self.create_class(work_item_dict.node_id, work_item_dict.node_type)
            elif work_item_dict.entry == "create_apidocs":
                await self.create_apidocs(work_item_dict.node_id, work_item_dict.node_type)
            elif work_item_dict.entry == "finalize":
                await self.finalize(work_item_dict.node_id, work_item_dict.node_type)
            else:
                self.update_logger_agent.warning(f"Task {work_item_dict.entry} not found for agent {self.name}")
        except Exception as e:
            self.update_logger_agent.error(f"Error processing work item: {str(e)}", exc_info=True)

    async def run(self):
        while True:
            try:
                work_item_dict = await self.queue.get()
                await self.process_work_item(work_item_dict)
            except Exception as e:
                self.update_logger_agent.error(f"Error in run method: {str(e)}", exc_info=True)
            finally:
                self.queue.task_done()

    async def check_parent_configurations(self, node_id):
        """
        Check if all parent nodes up to the project node are properly configured.
        
        Args:
            node_id: ID of the node to check
            
        Returns:
            tuple: (is_valid, message) where:
                - is_valid is a boolean indicating if all parents are configured
                - message is a string containing error details or None if valid
        """
        try:
            # Get the current node
            node = await self.db.get_node_by_id(node_id)
            if not node:
                return False, f"Node {node_id} not found"
                
            # Initialize list to track unconfigured parents
            unconfigured_parents = []
            
            # Start with the immediate parent
            current_id = node_id
            
            # Check all parents up to project node
            while current_id:
                parent_node = await self.db.get_parent_node(current_id)
                
                # If no parent found, we've reached the top of the hierarchy
                if not parent_node:
                    break
                    
                # Check if parent is configured
                parent_state = parent_node.get('properties', {}).get('configuration_state')
                if parent_state != 'configured':
                    parent_type = Discussion.get_specific_node_type(parent_node['labels'])
                    parent_title = parent_node.get('properties', {}).get('Title', f"Node {parent_node['id']}")
                    unconfigured_parents.append(f"{parent_type}: {parent_title}")
                    
                # Move up to next parent
                current_id = parent_node['id']
                
                # Stop if we reach a Project node (top of hierarchy)
                if 'Project' in parent_node['labels']:
                    break
            
            # Determine result
            if unconfigured_parents:
                return False, f"The following parent nodes must be configured first: {', '.join(unconfigured_parents)}"
            
            return True, None
            
        except Exception as e:
            self.update_logger_agent.error(f"Error checking parent configurations: {str(e)}", exc_info=True)
            return False, f"Error checking parent configurations: {str(e)}"

    async def traverse_architecture(self, node_id, project_id):
        """Wrapper for architecture traversal"""
        task_config = await self.mongo_handler.get_by_task_id(self.task_id, self.mongo_handler.db)
        
        architecture_model = task_config.get('architecture_model', 'legacy')
        #Hard coded to c4 for now 
        architecture_model = 'c4'
        if architecture_model == 'c4':
            await self.traverse_c4_architecture(project_id)
        else:
            await self.traverse_legacy_architecture(node_id,project_id)

    async def traverse_c4_architecture(self, project_id):
        """Traverse C4 model components and configure design for leaf components"""
        try:
            query = """
            MATCH (p:Project)-[:HAS_CHILD]->(sc:SystemContext)
            -[:HAS_CHILD]->(container:Container)
            -[:HAS_CHILD]->(component:Component)
            WHERE ID(p) = $project_id
            RETURN DISTINCT ID(component) as id, component.Title as title
            """
            components = await self.db.async_run(query, project_id=project_id)

            components_configured = 0

            for component in components:
                self.update_logger_agent.info(f"Design configuration completed for ******** {component} components")
                # Get existing design node - no creation
                design_nodes = await self.db.get_child_nodes(component['id'], "Design")
                if not design_nodes:
                    self.update_logger_agent.warning(f"No design node found for leaf component {component['id']}. Skipping configuration.")
                    continue
                    
                design_node = design_nodes[0]
                self.current_work_item.node_id = design_node['id']
                self.current_work_item.node_type = "Design"

                # Configure all design aspects
                await self.create_specification(design_node['id'], "Design")
                await self.create_class(design_node['id'], "Design")
                await self.create_behavior(design_node['id'], "Design")
                await self.create_interaction_diagrams(design_node['id'], "Design")
                
                components_configured += 1

            self.update_logger_agent.info(f"Design configuration completed for {components_configured} components")
            self.current_work_item.node_type = "Design"
            # Notify completion
            await self.supervisor.notify_complete(self.current_work_item)
  
            
        except Exception as e:
            self.update_logger_agent.error(f"Error in traverse_c4_architecture: {str(e)}", exc_info=True)
            # Notify completion even in error case
            await self.supervisor.notify_complete(self.current_work_item)


    async def create_or_get_design_node(self, component_id):
        """Create or get existing design node for a component"""
        component_node = await self.db.get_node_by_id(component_id)
        existing = await self.db.get_child_nodes(component_id, "Design")
        if existing:
            self.update_logger_agent.info("Using existing design node")
            return existing[0]
            
        self.update_logger_agent.info("Creating new design node")
        properties = {
                    "Title": f"Design for {component_node['properties']['Title']}",
                    "Type": "Design"
                }
        
        return await self.db.create_node(["Design"], properties, component_id)

    async def traverse_legacy_architecture(self, node_id, project_id):
        try:
            self.update_logger_agent.info(f"Traversing architecture for project: {project_id}")
            task_configuration = await self.mongo_handler.get_by_task_id(self.task_id, self.mongo_handler.db)

            if not task_configuration:
                task_configuration = {'task_id': self.task_id, 'queue_messages': [], 'task_status': 'In Progress', 'configuration_status': {}}
                await self.mongo_handler.insert(task_configuration, self.mongo_handler.db)
                await self.send_update(self.task_id)

            self.queue_messages = task_configuration.get('queue_messages', [])
            self.configuration_status = task_configuration.get('configuration_status', {})
            
            node = await self.db.get_node_by_id(project_id)

            if "ArchitectureRoot" in node['labels']:
                architecture_root = node
                self.architecture_root_id = architecture_root['id']
            else: 
                architecture_root = await self.db.get_child_nodes(project_id, "ArchitectureRoot")
                self.architecture_root_id = architecture_root[0]['id']
            if not architecture_root:
                self.update_logger_agent.error("Architecture root not found")
                return

            self.leaf_count = 0

            async def traverse(node_id):
                node = await self.db.get_node_by_id(node_id)
                self.update_logger_agent.info(f"Traversing node: {node['properties'].get('Title', 'Untitled')} (ID: {node_id})")
                
                if node['properties'].get('IsArchitecturalLeaf', '').lower() == 'yes':
                    if not self.configuration_status.get(str(node_id)):
                        self.configuration_status[str(node_id)] = {
                            'node_type': "Architecture",
                            'title': node['properties']['Title'] if node else '',
                            'configuration': 'configured'
                        }
                    else:
                        self.configuration_status[str(node_id)]['title'] = node['properties']['Title'] if node else ''
                        self.configuration_status[str(node_id)]['configuration'] = 'configured'
                    
                    self.leaf_count += 1
                    await self.create_design(node_id, 'Design')
                    await self.db.update_node_by_id(node_id, {f'{"design_configuration"}_state': 'configured'}, 'Design')

                else:
                    child_nodes = await self.db.get_child_nodes(node_id, 'Architecture')
                    for child in child_nodes:
                        await traverse(child['id'])

            await traverse(self.architecture_root_id)
            self.update_logger_agent.info(f"Traversal complete. Processed {self.leaf_count} leaf nodes.")

            await self.supervisor.notify_complete(self.current_work_item)
        except Exception as e:
            self.update_logger_agent.error(f"Error in traverse_architecture: {str(e)}", exc_info=True)

    async def create_design(self, node_id, node_type):
        try:
            self.update_logger_agent.info(f"Creating design for node: {node_id}")
            self.node_id = node_id
            self.node_type = node_type
            self.node = await self.db.get_node_by_id(node_id)

            result = await self.db.get_child_nodes(self.node_id, "Design")
            if not result:
                properties = {
                    "Title": f"Design for {self.node['properties']['Title']}"
                }
                design_node = await self.db.create_node(["Design"], properties, self.node_id)
                self.update_logger_agent.info(f"Created new design node: {design_node['id']}")
            else:
                design_node = result[0]
                self.update_logger_agent.info(f"Using existing design node: {design_node['id']}")

            self.current_work_item.node_id = design_node['id']
            self.current_work_item.node_type = "Design"

            await self.supervisor.notify_complete(self.current_work_item)
        except Exception as e:
            self.update_logger_agent.error(f"Error in create_design: {str(e)}", exc_info=True)

    async def create_specification(self, node_id, node_type):
        try:
            self.update_logger_agent.info(f"Creating specification for node: {node_id}")
            self.node_id = node_id
            self.node_type = node_type
            await self.configure_node(node_id, node_type, self.root_node_type, "specification")
            # await self.supervisor.notify_complete(self.current_work_item)
        except Exception as e:
            self.update_logger_agent.error(f"Error in create_specification: {str(e)}", exc_info=True)

    async def interface_definition(self, node_id, node_type):
        try:
            self.update_logger_agent.info(f"Defining interfaces for node: {node_id}")
            self.node_id = node_id
            self.node_type = node_type
            architecture_node = await self.db.get_parent_node(self.node_id)
            interfaces = await self.db.get_relationships_involving_node(architecture_node['id'], "INTERFACES_WITH")
            
            for interface in interfaces:
                interface_state = interface['properties'].get('definition_state')
                if interface_state == 'configured':
                    continue
                if interface['target'] == architecture_node['id']:
                    source_node_id = interface['source']
                    target_node_id = self.node_id
                    source_node = await self.db.get_node_by_id(source_node_id)
                    result = await self.db.get_child_nodes(source_node['id'], "Design")
                    source_design_node = result[0] if result else None
                    if source_design_node is None:
                        continue
                if interface['source'] == architecture_node['id']:
                    target_node_id = interface['target']
                    source_node_id = self.node_id
                    target_node = await self.db.get_node_by_id(target_node_id)
                    result = await self.db.get_child_nodes(target_node['id'], "Design")
                    target_design_node = result[0] if result else None
                    if target_design_node is None:
                        continue
                
                interface_node_id = interface['properties'].get('interface_node_id')
                await self.configure_node(interface_node_id, "Interface", self.root_node_type, "definition")
                await self.db.update_relationship_properties(source_node_id, target_node_id, "INTERFACES_WITH", {"definition_state": "configured"})

            await self.supervisor.notify_complete(self.current_work_item)
        except Exception as e:
            self.update_logger_agent.error(f"Error in interface_definition: {str(e)}", exc_info=True)

    async def create_software_architecture_docs(self, node_id, node_type):
        try:
            self.update_logger_agent.info(f"Creating software architecture docs for node: {node_id}")
            self.node_id = node_id
            self.node_type = node_type
            await self.configure_node(self.node_id, self.node_type, self.root_node_type, "software_architecture_doc")
            # await self.supervisor.notify_complete(self.current_work_item)
        except Exception as e:
            self.update_logger_agent.error(f"Error in create_software_architecture_docs: {str(e)}", exc_info=True)

    async def create_behavior(self, node_id, node_type):
        try:
            self.update_logger_agent.info(f"Creating behavior for node: {node_id}")
            self.node_id = node_id
            self.node_type = node_type
            await self.configure_node(self.node_id, self.node_type, self.root_node_type, "behavior")
            # await self.supervisor.notify_complete(self.current_work_item)
        except Exception as e:
            self.update_logger_agent.error(f"Error in create_behavior: {str(e)}", exc_info=True)

    async def create_class(self, node_id, node_type):
        try:
            self.update_logger_agent.info(f"Creating class diagram for node: {node_id}")
            self.node_id = node_id
            self.node_type = node_type
            await self.configure_node(self.node_id, self.node_type, self.root_node_type, "classdiagram")
            # await self.supervisor.notify_complete(self.current_work_item)
        except Exception as e:
            self.update_logger_agent.error(f"Error in create_class: {str(e)}", exc_info=True)

    async def create_apidocs(self, node_id, node_type):
        try:
            self.update_logger_agent.info(f"Creating API docs for node: {node_id}")
            self.node_id = node_id
            self.node_type = node_type
            await self.configure_node(self.node_id, self.node_type, self.root_node_type, "apidocs")
            # await self.supervisor.notify_complete(self.current_work_item)
        except Exception as e:
            self.update_logger_agent.error(f"Error in create_apidocs: {str(e)}", exc_info=True)

    async def create_interaction_diagrams(self, node_id, node_type):
        try:
            self.update_logger_agent.info(f"Creating interaction diagrams for node: {node_id}")
            self.node_id = node_id
            self.node_type = node_type
            await self.configure_node(self.node_id, self.node_type, self.root_node_type, "component_interactions")
            # await self.supervisor.notify_complete(self.current_work_item)
        except Exception as e:
            self.update_logger_agent.error(f"Error in create_interaction_diagrams: {str(e)}", exc_info=True)
    
    async def create_deployment_configuration(self, node_id, node_type):
        try:
            self.update_logger_agent.info(f"Creating deployment configuration for node: {node_id}")
            self.node_id = node_id
            self.node_type = node_type
            await self.configure_node(self.node_id, self.node_type, self.root_node_type, "deployment_configuration")
            # await self.supervisor.notify_complete(self.current_work_item)
        except Exception as e:
            self.update_logger_agent.error(f"Error in create_deployment_configuration: {str(e)}", exc_info=True)

    async def finalize(self, node_id, node_type):
        try:
            self.update_logger_agent.info(f"Finalizing node: {node_id}")
            self.node_id = node_id
            self.node_type = node_type
            self.processed_nodes += 1

            if self.processed_nodes == self.leaf_count:
                self.update_logger_agent.info("All nodes have been traversed and design nodes have been created.")
                # Update the task status to indicate completion
                task_configuration = await self.mongo_handler.get_by_task_id(self.task_id, self.mongo_handler.db)
                task_configuration['task_status'] = 'Completed'
                await self.mongo_handler.update_by_task_id(self.task_id, task_configuration)
            else:
                self.update_logger_agent.info(f"{self.processed_nodes} out of {self.leaf_count} nodes were processed.")

            # Finalize the architecture configuration
            await self.db.update_node_by_id(self.node_id, {"autoconfig_state": "configured"}, self.node_type)
            self.update_logger_agent.info(f"Node {self.node_id} autoconfig_state set to configured")
        except Exception as e:
            self.update_logger_agent.error(f"Error in finalize: {str(e)}", exc_info=True)

    async def configure_node(self, node_id, node_type, root_node_type, configuration_type):
        """
        Configure or reconfigure a requirement root node. This method handles both initial configuration 
        and subsequent reconfigurations based on system changes.
        
        Args:
            node_id: The ID of the node to configure
            node_type: The type of the node (RequirementRoot in this case)
            root_node_type: The type of the root node (typically Project)
            configuration_type: The type of configuration to perform
        """
        try:
            # Log the start of configuration process
            self.update_logger_agent.info(f"Starting configuration process for requirement root node: {node_id}")

            # Get or create task configuration from MongoDB
            # This maintains the state and progress of the configuration task
            task_configuration = await self.mongo_handler.get_by_task_id(self.task_id, self.mongo_handler.db)
            
            if not task_configuration:
                # Initialize new task configuration if none exists
                task_configuration = {
                    'task_id': self.task_id, 
                    'queue_messages': [], 
                    'task_status': 'In Progress',
                    'configuration_status': {},
                    'progress': 0
                }
                await self.mongo_handler.insert(task_configuration, self.mongo_handler.db)
                await self.send_update(self.task_id)
                self.update_logger_agent.info(f"Created new task configuration for task {self.task_id}")

            # Extract task tracking information
            self.queue_messages = task_configuration['queue_messages']
            self.configuration_status = task_configuration['configuration_status']

            # Retrieve the node to be configured
            node = await self.db.get_node_by_id(node_id)
            if not node:
                self.update_logger_agent.error(f"Node {node_id} not found")
                self.configuration_status[str(node_id)] = {
                    'node_type': node_type,
                    'title': 'Node not found',
                    configuration_type: 'not_found'
                }
                await self.mongo_handler.update_by_task_id(self.task_id, 
                                                        {'configuration_status': self.configuration_status})
                await self.send_update(self.task_id)
                return None

            # Update configuration status tracking
            # This helps in monitoring the progress of configuration
            if not self.configuration_status.get(str(node_id)):
                self.configuration_status[str(node_id)] = {
                    'node_type': node_type,
                    'title': node['properties']['Title'],
                    configuration_type: 'configuring'
                }
            else:
                self.configuration_status[str(node_id)]['title'] = node['properties']['Title']
                self.configuration_status[str(node_id)][configuration_type] = 'configuring'

            await self.mongo_handler.update_by_task_id(self.task_id, 
                                                    {'configuration_status': self.configuration_status})
            await self.send_update(self.task_id)

            # Get the specific node type from the node's labels
            # This ensures we're using the correct type for the discussion
            node_type = Discussion.get_specific_node_type(node['labels'])
            
            # Initiate the configuration discussion
            # This will handle both initial configuration and reconfiguration
            # The discussion class will manage version comparison and change detection
            await conduct_discussion(
                node_id=node_id,
                node_type=node_type,
                root_node_type=root_node_type,
                discussion_type=configuration_type,
                logging_context=self.logging_context,
                supervisor=self.supervisor
            )

            # Update the configuration status to reflect completion
            self.configuration_status[str(node_id)][configuration_type] = 'configured'
            await self.mongo_handler.update_by_task_id(self.task_id, 
                                                    {'configuration_status': self.configuration_status})
            await self.send_update(self.task_id)
            
            # Log successful completion
            self.update_logger_agent.info(f"Successfully completed configuration for node {node_id}")
            
            # Retrieve and return the updated node
            node = await self.db.get_node_by_id(node_id)
            return node

        except Exception as e:
            await self.mongo_handler.update_by_task_id(
                    self.task_id, 
                    {
                        'task_status': TaskStatus.FAILED, 
                        'run_completed': True, 
                        'progress': 100
                    }
                )
            # Log any errors that occur during configuration
            self.update_logger_agent.error(f"Error in configure_node for node {node_id}: {str(e)}", 
                                        exc_info=True)
            raise 