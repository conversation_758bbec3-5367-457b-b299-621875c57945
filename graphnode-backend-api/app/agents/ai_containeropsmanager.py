import asyncio
from app.agents.agent import Agent
from app.discussions.discussion_util import  conduct_discussion,create_root_node_properties
from app.utils.node_utils import get_node_type
from app.connection.establish_db_connection import get_node_db
from app.discussions.discussion import Discussion
from app.telemetry.logger_config import get_logger
from app.discussions.types.databasemodel_configuration import DatabaseModelDiscussion
import datetime
import json
from app.core.task_framework import TaskStatus

class ContainerOpsAgent(Agent):
    def __init__(self, name, node_id, node_type, root_node_type, discussion_type, logging_context, levels, semaphore, supervisor=None):
        super().__init__(name)
        self.current_step = 0
        self.supervisor = supervisor
        self.node_id = node_id
        self.node_type = node_type
        
        self.root_node_type = root_node_type
        self.discussion_type = discussion_type
        self.db = get_node_db()
        self.semaphore = semaphore
        self.levels = levels
        self.architecture_root_id = None
        self.update_logger_agent = get_logger(__name__)

        if logging_context:
            self.logging_context = logging_context
            self.mongo_handler = logging_context.get('mongo_handler')
            self.task_id = logging_context.get('task_id')
        else:
            self.logging_context = {}
            self.task_id = None
            self.mongo_handler = None
        self.queue: asyncio.Queue = asyncio.Queue()

    async def process_work_item(self, work_item_dict):
        try:
            self.current_work_item = work_item_dict
            if work_item_dict.entry == "traverse_containers":
                await self.traverse_containers(work_item_dict.node_id, work_item_dict.node_type,work_item_dict.project_id)
            
        except Exception as e:
            self.update_logger_agent.error(f"Error processing work item: {str(e)}", exc_info=True)

    async def traverse_containers(self, node_id, node_type, project_id):
        """Configure containers in C4 model"""
        try:
            # Get system context
            self.project_id = project_id
            system_context = await self.db.get_child_nodes(project_id, "SystemContext")
            if not system_context:
                raise ValueError("System context not found")
            
            system_context = system_context[0]
            
            # Get existing containers
            containers = await self.db.get_child_nodes(system_context['id'], "Container")
            
            # Configure each container
            for container in containers:
                container_type = await self.determine_component_type(container)
                print(container['properties'].get('Title', '').lower())
                print(f"Container type: {container_type}")
                container_type = 'frontend'
                # Configure deployment for frontend and backend components
                if container_type in ['frontend', 'backend']:
                    self.update_logger_agent.info(f"Configuring deployment for {container_type} component: {container['properties'].get('Title', 'Untitled')}")
                    await self.configure_deployment(container['id'], container_type)
                else:
                    await self.configure_database(container['id'], container_type)
                    self.update_logger_agent.info(f"Skipping deployment configuration for {container_type} component: {container['properties'].get('Title', 'Untitled')}")

            # Notify completion
            self.current_work_item.node_id = system_context['id']
            self.current_work_item.node_type = "SystemContext"
            await self.supervisor.notify_complete(self.current_work_item)
            
        except Exception as e:
            self.update_logger_agent.error(f"Error configuring containers: {str(e)}", exc_info=True)
            raise

    async def configure_deployment(self, container_id, container_type):
        """Configure deployment settings for a specific component."""
        try:
            self.update_logger_agent.info(f"Configuring deployment for container {container_id} of type {container_type}")
            
            # Get deployment root
            deployment_root = await self.db.get_child_nodes(container_id, "Deployment")
            if deployment_root and len(deployment_root) > 0:
                node_to_configure = deployment_root[0]
            else:
                container_node = await self.db.get_node_by_id(container_id)
                properties = create_root_node_properties(["Deployment"], self.root_node_type, container_node)
                node_to_configure = await self.db.create_node(["Deployment"], properties, container_id)

            if not node_to_configure:
                error_message = f"Failed to create {container_type}"
                self.update_logger_agent.error(error_message)
                self.queue_messages.append({"error": error_message})
                await self.mongo_handler.push_to_array({'task_id': self.task_id}, 'queue_messages', {"error": error_message})
                self.ws_client.send_message("update_queue_messages", json.dumps(self.queue_messages))
                return None
                
            
            deployment_root = node_to_configure
            
            await self.configure_node(deployment_root['id'], "Deployment", self.root_node_type, "Configuration")

            self.update_logger_agent.info(f"Deployment configuration completed for component {container_id}")
            
        except Exception as e:
            self.update_logger_agent.error(f"Error configuring deployment for component {container_id}: {str(e)}")
            raise
    
    async def configure_database(self, container_id, container_type):
        """Configure deployment settings for a specific component."""
        try:
            self.update_logger_agent.info(f"Configuring deployment for container {container_id} of type {container_type}")
            
            # Get deployment root
            DatabaseModel = await self.db.get_child_nodes(container_id, "DatabaseModel")
            if DatabaseModel and len(DatabaseModel) > 0:
                node_to_configure = DatabaseModel[0]
            else:
                container_node = await self.db.get_node_by_id(container_id)
                properties = create_root_node_properties("Database", self.root_node_type, container_node)
                node_to_configure = await self.db.create_node(["DatabaseModel"], properties, container_id)

            if not node_to_configure:
                error_message = f"Failed to create {container_type}"
                self.update_logger_agent.error(error_message)
                self.queue_messages.append({"error": error_message})
                await self.mongo_handler.push_to_array({'task_id': self.task_id}, 'queue_messages', {"error": error_message})
                return None
                
            
            DatabaseModel = node_to_configure
            
            await self.configure_node(DatabaseModel['id'], "DatabaseModel", self.root_node_type, "Configuration")

            
            self.update_logger_agent.info(f"DatabaseModel configuration completed for component {container_id}")
            
        except Exception as e:
            self.update_logger_agent.error(f"Error configuring DatabaseModel for component {container_id}: {str(e)}")
            raise
        

    async def determine_component_type(self, component):
        """
        Analyze component to determine its type based on properties and description.
        Returns: str - Component type ('frontend', 'backend', 'database', or 'unknown')
        """
        # Extract properties and convert to lowercase for case-insensitive matching
        title = component['properties'].get('Title', '').lower()
        description = component['properties'].get('Description', '').lower()
        technology = component['properties'].get('Technology', '').lower()
        
        # Combine all fields for comprehensive searching
        combined_text = f"{title} {description} {technology}"
        
        # Enhanced keyword sets with more comprehensive terms
        database_keywords = {
            'database', 'db', 'storage', 'mongo', 'postgresql', 'mysql', 'redis',
            'oracle', 'sqlserver', 'cassandra', 'elasticsearch', 'dynamodb',
            'mariadb', 'sql', 'nosql', 'data store'
        }
        
        frontend_keywords = {
            'ui', 'frontend', 'react', 'vue', 'angular', 'web', 'client',
            'browser', 'spa', 'html', 'css', 'javascript', 'tsx', 'jsx'
        }
        
        backend_keywords = {
            'api', 'backend', 'server', 'service', 'nodejs', 'java', 'python',
            'microservice', 'rest', 'graphql', 'grpc', 'flask', 'django', 'express'
        }

        # Count matches for each type to handle components that might match multiple types
        matches = {
            'database': sum(1 for keyword in database_keywords if keyword in combined_text),
            'frontend': sum(1 for keyword in frontend_keywords if keyword in combined_text),
            'backend': sum(1 for keyword in backend_keywords if keyword in combined_text)
        }
        
        # Debug logging to help track matches
        print(f"Component: {title}")
        print(f"Matches found: {matches}")
        print("--------------------}")
        
        # If we have matches, return the type with the most matches
        if any(matches.values()):
            return max(matches.items(), key=lambda x: x[1])[0]
        
        return "unknown"
    
    async def configure_node(self, node_id, node_type, root_node_type, configuration_type, semaphore=None, levels=0):
        try:
            self.update_logger_agent.info(f"Configuring node: ID={node_id}, Type={node_type}, Root Type={root_node_type}, Config Type={configuration_type}")

            task_configuration = await self.mongo_handler.get_by_task_id(self.task_id, self.mongo_handler.db)

            if not task_configuration:
                task_configuration = {'task_id': self.task_id, 'queue_messages': [], 'task_status': 'In Progress', 'configuration_status': {}, 'progress': 0}
                await self.mongo_handler.insert(task_configuration, self.mongo_handler.db)
                self.ws_client.send_message("update_task_configuration", json.dumps(task_configuration))
                self.update_logger_agent.info(f"Created new task configuration for task {self.task_id}")

            self.queue_messages = task_configuration['queue_messages']
            self.configuration_status = task_configuration['configuration_status']
            
            node = await self.db.get_node_by_id(node_id)
            
            if not node:
                self.update_logger_agent.error(f"Node {node_id} not found")
                self.configuration_status[str(node_id)] = {
                    'node_type': node_type,
                    'title': 'Node not found',
                    configuration_type: 'not_found'
                }
                await self.mongo_handler.update_by_task_id(self.task_id, {'configuration_status': self.configuration_status})
                await self.send_update(self.task_id)
                return None

            if not self.configuration_status.get(str(node_id)):
                self.configuration_status[str(node_id)] = {
                    'node_type': node_type,
                    'title': node['properties']['Title'],
                    configuration_type: 'configuring'
                }
            else:
                self.configuration_status[str(node_id)]['title'] = node['properties']['Title']
                self.configuration_status[str(node_id)][configuration_type] = 'configuring'
            
            if node_type not in node['labels']:
                self.update_logger_agent.error(f"Node {node_id} is not of type {node_type}")
                self.configuration_status[str(node_id)][configuration_type] = 'failed'
                await self.mongo_handler.update_by_task_id(self.task_id, {'configuration_status': self.configuration_status})
                await self.send_update(self.task_id)
                return None
            
            state = node['properties'].get(f'{configuration_type}_state')

            if state != 'configured':
                self.update_logger_agent.info(f"Configuring node {node_id} for {configuration_type}")
                self.configuration_status[str(node_id)][configuration_type] = 'configuring'
                await self.mongo_handler.update_by_task_id(self.task_id, {'configuration_status': self.configuration_status})
                await self.send_update(self.task_id)
                
                node_type = Discussion.get_specific_node_type(node['labels'])
                if node_type == None:
                    node_type = node_type
                await conduct_discussion(node_id, node_type=node_type, root_node_type=root_node_type, discussion_type=configuration_type, logging_context=self.logging_context, semaphore=semaphore, levels=levels,supervisor=self.supervisor)

                self.configuration_status[str(node_id)][configuration_type] = 'configured'
                await self.mongo_handler.update_by_task_id(self.task_id, {'configuration_status': self.configuration_status})
                await self.send_update(self.task_id)
                await self.db.update_node_by_id(node_id, {f'{configuration_type}_state': 'configured'}, node_type)
                self.update_logger_agent.info(f"Node {node_id} configured successfully for {configuration_type}")
            else:
                self.update_logger_agent.info(f"Node {node_id} is already configured for {configuration_type}")
                self.configuration_status[str(node_id)][configuration_type] = 'already_configured'
                await self.mongo_handler.update_by_task_id(self.task_id, {'configuration_status': self.configuration_status})
                await self.send_update(self.task_id)
            node = await self.db.get_node_by_id(node_id)
            return node

        except Exception as e:
            await self.mongo_handler.update_by_task_id(
                    self.task_id, 
                    {
                        'task_status': TaskStatus.FAILED, 
                        'run_completed': True, 
                        'progress': 100
                    }
                )
            self.update_logger_agent.error(f"Error in configure_node for node {node_id}: {str(e)}", exc_info=True)
            raise
    

