{"API": {"title_prefix": "API Documentation", "root_description_template": "Complete API documentation for {title} ({type}). {description}", "sections": [{"title": "Resource Description", "type": "resource_description", "description": "Detailed description of the API resource and its capabilities", "order": 1}, {"title": "Endpoints and Methods", "type": "endpoints_methods", "description": "List of all available endpoints and their HTTP methods", "order": 2}, {"title": "Parameters", "type": "parameters", "description": "Detailed description of all request parameters", "order": 3}, {"title": "Request Examples", "type": "request_examples", "description": "Example requests for each endpoint", "order": 4}, {"title": "Response Schemas", "type": "response_schemas", "description": "Response formats and examples", "order": 5}]}, "PRD": {"title_prefix": "Product Requirements Document", "root_description_template": "Product Requirements Documentation for {title}. {description}", "sections": [{"title": "Product Overview", "type": "product_overview", "description": "providing a high-level overview of the product, its core value proposition, and key objectives. This section should be informative enough for stakeholders to quickly understand the product's purpose.Start with a one-paragraph product overview. Do not include timelines and Team compositions. Here is a guide for constructing sub section: - Define the primary purpose and value proposition - List key differentiators from existing solutions - Specify the target market segment", "order": 1}, {"title": "Problem Statement", "type": "problem_statement", "description": "clearly articulating the specific problems or needs the product aims to address. This includes market analysis, user pain points, and any relevant research or data supporting the problem's existence and significance. Here is a guide for constructing sub section: 1. Describe the current situation and challenges 2. Provide market research data supporting the problem's existence 3. Outline the impact of not solving this problem 4. Include relevant user feedback or market insights 5. Quantify the problem's scope. Key areas to Cover with above content: - What specific problems are users facing? - Why do existing solutions fall short? - What evidence supports this being a significant problem?", "order": 2}, {"title": "Product Goals and Objectives", "type": "product_goals", "description": "outlining specific, measurable targets the product should achieve. This includes business goals, user-centric objectives, and success metrics that will determine if the product meets its intended purpose. Use Markdown tables where its needed. Here is a guide for constructing sub section: 1. Define SMART goals (Specific, Measurable, Achievable, Relevant, Time-bound) 2. List primary objectives (3-5 maximum) 3. Define success metrics for each goal 4. Align objectives with company strategy", "order": 3}, {"title": "User Personas and Target Audience", "type": "user_personas", "description": "intended users in detail. This section includes demographic information, behavioral patterns, needs, goals, and frustrations of your target users. Include use cases and user journeys to illustrate how different personas will interact with the product.  Use Markdown tables where its needed. Here is a guide for constructing sub section: 1. Create detailed user profiles including: - Demographics - Behavioral patterns - Goals and motivations - Pain points and frustrations 2. Develop user scenarios 3. Map user journeys 4. Define use cases", "order": 4}, {"title": "Functional Requirements", "type": "functional_requirements", "description": "Functional Requirements details the specific features and capabilities the product must have. This section should describe what the product does, how it behaves, and what functionalities it offers. Each requirement should be specific, measurable, and testable. Here is a guide for constructing sub section: 1. List features in priority order 2. For each feature, specify: - Detailed description - User benefit - Acceptance criteria - Dependencies - Priority level 3. Include input and output requirements 4. Define system behaviors and interactions 5. Specify error handling scenarios", "order": 5}, {"title": "Non-Functional Requirements", "type": "non_functional_requirements", "description": "Non-Functional Requirements covers aspects like performance specifications, security requirements, scalability needs, and other technical constraints. This includes loading times, uptime requirements, compliance needs, and any other quality attributes. Here is a guide for constructing sub section: 1. Define performance requirements: - Load times - Response times - Concurrent users 2. Specify security requirements 3. List compliance needs 4. Define scalability requirements 5. Include reliability metrics 6. Specify compatibility requirements", "order": 6}, {"title": "Success Metrics", "type": "success_metrics", "description": "Success Criteria and Metrics defines how success will be measured, including specific KPIs, performance metrics, and acceptance criteria for each requirement.  Use Markdown tables where its needed. Here is a guide for constructing sub section: 1. Define key performance indicators (KPIs) 2. Set baseline metrics 3. Establish target metrics 4. Define measurement methods 5. Set up tracking mechanisms 6. Create reporting structure", "order": 7}]}, "SAD": {"title_prefix": "Software Architecture Document", "root_description_template": "Software Architecture Documentation for {title}. {description}", "sections": [{"title": "Architecture Overview", "type": "architecture_overview", "description": "Provides a comprehensive high-level overview of the system architecture. This section should include the architectural style/pattern chosen, key architectural principles, system-wide design decisions, and technical constraints. Include diagrams appropriately using mermaid with correct syntax , wrap the diagrams <mermaid> tags. Here is a guide for constructing sub section: 1. Define the overall architectural style (e.g., microservices, monolithic, event-driven) 2. List key architectural principles and patterns being followed 3. Outline major system components and their relationships 4. Describe technical constraints and assumptions", "order": 1}, {"title": "System Context", "type": "system_context", "description": "Details the system boundaries, external dependencies, and interactions with other systems. This section should clearly define what is within and outside the system scope. Here is a guide for constructing sub section: 1. Identify all external systems and interfaces 2. Define system boundaries and scope 3. Document all integration points 4. Describe communication protocols and methods 5. Include context diagrams showing system interactions 6. List all external dependencies and third-party services", "order": 2}, {"title": "Design Decisions", "type": "design_decisions", "description": "Documents key architectural decisions and their rationale, including alternatives considered and trade-offs made. Here is a guide for constructing sub section: 1. List significant architectural decisions using ADR (Architecture Decision Record) format 2. For each decision include: - Context and problem statement - Considered alternatives - Chosen solution and justification - Consequences and trade-offs 3. Document technical constraints that influenced decisions 4. Include relevant proof-of-concepts or prototypes 5. Reference any architectural fitness functions", "order": 3}, {"title": "Component Design", "type": "component_design", "description": "Provides detailed design of system components, their responsibilities, and interactions. This section should cover both high-level components and their internal structures. Include diagrams appropriately using mermaid with correct syntax , wrap the diagrams <mermaid> tags. Here is a guide for constructing sub section: 1. Define component boundaries and responsibilities 2. Document component interfaces and APIs 3. Describe internal component structure and design patterns 4. Detail component interactions and dependencies 5. Include component diagrams and sequence diagrams 6. Specify error handling and fault tolerance mechanisms 7. Document component configuration and deployment requirements", "order": 4}, {"title": "Data Architecture", "type": "data_architecture", "description": "Comprehensive coverage of data architecture including data models, storage solutions, and data flow patterns. Use Markdown tables where its needed. Here is a guide for constructing sub section: 1. Define data models and schemas 2. Document database design and storage solutions 3. Describe data flow patterns and ETL processes 4. Specify data retention and archival strategies 5. Include data security and privacy considerations 6. Detail backup and recovery procedures 7. Document data consistency and transaction management approaches 8. Specify caching strategies and data access patterns", "order": 5}, {"title": "Security Architecture", "type": "security_architecture", "description": "Details the security design, including authentication, authorization, data protection, and security controls. Here is a guide for constructing sub section: 1. Define authentication and authorization mechanisms 2. Document security controls and compliance requirements 3. Describe data encryption and protection measures 4. Specify security monitoring and logging approaches 5. Detail identity management and access control 6. Include security testing and vulnerability assessment procedures 7. Document incident response and security operations procedures 8. Specify network security and firewall configurations", "order": 6}, {"title": "Performance Considerations", "type": "performance", "description": "Addresses performance design, scalability strategies, and capacity planning. Here is a guide for constructing sub section: 1. Define performance requirements and SLAs 2. Document scalability strategies (vertical/horizontal) 3. Specify capacity planning approach and metrics 4. Detail caching strategies and content delivery 5. Include load balancing and failover mechanisms 6. Document performance monitoring and optimization approaches 7. Specify resource utilization targets and limits 8. Include performance testing strategies and benchmarks", "order": 7}]}, "COMPONENT_DOCUMENTATION": {"title_prefix": "Component Documentation", "root_description_template": "Technical documentation for {title} component. {description}", "sections": [{"title": "Component Overview", "type": "component_overview", "description": "Overview of the component's purpose and functionality", "order": 1}, {"title": "Technical Architecture", "type": "technical_architecture", "description": "Internal architecture and design patterns", "order": 2}, {"title": "Dependencies", "type": "dependencies", "description": "External dependencies and requirements", "order": 3}, {"title": "Integration Guide", "type": "integration_guide", "description": "Instructions for integrating with the component", "order": 4}, {"title": "Configuration", "type": "configuration", "description": "Configuration options and settings", "order": 5}, {"title": "Usage Examples", "type": "usage_examples", "description": "Code examples and usage scenarios", "order": 6}]}}