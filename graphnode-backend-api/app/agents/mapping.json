{"project_autoconfig": {"steps": [{"agent": "ProjectAgent", "entry": "configure_project"}], "next": "requirements_autoconfig"}, "deployment_autoconfig": {"steps": [{"agent": "ArchitectureAgent", "entry": "get_deployment_root"}, {"agent": "ArchitectureAgent", "entry": "configure_deployment"}], "next": "work_item_autoconfig"}, "work_item_autoconfig": {"steps": [{"agent": "ProjectAgent", "entry": "get_workitem_root"}, {"agent": "ProjectAgent", "entry": "configure_workitem"}], "next": "requirements_autoconfig"}, "requirements_autoconfig": {"steps": [{"agent": "ProjectAgent", "entry": "get_requirement_root"}, {"agent": "ProjectAgent", "entry": "configure_requirements", "children": "epic_autoconfig"}], "next": "architectural_requirements_autoconfig"}, "epic_autoconfig": {"steps": [{"agent": "epic_agent", "entry": "configure_epics", "children": "user_story_autoconfig"}], "next": null}, "user_story_autoconfig": {"steps": [{"agent": "userstory_agent", "entry": "configure_userstory"}], "next": "testcase_autoconfig"}, "testcase_autoconfig": {"steps": [{"agent": "TestCaseGeneratorAgent", "entry": "generate_test_cases"}], "next": null}, "component_testcase_autoconfig": {"steps": [{"agent": "ComponentTestCaseGeneratorAgent", "entry": "generate_component_test_cases"}], "next": "interface_autoconfig"}, "architectural_requirements_autoconfig": {"steps": [{"agent": "ArchitectureAgent", "entry": "get_architecture_root"}, {"agent": "ArchitectureAgent", "entry": "capture_requirements_for_architecture"}], "next": "system_context_autoconfig"}, "system_context_autoconfig": {"steps": [{"agent": "ArchitectureAgent", "entry": "configure_system_context"}], "next": "container_autoconfig"}, "container_autoconfig": {"steps": [{"agent": "ArchitectureAgent", "entry": "configure_containers"}], "next": "components_autoconfig"}, "components_autoconfig": {"steps": [{"agent": "ArchitectureAgent", "entry": "configure_components"}], "next": "component_testcase_autoconfig"}, "interface_autoconfig": {"steps": [{"agent": "interface_agent", "entry": "interface_definition_all"}], "next": "design_autoconfig"}, "container_ops_autoconfig": {"steps": [{"agent": "container_ops_agent", "entry": "traverse_containers"}], "next": "design_autoconfig"}, "design_autoconfig": {"steps": [{"agent": "DesignerAgent", "entry": "traverse_architecture"}], "next": "documentation_autoconfig"}, "documentation_autoconfig": {"steps": [{"agent": "documentation_agent", "entry": "create_documentation"}]}, "termination": {"steps": [{"entry": "stop_tasks"}], "next": "termination"}}