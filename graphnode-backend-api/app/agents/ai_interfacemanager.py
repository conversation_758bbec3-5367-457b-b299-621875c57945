import asyncio
from fastapi import HTT<PERSON>Exception
from jinja2 import Environment, FileSystemLoader
import json
from app.agents.agent import Agent
from app.models.node_model import ConfigureNodeRequest
from app.discussions.discussion_util import  conduct_discussion,create_root_node_properties
from app.discussions.discussion_factory import DiscussionFactory
from app.utils.node_utils import get_node_type
from app.connection.establish_db_connection import get_node_db
from app.connection.llm_init import get_llm_interface
from app.discussions.discussion import Discussion
from app.discussions.types.architecture_design_details import ArchitectureDesignDetails
from app.telemetry.logger_config import get_logger, set_task_id
from app.core.task_framework import TaskStatus



class InterfaceManagerAgent(Agent):
    def __init__(self, name, node_id, node_type, root_node_type, discussion_type, logging_context, levels, semaphore, supervisor=None):
        super().__init__(name)
        self.current_step = 0
        self.supervisor = supervisor
        self.llm_interface = get_llm_interface()
        self.node_id = node_id
        self.node_type = node_type
        self.root_node_type = root_node_type
        self.discussion_type = discussion_type
        self.db = get_node_db()
        self.semaphore = semaphore
        self.levels = levels
        self.architecture_root_id = None
        self.update_logger_agent = get_logger(__name__)

        if logging_context:
            self.logging_context = logging_context
            self.mongo_handler = logging_context.get('mongo_handler')
            self.task_id = logging_context.get('task_id')
            set_task_id(self.task_id)
        else:
            self.logging_context = {}
            self.task_id = None
            self.mongo_handler = None
        self.queue: asyncio.Queue = asyncio.Queue()

    async def process_work_item(self, work_item_dict):
        
        self.current_work_item = work_item_dict
        print(f"Processing WorkItem: {work_item_dict.entry} for node {work_item_dict.node_id}")
        if work_item_dict.entry == "interface_definition_all":
            await self.interface_definition_all(work_item_dict.node_id,work_item_dict.node_type,work_item_dict.project_id)
        else:
            print(f"Task {work_item_dict.entry} not found for agent {self.name}")


    async def run(self):
        while True:
            self.current_work_item = await self.queue.get()
            try:
                await self.process_work_item(self.current_work_item)
            finally:
                self.queue.task_done()
                
    async def interface_definition_all(self, node_id, node_type, project_id):
        """Define interfaces for all components by traversing through C4 model hierarchy"""
        try:
            self.update_logger_agent.info(f"Starting C4 interface definition for project {project_id}")
            
            # Get SystemContext
            system_contexts = await self.db.get_child_nodes(project_id, "SystemContext")
            if not system_contexts:
                self.update_logger_agent.info("No SystemContext found")
                return

            system_context = system_contexts[0]
            self.update_logger_agent.info(f"Processing SystemContext: {system_context['properties'].get('Title', 'Untitled')}")

            # First handle container interfaces
            await self.handle_container_interfaces(system_context['id'])
            
            # Then handle component interfaces
            await self.handle_component_interfaces(system_context['id'])

            self.update_logger_agent.info("Interface definition completed for all containers and components")
            await self.supervisor.notify_complete(self.current_work_item)
            
        except Exception as e:
            self.update_logger_agent.error(f"Error in interface_definition_all: {str(e)}", exc_info=True)
            await self.supervisor.notify_complete(self.current_work_item)

    async def handle_container_interfaces(self, system_context_id):
        """Handle interfaces between containers with improved state checking"""
        try:
            containers = await self.db.get_child_nodes(system_context_id, "Container")
            self.update_logger_agent.info(f"Found {len(containers)} containers for interface processing")

            for container in containers:
                container_id = container['id']
                self.update_logger_agent.info(f"Processing interfaces for container: {container['properties'].get('Title', 'Untitled')}")

                # Get or create interface node for container
                interface_nodes = await self.db.get_child_nodes(container_id, "Interface")
                if interface_nodes:
                    interface_node = interface_nodes[0]
                    
                    # Check both design_details and definition states
                    design_state = interface_node['properties'].get('Interface_design_details_state')
                    definition_state = interface_node['properties'].get('definition_state')
                    
                    # First ensure design details are configured
                    if design_state != 'configured':
                        try:
                            await self.configure_node(
                                interface_node['id'],
                                "Interface",
                                self.root_node_type,
                                "design_details"
                            )
                            self.update_logger_agent.info(f"Configured design details for container: {container['properties'].get('Title')}")
                        except Exception as e:
                            self.update_logger_agent.error(f"Error configuring design details for container {container['properties'].get('Title')}: {str(e)}")
                            continue  # Skip definition if design fails

                    # Then proceed with interface definition
                    if definition_state != 'configured':
                        try:
                            await self.configure_node(
                                interface_node['id'],
                                "Interface",
                                self.root_node_type,
                                "definition"
                            )
                            self.update_logger_agent.info(f"Configured interface definition for container: {container['properties'].get('Title')}")
                        except Exception as e:
                            self.update_logger_agent.error(f"Error configuring interface definition for container {container['properties'].get('Title')}: {str(e)}")

        except Exception as e:
            self.update_logger_agent.error(f"Error handling container interfaces: {str(e)}", exc_info=True)

    async def handle_component_interfaces(self, system_context_id):
            """Handle interfaces between components with improved state checking"""
            try:
                containers = await self.db.get_child_nodes(system_context_id, "Container")
                
                for container in containers:
                    components = await self.db.get_child_nodes(container['id'], "Component")
                    self.update_logger_agent.info(f"Processing {len(components)} components in container: {container['properties'].get('Title')}")

                    for component in components:
                        component_id = component['id']
                        self.update_logger_agent.info(f"Processing interfaces for component: {component['properties'].get('Title')}")

                        interface_nodes = await self.db.get_child_nodes(component_id, "Interface")
                        if interface_nodes:
                            interface_node = interface_nodes[0]
                            
                            # Check both design_details and definition states
                            design_state = interface_node['properties'].get('Interface_design_details_state')
                            definition_state = interface_node['properties'].get('definition_state')

                            # First ensure design details are configured
                            if design_state != 'configured':
                                try:
                                    await self.configure_node(
                                        interface_node['id'],
                                        "Interface",
                                        self.root_node_type,
                                        "design_details"
                                    )
                                    self.update_logger_agent.info(f"Configured design details for component: {component['properties'].get('Title')}")
                                except Exception as e:
                                    self.update_logger_agent.error(f"Error configuring design details for component {component['properties'].get('Title')}: {str(e)}")
                                    continue  # Skip definition if design fails

                            # Then proceed with interface definition
                            if definition_state != 'configured':
                                try:
                                    await self.configure_node(
                                        interface_node['id'],
                                        "Interface",
                                        self.root_node_type,
                                        "definition"
                                    )
                                    self.update_logger_agent.info(f"Configured interface definition for component: {component['properties'].get('Title')}")
                                except Exception as e:
                                    self.update_logger_agent.error(f"Error configuring interface definition for component {component['properties'].get('Title')}: {str(e)}")
            except Exception as e:
                self.update_logger_agent.error(f"Error handling component interfaces: {str(e)}", exc_info=True)

    # async def interface_definition_all(self, node_id, node_type, project_id):
    # #     """Define interfaces for all components by traversing through C4 model hierarchy"""
    #     try:
    #         self.update_logger_agent.info(f"Starting C4 interface definition for project {project_id}")
            
    #         query = """
    #         MATCH (p:Project)-[:HAS_CHILD]->(sc:SystemContext)
    #         -[:HAS_CHILD]->(container:Container)
    #         -[:HAS_CHILD]->(component:Component)
    #         WHERE ID(p) = $project_id
    #         RETURN DISTINCT ID(component) as id, component.Title as title
    #         """
    #         components = await self.db.async_run(query, project_id=project_id)

    #         # self.update_logger_agent.info(f"Found {len(components)} components in container {container['properties'].get('Title', 'Untitled')}")

    #         for component in components:
    #             component_id = component['id']
    #             self.update_logger_agent.info(f"Processing interfaces for component: {component['properties'].get('Title', 'Untitled')}")
                
    #             # Get Interface node for this component
    #             interface_nodes = await self.db.get_child_nodes(component_id, "Interface")
                
    #             if interface_nodes:
    #                 interface_node = interface_nodes[0]
    #                 # Define/configure the interface
    #                 await self.configure_node(
    #                     interface_node['id'],
    #                     "Interface",
    #                     self.root_node_type,
    #                     "definition"
    #                 )
    #                 self.update_logger_agent.info(f"Configured interface for component {component['properties'].get('Title', 'Untitled')}")
    #             else:
    #                 self.update_logger_agent.info(f"No interface node found for component {component['properties'].get('Title', 'Untitled')}")

    #         self.update_logger_agent.info("Interface definition completed for all components")
    #         await self.supervisor.notify_complete(self.current_work_item)
            
    #     except Exception as e:
    #         self.update_logger_agent.error(f"Error in interface_definition_all: {str(e)}", exc_info=True)
    #         # Notify supervisor even in case of error
    #         await self.supervisor.notify_complete(self.current_work_item)

    # async def traverse_and_define_interfaces(self, node_id):
    #     print(f"Traversing node: {node_id}")

    #     # Define interfaces for the current node
    #     await self.interface_definition(node_id, "Architecture")

    #     # Get child nodes
    #     child_nodes = await self.db.get_child_nodes(node_id, 'Architecture')
        
    #     # Recursively process child nodes
    #     for child in child_nodes:
    #         await self.traverse_and_define_interfaces(child['id'])

    # async def interface_definition(self, node_id, node_type):
    #     """
    #     Define/enrich interfaces that were already identified during architecture configuration.
    #     We don't need to re-capture interfaces, just process existing ones.
    #     """
    #     # Get the Interface node - should already exist from architecture phase
    #     interface_nodes = await self.db.get_child_nodes(node_id, 'Interface')
    #     if not interface_nodes:
    #         self.update_logger_agent.warning(f"No interface node found for {node_id} - skipping definition")
    #         return
            
    #     interface_node = interface_nodes[0]
        
    #     # Load existing interfaces (captured during architecture phase)
    #     #existing_interfaces = json.loads(interface_node['properties'].get('incoming_interfaces', '[]'))
        
    #     # Only proceed with definition if we have interfaces to define
    #     if interface_node:
    #         # Configure/define the interfaces through discussion
    #         await self.configure_node(
    #             interface_node['id'], 
    #             "Interface", 
    #             self.root_node_type, 
    #             "definition"
    #         )
    #     else:
    #         self.update_logger_agent.info(f"No interfaces to define for node {node_id}")

    async def configure_node(self, node_id, node_type, root_node_type, configuration_type):
        """
        Configure or reconfigure a requirement root node. This method handles both initial configuration 
        and subsequent reconfigurations based on system changes.
        
        Args:
            node_id: The ID of the node to configure
            node_type: The type of the node (RequirementRoot in this case)
            root_node_type: The type of the root node (typically Project)
            configuration_type: The type of configuration to perform
        """
        try:
            # Log the start of configuration process
            self.update_logger_agent.info(f"Starting configuration process for requirement root node: {node_id}")

            # Get or create task configuration from MongoDB
            # This maintains the state and progress of the configuration task
            task_configuration = await self.mongo_handler.get_by_task_id(self.task_id, self.mongo_handler.db)
            
            if not task_configuration:
                # Initialize new task configuration if none exists
                task_configuration = {
                    'task_id': self.task_id, 
                    'queue_messages': [], 
                    'task_status': 'In Progress',
                    'configuration_status': {},
                    'progress': 0
                }
                await self.mongo_handler.insert(task_configuration, self.mongo_handler.db)
                await self.send_update(self.task_id)
                self.update_logger_agent.info(f"Created new task configuration for task {self.task_id}")

            # Extract task tracking information
            self.queue_messages = task_configuration['queue_messages']
            self.configuration_status = task_configuration['configuration_status']

            # Retrieve the node to be configured
            node = await self.db.get_node_by_id(node_id)
            if not node:
                self.update_logger_agent.error(f"Node {node_id} not found")
                self.configuration_status[str(node_id)] = {
                    'node_type': node_type,
                    'title': 'Node not found',
                    configuration_type: 'not_found'
                }
                await self.mongo_handler.update_by_task_id(self.task_id, 
                                                        {'configuration_status': self.configuration_status})
                await self.send_update(self.task_id)
                return None

            # Update configuration status tracking
            # This helps in monitoring the progress of configuration
            if not self.configuration_status.get(str(node_id)):
                self.configuration_status[str(node_id)] = {
                    'node_type': node_type,
                    'title': node['properties']['Title'],
                    configuration_type: 'configuring'
                }
            else:
                self.configuration_status[str(node_id)]['title'] = node['properties']['Title']
                self.configuration_status[str(node_id)][configuration_type] = 'configuring'

            await self.mongo_handler.update_by_task_id(self.task_id, 
                                                    {'configuration_status': self.configuration_status})
            await self.send_update(self.task_id)

            # Get the specific node type from the node's labels
            # This ensures we're using the correct type for the discussion
            node_type = Discussion.get_specific_node_type(node['labels'])
            
            # Initiate the configuration discussion
            # This will handle both initial configuration and reconfiguration
            # The discussion class will manage version comparison and change detection
            await conduct_discussion(
                node_id=node_id,
                node_type=node_type,
                root_node_type=root_node_type,
                discussion_type=configuration_type,
                logging_context=self.logging_context,
                supervisor=self.supervisor
            )

            # Update the configuration status to reflect completion
            self.configuration_status[str(node_id)][configuration_type] = 'configured'
            await self.mongo_handler.update_by_task_id(self.task_id, 
                                                    {'configuration_status': self.configuration_status})
            await self.send_update(self.task_id)
            
            # Log successful completion
            self.update_logger_agent.info(f"Successfully completed configuration for node {node_id}")
            
            # Retrieve and return the updated node
            node = await self.db.get_node_by_id(node_id)
            return node

        except Exception as e:
            await self.mongo_handler.update_by_task_id(
                    self.task_id, 
                    {
                        'task_status': TaskStatus.FAILED, 
                        'run_completed': True, 
                        'progress': 100
                    }
                )
            # Log any errors that occur during configuration
            self.update_logger_agent.error(f"Error in configure_node for node {node_id}: {str(e)}", 
                                        exc_info=True)
            raise