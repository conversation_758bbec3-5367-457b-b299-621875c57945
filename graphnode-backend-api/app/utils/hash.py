import hashlib
from Crypto.Cipher import AES
import base64
import os
from app.core.Settings import settings
from cryptography.fernet import Fernet
cipher_suite = Fernet(settings.ENCRYPTION_KEY.encode()) if settings.ENCRYPTION_KEY else None

# Constants
TENANT_SALT = settings.TENANT_SALT
ROOT_TENANT_ID = settings.KAVIA_SUPER_TENANT_ID

if not TENANT_SALT:
    raise ValueError("TENANT_SALT is not defined or empty")

def pad(data: str) -> bytes:
    """Pad the input data to make it a multiple of the block size."""
    block_size = AES.block_size
    padding_length = block_size - len(data.encode('utf-8')) % block_size
    return data.encode('utf-8') + bytes([padding_length] * padding_length)

def unpad(data: bytes) -> str:
    """Remove padding from the decrypted data."""
    padding_length = data[-1]
    return data[:-padding_length].decode('utf-8')

def encrypt(data: str, salt: str) -> str:
    """Encrypt a string using AES-128-ECB and a salt."""
    key = hashlib.sha256(salt.encode('utf-8')).digest()[:16]  # Ensure 16-byte key
    cipher = AES.new(key, AES.MODE_ECB)
    encrypted = cipher.encrypt(pad(data))
    return base64.b64encode(encrypted).decode('utf-8').rstrip('=')

def decrypt(encrypted_data: str, salt: str) -> str:
    """Decrypt an AES-128-ECB encrypted string."""
    key = hashlib.sha256(salt.encode('utf-8')).digest()[:16]  # Ensure 16-byte key
    cipher = AES.new(key, AES.MODE_ECB)
    encrypted_data += '=' * (-len(encrypted_data) % 4)  # Pad base64 string
    decrypted = cipher.decrypt(base64.b64decode(encrypted_data))
    return unpad(decrypted)

def encrypt_string(data: str) -> str:
    """Encrypt a string."""
    return encrypt(data, TENANT_SALT)

def decrypt_string(encrypted_data: str) -> str:
    """Decrypt an encrypted string."""
    try:
        return decrypt(encrypted_data, TENANT_SALT)
    except Exception as e:
        print(f"Error decrypting string: {e}")
        return None

def encrypt_tenant_id(tenant_id: str) -> str:
    """Encrypt a tenant ID."""
    key = hashlib.sha256(TENANT_SALT.encode('utf-8')).digest()[:16]  # Ensure 16-byte key
    print("Key Length:", len(key))  # Debugging
    cipher = AES.new(key, AES.MODE_ECB)
    encrypted = cipher.encrypt(pad(tenant_id))
    return base64.b64encode(encrypted).decode('utf-8').rstrip('=')

def decrypt_tenant_id(encrypted: str) -> str:
    """Decrypt an encrypted tenant ID."""
    try:
        if not encrypted:
            raise ValueError("Encrypted tenant ID is required")
        key = hashlib.sha256(TENANT_SALT.encode('utf-8')).digest()[:16]  # Ensure 16-byte key
        print("Key Length:", len(key))  # Debugging
        cipher = AES.new(key, AES.MODE_ECB)
        encrypted += '=' * (-len(encrypted) % 4)  # Pad base64 string
        decrypted = cipher.decrypt(base64.b64decode(encrypted))
        return unpad(decrypted)
    except Exception as error:
        print("Error decrypting tenant ID:", error)
        return None

def encrypted_tenant_id() -> str:
    """Encrypt the root tenant ID."""
    return encrypt_tenant_id(ROOT_TENANT_ID)


def encrypt_data(data: str) -> str:
    """Encrypt sensitive data"""
    if not cipher_suite:
        raise ValueError("Encryption key not configured")
    return cipher_suite.encrypt(data.encode()).decode()

def decrypt_data(encrypted_data: str) -> str:
    """Decrypt sensitive data"""
    if not cipher_suite:
        raise ValueError("Encryption key not configured")
    return cipher_suite.decrypt(encrypted_data.encode()).decode()