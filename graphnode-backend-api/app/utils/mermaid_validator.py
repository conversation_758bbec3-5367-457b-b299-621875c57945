"""
A utility module for validating and fixing mermaid diagram syntax.
This module helps prevent rendering issues with mermaid diagrams in the discussion system.

File path: app/utils/mermaid_validator.py
"""
import re
from typing import <PERSON><PERSON>, Dict, Any
from app.models.user_model import LLMModel
import logging


def validate_mermaid_code(mermaid_text: str) -> Tuple[bool, str]:
    """
    Validates mermaid diagram syntax and returns any errors found.
    
    Args:
        mermaid_text: The mermaid diagram text to validate
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    logger = logging.getLogger("MermaidValidator")
    logger.info(f"Input to validate_mermaid_code: {repr(mermaid_text)}")
    if not mermaid_text or not isinstance(mermaid_text, str):
        error_message = f"Empty or non-string mermaid code. Input: {repr(mermaid_text)}"
        logger.error(f"Error message: {error_message}")
        return False, error_message
    
    # Trim unnecessary whitespace and remove markdown code block markers if present
    cleaned_text = mermaid_text.strip()
    logger.info(f"Validating cleaned input: {repr(cleaned_text)}")
    if cleaned_text.startswith("```mermaid"):
        cleaned_text = cleaned_text[len("```mermaid"):].strip()
    if cleaned_text.startswith("```"):
        cleaned_text = cleaned_text[3:].strip()
    if cleaned_text.endswith("```"):
        cleaned_text = cleaned_text[:-3].strip()
    
    # Determine diagram type
    diagram_type = None
    diagram_types = [
        "graph", "flowchart", "sequenceDiagram", "classDiagram", "stateDiagram", 
        "stateDiagram-v2", "gantt", "pie", "er", "journey"
    ]
    
    for d_type in diagram_types:
        if cleaned_text.startswith(d_type):
            diagram_type = d_type
            break
    
    if not diagram_type:
        logger.error(f"Unable to determine diagram type. Input: {repr(cleaned_text)}")
        return False, f"Unable to determine diagram type. Input: {repr(cleaned_text)}"
    
    # Common syntax errors to check for based on diagram type
    errors = []
    
    # General checks for all diagram types
    if cleaned_text.count('"') % 2 != 0:
        errors.append("Unmatched double quotes")
    
    # Check for markdown formatting accidentally mixed with mermaid
    if re.search(r'\*\*.+\*\*', cleaned_text):
        errors.append("Possible markdown formatting (bold with **) in mermaid code")
    
    # IMPROVED CHECK: Detect all HTML/XML tags
    # Remove UML stereotypes first
    stereotype_pattern = r'<<\w+>>'
    text_without_stereotypes = re.sub(stereotype_pattern, '', cleaned_text)
    # Then check for HTML tags
    html_pattern = r'<[^>]+>'
    html_tags = re.findall(html_pattern, text_without_stereotypes)
    if html_tags:
        tag_examples = ', '.join(html_tags[:3])
        errors.append(f"HTML tags found in diagram (e.g., {tag_examples}). HTML tags are not supported in standard mermaid syntax. Remove all HTML tags.")
    
    # Diagram-specific checks
    if diagram_type in ["graph", "flowchart"]:
        # Check for single-dash arrows (incorrect)
        if re.search(r'(\w+)\s*->\s*(\w+)', cleaned_text):
            errors.append("Invalid arrow: '->' should be '-->' in flowcharts. Use double dashes for connections.")
        
        # Check for triple-dash or more (incorrect)
        if re.search(r'(\w+)\s*---+>\s*(\w+)', cleaned_text):
            errors.append("Invalid arrow: '--->' has too many dashes. Use exactly two dashes: '-->'.")
        
        # Check for => syntax which is not valid in mermaid flowcharts
        if "=>" in cleaned_text:
            errors.append("Invalid arrow: '=>' is not valid in flowcharts. Use '-->' instead.")
        
        # NEW CHECK: Check for parentheses in node labels which can cause parsing issues
        if re.search(r'\[.*\(.*\).*\]', cleaned_text):
            errors.append("Parentheses found in node labels (e.g., 'Tool (e.g., CodeExecution)'). Remove parentheses or replace with alternative text to avoid parsing issues.")

        # Check for invalid Title: syntax in flowcharts
        if re.search(r'^\s*Title:\s*.*$', cleaned_text, re.MULTILINE):
            errors.append("Invalid 'Title:' syntax found. Remove 'Title:' lines as they are not valid in mermaid flowcharts. Use comments with %% instead if needed.")
    
        # Remove strict node definition check: allow plain node IDs
        # Only check for undefined nodes if they are used with a label/shape (e.g., A[Label])
        # (No error for A-->B if A and B are not defined with [])
        # If you want to add a check for label/shape nodes, you can do so here, but default Mermaid allows plain IDs.
    
    elif diagram_type in ["sequenceDiagram"]:
        # Check for common sequence diagram errors
        if re.search(r'-->(?!>)', cleaned_text):
            errors.append("Invalid arrow: '-->' should be '->>' in sequence diagrams")
        
        if "->" in cleaned_text and not "->>" in cleaned_text:
            errors.append("Invalid arrow: '->' should be '->>' in sequence diagrams")
    
    elif diagram_type in ["classDiagram"]:
        # Check for common class diagram errors
        if "-->" in cleaned_text:
            errors.append("Invalid relationship: '-->' is not valid in class diagrams. Use valid relationships like '..|>..'")
    
    elif diagram_type in ["stateDiagram", "stateDiagram-v2"]:
        # Check for invalid reverse arrows
        if re.search(r'<--', cleaned_text):
            errors.append("Invalid reverse arrow '<--' found. All transitions must use '-->'.")
        
        # Check invalid final state syntax  
        if re.search(r'\[\*\]\s*<--', cleaned_text):
            errors.append("Invalid final state: '[*] <-- State' should be 'State --> [*]'.")
    
    elif diagram_type == "er":
        # erDiagram specific checks
        # Check for valid relationship symbols (||--o{, }o--||, etc.)
        valid_er_rel = re.compile(r'\s*(\w+)\s+([|}o\-]+)\s+(\w+)\s*:\s*([\w\s]+)')
        lines = cleaned_text.splitlines()[1:]  # skip the 'erDiagram' header
        for line in lines:
            line = line.strip()
            if not line:
                continue
            if not valid_er_rel.match(line):
                errors.append(f"Invalid ER relationship syntax: '{line}'")
        # Check for unmatched colons (should be one per relationship line)
        for line in lines:
            if line and line.count(":") != 1:
                errors.append(f"Line should contain exactly one colon (:) separating relationship and label: '{line}'")
    
    # Check for common bracket/brace/parenthesis mismatches
    opening_brackets = sum(1 for c in cleaned_text if c in "([{")
    closing_brackets = sum(1 for c in cleaned_text if c in ")]}" )
    if opening_brackets != closing_brackets:
        errors.append(f"Mismatched brackets/braces/parentheses: {opening_brackets} opening vs {closing_brackets} closing")
    
    # Check for valid subgraph syntax
    subgraphs = re.findall(r'subgraph\s+(.*?)[\n\r]', cleaned_text)
    end_subgraphs = len(re.findall(r'^\s*end\s*$', cleaned_text, re.MULTILINE))
    if len(subgraphs) != end_subgraphs:
        errors.append(
            f'The number of "subgraph" blocks does not match the number of "end" statements. Found {len(subgraphs)} "subgraph" and {end_subgraphs} "end". Each "subgraph" must be closed with a single "end". Please ensure every "subgraph" has a matching "end", and there are no extra or missing "end" statements.'
        )
    
    # Format the error message
    if errors:
        error_msg = "Mermaid syntax validation errors:\n- " + "\n- ".join(errors)
        error_msg += f"\n[Validator] Cleaned input: {repr(cleaned_text)}\n[Validator] Errors: {errors}"
        logger.error(f"Error message: {error_msg}")
        return False, error_msg
    
    return True, "Mermaid syntax appears valid"


def strip_mermaid_fence(text: str) -> str:
    """
    Removes any leading/trailing code fences and the 'mermaid' keyword from the LLM output.
    """
    text = text.strip()
    # Remove ```mermaid or ``` at the start
    text = re.sub(r'^```mermaid\s*', '', text, flags=re.IGNORECASE)
    text = re.sub(r'^```\s*', '', text)
    # Remove closing ```
    text = re.sub(r'```$', '', text)
    return text.strip()


async def fix_mermaid_with_llm(llm, mermaid_code: str, error_msg: str) -> Tuple[str, bool]:
    """
    Uses LLM to fix mermaid syntax errors.
    
    Args:
        llm: LLM interface to use for fixing
        mermaid_code: The mermaid code with errors
        error_msg: Error message from validation
        
    Returns:
        Tuple of (fixed_code, is_fixed)
    """
    logger = getattr(llm, "llm_logger", None)
    try:
        system_prompt = "You are a mermaid diagram syntax expert."
        generic_instructions = (
            "In addition to the errors listed, always do the following: "
            "Remove ALL HTML tags (e.g., <br>, <i>, <b>, <u>, <div>, etc.), "
            "replace HTML line breaks with \\n, balance all subgraph/end blocks, "
            "and ensure only standard Mermaid syntax is used. "
        )
        user_prompt = f"""You are an expert in mermaid diagram syntax.\nThe following mermaid code has syntax errors:\n\n{mermaid_code}\n\nThese are the errors identified:\n{error_msg}\n\n{generic_instructions}. \nReturn ONLY the corrected mermaid code, with NO code fences, NO ```mermaid, and NO explanations or additional text.\n"""
        if logger:
            logger.info(f"Prompt sent to LLM:\n{user_prompt}")
        if logger:
            logger.info("[MermaidValidator] Invoking LLM to fix mermaid code...")

        corrected_code = await llm.llm_interaction_wrapper(
            messages=[],
            user_prompt=user_prompt,
            system_prompt=system_prompt,
            model=LLMModel.gpt_4_1.value,
            response_format={"type": "text"},
            stream=False
        )
        if not corrected_code or not isinstance(corrected_code, str):
            if logger:
                logger.error("[MermaidValidator] LLM returned None or invalid output. Check API keys, model, and logs.")
            return mermaid_code, False
        if logger:
            logger.info(f"Raw LLM output: {repr(corrected_code)}")
        is_valid, new_error_msg = validate_mermaid_code(corrected_code)
        if is_valid:
            if logger:
                logger.info("[MermaidValidator] LLM returned valid mermaid code.")
            return corrected_code, True
        if logger:
            logger.warning(f"[MermaidValidator] LLM did not return valid mermaid code. Validation errors: {new_error_msg}\nLLM response: {repr(corrected_code)}")
        return mermaid_code, False
    except Exception as e:
        if logger:
            logger.error(f"[MermaidValidator] Error during LLM fix attempt: {str(e)}")
        return mermaid_code, False


def get_display_type(node_type: str, field_name: str, data_model_helper) -> str:
    """
    Gets the display type for a node field from the data model.
    
    Args:
        node_type: Type of the node
        field_name: Name of the field
        data_model_helper: Helper to access data model
        
    Returns:
        Display type of the field
    """
    try:
        # Get the node definition from the data model
        node_def = data_model_helper.data_model['model'].get(node_type, {})
        # Get the UI metadata for the node type
        ui_metadata = node_def.get('ui_metadata', {})
        # Get the field's UI metadata
        field_metadata = ui_metadata.get(field_name, {})
        # Return the display type, defaulting to 'text' if not specified
        return field_metadata.get('display_type', 'text')
    except Exception as e:
        # Log any errors and return default display type
        print(f"Error getting display type for {node_type}.{field_name}: {str(e)}")
        return 'text'


def is_likely_mermaid(field_name: str, field_value: str) -> bool:
    """
    Checks if a field is likely to contain a mermaid diagram based on name and content.
    
    Args:
        field_name: Name of the field
        field_value: Value of the field
        
    Returns:
        True if the field is likely to contain a mermaid diagram
    """
    if not isinstance(field_value, str) or not field_value.strip():
        return False
        
    # Check field name for diagram-related keywords
    diagram_keywords = ['diagram', 'chart', 'flow', 'graph', 'sequence']
    has_diagram_keyword = any(keyword in field_name.lower() for keyword in diagram_keywords)
    
    # Check field value for mermaid syntax
    starts_with_mermaid_syntax = (
        field_value.strip().startswith('graph') or 
        field_value.strip().startswith('flowchart') or
        field_value.strip().startswith('sequenceDiagram') or
        field_value.strip().startswith('classDiagram') or
        field_value.strip().startswith('stateDiagram')
    )
    
    return has_diagram_keyword or starts_with_mermaid_syntax