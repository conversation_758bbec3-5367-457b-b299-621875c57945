#!/usr/bin/env python3

import os
import sys
import time
import json
import asyncio
import argparse
from dotenv import load_dotenv
from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
from app.discussions.types.requirement_root import RequirementRootConfiguration
from app.discussions.types.project_configuration import ProjectConfiguration
from app.discussions.types.workitem_configuration import WorkItemConfiguration
from app.discussions.types.system_context_configuration import SystemContextConfiguration
from app.discussions.types.container_configuration import ContainerConfiguration
from app.discussions.types.component_configuration import ComponentConfiguration
from app.discussions.types.architecture_configuration import ArchitectureConfiguration
from app.discussions.types.architecture_requirement import ArchitectureRequirementDiscussion
from app.discussions.types.epic_configuration import EpicConfiguration
from app.discussions.types.userstory_configuration import UserStoryConfiguration
from app.utils.prodefn.projdefn import ProjDefn, Proj<PERSON><PERSON><PERSON><PERSON><PERSON>orter, ProjDefnDocSpecifier
from app.utils.prodefn.projdefn_helper import Reporter, Helpers, ProjDefn_Helper
from app.connection.establish_db_connection import get_node_db, get_vector_db, get_mongo_db
from app.connection.tenant_middleware import TenantMiddleware, tenant_context, get_tenant_id
from app.utils.file_utils.upload_utils import upload_and_process, s3_client, get_tenant_bucket
from app.connection.llm_init import get_llm_interface
from app.utils.datetime_utils import generate_timestamp
from app.utils.node_utils import create_node
from contextvars import ContextVar

'''
Sample sequence:
action:new-project
action:add-doc
action:discussion discussion_type:configuration node_type:Project node_id:project_node_id
action:discussion discussion_type:configuration node_type:RequirementRoot node_id:project_node_id user_input: "I need the project to consist of 30 to 35 epics"
action:discussion discussion_type:configuration node_type:Epic node_id:project_node_id discuss_all:True user_input: "I need the epic to consist of 6 to 10 user stories"
action:discussion discussion_type:configuration node_type:UserStory node_id:project_node_id discuss_all:True
action:discussion discussion_type:configuration node_type:Architecture node_id:project_node_id
action:discussion discussion_type:architecture_requirement node_type:ArchitecturalRequirement node_id:project_node_id

'''
async def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='CLI tool for executing discussions')
    parser.add_argument('--action', default='discussion', help='Action to perform. One of discussion,examine,new-project,add-doc,unconfigure')
    parser.add_argument('--discuss-all', default=False, help='True to discuss all unconfigured nodes of node-type')
    parser.add_argument('--node-id', help='ID of the node to discuss, usually a project node')
    parser.add_argument('--discussion-type', help='Type of discussion to execute')
    parser.add_argument('--discussion-id', default=None, help='ID of the discussion to resume')
    parser.add_argument('--title', help='Title for the discussion')
    parser.add_argument('--description', help='Description for the discussion')
    parser.add_argument('--node-type', help='Type of node being discussed')
    parser.add_argument('--invocation-type', default='interactive_config', help='Type of invocation')
    parser.add_argument('--user-input',default=None, help='User input to add to the start of the discussion')
    parser.add_argument('--document',help='Full pathname of document file to ingest')
    parser.add_argument('--env-file', default='.env', help='Path to .env file (default: .env)')
    parser.add_argument('--user', default='cli-user', help='User identifier (default: cli-user)')
    parser.add_argument('--tenant',default='T0005', help='Tenant ID (default T0005)')
    
    args = parser.parse_args()

    # Load environment variables
    if not os.path.exists(args.env_file):
        print(f"Error: Environment file {args.env_file} not found")
        sys.exit(1)
    load_dotenv(args.env_file)

    tenant_context.set(args.tenant)

    DiscussionFactory.register('configuration', ProjectConfiguration, "Project")
    DiscussionFactory.register('configuration', WorkItemConfiguration, "WorkItem")
    DiscussionFactory.register('configuration', SystemContextConfiguration, "SystemContext")
    DiscussionFactory.register('configuration', ContainerConfiguration, "Container")
    DiscussionFactory.register('configuration', ComponentConfiguration, "Component")
    DiscussionFactory.register('configuration', ArchitectureConfiguration, "Architecture")
    DiscussionFactory.register('configuration', EpicConfiguration, "Epic")
    DiscussionFactory.register('configuration', UserStoryConfiguration, "UserStory")
    DiscussionFactory.register('configuration', RequirementRootConfiguration, "RequirementsRoot")
    DiscussionFactory.register('architecture_requirement', ArchitectureRequirementDiscussion, "ArchitecturalRequirement")

    if args.action == 'examine':
        await examine_nodes(int(args.node_id))
    elif args.action == 'new-project':
        properties = None
        node = await create_node('Project', args.title, args.description, properties )
        print(f"created new project: id {node['id']}")
    elif args.action == 'add-doc':
        project_id = int(args.node_id)
        filename = args.document
        await ingest_document(project_id, filename)
    elif args.action == 'unconfigure':
        node_id = int(args.node_id)
        db = get_node_db()
        node = await db.get_node_by_id(node_id)
        if 'configuration_state' in node['properties']:
            node['properties']['configuration_state'] = 'unconfigured'
            await db.update_node_by_id(node_id,node['properties'])
    else:
        db = get_node_db()

        discussion_id = None
        if args.discussion_id:
            discussion_id = int(args.discussion_id)

        user_input = None
        if args.user_input:
            user_input = args.user_input

        need_discussion= True
        while need_discussion:
            bad_config = False
            have_target = False
            node_id = int(args.node_id)    
            node = await db.get_node_by_id(node_id)
            node_type = node['properties']['Type']
            if node_type == 'project':
                if args.node_type == 'Project':
                    have_target = True
                elif args.node_type == 'RequirementRoot':
                    bad_config = True
                    requirements = await db.get_child_requirements(node_id, 'RequirementRoot')
                    if requirements:
                        for req in requirements:
                            if req['properties']['Type'] == 'RequirementRoot':
                                node_id = req['id']
                                bad_config = False
                                have_target = True
                                break
                elif args.node_type == 'WorkItem':
                    bad_config = True
                    requirements = await db.get_child_requirements(node_id, 'WorkItemRoot')
                    if requirements:
                        for req in requirements:
                            if req['properties']['Type'] == 'WorkItemRoot':
                                node_id = req['id']
                                bad_config = False
                                have_target = True
                                break
                elif args.node_type == 'SystemContext':
                    bad_config = True
                    requirements = await db.get_child_requirements(node_id, 'SystemContext')
                    if requirements:
                        for req in requirements:
                            if req['properties']['Type'] == 'SystemContext':
                                node_id = req['id']
                                bad_config = False
                                have_target = True
                                break
                elif args.node_type == 'Architecture':
                    bad_config = True
                    requirements = await db.get_child_requirements(node_id, 'Architecture')
                    if requirements:
                        for req in requirements:
                            if req['properties']['Type'] == 'Architecture':
                                node_id = req['id']
                                bad_config = False
                                have_target = True
                                break
                elif args.node_type == 'ArchitecturalRequirement':
                    bad_config = True
                    children = await db.get_child_nodes(node_id,'ArchitectureRoot')
                    if children:
                        architecture_root_id = None
                        for child in children:
                            if 'ArchitectureRoot' in child['labels']:
                                architecture_root_id = child.get('id')
                                break
                        if architecture_root_id:
                            children = await db.get_child_nodes(architecture_root_id,'ArchitecturalRequirement')
                            if children:
                                for child in children:
                                    if 'configuration_state' not in child['properties'] or child['properties']['configuration_state'] != 'configured':
                                        node_id = child['id']
                                        bad_config = False
                                        have_target = True
                                        break
                                if not have_target:
                                    child = children[0]
                                    node_id = child['id']
                                    bad_config = False
                                    have_target = True
                elif args.node_type == 'Epic':
                    bad_config = True
                    children = await db.get_child_nodes(node_id,None)
                    if children:
                        requirement_root_id = None
                        for child in children:
                            if child['properties'].get('Type') == 'RequirementRoot':
                                requirement_root_id = child.get('id')
                                break
                        if requirement_root_id:
                            epics = await db.get_child_nodes(requirement_root_id,'Epic')
                            if epics:
                                bad_config = False
                                for epic in epics:
                                    if 'configuration_state' not in epic['properties']:
                                        node_id = epic['id']
                                        have_target = True
                                        break
                                    elif epic['properties'].get('configuration_state') == 'configured':
                                        user_stories = await db.get_child_nodes(epic['id'],'UserStory')
                                        if not user_stories:
                                            node_id = epic['id']
                                            have_target = True
                                            break
                elif args.node_type == 'UserStory':
                    bad_config = True
                    children = await db.get_child_nodes(node_id,None)
                    if children:
                        requirement_root_id = None
                        for child in children:
                            if child['properties'].get('Type') == 'RequirementRoot':
                                requirement_root_id = child.get('id')
                                break
                        if requirement_root_id:
                            epics = await db.get_child_nodes(requirement_root_id,'Epic')
                            if epics:
                                for epic in epics:
                                    if have_target:
                                        break
                                    if epic['properties'].get('configuration_state') == 'configured':
                                        user_stories = await db.get_child_nodes(epic['id'],'UserStory')
                                        if user_stories:
                                            bad_config = False
                                            for user_story in user_stories:
                                                if 'configuration_state' not in user_story['properties']:
                                                    node_id = user_story['id']
                                                    have_target = True
                                                    break

            if not have_target:
                need_discussion = False

            if bad_config:
                print("Unable to perform request")
                sys.exit(0)

            if need_discussion:
                # Initialize discussion
                discussion = await DiscussionFactory.create_discussion( discussion_type= args.discussion_type,
                                                                        current_user= args.user,
                                                                        node_type= args.node_type,
                                                                        node_id= node_id,
                                                                        discussion_node_id= discussion_id,
                                                                        title= args.title,
                                                                        description= args.description,
                                                                        invocation_type= args.invocation_type
                                                                    )
                # Set current user
                discussion.set_current_user(args.user)

                # Execute discussion steps
                try:
                    # Execute retrieve_info step
                    next_step, result = await discussion.execute_to_step('retrieve_info')
                    print("Retrieved info:", json.dumps(result, indent=2))

                    # Execute main discussion
                    print("\nStarting main discussion...")
                    while True:
                        done = False
                        async for response in discussion.main_discussion(user_comment=user_input):
                            if isinstance(response, dict):
                                print(json.dumps(response, indent=2))
                                done = True
                            else:
                                print(response)
                        if not done and args.invocation_type == 'interactive_config':
                            user_input = input("User input: ")
                            if len(user_input) == 0 or user_input == '\n':
                                user_input = None
                                discussion.current_step_index += 1
                                await discussion.db.update_node_by_id(discussion.discussion_id, {'current_step_index': discussion.current_step_index})
                                break
                        else:
                            discussion.current_step_index += 1
                            await discussion.db.update_node_by_id(discussion.discussion_id, {'current_step_index': discussion.current_step_index})
                            break

                    # Execute merge step
                    print("\nMerging captured items...")
                    next_step, result = await discussion.execute_to_step('merge_captured_items')
                    print("Merge result:", result)

                    # Finalize discussion
                    print("\nFinalizing discussion...")
                    next_step, result = await discussion.execute_to_step('finalize')
                    print("Discussion finalized")

                    if args.discuss_all.lower() != 'true':
                        need_discussion = False

                except Exception as e:
                    print(f"Error during discussion execution: {str(e)}")
                    sys.exit(1)

async def examine_nodes( node_id ):
    project_info = {}
    db = get_node_db()
    node = await db.get_node_by_id(node_id)

    project_info['project'] = node

    async def get_relations(node, relation):
        relations = []
        node_id = node['id']
        relationships = await db.get_all_relationships(node_id, relation, None, 400)
        for rel in relationships:
            id = rel['EndNodeID']
            n = await db.get_node_by_id(id)
            print(f"node {node_id} labels: {', '.join(str(l) for l in node['labels'])} interfaces_with node {id} labels: {', '.join(str(l) for l in n['labels'])}")
            relations.append(n)
        return relations

    for relation in ['INTERFACES_WITH','HAS_CHILD']:
        project_info[relation] = await get_relations(node, relation)
 
    async def get_child(id):
        children = project_info['HAS_CHILD']
        for child in children:
            if child['id'] == id:
                return child
        return None

    while True:
        user_input = input("Input node id: ")
        if user_input:
            id = int(user_input)
            if id:
                n = await get_child(id)
                if n:
                    properties= n['properties']
                    type = properties['Type']
                    print(f'{type}\n')
                    if type in ['Epic', 'UserStory']:
                        title= properties['Title']
                        description = properties['Description']
                        print(f' Title: {title}')
                        print(f' Description: {description}')
        else:
            break
    return

async def ingest_document( project_id: int, filename: str):
    try:
        print(f"Starting to ingest document: {filename}...")

        auto_config_session_context = ContextVar("auto_config_session_id", default=None)
        session_id = auto_config_session_context.get()
        tenant_id = get_tenant_id()

        # Set up directories
        base_path = f"/tmp/project-{project_id}"
        identifier = os.path.join(base_path, 'digested-files')
        os.makedirs(identifier, exist_ok=True)

        # Initialize project definition handler
        reporter = Reporter()
        helper = ProjDefn_Helper( reporter= reporter,
                                base_path= base_path,
                                session_id= session_id,
                                tenant_id= tenant_id,
                                project_id= project_id
                                )
        configuration = {
            "base_path": base_path,
            "model": "gpt-4o-mini",
            "timeout": 60,
            "chunk_size": 64*1024,
            "cost_tracer": None,
            "reporter": reporter,
            "helpers": helper,
            "project_id": project_id
        }
        projdefn = ProjDefn.getInstance(configuration, f"default_{session_id}")
        projdefn.start()

        # Add document to ingest queue
        basename= os.path.basename(filename)
        projdefn.addToIngestQueue(basename, filename)

        # Set up S3 paths
        bucket_name = get_tenant_bucket(tenant_id)
        prefix = f"extracted-docs-{tenant_id}/project-{project_id}/digested-files/doc__{basename}.json"
        
        # Wait for processing to complete with timeout
        process_timeout = 36000
        start_time = time.time()
        file_exists = False
        
        print(f"Waiting for file to appear in S3: {bucket_name}/{prefix}")
        
        while not file_exists and (time.time() - start_time) < process_timeout:
            try:
                # List objects in the S3 bucket with the specified prefix
                response = s3_client.list_objects_v2(
                    Bucket=bucket_name,
                    Prefix=prefix
                )
                # Check if file exists in S3
                s3_client.head_object(Bucket=bucket_name, Key=prefix)
                file_exists = True
                print(f"File found in S3 after {time.time() - start_time:.2f} seconds")
            except:
                # File doesn't exist yet, continue waiting
                await asyncio.sleep(2)  # Check every 2 seconds
                elapsed_time = time.time() - start_time
                print(f"Waiting... elapsed time: {elapsed_time:.2f}s")

        if file_exists:
            while not reporter.is_ready() and (time.time() - start_time) < process_timeout:
                time.sleep(1)
            print("Document ingestion complete")

    except Exception as e:
        print(f"Processing failed with error: {str(e)}")
    finally:
        if 'projdefn' in locals():
            print("Releasing ProjDefn instance")
            ProjDefn.releaseInstance("default")

    return

if __name__ == '__main__':
    asyncio.run(main())