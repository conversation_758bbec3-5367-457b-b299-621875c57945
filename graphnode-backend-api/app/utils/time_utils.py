

def minutes_to_seconds(minutes: float) -> float:
    """
    Convert minutes to seconds.
    
    Args:
        minutes (float): The number of minutes to convert
        
    Returns:
        float: The equivalent number of seconds
    """
    return minutes * 60

def seconds_to_minutes(seconds: float) -> float:
    """
    Convert seconds to minutes.
    
    Args:
        seconds (float): The number of seconds to convert
        
    Returns:
        float: The equivalent number of minutes
    """
    return seconds / 60