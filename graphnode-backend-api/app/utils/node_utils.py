import json
from app.connection.establish_db_connection import get_node_db, get_vector_db, get_mongo_db
from app.core.constants import NodeType
from fastapi import HTT<PERSON>Exception
from datetime import date, datetime
from enum import Enum
import asyncio
from app.utils.cache_utils import async_ttl_cache
from app.core.Settings import settings

from app.connection.tenant_middleware import get_tenant_id
from app.routes.file_route import list_project_files
from app.classes.S3Handler import <PERSON><PERSON><PERSON><PERSON><PERSON>

def get_node_type(node_type_input: str) -> str:
    """Maps a potentially case-insensitive node type input to the correct NodeType constant."""
    try:
        # Convert input to uppercase and match it directly in the NodeType enum
        return NodeType[node_type_input.upper()].value 
    except KeyError:  # Catch the KeyError in case of invalid input
        raise HTTPException(status_code=400, detail=f"Invalid node type: '{node_type_input}'")
    
node_types = ["Project", "Product", "User", "Requirement" ,"RequirementRoot", "WorkItem", "WorkItemRoot" ,"Discussion", "Epic", "UserStory", "Task" ,"Architecture", "Inteface", "ArchitecturalRequirement", "Component","Sub_Component","FunctionalRequirement", "ArchitecturalRequirementNode","StateDiagram","RobustnessTest","StateLogic","Diagram","ClassDiagram","Design","DesignElement","Algorithm","PerformanceTest","UnitTest","IntegrationTest","Sequence","Database"]


def format_node_properties( properties):  
    class DataType(Enum):
        DATETIME = "DateTime"
        DATE = "Date"
        STRING = "String"
        LONG_STRING = "LongString"
        NUMBER = "Number"
        FLOAT = "Float"
        BOOLEAN = "Boolean"
        LIST = "List"
        OBJECT = "Object"
        UNKNOWN = "Unknown" 
        
    TYPE_MAPPING = {
        str: DataType.STRING,
        int: DataType.NUMBER,
        float: DataType.FLOAT,
        bool: DataType.BOOLEAN,
        date: DataType.DATE,
        datetime: DataType.DATETIME,
        list: DataType.LIST,
        dict: DataType.OBJECT,
    }      

    def get_data_type(value):
        """Infers the data type of a given value."""
        DATE_FORMATS = ["%Y-%m-%d", "%Y-%m-%dT%H:%M:%S", "%Y-%m-%dT%H:%M:%S.%f"]
        # 1. Direct Type Lookup
        for python_type, data_type in TYPE_MAPPING.items():
            if isinstance(value, python_type):
                for date_format in DATE_FORMATS:
                    try:
                        datetime.strptime(value, date_format)  # Try parsing the string
                        return DataType.DATETIME.value  # If successful, it's a DateTime
                    except:
                        pass  # Keep trying other formats if this one fails
                if python_type is str and len(value) >= 256:
                    return DataType.LONG_STRING
                return data_type

        # 2. Handle Special Cases (If Any)
        if isinstance(value, Enum):
            return DataType.STRING  # Assuming enums are represented as strings

        # 3. Fallback to Unknown
        return DataType.UNKNOWN

    def should_display(attr_name):
        #Logic to decide whether to display an attribute
        with open('display_config.json', 'r') as config_file:
            display_config = json.load(config_file)

        # Check if the attribute is in the hidden fields list
        if attr_name in display_config['hide'].get('fields', []):
            return False

        # Check if the attribute ends with any of the specified suffixes
        if any(attr_name.endswith(suffix) for suffix in display_config['hide'].get('ends_with', [])):
            return False
        
        return True

    formatted_attributes = []
    for attr_name, attr_value in properties.items():
        attribute_config = {
            "Name": attr_name,
            "Value": attr_value,
            "DataType": get_data_type(attr_value),  # Helper function for data type inference
            "Display": should_display(attr_name),
            "Label": attr_name.replace("_", " ").title(), # Helper function to decide display
        }
        formatted_attributes.append(attribute_config)

 
    return formatted_attributes  # Return the formatted attributes

# Root node types (node types that don't need a parent node to create)
root_node_types = [
    "Project",
    "Product",
    "User",
]

# mapping for the root node type from the discussion node type
discussion_root_node_type_map = {
    'Requirement': 'Product',
    'RequirementRoot': 'Product',
    'Product': 'Product',
    'Architecture': 'Product',
    'WorkItem': 'Project',
    'WorkItemRoot': 'Project',
    'Project': 'Project'
}

def get_labels(node_type):
    if node_type == "Epic":
        return ["Epic", "Requirement"]
    if node_type == "UserStory":
        return ["UserStory", "Requirement"]
    if node_type == "TechRequirement":
        return ["TechRequirement", "Requirement"]
    return [node_type]

def get_specific_node_type(node_types):
    if 'RequirementRoot' in node_types:
        return 'RequirementRoot'
    if 'Epic' in node_types:
        return 'Epic'
    if 'UserStory' in node_types:
        return 'UserStory'
    if 'TechRequirement' in node_types:
        return 'TechRequirement'
    if 'WorkItem' in node_types:
        return 'WorkItem'
    if 'Project' in node_types:
        return 'Project'
    if 'Product' in node_types:
        return 'Product'
    if 'Architecture' in node_types:
        return 'Architecture'
    if 'SystemContext' in node_types:
        return 'SystemContext'

async def create_node(node_type,name, description, properties, function_call=None):
    vector_db = get_vector_db()
    db = get_node_db()
    
    
    # Handle special cases for Project and Product types
    if node_type in ["Project", "Product"] and name and description:
        
        main_properties = {
            "Title": name,
            "Description": description,
            "Type": node_type.lower(),
        }
        if properties:
            main_properties.update(properties)
        print(main_properties)
        created_node = await db.create_node([node_type], main_properties)
        # await db.add_embedding_to_node(created_node["id"], main_properties)
        # await vector_db.add_node_to_vector_db(created_node["id"], main_properties, created_node["id"], node_type)

        # Create root work item for Project or root requirement for Product
    if node_type == "Project":
        requirement_root_properties = {
            "Title": f"Root requirement for product {name}",
            "Description": description,
            "Type": "RequirementRoot",
            "Details": "No details provided"
        }
        
        architecture_root_properties = {
            "Title": f"Root architecture for project {name}",
            "Description": description,
            "Type": "Architecture",
            "Details": "No details provided"
        }
        
        work_item_root_properties = {
            "Title": f"Root work item for project {name}",
            "Description": description,
            "Type": "WorkItemRoot",
            "Details": "No details provided"
        }

        system_context_properties = {
            "Title": f"System Context for project {name}",
            "Description": "High-level view of the system",
            "Type": "SystemContext",
            "Details": "System Context details will be populated during the configuration step"
        }

        async def create_requirement_root():
            requirement_node = await db.create_node(["RequirementRoot", "Requirement"], requirement_root_properties, created_node["id"])
            # await vector_db.add_node_to_vector_db(requirement_node["id"], requirement_root_properties, created_node["id"], "Requirement")

        async def create_architecture_root():
            architecture_node = await db.create_node(["ArchitectureRoot", "Architecture"], architecture_root_properties, created_node["id"])
            # await vector_db.add_node_to_vector_db(architecture_node["id"], architecture_root_properties, created_node["id"], "Architecture")
            # Create ArchitecturalRequirement as a child of ArchitectureRoot
            architectural_requirement_properties = {
                "Title": f"Architectural requirements for project {name}",
                "Description": "Capturing architectural requirements for the project",
                "Type": "ArchitecturalRequirement",
                "Details": "To be populated during architectural discussions"
            }
            arch_req_node = await db.create_node(["ArchitecturalRequirement", "Requirement"],
                                                 architectural_requirement_properties, architecture_node["id"])
            
            # await vector_db.add_node_to_vector_db(arch_req_node["id"], architectural_requirement_properties, created_node["id"], "Requirement")

        async def create_system_context():
            await db.create_node(["SystemContext"], system_context_properties, created_node["id"])
         
        async def create_work_item_root():
                work_item_node = await db.create_node(["WorkItemRoot", "WorkItem"], work_item_root_properties, created_node["id"])
                # await vector_db.add_node_to_vector_db(work_item_node["id"], work_item_root_properties, created_node["id"], "WorkItem")

        # Run all three operations in parallel
        await asyncio.gather(
                create_requirement_root(),
                create_architecture_root(),
                create_system_context(),
                create_work_item_root()
            )
        return created_node
    
    # check for required fields Title, Description and Details in request body
    required_fields = ["Title", "Description"]

    for field in required_fields:
        if field not in properties:
            return ({"error": f"{field} not provided"}), 400
    
    
    # Handle other node types with a possible parent relationship
    if "parent_id" in properties:
        labels = get_labels(node_type)
        node = await db.create_node(labels, properties, properties["parent_id"])
    else:
        node = await db.create_node([node_type], properties)
        
    # await db.add_embedding_to_node(node["id"], properties)
    # root_node = db.get_root_node(node["id"])
    # if root_node:
    #     await vector_db.add_node_to_vector_db(node["id"], properties, root_node['id'], node_type)
    
    return node

async def add_parent_node_id(data):
    node_db = get_node_db()
    node_ids = data.keys()
    for id in node_ids:
        try:
            if data[id].get('parent_node_id'):
                continue
            parent_node = await node_db.get_parent_node(int(id))
            if parent_node:
                parent_id = parent_node.get("id")
                data[id]['parent_node_id']=parent_id
        except Exception as e:
            continue
    return data


async def clone_node(source_node_id, current_user, title):
    try:
        node_db = get_node_db()
        user_id = current_user.get('cognito:username','')
        if not user_id:
            user_id = current_user.get('sub','')
        
        current_tenant_id = get_tenant_id()

        ingested_documents_data_col = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='ingested_documents'
        )

        docs_ingest_data_col = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='docs_ingest_data'
        )

        response = await list_project_files(source_node_id)
        body_bytes = response.body
        project_files = json.loads(body_bytes)

        source_project_info = await node_db.get_node_by_id(source_node_id)

        source_project_prop_info = source_project_info.get("properties", {})
        repo_info = source_project_prop_info.get('repositories','{}')
        repo_info = json.loads(repo_info)

        async def get_ingested_documents_data(source_node_id):
            try:
                ingested_documents_data = await ingested_documents_data_col.get_one(
                        {
                            'document_record.project_id': source_node_id,
                            '$or': [
                                {'isDeleted': {'$exists': False}},
                                {'isDeleted': False}
                            ]
                        },
                        ingested_documents_data_col.db
                    )
                return ingested_documents_data
            except Exception as e:
                raise ValueError(f"{e}")

        async def get_docs_ingest_data(source_node_id):
            try:
                docs_ingest_data = await docs_ingest_data_col.get_one(
                            {
                                'project_id': source_node_id,
                                "status" : "active"
                            },
                            docs_ingest_data_col.db
                        )
                return docs_ingest_data
            except Exception as e:
                raise ValueError(f"{e}")

        async def insert_docs_ingest_data(docs_ingest_data, new_project_id):
            try:
            
                docs_ingest_data['user_id'] = current_user
                docs_ingest_data['created_at'] = datetime.utcnow()
                docs_ingest_data['project_id'] = new_project_id
                docs_ingest_data.pop('_id', None)
                ingest_data = await docs_ingest_data_col.create(
                        docs_ingest_data,
                        docs_ingest_data_col.db
                    )

                return ingest_data

            except Exception as e:
                raise ValueError(f"{e}")
    
        async def insert_ingested_documents_data(file_copy_resp, ingested_documents_data, new_project_id):
            try:

                s3_location = file_copy_resp['target_bucket'] + '/' + file_copy_resp['destination_prefix'] + '/'.join(ingested_documents_data['processing_status']['s3_location'].split('/')[-2:])

                ingested_documents_data['document_record']['project_id'] = new_project_id
                ingested_documents_data['document_record']['uploaded_by']['user_id'] = current_user
                ingested_documents_data['document_record']['uploaded_by']['user_name'] = ''
                ingested_documents_data['document_record']['created_at'] = datetime.utcnow()
                
                ingested_documents_data['processing_status']['s3_location'] = s3_location
                ingested_documents_data.pop('_id', None)

                ingested_documents_dat = await ingested_documents_data_col.create(
                    ingested_documents_data,
                    ingested_documents_data_col.db
                )
                return ingested_documents_dat
            except Exception as e:
                raise ValueError(f"{e}")    

        async def S3_folder_copy(original_tenantId, new_project_id):
            try:
                s3_handler = S3Handler(original_tenantId)
                file_copy_resp = s3_handler.copy_files(
                        source_path,
                        f"extracted-docs-{current_tenant_id}/project-{str(new_project_id)}/",
                        destination_bucket=current_tenant_id,
                        recursive=True
                    )
                
                return file_copy_resp
            except Exception as e :
                raise

        async def create_new_repo(new_project_id, db):
            try:
                from app.utils.respository_utils import create_repository_in_workspace
                query = '''MATCH (n:Project)-[:HAS_CHILD*]->(sc:SystemContext)-[:HAS_CHILD]->(c:Container) 
                    WHERE ID(n) = $source_node_id 
                    RETURN Id(c)'''

                resp = await db.async_run(query, source_node_id=new_project_id)
                data = resp.data()
                container_id = None
                if data:
                    container_id = data[0].get('Id(c)')

                    db = get_node_db()
                    new_repo_info = await create_repository_in_workspace(project_id=new_project_id, container_id=container_id, db=db)

                    query = '''MATCH (n:Project) 
                    WHERE ID(n) = $source_node_id 
                    SET n.repositories = $repo_info
                    RETURN Id(n)'''

                    resp = await db.async_run(query, source_node_id=new_project_id, repo_info=json.dumps({str(container_id): new_repo_info}))
                    data = resp.data()

                    return new_repo_info
                else:
                    print(f'Container not available for project {source_node_id}. Stopped creating a new project repository')
                    return 

            except Exception as e:
                raise ValueError(e)

        async def fork_repo(new_project_id, db):
            try:
                from app.utils.respository_utils import fork_repository_in_workspace
                query = '''MATCH (n:Project)-[:HAS_CHILD*]->(sc:SystemContext)-[:HAS_CHILD]->(c:Container) 
                    WHERE ID(n) = $source_node_id 
                    RETURN Id(c)'''

                resp = await db.async_run(query, source_node_id=new_project_id)
                data = resp.data()
                container_id = None
                if data:
                    container_id = data[0].get('Id(c)')

                    db = get_node_db()
                    new_repo_info = await fork_repository_in_workspace(project_id=new_project_id, container_id=container_id, db=db)

                    query = '''MATCH (n:Project) 
                    WHERE ID(n) = $source_node_id 
                    SET n.repositories = $repo_info
                    RETURN Id(n)'''

                    resp = await db.async_run(query, source_node_id=new_project_id, repo_info=json.dumps({str(container_id): new_repo_info}))
                    data = resp.data()

                    return new_repo_info
                else:
                    print(f'Container not available for project {source_node_id}. Stopped creating a new project repository')
                    return 
                
            except Exception as e:
                raise ValueError(e)

        if project_files.get('file_count', 0):
            ingested_documents_data, docs_ingest_data = await asyncio.gather(
                get_ingested_documents_data(source_node_id),
                get_docs_ingest_data(source_node_id)
            )
            if ingested_documents_data :
                s3_path = ingested_documents_data.get('processing_status', '').get('s3_location', '')
                if s3_path:
                    source_path = '/'.join(s3_path.split('/')[1:3]) + '/'
                    print('source_path', source_path)
                else:
                    raise ValueError(f"Could not get source s3_location got ''")

            if ingested_documents_data :
                original_tenantId = ingested_documents_data.get('document_record', {}).get('tenant_id','')
                if not original_tenantId:
                    raise ValueError(f"Could not get source tenantId got '' {original_tenantId}")

        created_node = await node_db.clone_node(source_node_id, str(user_id), title)
        created_node = created_node.data()

        if created_node and project_files.get('file_count', 0):
            new_project_id = created_node[0].get('cloneId','')
            if ingested_documents_data:
                file_copy_resp = await S3_folder_copy(original_tenantId, new_project_id)

            if docs_ingest_data and ingested_documents_data :
                insert_docs_ingest_resp, insert_ingested_documents_resp = await asyncio.gather(
                    insert_docs_ingest_data(docs_ingest_data, new_project_id),
                    insert_ingested_documents_data(file_copy_resp, ingested_documents_data, new_project_id)
                )

        if repo_info :
            new_project_id = created_node[0].get('cloneId','')
            resp = await fork_repo(new_project_id, node_db)

        return created_node
    
    except Exception as e:
        raise 