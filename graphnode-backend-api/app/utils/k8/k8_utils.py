
# app.utils.k8.k8_utils.py

from kubernetes import client, config
from kubernetes.client.rest import ApiException
import time
from app.utils.aws.ssm_loader import load_ssm_dev_param, load_ssm_qa_param, load_ssm_pre_prod_param
from app.core.k8_settings import k8_settings
import os
from datetime import datetime
import logging
from pathlib import Path

def read_config_map(project_id, env_name):
    """
    Read the ConfigMap associated with a project and environment.
    
    Args:
        project_id (str): Project ID
        env_name (str): Environment name
        
    Returns:
        dict: ConfigMap data if found, None otherwise
    """
    try:
        # Initialize Kubernetes client
        config.load_incluster_config()
        
        # Create Kubernetes API client
        core_v1 = client.CoreV1Api()
        
        # Construct ConfigMap name and namespace
        project_id_lower = str(project_id).lower()
        env_suffix = env_name.lower()
        configmap_name = f"pod-status-{project_id_lower}-{env_suffix}"
        namespace = k8_settings.NAMESPACE
        
        print(f"Attempting to read ConfigMap: {configmap_name} in namespace: {namespace}")
        
        # Read the ConfigMap
        config_map = core_v1.read_namespaced_config_map(name=configmap_name, namespace=namespace)
        
        print(f"ConfigMap {configmap_name} read successfully")
        return config_map.data if config_map else None
        
    except ApiException as e:
        if e.status == 404:
            print(f"ConfigMap {configmap_name} not found")
            return None
        else:
            print(f"Error reading ConfigMap: API error code {e.status}, reason: {e.reason}")
            return None
    except Exception as e:
        print(f"Unexpected error reading ConfigMap: {str(e)}")
        return None

def check_pod_usage_from_configmap(project_id: str, env_name: str) -> bool:
    """
    Check if the pod has been used by reading the ConfigMap.
    
    Args:
        project_id (str): Project ID
        env_name (str): Environment name
        
    Returns:
        bool: True if pod is marked as used in ConfigMap, False otherwise
    """
    try:
        logging.info(f"Checking ConfigMap for pod usage status - project: {project_id}, env: {env_name}")
        config_data = read_config_map(project_id, env_name)
        
        if config_data:
            pod_status = config_data.get('pod-status', '').strip().lower()
            logging.info(f"ConfigMap pod-status value: '{pod_status}'")
            
            if pod_status == 'used':
                logging.info("Pod marked as used via ConfigMap")
                return True
            else:
                logging.info(f"Pod not marked as used in ConfigMap (status: '{pod_status}')")
                return False
        else:
            logging.info("No ConfigMap data found")
            return False
            
    except Exception as e:
        logging.error(f"Error reading ConfigMap for pod usage: {str(e)}")
        return False

def check_pod_usage_status(project_id: str, env_name: str) -> bool:
    """
    Check if the pod has been used by checking both the flag file and ConfigMap.
    
    Args:
        project_id (str): Project ID
        env_name (str): Environment name
        
    Returns:
        bool: True if pod has been used, False otherwise
    """
    pod_used_flag_file = Path("/tmp/pod_used.flag")
    
    # First check the local flag file
    if pod_used_flag_file.exists():
        logging.info("Pod marked as used via local flag file")
        return True
    
    # If no local flag, check ConfigMap
    configmap_used = check_pod_usage_from_configmap(project_id, env_name)
    
    if configmap_used:
        # Create local flag file to cache this status for future checks
        pod_used_flag_file.write_text(datetime.now().isoformat())
        logging.info("Created local flag file based on ConfigMap status")
        return True
    
    return False

def check_deployment_status(project_id: str, env_name: str) -> None:
    """
    Check deployment status and force update if not up-to-date.
    
    Args:
        project_id (str): Project ID to check
        env_name (str): Environment name
    """
    try:
        logging.info("Checking deployment status...")
        config_data = read_config_map(project_id, env_name)
        
        if config_data:
            deployment_status = config_data.get('deployment-status', 'up-to-date')
            
            if deployment_status == 'not-up-to-date':
                logging.warning(f"Deployment not up-to-date for project {project_id}, forcing update")
                
                # Get image SHA from ConfigMap if available
                image_sha = config_data.get('image-sha')
                
                # Force deployment update
                success = force_deployment_update(project_id, env_name, image_sha)
                
                if success:
                    logging.info(f"Successfully forced deployment update for project {project_id}")
                else:
                    logging.error(f"Failed to force deployment update for project {project_id}")
            else:
                logging.info(f"Deployment is up-to-date for project {project_id}")
        else:
            logging.info(f"No ConfigMap data found for project {project_id}, assuming deployment is up-to-date")
            
    except Exception as e:
        logging.error(f"Error in deployment monitoring: {str(e)}")

def force_deployment_update(project_id, env_name, image_sha=None):
    """
    Force a new deployment by updating the deployment's image or adding a restart annotation.
    
    Args:
        project_id (str): Project ID
        env_name (str): Environment name  
        image_sha (str): New image SHA to deploy (optional)
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Initialize Kubernetes client
        config.load_incluster_config()
        apps_v1 = client.AppsV1Api()
        
        # Construct deployment name and namespace
        project_id_lower = str(project_id).lower()
        env_suffix = env_name.lower()
        deployment_name = f"{project_id_lower}-{env_suffix}"
        namespace = k8_settings.NAMESPACE
        
        print(f"Forcing deployment update for: {deployment_name}")
        
        # Get current deployment
        deployment = apps_v1.read_namespaced_deployment(
            name=deployment_name,
            namespace=namespace
        )
        
        # Update deployment with new image or restart annotation
        if image_sha:
            # Update image if SHA provided
            container_name = f"{project_id_lower}-{env_suffix}"
            image_prefix = "127214169382.dkr.ecr.us-west-2.amazonaws.com/codegenservice"
            new_image = f"{image_prefix}:{image_sha}"
            
            for container in deployment.spec.template.spec.containers:
                if container.name == container_name:
                    container.image = new_image
                    print(f"Updated container image to: {new_image}")
                    break
        else:
            # Force restart by adding/updating restart annotation
            if not deployment.spec.template.metadata.annotations:
                deployment.spec.template.metadata.annotations = {}
            
            deployment.spec.template.metadata.annotations['kubectl.kubernetes.io/restartedAt'] = \
                datetime.now().isoformat()
        
        # Apply the update
        apps_v1.patch_namespaced_deployment(
            name=deployment_name,
            namespace=namespace,
            body=deployment
        )
        
        print(f"Successfully triggered deployment update for {deployment_name}")
        return True
        
    except ApiException as e:
        print(f"Kubernetes API error forcing deployment update: {e.status} - {e.reason}")
        return False
    except Exception as e:
        print(f"Unexpected error forcing deployment update: {str(e)}")
        return False