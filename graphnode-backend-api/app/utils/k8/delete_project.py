from kubernetes import client, config
from kubernetes.client.rest import ApiException
import time
from app.utils.aws.ssm_loader import load_ssm_dev_param, load_ssm_qa_param, load_ssm_pre_prod_param
from app.core.k8_settings import k8_settings
import os


def delete_config_map(project_id, env_name):
    """
    Delete the ConfigMap associated with a project and environment.
    
    Args:
        project_id (str): Project ID
        env_name (str): Environment name
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Initialize Kubernetes client
        try:
            config.load_incluster_config()
        except config.ConfigException:
            try:
                config.load_kube_config()
            except config.ConfigException:
                if env_name.lower() == 'dev':
                    kubeconfig = load_ssm_dev_param()
                elif env_name.lower() == 'qa':
                    kubeconfig = load_ssm_qa_param()
                elif env_name.lower() in ['pre-prod', 'preprod']:
                    kubeconfig = load_ssm_pre_prod_param()
                else:
                    kubeconfig = load_ssm_dev_param()  # Default fallback
                config.load_kube_config_from_dict(kubeconfig)
        
        # Create Kubernetes API client
        core_v1 = client.CoreV1Api()
        
        # Construct ConfigMap name and namespace
        project_id_lower = str(project_id).lower()
        env_suffix = env_name.lower()
        configmap_name = f"pod-status-{project_id_lower}-{env_suffix}"
        namespace = k8_settings.NAMESPACE
        
        print(f"Attempting to delete ConfigMap: {configmap_name} in namespace: {namespace}")
        
        # Delete the ConfigMap
        core_v1.delete_namespaced_config_map(
            name=configmap_name,
            namespace=namespace
        )
        
        print(f"ConfigMap {configmap_name} deleted successfully")
        return True
        
    except ApiException as e:
        if e.status == 404:
            print(f"ConfigMap {configmap_name} not found (already deleted)")
            return True
        else:
            print(f"Error deleting ConfigMap: API error code {e.status}, reason: {e.reason}")
            return False
    except Exception as e:
        print(f"Unexpected error deleting ConfigMap: {str(e)}")
        return False
    
def delete_kubernetes_deployment(project_id=None, env_name=None, kubeconfig_dict=None, k8_config_override=None):
    """
    Delete a Kubernetes deployment based on project_id and env_name.
    
    Args:
        project_id (str): Project ID for the deployment to delete
        env_name (str): Environment name
        kubeconfig_dict (dict, optional): Kubernetes config dictionary
        k8_config_override (dict, optional): Config overrides
        
    Raises:
        ValueError: If project_id or env_name is not provided
        ApiException: If Kubernetes API operations fail
    """
    # Override settings if provided
    if k8_config_override:
        k8_settings.override(**k8_config_override)
    
    # Use provided values or fallback to k8_settings
    if project_id:
        k8_settings.PROJECT_ID = project_id
    if env_name:
        k8_settings.ENVIRONMENT = env_name
        
    # if not k8_settings.PROJECT_ID or not k8_settings.ENVIRONMENT:
    #     raise ValueError("Both PROJECT_ID and ENV_NAME must be provided.")

    action = "delete"

    # Load Kubernetes configuration
    if kubeconfig_dict:
        try:
            config.load_kube_config_from_dict(config_dict=kubeconfig_dict)
        except config.ConfigException:
            try:
                config.load_incluster_config()
            except config.ConfigException:
                config.load_kube_config()
    else:
        try:
            config.load_incluster_config()
        except config.ConfigException:
            config.load_kube_config()

    # Create Kubernetes API client
    batch_v1 = client.BatchV1Api()

    # Use settings for job name and namespace
    job_name = k8_settings.get_job_name(action)
    namespace = k8_settings.NAMESPACE

    # Define kubectl command using k8_settings
    kubectl_command = f"""
        echo "Deleting deployment for {job_name}..."
        nginx_pod=$(kubectl get pods -n {namespace} -l app=nginx -o name | head -1 | cut -d'/' -f2)
        echo "Using nginx pod: $nginx_pod"

        if [ -n "$nginx_pod" ]; then
            svc_name=$(kubectl exec -n {namespace} "$nginx_pod" -- cat /etc/nginx/conf.d/custom_{k8_settings.PROJECT_ID}.conf | grep -i "proxy_pass" | head -1 | awk '{{print $2}}' | sed -E 's|http://([^:]+):.*|\\1|')
            echo "Service name: $svc_name"
            
            if [ -n "$svc_name" ]; then
                deployment_name=$(echo $svc_name | cut -d "-" -f2-3)
                echo "Deployment name: $deployment_name"
                svc_name_clusterip="internal-clusterip-$deployment_name"
                echo "Service ClusterIP name: $svc_name_clusterip"
                pvc_name="pvc-$deployment_name"
                echo "PVC NAME: $pvc_name"
                
                echo "Deleting deployment and service"
                kubectl delete deployment "$deployment_name" -n {namespace} --ignore-not-found=true
                kubectl delete service "$svc_name" -n {namespace} --ignore-not-found=true
                kubectl delete service "$svc_name_clusterip" -n {namespace} --ignore-not-found=true
                kubectl delete pvc "$pvc_name" -n {namespace} --ignore-not-found=true
                kubectl exec -n {namespace} "$nginx_pod" -- rm -f /etc/nginx/conf.d/custom_{k8_settings.PROJECT_ID}.conf
                
                echo "Deployment deletion completed successfully"
            else
                echo "Error: Could not determine service name"
                exit 1
            fi
        else
            echo "Error: No nginx pod found"
            exit 1
        fi
        """

    # Define the job manifest using k8_settings
    job_manifest = {
        "apiVersion": "batch/v1",
        "kind": "Job",
        "metadata": {
            "name": job_name,
            "namespace": namespace,
            "labels": {
                "app": job_name,
                "owner": "duploservices",
                "tenantname": namespace,
            },
        },
        "spec": {
            "parallelism": 1,
            "completions": 1,
            "backoffLimit": 6,
            "template": {
                "metadata": {
                    "labels": {
                        "app": job_name,
                        "owner": "duploservices",
                        "tenantname": namespace,
                    },
                },
                "spec": {
                    "serviceAccountName": k8_settings.SERVICE_ACCOUNT_NAME,
                    "restartPolicy": "Never",
                    "containers": [
                        {
                            "name": "duplocloudcodegen",
                            "image": "bitnami/kubectl:latest",
                            "command": ["/bin/sh", "-c"],
                            "args": [kubectl_command],
                            "env": [
                                {"name": "ACTION", "value": action},
                                {"name": "PROJECT_ID", "value": project_id},
                                {"name": "ENV_NAME", "value": k8_settings.ENVIRONMENT},
                            ],
                            "volumeMounts": [
                                {"name": k8_settings.VOLUME_MOUNT, "mountPath": "/app"},
                                {"name": k8_settings.INGRESS_NAME, "mountPath": "/app/ingress"},
                                {"name": "codegenpvc", "mountPath": "/app/pvc"},
                                {"name": "nginx", "mountPath": "/app/nginx"},
                            ],
                        }
                    ],
                    "volumes": [
                        {
                            "name": k8_settings.VOLUME_MOUNT,
                            "configMap": {"name": k8_settings.CONFIG_MAP_NAME},
                        },
                        {
                            "name": k8_settings.INGRESS_NAME,
                            "configMap": {"name": k8_settings.INGRESS_NAME},
                        },
                        {
                            "name": "codegenpvc",
                            "configMap": {"name": "codegenpvc"},
                        },
                        {
                            "name": "nginx",
                            "configMap": {"name": "nginx"},
                        },
                    ],
                },
            },
        },
    }

    # Monitor job completion using k8_settings
    def wait_for_job_completion(job_name, namespace):
        max_iterations = k8_settings.JOB_COMPLETION_TIMEOUT // k8_settings.POLLING_INTERVAL
        iterations = 0
        
        while iterations < max_iterations:
            job = batch_v1.read_namespaced_job(name=job_name, namespace=namespace)
            if job.status.succeeded:
                print(f"Job {job_name} completed successfully, deleting...")
                batch_v1.delete_namespaced_job(
                    name=job_name,
                    namespace=namespace,
                    body=client.V1DeleteOptions(propagation_policy='Foreground', grace_period_seconds=0),
                )
                print(f"Deleted job: {job_name}")
                break
            elif job.status.failed:
                print(f"Job {job_name} failed. Check logs for details.")
                raise RuntimeError(f"Job {job_name} failed to complete")
                
            time.sleep(k8_settings.POLLING_INTERVAL)
            iterations += 1
        
        if iterations >= max_iterations:
            raise TimeoutError(f"Job {job_name} timed out after {k8_settings.JOB_COMPLETION_TIMEOUT} seconds")

    # Create the delete job
    try:
        # Check if job already exists, and delete it if it does
        try:
            existing_job = batch_v1.read_namespaced_job(name=job_name, namespace=namespace)
            print(f"Job {job_name} already exists, deleting it first...")
            batch_v1.delete_namespaced_job(
                name=job_name,
                namespace=namespace,
                body=client.V1DeleteOptions(propagation_policy='Foreground', grace_period_seconds=0),
            )
            
            # Wait for the job to be fully deleted
            retry_count = 0
            max_retries = 10
            while retry_count < max_retries:
                try:
                    batch_v1.read_namespaced_job(name=job_name, namespace=namespace)
                    print(f"Waiting for job {job_name} to be deleted...")
                    time.sleep(k8_settings.POLLING_INTERVAL)
                    retry_count += 1
                except ApiException as e:
                    if e.status == 404:
                        print(f"Job {job_name} has been deleted successfully")
                        break
                    else:
                        raise
            
            if retry_count >= max_retries:
                print(f"Warning: Timed out waiting for job {job_name} to be deleted")
        
        except ApiException as e:
            if e.status != 404:  # 404 means job doesn't exist, which is fine
                raise
            print(f"Job {job_name} does not exist, proceeding with creation")
        
        # Now create the new job
        response = batch_v1.create_namespaced_job(namespace=namespace, body=job_manifest)
        
        print("Deleting associated ConfigMap...")
        delete_config_map(project_id, k8_settings.ENVIRONMENT)
        
        print(f"Job created: {response.metadata.name}")
        wait_for_job_completion(job_name, namespace)
        
        

    except ApiException as e:
        print(f"An error occurred: {e}")
        raise



# Example usage
if __name__ == "__main__":
    # Method 1: Use environment variables
    delete_kubernetes_deployment(project_id=os.getenv("PROJECT_ID"), env_name=os.getenv("ENVIRONMENT"))
