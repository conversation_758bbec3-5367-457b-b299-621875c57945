from kubernetes import client, config
from kubernetes.client.rest import ApiException
import time
from typing import Literal, Optional
import logging
import os
from app.utils.aws.ssm_loader import load_ssm_dev_param

def create_kubernetes_job(
    project_id: str,
    env_name: str,
    action: Literal["create", "delete"],
    namespace: Optional[str] = None,
    kubeconfig_dict: Optional[dict] = None
) -> str:
    """
    Creates and monitors a Kubernetes job to manage codegen deployments.
    
    Args:
        project_id: The project identifier
        env_name: The environment name (e.g., 'dev', 'prod')
        action: Either 'create' or 'delete'
        namespace: The Kubernetes namespace (defaults to "duploservices-kavia-{env_name}")
        kubeconfig_dict: Optional Kubernetes config dict; if None, will try to load from environment
        
    Returns:
        str: A message indicating the result of the operation
        
    Raises:
        ValueError: If inputs are invalid
        ApiException: If Kubernetes API operations fail
    """
    # Validate inputs
    if not project_id or not env_name:
        raise ValueError("PROJECT_ID and ENV_NAME must be provided.")
    
    if action not in ["create", "delete"]:
        raise ValueError("ACTION must be either 'create' or 'delete'.")
    
    # Set default namespace if not provided
    if namespace is None:
        namespace = f"duploservices-kavia-{env_name}"
    
    # Configure Kubernetes client
    if kubeconfig_dict:
        config.load_kube_config_from_dict(kubeconfig_dict)
    else:
        try:
            # Try loading in-cluster config first
            config.load_incluster_config()
        except config.ConfigException:
            # Fall back to local kubeconfig
            try:
                config.load_kube_config()
            except config.ConfigException:
                raise ValueError("Unable to load Kubernetes configuration. Please provide a valid kubeconfig_dict.")
    
    # Create Kubernetes API client
    batch_v1 = client.BatchV1Api()
    
    # Define job parameters
    job_name = f"codegen-{project_id}-{env_name}-{action}"
    hostname = f"vscode-internal-{project_id}.{env_name}.cloud.kavia.ai"
    
    # Generate appropriate command based on action
    kubectl_command = _generate_kubectl_command(project_id, env_name, action, namespace)
    
    # Create job manifest
    job_manifest = {
        "apiVersion": "batch/v1",
        "kind": "Job",
        "metadata": {
            "name": job_name,
            "namespace": namespace,
            "labels": {
                "app": job_name,
                "owner": "duploservices",
                "tenantname": namespace,
            },
        },
        "spec": {
            "parallelism": 1,
            "completions": 1,
            "backoffLimit": 6,
            "template": {
                "metadata": {
                    "labels": {
                        "app": job_name,
                        "owner": "duploservices",
                        "tenantname": namespace,
                    },
                },
                "spec": {
                    "serviceAccountName": f"{namespace}-edit-user",
                    "restartPolicy": "Never",
                    "containers": [
                        {
                            "name": "duplocloudcodegen",
                            "image": "bitnami/kubectl:latest",
                            "command": ["/bin/sh", "-c"],
                            "args": [kubectl_command],
                            "env": [
                                {"name": "ACTION", "value": action},
                                {"name": "PROJECT_ID", "value": project_id},
                                {"name": "ENV_NAME", "value": env_name},
                                {"name": "NAMESPACE", "value": namespace}
                            ],
                            "volumeMounts": [
                                {"name": "codegenservicedeployment3", "mountPath": "/app"},
                                {"name": "ingressservice1", "mountPath": "/app/ingress"},
                                {"name": "codegenpvc", "mountPath": "/app/pvc"},
                                {"name": "nginx", "mountPath": "/app/nginx"},
                            ],
                        }
                    ],
                    "volumes": [
                        {
                            "name": "codegenservicedeployment3",
                            "configMap": {"name": "codegenservicedeployment3"},
                        },
                        {
                            "name": "ingressservice1",
                            "configMap": {"name": "ingressservice1"},
                        },
                        {
                            "name": "codegenpvc",
                            "configMap": {"name": "codegenpvc"},
                        },
                        {
                            "name": "nginx",
                            "configMap": {"name": "nginx"},
                        },
                    ],
                },
            },
        },
    }
    
    # Create and monitor the job
    try:
        response = batch_v1.create_namespaced_job(namespace=namespace, body=job_manifest)
        print(f"Job created: {response.metadata.name}")
        
        # Wait for job completion
        _wait_for_job_completion(batch_v1, job_name, namespace)
        
        # Return appropriate message based on action
        if action == "create":
            return f"Deployment created successfully. Ingress hostname: {hostname}"
        else:
            return f"Deployment deleted successfully for project {project_id} in {env_name} environment."
    
    except ApiException as e:
        error_message = f"Kubernetes API error: {e}"
        print(error_message)
        raise ApiException(error_message)


def _wait_for_job_completion(batch_v1_api, job_name, namespace, timeout_seconds=300):
    """
    Monitors a job until it completes or times out, then deletes it.
    
    Args:
        batch_v1_api: The Kubernetes BatchV1Api client
        job_name: The name of the job to monitor
        namespace: The namespace containing the job
        timeout_seconds: Maximum time to wait for completion
    """
    start_time = time.time()
    
    while time.time() - start_time < timeout_seconds:
        job = batch_v1_api.read_namespaced_job(name=job_name, namespace=namespace)
        
        if job.status.succeeded:
            print(f"Job {job_name} completed successfully, deleting...")
            batch_v1_api.delete_namespaced_job(
                name=job_name,
                namespace=namespace,
                body=client.V1DeleteOptions(propagation_policy='Foreground', grace_period_seconds=0),
            )
            print(f"Deleted job: {job_name}")
            return
        
        # Check for failed jobs to avoid waiting unnecessarily
        if job.status.failed and job.status.failed >= job.spec.backoff_limit:
            print(f"Job {job_name} failed after {job.status.failed} attempts.")
            return
            
        time.sleep(5)
    
    print(f"Job monitoring timed out after {timeout_seconds} seconds.")


def _generate_kubectl_command(project_id, env_name, action, namespace):
    """
    Generates the kubectl command to be executed in the job container.
    
    Args:
        project_id: The project identifier
        env_name: The environment name
        action: The action to perform (create/delete)
        namespace: The Kubernetes namespace
        
    Returns:
        str: The shell command to execute
    """
    if action == "create":
        servicename = f"internal-{project_id}-{env_name}"
        server_name = f"vscode-internal-{project_id}-{env_name}.dev-vscode.cloud.kavia.ai"
        
        return f"""
        echo "Creating deployment for codegen-{project_id}-{env_name}-{action}..."
        
        echo " Starting ingress update script..."
        CONFIG_FILE="/tmp/custom_{project_id}.conf"
        POD_NAME="nginx-c96774b8d-d98nb"
        echo $NAMESPACE
        echo " Environment: $ENV_NAME"
        echo " Finding oldest pod with label 'pod-status=available'..."
        POD_APP=`kubectl get pods -n "$NAMESPACE" -l pod-status=available \\
        --sort-by=.metadata.creationTimestamp \\
        -o jsonpath="{{.items[0].metadata.name}} {{.items[0].metadata.labels.app}}"`
        POD=`echo "$POD_APP" | awk '{{print $1}}'`
        APP=`echo "$POD_APP" | awk '{{print $2}}'`
        echo " Using pod: $POD"
        echo " App label: $APP"
        
        SERVER_NAME="{server_name}"
        PROXY_port1="http://internal-${{APP}}:3000"
        PROXY_port2="http://internal-${{APP}}:3001"
        PROXY_port3="http://internal-${{APP}}:3002"
        PROXY_port4="http://internal-${{APP}}:3003"
        PROXY_port5="http://internal-${{APP}}:3004"
        PROXY_port6="http://internal-${{APP}}:3005"
        PROXY_port7="http://internal-${{APP}}:3006"
        PROXY_port8="http://internal-${{APP}}:3007"
        PROXY_port9="http://internal-${{APP}}:3008"
        PROXY_port10="http://internal-${{APP}}:3009"
        PROXY_port11="http://internal-${{APP}}:3010"
        PROXY_port12="http://internal-${{APP}}:5000"
        PROXY_port13="http://internal-${{APP}}:5001"
        PROXY_port14="http://internal-${{APP}}:5002"
        PROXY_port15="http://internal-${{APP}}:5003"
        PROXY_port16="http://internal-${{APP}}:5004"
        PROXY_port17="http://internal-${{APP}}:5005"
        PROXY_port18="http://internal-${{APP}}:5006"
        PROXY_port19="http://internal-${{APP}}:5007"
        PROXY_port20="http://internal-${{APP}}:5008"
        PROXY_port21="http://internal-${{APP}}:5009"
        PROXY_port22="http://internal-${{APP}}:5010"
        PROXY_port23="http://internal-${{APP}}:8000"
        PROXY_port24="http://internal-${{APP}}:8001"
        PROXY_port25="http://internal-${{APP}}:8002"
        PROXY_port26="http://internal-${{APP}}:8003"
        PROXY_port27="http://internal-${{APP}}:8004"
        PROXY_port28="http://internal-${{APP}}:8005"
        PROXY_port29="http://internal-${{APP}}:8006"
        PROXY_port30="http://internal-${{APP}}:8007"
        PROXY_port31="http://internal-${{APP}}:8008"
        PROXY_port32="http://internal-${{APP}}:8009"
        PROXY_port33="http://internal-${{APP}}:8010"
        PROXY="http://internal-${{APP}}:8080"
        
        echo " SERVER_NAME to be added: $SERVER_NAME"
        echo " PROXY to be routed: $PROXY"
        
        sed "s|{{{{SERVER_NAME}}}}|${{SERVER_NAME}}|g; \\
        s|{{{{PROXY}}}}|${{PROXY}}|g; \\
        s|{{{{PROXY_port1}}}}|${{PROXY_port1}}|g; \\
        s|{{{{PROXY_port2}}}}|${{PROXY_port2}}|g; \\
        s|{{{{PROXY_port3}}}}|${{PROXY_port3}}|g; \\
        s|{{{{PROXY_port4}}}}|${{PROXY_port4}}|g; \\
        s|{{{{PROXY_port5}}}}|${{PROXY_port5}}|g; \\
        s|{{{{PROXY_port6}}}}|${{PROXY_port6}}|g; \\
        s|{{{{PROXY_port7}}}}|${{PROXY_port7}}|g; \\
        s|{{{{PROXY_port8}}}}|${{PROXY_port8}}|g; \\
        s|{{{{PROXY_port9}}}}|${{PROXY_port9}}|g; \\
        s|{{{{PROXY_port10}}}}|${{PROXY_port10}}|g; \\
        s|{{{{PROXY_port11}}}}|${{PROXY_port11}}|g; \\
        s|{{{{PROXY_port12}}}}|${{PROXY_port12}}|g; \\
        s|{{{{PROXY_port13}}}}|${{PROXY_port13}}|g; \\
        s|{{{{PROXY_port14}}}}|${{PROXY_port14}}|g; \\
        s|{{{{PROXY_port15}}}}|${{PROXY_port15}}|g; \\
        s|{{{{PROXY_port16}}}}|${{PROXY_port16}}|g; \\
        s|{{{{PROXY_port17}}}}|${{PROXY_port17}}|g; \\
        s|{{{{PROXY_port18}}}}|${{PROXY_port18}}|g; \\
        s|{{{{PROXY_port19}}}}|${{PROXY_port19}}|g; \\
        s|{{{{PROXY_port20}}}}|${{PROXY_port20}}|g; \\
        s|{{{{PROXY_port21}}}}|${{PROXY_port21}}|g; \\
        s|{{{{PROXY_port22}}}}|${{PROXY_port22}}|g; \\
        s|{{{{PROXY_port23}}}}|${{PROXY_port23}}|g; \\
        s|{{{{PROXY_port24}}}}|${{PROXY_port24}}|g; \\
        s|{{{{PROXY_port25}}}}|${{PROXY_port25}}|g; \\
        s|{{{{PROXY_port26}}}}|${{PROXY_port26}}|g; \\
        s|{{{{PROXY_port27}}}}|${{PROXY_port27}}|g; \\
        s|{{{{PROXY_port28}}}}|${{PROXY_port28}}|g; \\
        s|{{{{PROXY_port29}}}}|${{PROXY_port29}}|g; \\
        s|{{{{PROXY_port30}}}}|${{PROXY_port30}}|g; \\
        s|{{{{PROXY_port31}}}}|${{PROXY_port31}}|g; \\
        s|{{{{PROXY_port32}}}}|${{PROXY_port32}}|g; \\
        s|{{{{PROXY_port33}}}}|${{PROXY_port33}}|g" /app/nginx/nginx > "${{CONFIG_FILE}}"

        echo " Created local config: $CONFIG_FILE"

        DEST_FILE="/etc/nginx/conf.d/$(basename "$CONFIG_FILE")"
        kubectl cp "$CONFIG_FILE" "$NAMESPACE/$POD_NAME:$DEST_FILE"
        echo " Copied config into pod: $POD_NAME"
        kubectl exec -n "$NAMESPACE" "$POD_NAME" -- nginx -s reload
        echo " Reloaded nginx in pod: $POD_NAME"
        echo " Labeling pod '$POD' as used..."
        kubectl label pod "$POD" -n "$NAMESPACE" pod-status=used --overwrite
        """
    elif action == "delete":
        return f"""
        echo "Deleting deployment for codegen-{project_id}-{env_name}-{action}..."
        
        # Get the nginx pod name
        nginx_pod=$(kubectl get pods -n {namespace} | grep -i "nginx" | cut -d " " -f1)
        
        # Get the service name from the nginx configuration
        svc_name=$(kubectl exec -n "$NAMESPACE" "$nginx_pod" -- cat /etc/nginx/conf.d/custom_{project_id}.conf | grep -i "proxy_pass" | head -1 | awk '{{print $2}}' | sed -E 's|http://([^:]+):.*|\\1|')
        
        echo "Service name: $svc_name"
        
        # Extract deployment name from service name
        deployment_name=$(echo $svc_name | cut -d "-" -f2-3)
        echo "Deployment name: $deployment_name"
        
        pvc_name="pvc-$deployment_name"
        echo "PVC name: $pvc_name"
        
        # Delete the deployment, service, and PVC
        echo "Deleting deployment, service, and PVC..."
        kubectl delete deployment "$deployment_name" -n "$NAMESPACE" --ignore-not-found=true
        kubectl delete service "$svc_name" -n "$NAMESPACE" --ignore-not-found=true
        kubectl delete pvc "$pvc_name" -n "$NAMESPACE" --ignore-not-found=true
        
        # Remove nginx configuration
        kubectl exec -n "$NAMESPACE" "$nginx_pod" -- rm -f /etc/nginx/conf.d/custom_{project_id}.conf
        kubectl exec -n "$NAMESPACE" "$nginx_pod" -- nginx -s reload
        
        echo "Cleanup completed successfully"
        """


def auto_terminate_pod(project_id: str = None, env_name: str = None) -> None:
    """
    Function to trigger self-termination of the pod by creating a Kubernetes job
    that will delete the deployment.
    
    Args:
        project_id: The project ID to terminate (defaults to PROJECT_ID env var)
        env_name: The environment name (defaults to ENV_NAME env var or "dev")
    """
    # Configure logging if not already configured
    if not logging.getLogger().handlers:
        logging.basicConfig(level=logging.INFO)
    
    # Get project_id and env_name from environment variables if not provided
    if project_id is None:
        project_id = os.environ.get("PROJECT_ID")
        if not project_id:
            logging.error("Cannot auto-terminate: No PROJECT_ID available")
            return
    
    if env_name is None:
        env_name = os.environ.get("ENVIRONMENT", "dev")
    
    logging.warning(f"Starting auto-termination for project {project_id} in {env_name} environment")
    # kubeconfig = load_ssm_dev_param()
    try:
        # Use the create_kubernetes_job function to execute a deletion job
        result = create_kubernetes_job(
            project_id=str(project_id),
            env_name=env_name,
            action="delete",
            # kubeconfig_dict=kubeconfig,
        )
        
        logging.info(f"Auto-termination job created: {result}")
        return result
    except Exception as e:
        logging.error(f"Error during auto-termination: {str(e)}")
        return f"Error: {str(e)}"

# Example usage
if __name__ == "__main__":
    # If you need to load SSM parameters for kubeconfig, do it here
    
    
    try:
        # Example: Load kubeconfig from SSM
        kubeconfig = load_ssm_dev_param()
        
        # Sample parameters
        project_id = "9813"
        env_name = "dev"
        action = "delete"  # or "delete"
        
        # Call the function
        result = create_kubernetes_job(
            project_id=project_id,
            env_name=env_name,
            action=action,
            kubeconfig_dict=kubeconfig
        )
        
        print(result)
        
    except Exception as e:
        print(f"Error: {str(e)}")