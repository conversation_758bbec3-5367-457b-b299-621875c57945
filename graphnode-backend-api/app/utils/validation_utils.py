
import asyncio
import re
from typing import List, <PERSON><PERSON>

from llm_wrapper.core.llm_interface import LLMInterface
from app.utils.logs_utils import get_path
from app.utils.mermaid_validator import fix_mermaid_with_llm, validate_mermaid_code

async def validate_and_fix_mermaid_in_response(response_content: str) -> str:
    llm = LLMInterface(str(get_path()), 'knowledge', 'test-123' ,int(2),'code_query')

    """
    Extract, validate, and fix all mermaid diagrams in the response content.
    Only updates content if diagrams are successfully fixed.
    
    Args:
        llm: LLM interface for fixing invalid diagrams
        response_content: The complete response content containing mermaid diagrams
        
    Returns:
        Updated response content with fixed mermaid diagrams (only if successfully fixed)
    """
    
    # Pattern to match mermaid code blocks
    mermaid_pattern = r'```mermaid\n(.*?)\n```'
    
    def extract_mermaid_blocks(content: str) -> List[Tuple[str, int, int]]:
        """Extract all mermaid blocks with their positions"""
        blocks = []
        for match in re.finditer(mermaid_pattern, content, re.DOTALL):
            mermaid_code = match.group(1).strip()
            start_pos = match.start()
            end_pos = match.end()
            blocks.append((mermaid_code, start_pos, end_pos))
        return blocks
    
    mermaid_blocks = extract_mermaid_blocks(response_content)
    
    if not mermaid_blocks:
        return response_content
    
    print(f"Found {len(mermaid_blocks)} mermaid blocks to validate")
    
    # Track if any diagrams were actually fixed
    any_fixed = False
    updated_content = response_content
    
    # Process blocks in reverse order to maintain position indices
    for mermaid_code, start_pos, end_pos in reversed(mermaid_blocks):
        is_valid, error_msg = validate_mermaid_code(mermaid_code)
        
        if not is_valid:
            print(f"Invalid mermaid diagram found. Attempting to fix...")
            
            # Try to fix the diagram
            fixed_diagram, is_fixed = await fix_mermaid_with_llm(llm, mermaid_code, error_msg)
            
            if is_fixed and fixed_diagram != mermaid_code:
                # Replace the invalid mermaid block with the fixed one
                fixed_block = f"```mermaid\n{fixed_diagram}\n```"
                print("FIxed Block", fixed_block)
                updated_content = updated_content[:start_pos] + fixed_block + updated_content[end_pos:]
                any_fixed = True
                print("Mermaid diagram fixed successfully")
                print(fixed_block)
            else:
                print(f"Failed to fix mermaid diagram")
        else:
            print("Mermaid diagram is valid, no fix needed")
            print(mermaid_code)
            print("------------------------------------------")
    
    # Only return updated content if we actually fixed something
    if any_fixed:
        print("Returning updated content with fixed mermaid diagrams")
        print(updated_content)
        return updated_content
    else:
        print("No diagrams were fixed, returning original content")
        return response_content



def validate_name(name: str, type: str = "Section") -> tuple[bool, str]:
    """
    Validate section name
    Returns: (is_valid, error_message)
    """
    name = name.strip()
    
    # Validation rules with corresponding error messages
    validations = [
        (bool(name), f"{type} name cannot be empty"),
        (len(name) >= 3, f"{type} name must be at least 3 characters long"),
        (len(name) <= 100, f"{type} name cannot exceed 100 characters"),
        (any(c.isalnum() for c in name), f"{type} name must contain at least one alphanumeric character"),
        (not any(c in '<>{}[]\\/' for c in name), f"{type} name contains invalid characters")
    ]

    # Return first failed validation's error message, or success
    for is_valid, error_msg in validations:
        if not is_valid:
            return False, error_msg
            
    return True, f"{type} name is valid"
