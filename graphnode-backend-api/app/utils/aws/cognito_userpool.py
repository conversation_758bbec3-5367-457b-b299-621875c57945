from typing import List, Dict, Optional, <PERSON><PERSON>
import boto3
from botocore.exceptions import Client<PERSON>rror
from app.core.Settings import settings as base_settings

class CognitoUserPoolCreator:
    def __init__(self):
        self.settings = base_settings
        self.cognito = boto3.client(
            'cognito-idp',
            aws_access_key_id=self.settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=self.settings.AWS_SECRET_ACCESS_KEY,
            region_name=self.settings.AWS_REGION
        )

    def check_pool_exists(self, pool_name: str) -> bool:
        """
        Check if a user pool with the given name already exists
        
        Args:
            pool_name (str): Name of the user pool to check
            
        Returns:
            bool: True if pool exists, False otherwise
            
        Raises:
            Exception: If there's an error checking the pools
        """
        try:
            response = self.cognito.list_user_pools(MaxResults=60)
            existing_pools = [pool for pool in response['UserPools'] 
                            if pool['Name'] == pool_name]
            return len(existing_pools) > 0
        except ClientError as e:
            raise Exception(f"Failed to check existing pools: {str(e)}")

    def create_user_pool(
        self,
        pool_name: str,
        password_policy: Dict = None,
        mfa_configuration: str = 'OFF',
        schema_attributes: List[Dict] = None,
        auto_verified_attributes: List[str] = None
    ) -> Dict:
        """
        Create a new Cognito User Pool with specified configurations
        
        Args:
            pool_name (str): Name of the user pool
            password_policy (Dict): Password policy configuration
            mfa_configuration (str): MFA configuration ('OFF', 'ON', or 'OPTIONAL')
            schema_attributes (List[Dict]): Custom schema attributes
            auto_verified_attributes (List[str]): Attributes to auto-verify
            
        Returns:
            Dict: Created user pool information
            
        Raises:
            Exception: If pool creation fails or pool name already exists
        """
        try:
            # Check if pool already exists
            if self.check_pool_exists(pool_name):
                raise Exception(f"User pool with name '{pool_name}' already exists")

            # Default password policy if none provided
            default_password_policy = {
                'MinimumLength': 8,
                'RequireUppercase': True,
                'RequireLowercase': True,
                'RequireNumbers': True,
                'RequireSymbols': True,
                'TemporaryPasswordValidityDays': 7
            }
            
            # Define custom attributes schema
            default_schema_attributes = [
                {
                    'Name': 'tenant_id',
                    'AttributeDataType': 'String',
                    'DeveloperOnlyAttribute': False,
                    'Mutable': True,
                    'Required': False,
                    'StringAttributeConstraints': {
                        'MinLength': '1',
                        'MaxLength': '256'
                    }
                },
                {
                    'Name': 'is_admin',
                    'AttributeDataType': 'Boolean',
                    'DeveloperOnlyAttribute': False,
                    'Mutable': True,
                    'Required': False
                },
                {
                    'Name': 'Name',
                    'AttributeDataType': 'String',
                    'DeveloperOnlyAttribute': False,
                    'Mutable': True,
                    'Required': False,
                    'StringAttributeConstraints': {
                        'MinLength': '1',
                        'MaxLength': '256'
                    }
                },
                {
                    'Name': 'Department',  # Changed from 'Department'
                    'AttributeDataType': 'String',
                    'DeveloperOnlyAttribute': False,
                    'Mutable': True,
                    'Required': False,
                    'StringAttributeConstraints': {
                        'MinLength': '1',
                        'MaxLength': '256'
                    }
                },
                {
                    'Name': 'Designation',  # Remains the same if no conflict
                    'AttributeDataType': 'String', 
                    'DeveloperOnlyAttribute': False,
                    'Mutable': True,
                    'Required': False,
                    'StringAttributeConstraints': {
                        'MinLength': '1',
                        'MaxLength': '256'
                    }
                }
            ]

            
            # Build the create request parameters
            params = {
                'PoolName': pool_name,
                'Policies': {
                    'PasswordPolicy': password_policy or default_password_policy
                },
                'MfaConfiguration': mfa_configuration,
                'AutoVerifiedAttributes': auto_verified_attributes or ['email'],
                'VerificationMessageTemplate': {
                    'DefaultEmailOption': 'CONFIRM_WITH_CODE',
                    'EmailMessage': 'Your verification code is {####}.',
                    'EmailSubject': 'Your verification code'
                },
                'EmailConfiguration': {
                    'EmailSendingAccount': 'DEVELOPER',
                    'SourceArn': self.settings.SES_SOURCE_ARN,
                    'ReplyToEmailAddress': self.settings.SES_REPLY_TO,
                    'From': self.settings.SES_FROM_EMAIL
                },
                'AccountRecoverySetting': {
                    'RecoveryMechanisms': [
                        {
                            'Priority': 1,
                            'Name': 'verified_email'
                        }
                    ]
                },
                'UsernameAttributes': ['email'],
                'Schema': default_schema_attributes
            }

            response = self.cognito.create_user_pool(**params)
            return response['UserPool']

        except ClientError as e:
            raise Exception(f"Failed to create user pool: {str(e)}")
        
    # Add this method to CognitoUserPoolCreator class if you want to do it programmatically
    def delete_existing_pool(self, pool_name: str):
        try:
            # Get the pool ID first
            pool_id = self.get_user_pool_id_by_name(pool_name)
            if pool_id:
                self.delete_user_pool(pool_id)
                print(f"Successfully deleted pool: {pool_name}")
        except Exception as e:
            print(f"Error deleting pool: {str(e)}")

    def create_pool_and_client(self, tenant_id: str, app_name: str) -> Dict:
        """
        Create a user pool and client in one go if they don't exist
        
        Args:
            tenant_id (str): Tenant ID to use as pool name
            app_name (str): Name for the app client
            
        Returns:
            Dict: Dictionary containing all relevant IDs and secrets
            
        Raises:
            Exception: If setup fails or pool already exists
        """
        try:
            # First check if pool exists
            if self.check_pool_exists(tenant_id):
                raise Exception(f"User pool for tenant '{tenant_id}' already exists")

            # Create user pool
            user_pool = self.create_user_pool(pool_name=tenant_id)
            pool_id = user_pool['Id']

            # Create app client
            app_client = self.create_app_client(
                user_pool_id=pool_id,
                client_name=app_name
            )
            
            return {
                'user_pool_id': pool_id,
                'client_id': app_client['ClientId'],
                'client_secret': app_client.get('ClientSecret')
            }
            
        except Exception as e:
            raise Exception(f"Failed to setup pool and client: {str(e)}")

    def create_app_client(
        self,
        user_pool_id: str,
        client_name: str,
        generate_secret: bool = False,
        refresh_token_validity: int = 30,
        access_token_validity: int = 24,
        id_token_validity: int = 24,
        allowed_oauth_flows: List[str] = None,
        allowed_oauth_scopes: List[str] = None,
        callback_urls: List[str] = None,
        logout_urls: List[str] = None
    ) -> Dict:
        """
        Create an app client for the user pool
        
        Args:
            user_pool_id (str): ID of the user pool
            client_name (str): Name of the app client
            generate_secret (bool): Whether to generate a client secret
            refresh_token_validity (int): Refresh token validity in days
            access_token_validity (int): Access token validity in hours
            id_token_validity (int): ID token validity in hours
            allowed_oauth_flows (List[str]): Allowed OAuth flows
            allowed_oauth_scopes (List[str]): Allowed OAuth scopes
            callback_urls (List[str]): Callback URLs for the client
            logout_urls (List[str]): Logout URLs for the client
            
        Returns:
            Dict: Created app client information
        """
        try:
            params = {
                'UserPoolId': user_pool_id,
                'ClientName': client_name,
                'GenerateSecret': generate_secret,
                'RefreshTokenValidity': refresh_token_validity,
                'AccessTokenValidity': access_token_validity,
                'IdTokenValidity': id_token_validity,
                'TokenValidityUnits': {
                    'AccessToken': 'hours',
                    'IdToken': 'hours',
                    'RefreshToken': 'days'
                },
                'PreventUserExistenceErrors': 'ENABLED',
                'ExplicitAuthFlows': [
                    'ALLOW_ADMIN_USER_PASSWORD_AUTH',  # This enables ADMIN_NO_SRP_AUTH
                    'ALLOW_USER_PASSWORD_AUTH',
                    'ALLOW_REFRESH_TOKEN_AUTH',
                    'ALLOW_USER_SRP_AUTH',
                    'ALLOW_CUSTOM_AUTH'
                ]
            }

            # Add OAuth configurations if provided
            if any([allowed_oauth_flows, allowed_oauth_scopes, callback_urls, logout_urls]):
                params['AllowedOAuthFlows'] = allowed_oauth_flows or ['code', 'implicit']
                params['AllowedOAuthScopes'] = allowed_oauth_scopes or [
                    'email',
                    'openid',
                    'profile',
                    'aws.cognito.signin.user.admin'
                ]
                params['CallbackURLs'] = callback_urls or ['http://localhost:3000/callback']
                params['LogoutURLs'] = logout_urls or ['http://localhost:3000/logout']
                params['AllowedOAuthFlowsUserPoolClient'] = True

            response = self.cognito.create_user_pool_client(**params)
            return response['UserPoolClient']

        except ClientError as e:
            raise Exception(f"Failed to create app client: {str(e)}")

    def get_user_pool_id_by_name(self, pool_name: str) -> str:
        """
        Get User Pool ID by pool name
        
        Args:
            pool_name (str): Name of the user pool
            
        Returns:
            str: User Pool ID if found
            
        Raises:
            Exception: If pool not found or multiple pools found with same name
        """
        try:
            response = self.cognito.list_user_pools(MaxResults=60)
            matching_pools = [
                pool for pool in response['UserPools']
                if pool['Name'] == pool_name
            ]
            
            if not matching_pools:
                raise Exception(f"No user pool found with name: {pool_name}")
            if len(matching_pools) > 1:
                raise Exception(f"Multiple user pools found with name: {pool_name}")
                
            return matching_pools[0]['Id']
            
        except ClientError as e:
            raise Exception(f"Failed to get user pool ID: {str(e)}")

    def get_client_id_by_name(self, user_pool_id: str, client_name: str) -> Tuple[str, Optional[str]]:
        """
        Get Client ID and Secret (if exists) by client name
        
        Args:
            user_pool_id (str): ID of the user pool
            client_name (str): Name of the client app
            
        Returns:
            Tuple[str, Optional[str]]: Tuple of (client_id, client_secret)
            
        Raises:
            Exception: If client not found or multiple clients found with same name
        """
        try:
            response = self.cognito.list_user_pool_clients(
                UserPoolId=user_pool_id,
                MaxResults=60
            )
            
            matching_clients = [
                client for client in response['UserPoolClients']
                if client['ClientName'] == client_name
            ]
            
            if not matching_clients:
                raise Exception(f"No client found with name: {client_name}")
            if len(matching_clients) > 1:
                raise Exception(f"Multiple clients found with name: {client_name}")
            
            # Get detailed client info to check for secret
            client_details = self.cognito.describe_user_pool_client(
                UserPoolId=user_pool_id,
                ClientId=matching_clients[0]['ClientId']
            )['UserPoolClient']
            
            return client_details['ClientId'], client_details.get('ClientSecret')
            
        except ClientError as e:
            raise Exception(f"Failed to get client ID: {str(e)}")

    def get_all_pool_info(self) -> List[Dict]:
        """
        Get information about all user pools
        
        Returns:
            List[Dict]: List of user pool information
        """
        try:
            response = self.cognito.list_user_pools(MaxResults=60)
            return response['UserPools']
        except ClientError as e:
            raise Exception(f"Failed to list user pools: {str(e)}")

    def get_all_client_info(self, user_pool_id: str) -> List[Dict]:
        """
        Get information about all clients in a user pool
        
        Args:
            user_pool_id (str): ID of the user pool
            
        Returns:
            List[Dict]: List of client information
        """
        try:
            response = self.cognito.list_user_pool_clients(
                UserPoolId=user_pool_id,
                MaxResults=60
            )
            return response['UserPoolClients']
        except ClientError as e:
            raise Exception(f"Failed to list clients: {str(e)}")

    def delete_user_pool(self, user_pool_id: str) -> None:
        """Delete a Cognito User Pool"""
        try:
            self.cognito.delete_user_pool(UserPoolId=user_pool_id)
        except ClientError as e:
            raise Exception(f"Failed to delete user pool: {str(e)}")
        
    def update_app_client_token_validity(
        self,
        user_pool_id: str,
        client_id: str,
        access_token_validity: int = 24,
        id_token_validity: int = 24,
        refresh_token_validity: int = 30
    ) -> Dict:
        """
        Update token validity settings for an existing app client
        
        Args:
            user_pool_id (str): ID of the user pool
            client_id (str): ID of the app client
            access_token_validity (int): Access token validity in hours (default: 24)
            id_token_validity (int): ID token validity in hours (default: 24)
            refresh_token_validity (int): Refresh token validity in days (default: 30)
            
        Returns:
            Dict: Updated app client information
            
        Raises:
            Exception: If update fails
        """
        try:
            response = self.cognito.update_user_pool_client(
                UserPoolId=user_pool_id,
                ClientId=client_id,
                AccessTokenValidity=access_token_validity,
                IdTokenValidity=id_token_validity,
                RefreshTokenValidity=refresh_token_validity,
                TokenValidityUnits={
                    'AccessToken': 'hours',
                    'IdToken': 'hours',
                    'RefreshToken': 'days'
                }
            )
            return response['UserPoolClient']
        except ClientError as e:
            raise Exception(f"Failed to update app client token validity: {str(e)}")

    def update_app_client_auth_flows(self, user_pool_id: str, client_id: str) -> Dict:
        """Update an existing app client to enable USER_PASSWORD_AUTH and ADMIN_NO_SRP_AUTH flows"""
        try:
            # First, get the current client configuration to preserve other settings
            client_details = self.cognito.describe_user_pool_client(
                UserPoolId=user_pool_id,
                ClientId=client_id
            )['UserPoolClient']
            
            # Update with all necessary auth flows
            response = self.cognito.update_user_pool_client(
                UserPoolId=user_pool_id,
                ClientId=client_id,
                # Preserve existing client name
                ClientName=client_details.get('ClientName', f'Client-{client_id}'),
                # Include all auth flows we need
                ExplicitAuthFlows=[
                    'ALLOW_ADMIN_USER_PASSWORD_AUTH',  # This enables ADMIN_NO_SRP_AUTH
                    'ALLOW_USER_PASSWORD_AUTH',
                    'ALLOW_REFRESH_TOKEN_AUTH',
                    'ALLOW_USER_SRP_AUTH',
                    'ALLOW_CUSTOM_AUTH'
                ],
                # Preserve token validity settings
                RefreshTokenValidity=client_details.get('RefreshTokenValidity', 30),
                AccessTokenValidity=client_details.get('AccessTokenValidity', 24),
                IdTokenValidity=client_details.get('IdTokenValidity', 24),
                TokenValidityUnits=client_details.get('TokenValidityUnits', {
                    'AccessToken': 'hours',
                    'IdToken': 'hours',
                    'RefreshToken': 'days'
                })
            )
            return response['UserPoolClient']
        except ClientError as e:
            raise Exception(f"Failed to update app client auth flows: {str(e)}")

    def update_user_pool_email_config(self, user_pool_id: str) -> Dict:
        """Update an existing user pool to use SES email configuration"""
        try:
            # Get current user pool configuration
            current_pool = self.cognito.describe_user_pool(UserPoolId=user_pool_id)['UserPool']
            
            # Update user pool with SES email configuration
            response = self.cognito.update_user_pool(
                UserPoolId=user_pool_id,
                EmailConfiguration={
                    'EmailSendingAccount': 'DEVELOPER',
                    'SourceArn': self.settings.SES_SOURCE_ARN,
                    'ReplyToEmailAddress': self.settings.SES_REPLY_TO,
                    'From': self.settings.SES_FROM_EMAIL
                },
                VerificationMessageTemplate={
                    'DefaultEmailOption': 'CONFIRM_WITH_CODE',
                    'EmailMessage': 'Your verification code is {####}.',
                    'EmailSubject': 'Your verification code'
                },
                EmailVerificationMessage='Your verification code is {####}.',
                EmailVerificationSubject='Your verification code'
            )
            
            return response['UserPool']
        except ClientError as e:
            raise Exception(f"Failed to update user pool email configuration: {str(e)}")

    def ensure_ses_email_config(self, user_pool_id: str) -> bool:
        """Ensure user pool is using SES email configuration, update if necessary"""
        try:
            # Get current user pool configuration
            current_pool = self.cognito.describe_user_pool(UserPoolId=user_pool_id)['UserPool']
            
            # Check current email configuration
            current_email_config = current_pool.get('EmailConfiguration', {})
            email_sending_account = current_email_config.get('EmailSendingAccount', 'COGNITO_DEFAULT')
            
            # If already using SES with our configuration, no need to update
            if (email_sending_account == 'DEVELOPER' and 
                current_email_config.get('SourceArn') == self.settings.SES_SOURCE_ARN):
                return True
            
            # Update to use SES
            self.update_user_pool_email_config(user_pool_id)
            return True
            
        except Exception as e:
            # Log error but don't fail the calling operation
            print(f"Warning: Could not update email configuration for pool {user_pool_id}: {str(e)}")
            return False

