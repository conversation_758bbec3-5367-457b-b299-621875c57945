from typing import Dict, List, Optional
from app.utils.aws.cognito_group_manager import CognitoGroupManager
from app.utils.aws.cognito_user_manager import CognitoUserManager
from app.utils.aws.cognito_userpool import CognitoUser<PERSON>ool<PERSON>reator
from app.connection.establish_db_connection import get_mongo_db_v1
from app.repository.mongodb.tenant_repository import TenantCredentialsRepository
from app.repository.mongodb.tenant_repository import TenantPermissionRepository
from app.repository.mongodb.client import get_db
from app.core.Settings import settings
from app.connection.tenant_middleware import KAVIA_ROOT_DB_NAME


class TenantService:
    def __init__(self):
        self.pool_creator = CognitoUserPoolCreator()

    def get_cognito_tenant_id(self, tenant_id: str) -> str:
        return f"{settings.STAGE}-{tenant_id}"

    async def get_tenant_cred(self, tenant_id: str) -> Dict[str, str]:
        """Get existing tenant credentials (user pool and client)."""
        try:
            if tenant_id.startswith('default_') or tenant_id.startswith('default-'):
                tenant_id = settings.KAVIA_B2C_CLIENT_ID
            # Get repositories and database connection
            creds_repo: TenantCredentialsRepository = await get_mongo_db_v1("tenant_credentials")
            db = await get_db(KAVIA_ROOT_DB_NAME)  # Use kaviaroot database for tenant credentials
            
            # First check if credentials exist in MongoDB
            existing_creds = await creds_repo.get_tenant_credentials(tenant_id, db)
            if existing_creds:
                return {
                    'user_pool_id': existing_creds['user_pool_id'],
                    'client_id': existing_creds['client_id']
                }
            
            # If not in MongoDB, get from Cognito
            cognito_tenant_id = self.get_cognito_tenant_id(tenant_id)
            pool_id = self.pool_creator.get_user_pool_id_by_name(cognito_tenant_id)
            client_id, _ = self.pool_creator.get_client_id_by_name(
                pool_id,
                f'app-{cognito_tenant_id}'
            )
            
            # Store credentials in MongoDB
            await creds_repo.store_tenant_credentials(
                tenant_id=tenant_id,
                user_pool_id=pool_id,
                client_id=client_id,
                db=db
            )
            
            return {
                'user_pool_id': pool_id,
                'client_id': client_id
            }
        except Exception as e:
            raise Exception(f"Failed to get tenant credentials: {str(e)}")

    async def create_tenant_cred(self, tenant_id: str) -> Dict[str, str]:
        """Create new tenant credentials (user pool and client)."""
        try:
            cognito_tenant_id = self.get_cognito_tenant_id(tenant_id)
            creds = self.pool_creator.create_pool_and_client(
                tenant_id=cognito_tenant_id,
                app_name=f'app-{cognito_tenant_id}'
            )

            # Store the new credentials
            creds_repo: TenantCredentialsRepository = await get_mongo_db_v1("tenant_credentials")
            db = await get_db(KAVIA_ROOT_DB_NAME)
            await creds_repo.store_tenant_credentials(
                tenant_id=tenant_id,
                user_pool_id=creds['user_pool_id'],
                client_id=creds['client_id'],
                db=db
            )

            return {
                'user_pool_id': creds['user_pool_id'],
                'client_id': creds['client_id']
            }
        except Exception as e:
            raise Exception(f"Failed to create tenant credentials: {str(e)}")

    async def get_or_create_tenant_cred(self, tenant_id: str) -> Dict[str, str]:
        """Get existing tenant credentials or create new ones if they don't exist."""
        try:
            try:
                return await self.get_tenant_cred(tenant_id)
            except Exception:
                return await self.create_tenant_cred(tenant_id)
        except Exception as e:
            raise Exception(f"Failed to get/create tenant credentials: {str(e)}")

    async def list_permissions(self, tenant_id: str, group_name: Optional[str] = None) -> Dict:
        """List permissions for a tenant or specific group."""
        try:
            # Get tenant credentials and initialize group manager
            creds = await self.get_tenant_cred(tenant_id)
            group_manager = CognitoGroupManager(creds['user_pool_id'])
            
            permission_repo: TenantPermissionRepository = await get_mongo_db_v1("tenant_permissions")
            db = await get_db(KAVIA_ROOT_DB_NAME)

            if group_name:
                # Verify group exists
                groups = group_manager.list_groups()
                if not any(g['GroupName'] == group_name for g in groups):
                    raise Exception(f"Group {group_name} not found")
                
                permission_id = f"{tenant_id}_{group_name}"
                return await permission_repo.get_permission(permission_id, db)
            else:
                # Get all groups and their permissions
                groups = group_manager.list_groups()
                permission_ids = [f"{tenant_id}_{group['GroupName']}" for group in groups]
                permissions = await permission_repo.get_permissions(permission_ids, db)
                
                return {
                    "groups": {
                        group['GroupName']: permissions.get(f"{tenant_id}_{group['GroupName']}", {})
                        for group in groups
                    }
                }
        except Exception as e:
            raise Exception(f"Failed to list permissions: {str(e)}")

    async def list_permissions_batch(self, tenant_id: str, group_names: Optional[List[str]] = None) -> Dict:
        """Get combined permissions for multiple groups."""
        try:
            if group_names:
                permission_repo: TenantPermissionRepository = await get_mongo_db_v1("tenant_permissions")
                db = await get_db(KAVIA_ROOT_DB_NAME)
                permission_ids = [f"{tenant_id}_{group}" for group in group_names]
                return await permission_repo.get_permissions(permission_ids, db)
            else:
                return await self.list_permissions(tenant_id)
        except Exception as e:
            raise Exception(f"Failed to list batch permissions: {str(e)}")

    async def create_group(self, tenant_id: str, group_name: str, permissions: Optional[Dict] = None) -> Dict:
        """Create a new group with optional permissions."""
        try:
            # Get tenant credentials
            creds = await self.get_tenant_cred(tenant_id)
            group_manager = CognitoGroupManager(creds['user_pool_id'])
            
            # Create group in Cognito
            group = group_manager.create_group(group_name)
            
            # Create permissions if provided
            if permissions:
                permission_repo: TenantPermissionRepository = await get_mongo_db_v1("tenant_permissions")
                db = await get_db(KAVIA_ROOT_DB_NAME)
                permission_id = f"{tenant_id}_{group_name}"
                await permission_repo.create_permission(permission_id, permissions, db)
            
            return group
        except Exception as e:
            raise Exception(f"Failed to create group: {str(e)}")

    async def update_group_permissions(self, tenant_id: str, group_name: str, permissions: Dict) -> bool:
        """Update permissions for an existing group."""
        try:
            # Verify group exists
            creds = await self.get_tenant_cred(tenant_id)
            group_manager = CognitoGroupManager(creds['user_pool_id'])
            groups = group_manager.list_groups()
            if not any(g['GroupName'] == group_name for g in groups):
                raise Exception(f"Group {group_name} not found")
            
            permission_repo: TenantPermissionRepository = await get_mongo_db_v1("tenant_permissions")
            db = await get_db(KAVIA_ROOT_DB_NAME)
            permission_id = f"{tenant_id}_{group_name}"
            return await permission_repo.update_permissions(permission_id, permissions, db)
        except Exception as e:
            raise Exception(f"Failed to update group permissions: {str(e)}")

    async def delete_group(self, tenant_id: str, group_name: str) -> None:
        """Delete a group and its associated permissions."""
        try:
            # Delete from Cognito
            creds = await self.get_tenant_cred(tenant_id)
            group_manager = CognitoGroupManager(creds['user_pool_id'])
            group_manager.delete_group(group_name)
            
            # Delete permissions
            permission_repo: TenantPermissionRepository = await get_mongo_db_v1("tenant_permissions")
            db = await get_db(KAVIA_ROOT_DB_NAME)
            permission_id = f"{tenant_id}_{group_name}"
            await permission_repo.delete_permission(permission_id, db)
        except Exception as e:
            raise Exception(f"Failed to delete group: {str(e)}")

    async def list_users(self, tenant_id: str) -> List[Dict]:
        """List all users in the tenant."""
        try:
            creds = await self.get_tenant_cred(tenant_id)
            user_manager = CognitoUserManager(creds['user_pool_id'], creds['client_id'])
            return user_manager.list_users()
        except Exception as e:
            raise Exception(f"Failed to list users: {str(e)}")

    async def get_group_users(self, tenant_id: str, group_name: str) -> List[Dict]:
        """Get users in a specific group."""
        try:
            creds = await self.get_tenant_cred(tenant_id)
            user_manager = CognitoUserManager(
                user_pool_id=creds['user_pool_id'],
                client_id=creds['client_id']
            )
            return user_manager.list_users_in_group(group_name)
        except Exception as e:
            raise Exception(f"Failed to get group users: {str(e)}")

    async def add_user_to_group(self, tenant_id: str, identifier: str, group_name: str) -> None:
        """Add a user to a group."""
        try:
            creds = await self.get_tenant_cred(tenant_id)
            user_manager = CognitoUserManager(
                user_pool_id=creds['user_pool_id'],
                client_id=creds['client_id']
            )
            
            # Get user details first
            user = user_manager.get_user_by_identifier(identifier)
            
            # Add user to group
            user_manager.cognito.admin_add_user_to_group(
                UserPoolId=creds['user_pool_id'],
                Username=user['Username'],
                GroupName=group_name
            )
        except Exception as e:
            raise Exception(f"Failed to add user to group: {str(e)}")

    async def remove_user_from_group(self, tenant_id: str, identifier: str, group_name: str) -> None:
        """Remove a user from a group."""
        try:
            creds = await self.get_tenant_cred(tenant_id)
            user_manager = CognitoUserManager(
                user_pool_id=creds['user_pool_id'],
                client_id=creds['client_id']
            )
            
            # Get user details first
            user = user_manager.get_user_by_identifier(identifier)
            
            # Remove user from group
            user_manager.cognito.admin_remove_user_from_group(
                UserPoolId=creds['user_pool_id'],
                Username=user['Username'],
                GroupName=group_name
            )
        except Exception as e:
            raise Exception(f"Failed to remove user from group: {str(e)}")

    async def get_user_groups(self, tenant_id: str, identifier: str) -> List[Dict]:
        """Get groups for a user."""
        try:
            creds = await self.get_tenant_cred(tenant_id)
            user_manager = CognitoUserManager(
                user_pool_id=creds['user_pool_id'],
                client_id=creds['client_id']
            )
            
            # Get user details first
            user = user_manager.get_user_by_identifier(identifier)
            
            # Get groups for user
            response = user_manager.cognito.admin_list_groups_for_user(
                UserPoolId=creds['user_pool_id'],
                Username=user['Username']
            )
            return response['Groups']
        except Exception as e:
            raise Exception(f"Failed to get user groups: {str(e)}")

tenant_service = TenantService()