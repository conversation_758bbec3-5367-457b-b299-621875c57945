import base64
import copy
import json
import os
import logging
import re
import shlex
import threading
import asyncio
from rapidfuzz import fuzz
from code_generation_core_agent.config import config
from code_generation_core_agent.agents.tools.shared_loop import shared_loop
from code_generation_core_agent.agents.framework.cost_tracker import Cost<PERSON>racer
from code_generation_core_agent.llm.llm_interface import LLMInterface
from app.connection.establish_db_connection import get_mongo_db

_KNOWLEDGE_FORMAT_VERSION = 3
_LAST_COMPATIBLE_KNOWLEDGE_FORMAT_VERSION = 3

_STATE_NONE = 0
_STATE_INITIAL = 1
_STATE_COMPLETE = 2

_lock = threading.Lock()

class Knowledge:

    _instance = None

    def __init__(self, agent):
        self.agent = agent
        self.base_path = agent.base_path
        self.model_name = os.getenv("MODEL_NAME", config.get("LLM", "model"))
        self.llm = LLMInterface(llm_api_key=None, session_dir=agent.base_path,
                            instance_name="knowledge", mongo_handler = get_mongo_db())
        self.other_files = []
        self.source_files = []
        self.source_languages = []
        self.search_terms = []
        self.source_file_info = {}
        self.reporter = agent.reporter
        self.cost_tracer = CostTracer()
        self.logger = self._setup_logger("Knowledge", agent.base_path)
        self.keys = ["source-languages","search-terms"]
        self.key_descriptions = [
            "List of source code languages used by the codebase",
            "List of search terms relevant to the codebase. The list is not exhaustive. Any search terms can be used with the find_relevant_files tool."
        ]
        self._lock = threading.Lock()
        self._state = _STATE_NONE
        self._ingest_queue = []
        self._fastpath = False
        self._check_fast_path_available()
        self.loop_nested = None
        self.thread_nested = None
        self.loop_worker = None
        self.thread_worker = None
        return

    def start(self):
        if not self.thread_nested and not self.thread_worker:
            self.loop_nested = asyncio.new_event_loop()
            self.thread_nested = threading.Thread(target=self._run_nested_event_loop, daemon=True)
            self.thread_nested.start()
            self.thread_worker= threading.Thread(target=self._worker_loop, daemon=True)
            self.thread_worker.start()

    def _run_nested_event_loop(self):
        asyncio.set_event_loop(self.loop_nested)
        self.loop_nested.run_forever()

    def _process_file_info(self, filename, file_info, persist=None):
        self.logger.info(f"process ingested knowledge for {filename}")
        file_info['filename'] = filename
        if not persist == False:
            file_info['hash'] = self._get_file_hash(filename)
        file_info['format-version'] = _KNOWLEDGE_FORMAT_VERSION
        with self._lock:
            if 'is_source_file' in file_info:
                if file_info['is_source_file']:
                        if filename in self.other_files:
                            self.other_files.remove(filename)
                        if filename in self.source_files:
                            self.source_files.remove(filename)
                        self.source_files.append(filename)
                        if 'format' in file_info:
                            language = file_info['format']
                            if language not in self.source_languages:
                                self.source_languages.append(language)
                        search_terms = file_info['search-terms']
                        for search_term in search_terms:
                            if search_term not in self.search_terms:
                                self.search_terms.append(search_term)
                        self.source_file_info[filename] = file_info
                else:
                    if filename in self.other_files:
                        self.other_files.remove(filename)
                    if filename in self.source_files:
                        self.source_files.remove(filename)
                    self.other_files.append(filename)
                if persist == None:
                    persist = True
        if persist:
            self._persist_knowledge( file_info)
            if file_info['state'] == _STATE_INITIAL:
                self.addToIngestQueue(filename)

    def _persist_knowledge(self, file_info):
        self.logger.info(f"persist ingested knowledge for {file_info['filename']}")
        with self._lock:
            folder = os.path.join(self.base_path, '.knowledge')
            persistent_filename = os.path.join( folder, file_info['filename'].replace( '/', '_' ))
            content = json.dumps(file_info)
            self._write_file( persistent_filename, content)

    def _load_persisted_knowledge(self):
        folder_name = os.path.join(self.base_path,".knowledge")
        files = self._list_directory(folder_name)
        for file in files:
            try:
                data = self._read_file(os.path.join(folder_name,file))
                file_info = json.loads(data)
            except Exception as e:
                self.logger.error(f"_load_peristed_knowledge had exception {e} for file {file}")
                pass
            format_version = file_info['format-version']
            if format_version >= _LAST_COMPATIBLE_KNOWLEDGE_FORMAT_VERSION:
                filename = file_info['filename']
                self._process_file_info(filename, file_info, False)
        if self.source_file_info:
            self.agent.reporter.send_agent_message("Knowlege loading complete")
    
    def _service_ingest_queue(self):
        filename = None
        file_info = None
        with self._lock:
            if self._ingest_queue:
                filename = self._ingest_queue[0]
                self._ingest_queue= self._ingest_queue[1:]
            if filename in self.source_files:
                file_info = self.source_file_info[filename]
        if filename:
            self.logger.info(f"pull {filename} from ingest queue")
            if not file_info:
                file_info = {
                    "is_source_file": True,
                    "state": _STATE_INITIAL,
                    "description": "",
                    "external_files": [],
                    "external_methods": [],
                    "published": [],
                    "classes": [],
                    "methods": [],
                    "calls": [],
                    "search-terms": []
                }
            else:
                try:
                    file_info_prev = file_info
                    file_info = self.loop_worker.run_until_complete(self._ingest_file(filename))
                    if not file_info:
                        file_info = file_info_prev
                    file_info["state"] = _STATE_COMPLETE
                except Exception as e:
                    self.logger.error(f"_service_ingest_queue had exception {e} for file {filename}")
            if file_info:
                self._get_ctags(filename, file_info)
                self._process_file_info(filename, file_info)

    def _worker_loop(self):
        self.loop_worker = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop_worker)
        self._load_persisted_knowledge()
        self._state = _STATE_INITIAL
        while True:
            if not self.source_file_info:
                with self._lock:
                    self._ingest_queue = self._list_important_files(self.base_path)

            try:
                self._service_ingest_queue()
            except Exception as e:
                self.logger.error(f"Exception servicing ingest queue {e}")

            if self._state == _STATE_INITIAL:
                with self._lock:
                    if not self._ingest_queue:
                        for file in self.source_files:
                            file_info = self.source_file_info[file]
                            if file_info['state'] == _STATE_INITIAL:
                                self._ingest_queue.append(file)
                        if not self._ingest_queue:
                            self._state = _STATE_COMPLETE
                if self._state == _STATE_COMPLETE:
                    self.agent.reporter.send_agent_message("Knowledge creation complete")

            if self._state == _STATE_COMPLETE:
                self.loop_worker.run_until_complete( asyncio.sleep(10) )
        return None

    def _check_fast_path_available(self):
        import random
        import string
        characters = string.ascii_letters + string.digits
        str = ''.join(random.choice(characters) for _ in range(20))
        filename = os.path.join(self.base_path,'.fptest')
        with open(filename, 'w') as file:
            file.write(str)
        contents = self._read_file(filename)
        if contents.__eq__(str):
            self._fastpath = True
        os.remove(filename)

    def _execute_cmd(self, cmd, allow_fast = True):
        if allow_fast and self._fastpath:
            import subprocess
            try:
                result = subprocess.run(cmd, capture_output=True, shell=True, text=True)
                return result.stdout, result.returncode
            except Exception as e:
                print(f"Error: cmd {cmd} had exception {e}")
            return None, None

        timeout = config.getint("TOOLS", "shell_command_timeout")
        async def _one_shot_docker_command():
            stdout_data, returncode = await self.agent.agent_tools.executor.create_subprocess_shell_one_shot(cmd)
            return stdout_data, returncode
        try:
            future = shared_loop.run_coroutine(_one_shot_docker_command())
            output, returncode = future.result(timeout=timeout)
            return output, returncode
        except Exception as e:
            print(f"Error: cmd {cmd} had exception {e}")
        return None, None

    def _get_ctags(self, filename, file_info):
        use_ctags = False
        for suffix in [
            '.js','.jsx','.ts','.tsx','.py','.c','.cpp','.c++','.cc','.h','.hpp']:
            if filename.endswith(suffix):
                use_ctags = True
                break
        if not use_ctags:
            return
        ctags = []
        language = None
        cmd = f'ctags -R --fields=+l -f- {filename}'
        output, returncode = self._execute_cmd(cmd, allow_fast = False)
        if returncode == 0:
            datalen = 0
            for line in output.split('\n'):
                if line and not line.startswith('!'):
                    parts = line.split('\t')
                    if len(parts) >= 4:
                        tag, file_path, pattern, other, lang = parts[:5]
                        entry = f"{tag}: {pattern}"
                        if datalen + len(entry) < 30000:
                            datalen += len(entry)
                            ctags.append(entry)
                        if not language:
                            language = lang.split(':')[1]
        file_info['ctags'] = ctags
        if 'format' not in file_info:
            file_info['format'] = language

    def _read_file(self, filename):
        contents = None
        if self._fastpath:
            try:
                with open(filename, 'r') as file:
                    contents = file.read()
            except FileNotFoundError:
                pass
        else:
            cmd = f'cat {filename}'
            output, returncode = self._execute_cmd(cmd)
            if returncode == 0:
                contents = output
        return contents
    
    def _write_file(self, filename, content):
        escaped_path = shlex.quote(filename)
        encoded_new_content = base64.b64encode(content.encode('utf-8')).decode('utf-8')
        cmd = f"mkdir -p $(dirname {escaped_path}) && echo '{encoded_new_content}' | base64 -d > {escaped_path}"
        output, returncode = self._execute_cmd(cmd)
        return returncode

    def _get_file_hash(self, filename):
        hash = None
        cmd = f"md5sum {filename}"
        output, returncode = self._execute_cmd(cmd, allow_fast = False)
        if returncode == 0:
            hash = output.partition(' ')[0]
        return hash

    def _list_directory(self, directory):
        list = []
        if self._fastpath:
            try:
                list = os.listdir(directory)
            except FileNotFoundError:
                pass
            except NotADirectoryError:
                pass
        else:
            cmd = f'ls -1 {directory}'
            output, returncode = self._execute_cmd(cmd)
            if returncode == 0:
                list = output.splitlines()
        return list

    def _list_important_files(self, base_folder):
        result = ""
        filelist = []

        def list_files_in_folder(folder_path):
            files = []
            items = self._list_directory(folder_path)
            for item in items:
                item_path = os.path.join(folder_path, item)
                nest = self._list_directory(item_path + "/")
                if len(nest) > 0:
                    skip_folder = False
                    if item.startswith('.'):
                        skip_folder = True
                    if item_path.endswith('/coverage'):
                        skip_folder = True
                    if item_path.endswith('/node_modules'):
                        skip_folder = True
                    if not skip_folder:
                        files.extend(list_files_in_folder(item_path))
                else:
                    add = True
                    if item.startswith('.'):
                        add = False
                    if item_path.endswith('.log'):
                        add = False
                    if item_path.endswith('files.yaml'):
                        add = False
                    if item_path.endswith('package-lock.json'):
                        add = False
                    good_suffix = False
                    for suffix in [
                        '.js','.jsx','.ts','.tsx','.html','.css','.scss','.json','.yaml','.md',
                        '.py','.c','.cpp','.c++','.cc','.h','.hpp','.prompt', '.j2']:
                        if item_path.endswith(suffix):
                            good_suffix = True
                            break
                    if not good_suffix:
                        add = False
                    if add:
                        files.append(os.path.join(folder_path, item))
            return files

        filelist.extend(list_files_in_folder(base_folder))
        return filelist

    def _check_file(self, filename, search_terms, and_search):
        match = False
        for term in search_terms:
            cmd = f"grep -l {term} {filename}"
            output, returncode = self._execute_cmd(cmd)
            if returncode == 0  and filename in output:
                match = True
                if not and_search:
                    break
            elif and_search:
                match = False
                break
        return match

    def _execute_ingest(self, filename):
        timeout = config.getint("TOOLS", "shell_command_timeout")
        try:
            future = asyncio.run_coroutine_threadsafe( self._ingest_file(filename), self.loop_nested)
            file_info = future.result(timeout=timeout)
            return file_info
        except Exception as e:
            print(f"Error: _ingest_command had exception {e}")
        return None

    def _function_executor(self, function_name, function_args):
        result= self.agent.agent_tools.function_executor(function_name, function_args)
        if 'memories' in result:
            result.pop('memories')
        return result

    async def _ingest_file(self, filename):
        import litellm
        self.logger.info(f"start ingest for {filename}")
        file_info = None
        file_data = self._read_file(filename)
        model_name = config.get("LLM", "summarization_model")
        system_prompt = "You are an expert software engineer."
        user_prompt = "Your task is to read the supplied file which is some sort of source file " \
                      "for a software project and to report information about it.\n" \
                      "First decide if the file is actually a source file and set is_source_file to true or false. " \
                      "If it is a source file set the remaining response fields appropriately, otherwise leave them empty. " \
                      "For search-terms pick terms that distinguish the file within the project not terms that are shared by " \
                      "files in the project in general.\n" \
                      "Please format your final response as a JSON object with the following structure:\n" \
                      "{{ " \
                      "   'is_source_file': <true or false>,\n" \
                      "   'format': '<the format or language of the file>', \n" \
                      "   'description': '<description of file contents>', \n" \
                      "   'external_files': [<list of external files referenced>], \n" \
                      "   'external_methods': [<list of external methods referenced>], \n" \
                      "   'published': [<list of symbols defined in this file that are visible to other files>], \n" \
                      "   'classes': [ #list of classes defined in file, if applicable\n" \
                      "       {{ \n" \
                      "         'name': '<class name>',\n" \
                      "         'description': '<description of class>', \n" \
                      "          ...\n" \
                      "       }}, \n" \
                      "     ], " \
                      "   'methods': [ #list of methods defined in file, if applicable\n" \
                      "       {{ \n" \
                      "         'name': '<method name>', \n" \
                      "         'description': '<description of method>', \n" \
                      "          ...\n" \
                      "       }}, \n" \
                      "     ], " \
                      "   'calls': [<list of methods called made in file, if applicable, use fully qualified names if possible], \n" \
                      "   'search-terms': [<list of search keys relevant to this file>] \n" \
                      f"The fully qualified filename of the file to examine is: {filename}.\n" \
                      f"The contents of the file to examine is: ({file_data}).\n" \
                      "}}"
        try:
            response = litellm.completion(
                model=model_name,
                response_format={"type": "json_object"},
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ]
            )
            output = response.choices[0].message.content

            prompt_tokens = int(response.usage.prompt_tokens)
            completion_tokens = int(response.usage.completion_tokens)

            self.cost_tracer.add_cost_by_tokens("Knowledge", model_name, prompt_tokens, completion_tokens)

            if self.reporter.cost_update_callback:
                all_costs = self.cost_tracer.get_all_costs()
                total_cost = self.cost_tracer.get_total_cost()
                self.reporter.cost_update_callback(all_costs, total_cost)

            try:
                file_info = json.loads(output)
            except json.JSONDecodeError:
                pass
        except Exception as e:
            self.logger.error(f"_ingest_file had exception {e} processing file {filename}")
            pass

        self.logger.info(f"done ingest for {filename}")
        return file_info

    def _setup_logger(self, name, base_path, log_level=logging.INFO):
        """
        Set up a logger with a file handler, preventing logs from going to the console.

        :param name: Name of the logger and the log file
        :param base_path: Base path for the log directory
        :param log_level: Logging level (default: logging.INFO)
        :return: Configured logger
        """
        logger = logging.getLogger(name)
        logger.setLevel(log_level)

        # Remove any existing handlers (including the default StreamHandler)
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        # Prevent the logger from propagating messages to the root logger
        logger.propagate = False

        # Create logs directory if it doesn't exist
        log_dir = os.path.join(base_path, "logs")
        os.makedirs(log_dir, exist_ok=True)

        # Create file handler
        file_handler = logging.FileHandler(os.path.join(log_dir, f"{name}.log"))
        file_handler.setLevel(log_level)

        # Create formatter
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)

        # Add file handler to logger
        logger.addHandler(file_handler)
        logger.propagate = False

        return logger

    def _finish_ingest(self, file_info):
        try:
            filename = file_info['filename']
            self.logger.info(f"need to finish ingest for {filename}")
            file_info = self._execute_ingest(filename)
            file_info["state"] = _STATE_COMPLETE
            self._get_ctags(filename, file_info)
            self._process_file_info(filename, file_info)
        except Exception as e:
            self.logger.error(f"_finish_ingest for {filename} had exception {e} ")
            pass
        return file_info

    def getSourceFileList(self):
        list = []
        files = self.source_files
        for file in files:
            description = self.source_file_info[file]['description']
            list.append( {"name": f"{file}", "description": f"{description}"})
        return list

    def getSourceFileDescription(self, file_path):
        description = "No such file"
        if file_path in self.source_file_info:
            file_info = self.source_file_info[file_path]
            if file_info['state'] == _STATE_INITIAL:
                file_info = self._finish_ingest(file_info)
            description = file_info['description']
        return description

    def _is_unit_match(self, term, text):
        # Create a pattern with word boundaries around term
        pattern = r'\b' + re.escape(term) + r'\b'
        
        # Search for the pattern in text, ignoring case
        return bool(re.search(pattern, text, re.IGNORECASE))

    def _check_against_file_search_keys(self, file_info, search_term):
        match = False
        terma = search_term
        terms = file_info['search-terms']
        for termb in terms:
            similarity = fuzz.ratio(terma, termb)
            if similarity > 80:
                match = True
                break
        return match

    def _check_against_path(self, path, search_term):
        match = False
        path_of_interest = path.removeprefix(self.base_path+'/')
        path_elements = path_of_interest.split('/')
        for path_element in reversed(path_elements):
            similarity = fuzz.ratio(search_term,path_element)
            if similarity > 70:
                match = True
                break
        return match

    def _check_against_file_description(self, file_info, search_term):
        match = False
        if self._is_unit_match(search_term, file_info['description']):
            match = True
        return match

    def _check_against_method_descriptions(self, file_info, search_term):
        match = False
        for method in file_info['methods']:
            if 'description' in method:
                if self._is_unit_match(search_term, method['description']):
                    match = True
                    break
        return match

    def _check_against_ctags(self, file_info, search_term):
        match = False
        ctags = file_info['ctags']
        for tag in ctags:
            if self._is_unit_match(search_term, tag):
                match = True
                break
        return match

    def _check_against_externals(self, file_info, search_term):
        match = False
        external_files = file_info['external_files']
        for external_file in external_files:
            if self._is_unit_match(search_term,external_file):
                match = True
                break
        return match

    def _check_against_class_descriptions(self, file_info, search_term):
        match = False
        for klass in file_info['classes']:
            if 'description' in klass:
                if self._is_unit_match( search_term, klass['description']):
                    match = True
                    break
        return match

    def findRelevantFiles(self, search_terms, and_search):
        relevant_files = []
        matches_per_term = {}
        for search_term in search_terms:
            matches_per_term[search_term] = 0
        source_files = copy.deepcopy(self.source_files)
        for file in source_files:
            matchInFile = False
            file_info = self.source_file_info[file]

            if file_info['state'] == _STATE_INITIAL:
                if self._check_file(file, search_terms, and_search):
                    matchInFile = True
            else:
                for search_term in search_terms:
                    match = False
                    for method in ["terms", "path", "description", "method",
                                "ctags", "external", "classes" ]:
                        if method == "terms" and 'search-terms' in file_info and file_info['search-terms']:
                            match |= self._check_against_file_search_keys(file_info, search_term)
                        elif method == "path":
                            match |= self._check_against_path(file, search_term)
                        elif method == "description" and 'description' in file_info:
                            match |= self._check_against_file_description(file_info, search_term)
                        elif method == "method" and 'methods' in file_info:
                            match |= self._check_against_method_descriptions(file_info, search_term)
                        elif method == "ctags" and 'ctags' in file_info:
                            match |= self._check_against_ctags(file_info, search_term)
                        elif method == "external" and 'external_files' in file_info:
                            match |= self._check_against_externals(file_info, search_term)
                        elif method == "classes" and 'classes' in file_info:
                            match |= self._check_against_class_descriptions(file_info, search_term)

                        if match:
                            matches_per_term[search_term] += 1
                            break

                    matchInFile |= match

                    if and_search and not match:
                        matchInFile = False
                        break
                    if not and_search and match:
                        break

            if matchInFile:
                filename = file_info['filename']
                if file_info['state'] == _STATE_INITIAL:
                    if 'ctags' in file_info:
                        description = f"ctags: {file_info['ctags']}"
                    else:
                        description = 'Description not yet available.  Call get_source_file_knowledge to get description'
                else:
                    description = file_info['description']
                if matches_per_term[search_term] < 50:
                    relevant_files.append( {"name": f"{filename}", "description": f"{description}"})

        return relevant_files

    def getSourceFileKnowledge(self, file_path):
        knowledge = "No such file"
        if file_path in self.source_file_info:
            file_info = self.source_file_info[file_path]
            if file_info['state'] == _STATE_INITIAL:
                file_info = self._finish_ingest(file_info)
            knowledge = copy.deepcopy(file_info)
            knowledge.pop('filename')
            knowledge.pop('format-version')
            knowledge.pop('hash')
            knowledge.pop('is_source_file')
        return knowledge

    def getKeys(self):
        return { "keys": self.keys, "descriptions": self.key_descriptions }
    
    def getKeyValue(self, key):
        value = None
        with self._lock:
            if key in self.keys:
                if key == 'source-file-list':
                    value = self.getSourceFileList()
                elif key == 'source-languages':
                    value = self.source_languages
                elif key == 'search-terms':
                    value = self.search_terms
        return value

    def addToIngestQueue(self, filename):
        with self._lock:
            if filename not in self._ingest_queue:
                self.logger.info(f"adding {filename} to ingest queue")
                self._ingest_queue.append(filename)

    @staticmethod
    def getKnowledge(agent = None):
        instance = None
        with _lock:
            if not Knowledge._instance:
                if agent:
                    Knowledge._instance = Knowledge(agent)
            instance = Knowledge._instance
        return instance