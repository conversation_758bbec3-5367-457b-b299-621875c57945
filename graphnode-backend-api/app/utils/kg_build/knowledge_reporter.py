from uuid import uuid4
from app.utils.kg_build.knowledge import KnowledgeReporter
from app.core.websocket.client import WebSocketClient
import time
import asyncio
from app.utils.kg_build.knowledge import Knowledge
from app.utils.kg_inspect.answer_question import answer_question
from app.utils.auth_utils import decode_token
from app.utils.cost_utils import check_free_credit_limits_crossed

class Reporter(KnowledgeReporter):
    def __init__(self, ws_client: WebSocketClient):
        self.ready = False
        self.is_answering_question = False
        self.ws_client = ws_client
        self._reconnect_attempts = 0
        self._max_reconnect_attempts = 5
        self._reconnect_delay = 1
        self.stop_streaming = False
        pass

    def initialize(self):
        if(self._try_connect()):
            self.ws_client.add_message_handler(self._handle_message)
            self.ws_client.start_message_handler()
            print("Reporter initialized")
        else:
            print("Not conntected")
            
    def _try_connect(self):
        """Attempt to connect with retry logic"""
        while self._reconnect_attempts < self._max_reconnect_attempts:
            try:
                if self.ws_client.connect():
                    self._reconnect_attempts = 0  # Reset counter on successful connection
                    self._reconnect_delay = 1  # Reset delay
                    return True
                
            except Exception as e:
                self._reconnect_attempts += 1
                # print(f"Connection attempt {self._reconnect_attempts} failed: {e}")
                
                if self._reconnect_attempts < self._max_reconnect_attempts:
                    time.sleep(self._reconnect_delay)
                    self._reconnect_delay = min(self._reconnect_delay * 2, 30)  # Exponential backoff, max 30 seconds
                
        print("Failed to establish WebSocket connection after maximum attempts")
        return False

    async def _handle_message(self, message):
        print(f"Reporter received message: {message}")
        
        try:
            # Try getting user details by user_id
            auth_token = message.get('auth_token')
            session_id = message.get('session_id')
            if not auth_token:
                raise Exception("Auth token is required")
            
            user_details = decode_token(auth_token)

                
            print(f"User details: {user_details}")
            message = {
                **user_details,
                **message
            }
            message_uuid = str(uuid4())
            self.ws_client.send_message("input_received", {"message": message})
            if message.get('type', '') != "stop_streaming" and await check_free_credit_limits_crossed(message.get("tenant_id")):
                self.ws_client.send_message("credit_limit_crossed", {"status": 402})
            else:
                knowlege_session = Knowledge.getKnowledge(id=session_id)
                if knowlege_session:
                    self.ws_client.send_message("code_query", {"message": 'Thinking...', 'message_uuid': message_uuid})
                
                    # sample message Input
                    # {"task_id":"365cda66-3289-4aca-80a8-70d9ed9d88ac","session_id": "365cda66-3289-4aca-80a8-70d9ed9d88ac", "project_id": "3157", "user_id": "a49884f8-c0c1-708f-fa90-6082bc7ed290", "discussion_id": "5579", "input": "What is the purpose of this codebase?", "type": "user_input"}
                    command = message.get('type')
                    if command == 'user_input':
                        user_input = message.get('input')
                        if user_input:
                            print(f"Processing user input: {user_input}")
                            
                            project_id = message.get('project_id')
                            user_id = message.get('user_id')
                            discussion_id = message.get('discussion_id')
                            
                            if not self.is_answering_question:
                                print(f"Starting to answer question with params: session_id={session_id}, project_id={project_id}, user_id={user_id}")
                                self.is_answering_question = True
                                try:
                                    # Get the async generator from answer_question
                                    answer_generator = answer_question(
                                        self, 
                                        session_id, 
                                        project_id, 
                                        user_id, 
                                        user_input, 
                                        message_uuid,
                                        reporterObj=self,
                                        discussion_id=discussion_id
                                    )
                                    
                                    # Iterate through the generator to get each chunk of the response
                                    async for chunk in answer_generator:
                                        # Send each chunk through websocket
                                        print(chunk)
                                        # self.ws_client.send_message("code_query", {"message": chunk})
                                        
                                finally:
                                    self.is_answering_question = False
                            else:
                                print("Already processing another question")
                                self.ws_client.send_message("code_query", {"message": "Already processing another question"})
                        else:
                            print("Received empty user input")
                    elif command=="stop_streaming":
                        self.stop_streaming=True
                    else:
                        print(f"Received unknown command type: {command}")
                else:
                    print(await self.session_manager.get_session(session_id))
                    self.ws_client.send_message("code_query", {"message": 'Your code query session has expired. Please close this window and start a new session to continue.', 'message_uuid': message_uuid})
                    self.ws_client.send_message("disconnected", True)
                
        except Exception as e:
            self.ws_client.send_message("error", {"message": str(e)})
            print(f"Error in _handle_message: {str(e)}")
            import traceback
            print(traceback.format_exc()) 

    def send_agent_message(self, message):
        print("Reporter message: ", message)
        if 'Knowledge creation complete' in message:
            self.ready = True
        pass


    def cost_update_callback(self, all_costs, total_cost ):
        pass

    def is_ready(self):
        return self.ready
    
    # streaming response to client
    def send_message(self, message_type, data):
        self.ws_client.send_message(message_type, data)

    def disconnect(self):
        self.ws_client.disconnect()