
from uuid import uuid4
from app.knowledge.redis_kg import getRedisKnowledge
from app.models.code_generation_model import Chat<PERSON>ontext
from app.utils.kg_inspect.knowledge import KnowledgeReporter
from app.core.websocket.client import WebSocketClient
import time
from app.utils.auth_utils import decode_token
from app.utils.cost_utils import check_free_credit_limits_crossed
from app.utils.conversation_utils import ConversationUtils
from app.connection.establish_db_connection import get_mongo_db, get_node_db
from app.conversation.conversation_finder import ConversationFinder
from app.utils.logs_utils import get_path
from llm_wrapper.utils.base_tools import DynamicToolFactory, ToolRegistry
from llm_wrapper.core.llm_interface import LLMInterface
from app.utils.kg_inspect.kg_tool import KgTools
from app.utils.kg_inspect.knowledge_cli import get_model_name
from app.utils.datetime_utils import generate_timestamp
from app.models.user_model import LLMModel
from code_generation_core_agent.agents.utilities import ModelNameSelector
import json
import litellm
import asyncio
from uuid import uuid4
from app.connection.tenant_middleware import  tenant_context
import boto3
from botocore.exceptions import ClientError
import os

from app.utils.message_store import save_messages_to_file
from app.utils.validation_utils import validate_and_fix_mermaid_in_response


def get_vertex_secret():

        secret_name = "preprod/vertext_ai/service_account"
        region_name = "us-east-1"
        
        aws_access_key_id = os.getenv("CODEGEN_AWS_ACCESS_KEY_ID", os.getenv("AWS_ACCESS_KEY_ID"))
        aws_secret_access_key = os.getenv("CODEGEN_AWS_SECRET_ACCESS_KEY", os.getenv("AWS_SECRET_ACCESS_KEY"))
        session = boto3.session.Session(
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key
        )

        # Create a Secrets Manager client
        session = boto3.session.Session()
        client = session.client(
            service_name='secretsmanager',
            region_name=region_name
        )

        try:
            get_secret_value_response = client.get_secret_value(
                SecretId=secret_name
            )
        except ClientError as e:
            # For a list of exceptions thrown, see
            # https://docs.aws.amazon.com/secretsmanager/latest/apireference/API_GetSecretValue.html
            raise e

        secret = get_secret_value_response['SecretString']
        print("AWS SECRETS : ", secret)
        return secret

def answer_question_sync(reporter, session_id, project_id, user_id, question, discussion_id=None):
    try:
        print("Starting answer_question_sync")
        asyncio.run(answer_question(reporter, session_id, project_id, user_id, question, discussion_id))
    except Exception as e:
        print(f"Exception {e}")
        
async def answer_question(reporter, session_id, project_id, user_id, question, message_uuid, actual_username, llm_model, user_input, file_attachments=None, discussion_id=None):
    try:
        print(f"Starting answer_question - {llm_model} ")
        log_dir = f"logs"
        os.makedirs(log_dir, exist_ok=True)
        reporter.is_answering_question = True
        
        context = ChatContext(
            project_id=project_id,
            user_id=user_id,
            message=question,
            discussion_id=discussion_id,
            agent_name='code_query'
        )
        
        _utils = ConversationUtils(context)
        _chat = ConversationFinder(context)
        node_db = get_node_db()
        user_prompt = question
        
        system_prompt = '''You are an expert software engineer and technical writer

        1. Write in clear and proper markdown format, flowing prose paragraphs rather than bullet points and use the correct numbers for sections and give a tab space before the sublist item.
        2. Provide detailed explanations of how components work and interact
        3. Use technical language while maintaining readability
        4. Explain the reasoning and context behind technical decisions
        5. Focus on system relationships and interactions rather than isolated features
        6. Include diagrams wherever appropriate. Use mermaid charts for diagrams and Title of the charts, unless other mechanisms were requested by the user.
        7. Follow official Mermaid grammar for the chosen diagram type only.
            •   Never combine different diagram syntaxes in one block.
            •	No HTML (<br>, <div>, etc.). For line breaks inside labels use the literal string \n.
            •	No Markdown formatting (bold, italic, etc.) inside the diagram text.
            •	Keep all brackets [], braces {}, and parentheses (), plus quotes " " / ' ', perfectly balanced.
            •	Use plain ASCII; avoid characters that are not part of Mermaid syntax.
            
        **Use of Tools:**

        - Use the provided tools to gather detailed information about the project codebase and ensure accuracy in your documentation.
        - Use the KnowledgeTools to understand the codebase and the architecture.
        - Use the KnowledgeTools tools to get information about the project codebase, and to determine what files in the project are relevant to your task.  
        - Use the KnowledgeTools tools to get file knowledge about relevant files in the project.
        - If the information you gather suggests there may be additional sources of information then do additional find_relevant_files searches and reads so your decisions are based on comprehensive information.  
        - If find_relevant_files results include an entry stating that the results are abridged, consider using separate searches with fewer search terms in each, or enabling and_search.
        - Before executing the read_file search the file in find_relevant_files bases on user input. Always use the base path for while reading the file.
        
        Note:
        
        These are the available keys -> ['source-languages', 'search-terms']
        Always use tool calls to get the available knowledge.
        '''

        docs_prompt = '''
        In necessary place always generate a single document in proper markdown format without any preliminary statements:

        1. Write in continuous prose paragraphs. Avoid bullet points, lists, or abbreviated descriptions.
        2. Organize the document into sections and provide suitable headers.
        3. Each section should flow naturally into the next, with clear transitions.
        4. Fully develop ideas and concepts, explaining not just what exists but how it works and why it matters.
        5. Use a professional technical tone while maintaining clarity and readability.
        6. Include relevant technical details within the narrative rather than as separate lists.
        7. When adding a sublist under any parent item (ordered list, unordered list, or any other case), give a tab space before the sublist item.
        8. Ensure proper markdown format for the entire document.
        9. Ensure document title should be short and in bigger size font like h1/h2.
        10. When including mermaid diagrams, ensure they follow proper syntax and are properly formatted.
        Format the document as:

        -------------------
        \nDocument:

        [Title on its own line]

        [Your comprehensive documentation in flowing prose paragraphs with proper line breaks between sections]
        -------------------

        Remember: Use \nDocument: for document always, No bullet points, no abbreviated lists, and no shallow descriptions. Every feature or component should be thoroughly explained in proper paragraph form. Ensure proper line breaks between the title, sections, and paragraphs.'''

        
                    
        if discussion_id:
            messages = await _utils._load_discussion(int(context.discussion_id), system=True)      
            
        else:
            messages = []
            discussion_id = await _chat._create_conversation('code_query', context.user_id ,actual_username, session_id, )
            print("DISCUSSION ID : ", discussion_id)
            # Generate message UUID for user's question
            messages.append({
                "role": "system",
                "content": system_prompt + docs_prompt
            })
            
        messages.append({
                "role": "user",
                "content": user_prompt,
                "timestamp": generate_timestamp()
            })
        
        
        
        try:
            
            if 'vertex' in llm_model.lower():  
                print('Vertex AI LLM Interface')
                llm = LLMInterface(str(get_path()), f'knowledge_{discussion_id}', user_id, int(project_id), 'code_query', mongo_handler=get_mongo_db(), vertex_secret=get_vertex_secret(),session_id = session_id)  # Removed await
            else:
                llm = LLMInterface(str(get_path()), f'knowledge_{discussion_id}', user_id, int(project_id), 'code_query', mongo_handler=get_mongo_db(),session_id = session_id)
            
            litellm.drop_params=True

            model_name = llm_model
                    
            general_registry = ToolRegistry()
            general_registry.register_tool("KgTools", KgTools)
            
            general_factory = DynamicToolFactory(general_registry)
            general_exec_agent = general_factory.create_dynamic_tool(["KgTools"])
            kg_tool = general_exec_agent('/test', logger=None, user_id=session_id)

            # cp = messages.copy()
            
            # print(messages)
        
            print("Getting into LLM wrapper with model", model_name)
            
            completion = await llm.llm_interaction_wrapper(
                messages=messages,
                user_prompt=None,
                system_prompt=None,
                model=model_name,
                stream=True,
                response_format={"type": "text"},
                function_schemas=kg_tool.function_schemas,
                function_executor=kg_tool.function_executor
            )   
            
            
            print(f"DEBUGING COMPLETION THINGS: {completion}")
            
            llm_response = ""
            STREAM_DELAY = 0.01
                        
            async for res in completion:
                if reporter.stop_streaming:
                    print("stop streaming requested, breaking the async generator")
                    break

                if "Checking the data" in res:
                    #yield f"data: {json.dumps({'content': 'Your code-related insights will be available shortly...', 'message_uuid': message_uuid})}\n\n"
                    await asyncio.sleep(STREAM_DELAY)
                elif "Reading File:" in res:
                    reporter.send_message("code_query", {
                        "message": res,
                        "message_uuid": message_uuid
                    })
                    #Commenting out the yields as data is directly reported from the reporter and yield is blocking the websocket message handler
                    #yield f"data: {json.dumps({'content': res, 'message_uuid': message_uuid})}\n\n"
                    await asyncio.sleep(STREAM_DELAY)
                else:
                    if reporter.stop_streaming:
                        print("stop streaming")
                        break
                    else:
                        llm_response += res
                        reporter.send_message("code_query", {
                            "message": llm_response,
                            "discussion_id": discussion_id,
                            "message_uuid": message_uuid,
                            "status": "streaming"    
                        })
                        #yield f"data: {json.dumps({'content': llm_response, 'discussion_id': discussion_id, 'message_uuid': message_uuid})}\n\n"
                        await asyncio.sleep(STREAM_DELAY)
            
            def add_document_end_marker(message):
                if '-------------------\nDocument:' in message and not message.endswith('-------------------'):
                    return message + '\n\n-------------------'
                return message

            if 'mermaid' in llm_response:
                llm_response = await validate_and_fix_mermaid_in_response(llm_response)
            
            reporter.send_message("code_query", {
                "message_end": True,
                "message": add_document_end_marker(llm_response),
                "message_uuid": message_uuid,
                "status": "message_end"
            })

            #Remove last message and add a message with user_input and file_attachments separately
            if(messages[-1]['role'] == "user"):
                timestamp = messages[-1]['timestamp']
                messages.pop()
                messages.append({
                    'role': 'user',
                    'content': user_input,
                    'timestamp': timestamp,
                })
                if file_attachments:
                    messages[-1]['file_attachments'] = file_attachments
            
            # Generate message UUID for assistant's response
            assistant_message_uuid = str(uuid4())
            messages.append({
                "role": "assistant",
                "content": llm_response,
                "timestamp": generate_timestamp(),
            })
            
            print("final_messages:", messages)
            await node_db.update_node_by_id(int(discussion_id), {'Discussion': json.dumps(messages)})            
            
            # file_path = save_messages_to_file(int(discussion_id), messages)
            # if file_path:
            #     print(f"Messages saved to temporary file: {file_path}")
            # else:
            #     print("Failed to save messages to file")
    
        except Exception as e:
            print(f"Exception {e}")
            reporter.send_message("code_query", {
                        "message": e,
                        "message_uuid": message_uuid
                    })
    except Exception as e:
        print(f"Exception {e}")
        reporter.send_message("code_query", {
                        "message": e,
                        "message_uuid": message_uuid
                    })
    finally:
        reporter.is_answering_question = False

class Reporter(KnowledgeReporter):
    def __init__(self, ws_client: WebSocketClient):
        self.ready = False
        self.is_answering_question = False
        self.ws_client = ws_client
        self._reconnect_attempts = 0
        self._max_reconnect_attempts = 5
        self._reconnect_delay = 1
        self.model_name_selector = ModelNameSelector("gpt-4.1")
        self.selected_model =  self.model_name_selector.get_selected_model()
        self.available_models = self.model_name_selector._available_models
        self.stop_streaming = False

    def initialize(self):
        if(self._try_connect()):
            self.ws_client.add_message_handler(self._handle_message)
            self.ws_client.start_message_handler()
            print("Reporter initialized")
        else:
            print("Not conntected")
            
    def _try_connect(self):
        """Attempt to connect with retry logic"""
        while self._reconnect_attempts < self._max_reconnect_attempts:
            try:
                if self.ws_client.connect():
                    self._reconnect_attempts = 0  # Reset counter on successful connection
                    self._reconnect_delay = 1  # Reset delay
                    return True
                
            except Exception as e:
                self._reconnect_attempts += 1
                print(f"Connection attempt {self._reconnect_attempts} failed: {e}")
                
                if self._reconnect_attempts < self._max_reconnect_attempts:
                    time.sleep(self._reconnect_delay)
                    self._reconnect_delay = min(self._reconnect_delay * 2, 30)  # Exponential backoff, max 30 seconds
                
        print("Failed to establish WebSocket connection after maximum attempts")
        return False
    
    async def _handle_message(self, message):
        print(f"Reporter received message: {message}")
        message_uuid = str(uuid4())
        tenant_id = tenant_context.get('tenant_id')
        if message.get('type', '') != "stop_streaming" and await check_free_credit_limits_crossed(tenant_id, message.get('user_id')):
            self.ws_client.send_message("credits_limit_crossed", {"status": 402})
        else:
            try:
                # Try getting user details by user_id
                if message.get("type") == "ready_check":
                    self.ws_client.send_message("ready_check", {"status": self.is_ready()})
                    return
                
                if message.get("type") == "stop_streaming":
                    self.stop_streaming = True
                    self.ws_client.send_message("stop_stream_alert", {'detail': "Streaming has been stopped"})
                elif message.get("type","") == "user_input":
                    self.stop_streaming = False #remove any stop_streaming value set before
                
                if message.get("type") == "set_model":
                    model_name = message.get("model_name")
                    self.selected_model = self.available_models[message.get("model_name")]
                    self.ws_client.send_message("selected_model", {"model": model_name, "model_change": True})
                    return

                auth_token = message.get('auth_token')
                session_id = message.get('session_id')
                if not auth_token:
                    raise Exception("Auth token is required")
                
                user_details = decode_token(auth_token)

                    
                print(f"User details: {user_details}")
                actual_username = user_details['custom:Name']
                message = {
                    **user_details,
                    **message
                }
                self.ws_client.send_message("input_received", {"message": message})
                build_ids = session_id.split('-')[1:]
            
                if not message.get('type','') == "stop_streaming":
                    knowlege_session = getRedisKnowledge(id=build_ids)
                    if knowlege_session:
                        self.ws_client.send_message("code_query", {"message": 'Thinking...', 'message_uuid': message_uuid})
                    
                        # sample message Input
                        # {"task_id":"365cda66-3289-4aca-80a8-70d9ed9d88ac","session_id": "365cda66-3289-4aca-80a8-70d9ed9d88ac", "project_id": "3157", "user_id": "a49884f8-c0c1-708f-fa90-6082bc7ed290", "discussion_id": "5579", "input": "What is the purpose of this codebase?", "type": "user_input"}
                        command = message.get('type')
                        if command == 'user_input':
                            user_input = message.get('input')
                            full_user_prompt = {
                                "content": user_input
                            }
                            file_attachments = message.get('file_attachments')

                            if file_attachments:
                                new_message_content = []
                                new_message_content.append({"type": "text", "text": user_input})
                                has_image_urls = False

                                for file in file_attachments:
                                    if file.get("file_type").startswith('image/'):
                                        has_image_urls = True
                                        new_message_content.append({
                                            "type": "image_url",
                                            "image_url": {
                                                "url": file.get("original_url"),
                                            }
                                        })
                                    else:
                                        new_message_content[0]["text"] += f"\n\nFile: {file.get('file_name')}\nType: {file.get('file_type')}\nContents:\n{file.get('extracted_content')}\n\n"
                                
                                if has_image_urls:       
                                    full_user_prompt['content'] = new_message_content
                                else:
                                    full_user_prompt['content'] = new_message_content[0]["text"]
                            
                            if user_input:
                                print(f"Processing user input: {user_input}")
                                print("Actual Username ; ", actual_username)
                                
                                project_id = message.get('project_id')
                                user_id = message.get('user_id')
                                discussion_id = message.get('discussion_id')

                                
                                
                                if not self.is_answering_question:
                                    print(f"Starting to answer question with params: session_id={session_id}, project_id={project_id}, user_id={user_id}")
                                    self.is_answering_question = True
                                    try:
                                        # Get the async generator from answer_question
                                        asyncio.create_task(answer_question(
                                            self, 
                                            session_id, 
                                            project_id, 
                                            user_id, 
                                            full_user_prompt['content'], 
                                            message_uuid,
                                            actual_username,
                                            self.selected_model,
                                            user_input,
                                            file_attachments,
                                            discussion_id
                                        ))
                                            
                                    finally:
                                        self.is_answering_question = False
                                else:
                                    print("Already processing another question")
                                    self.ws_client.send_message("code_query", {"message": "Already processing another question"})
                            else:
                                print("Received empty user input")
                        else:
                            print(f"Received unknown command type: {command}")
                    else:
                        # print(await self.session_manager.get_session(session_id))
                        self.ws_client.send_message("code_query", {"message": 'Your code query session has expired. Please close this window and start a new session to continue.', 'message_uuid': message_uuid})
                        self.ws_client.send_message("disconnected", True)
                    
            except Exception as e:
                self.ws_client.send_message("error", {"message": str(e)})
                print(f"Error in _handle_message: {str(e)}")
                import traceback
                print(traceback.format_exc()) 

    def send_agent_message(self, message):
        print("Reporter message: ", message)
        if 'Knowledge creation complete' in message:
            self.ready = True
        pass


    def cost_update_callback(self, all_costs, total_cost ):
        pass

    def is_ready(self):
        return self.ready
    
    # streaming response to client
    def send_message(self, message_type, data):
        self.ws_client.send_message(message_type, data)

    def disconnect(self):
        self.ws_client.disconnect()
