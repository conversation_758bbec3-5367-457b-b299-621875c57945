import copy
from datetime import datetime
import os
from llm_wrapper.utils.base_tools import BaseTool
from app.knowledge.redis_kg import getRedisKnowledge
from app.utils.kg_inspect.knowledge import Knowledge
from app.utils.kg_inspect.debug_knowledge import debug_redis_knowledge, debug_session_knowledge
from app.utils.kg_inspect.knowledge_embeddings import KnowledgeEmbeddings
import re
from fuzzywuzzy import fuzz
class KgTools(BaseTool):
        
    def __init__(self, base_path, logger, llm=None, user_id=None, discussion=None):
        super().__init__(logger)
        self.session_id = user_id
        self.function_schemas = [

    {
        "type": "function",
        "function": {
            "name": "get_keys",
            "description": "Get the list of available knowledge keys. This function is useful for understanding the available knowledge keys that the knowledge base has.",
            "parameters": {
                "type": "object",
                "strict": True,
                "properties": {
                }, 
                "required": []
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_key_value",
            "description": "Get the value associated with a specified knowledge key. Example: get_key_value(key='source-languages')",
            "parameters": {
                "type": "object",
                "strict": True,
                "properties": {
                    "key": {"type": "string", "description": "The knowledge key to being queried."},
                },
                "required": ["key"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "find_relevant_files",
            "description": "Find relevant files pertaining to the specified search terms. Before writing or editing any file in the codebase, it is recommended to use this function to find the most relevant files to edit. Example: find_relevant_files(search_terms=['authentication', 'login'], and_search=True, code_base='backend')",
            "parameters": {
                "type": "object",
                "strict": True,
                "properties": {
                    "search_terms": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "List of search terms.",
                    },
                    "and_search": {
                        "type": "boolean",
                        "description": "True for AND search, False for OR search",
                    },
                    "code_base": {
                        "type": "string",
                        "description": "Search only within this named codebase"
                    }
                },
                "required": ["search_terms", "and_search"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_source_file_description",
            "description": "Get source file description. This function is useful for understanding the purpose of a source file. Example: get_source_file_description(file_path='/src/utils/helpers.js')",
            "parameters": {
                "type": "object",
                "strict": True,
                "properties": {
                    "file_path": {"type": "string", "description": "The fully qualified path to the file."},
                },
                "required": ["file_path"]
            }
        }
    },
    {
        "type": "function", 
        "function": {
            "name": "get_source_files_knowledge",
            "description": "Get knowledge about multiple source files. This function retrieves knowledge for a list of source files. Example: get_source_files_knowledge(file_paths=['/src/models/user.py', '/src/controllers/auth.py'])"
            " This function will return the following information for each file: \n"
            " {{ "
            "   'is_source_file': <true or false>,\n"
            "   'format': '<the format or language of the file>', \n"
            "   'description': '<description of file contents>', \n"
            "   'external_files': [<list of external files referenced>], \n"
            "   'external_methods': [<list of external methods referenced>], \n"
            "   'published': [<list of symbols defined in this file that are visible to other files>], \n"
            "   'classes': [ #list of classes defined in file, if applicable\n"
            "       {{ \n"
            "         'name': '<class name>',\n"
            "         'description': '<description of class>', \n"
            "          ...\n"
            "       }}, \n"
            "     ], " 
            "   'methods': [ #list of methods defined in file, if applicable\n"
            "       {{ \n"
            "         'name': '<method name>', \n"
            "         'description': '<description of method>', \n"
            "          ...\n"
            "       }}, \n"
            "     ], " 
            "   'calls': [<list of methods called in file>'], \n"
            " }}\n",
            "parameters": {
                "type": "object",
                "strict": True,
                "properties": {
                    "file_paths": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "A list of fully qualified paths to the source files."
                    }
                },
                "required": ["file_paths"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "read_file",
            "description": "Reads the contents of a file using the base_path. Example: read_file(file_path='/path/to/your/file.txt')",
            "parameters": {
                "type": "object",
                "strict": True,
                "properties": {
                    "file_path": {"type": "string", "description": "The fully qualified path to the file."},
                },
                "required": ["file_path"]
            }
        },
    }         
]
        
        self.function_mapping.update({
            "get_keys": self.get_keys,
            "get_key_value": self.get_key_value,
            "find_relevant_files": self.find_relevant_files,
            "get_source_file_description": self.get_source_file_description,
            "get_source_files_knowledge": self.get_source_files_knowledge,
            "read_file": self.read_file,
        })
        
       
        build_ids = self.session_id.split('-')[1:]
        print("Session_id :  ", self.session_id)
        
        print("PASSING TO GET REDIS KNOWLEDGE", build_ids)
        
        if build_ids:
            self.knowledge = getRedisKnowledge(id=build_ids,verbose=True)
            # debug_redis_knowledge(self.knowledge)
            # knowledge = Knowledge.getKnowledge(id=self.session_id)
            # debug_session_knowledge(knowledge)
        else:
            # Handle error case where no build IDs are found
            logger.error(f"No build IDs found in session ID: {self.session_id}")
            self.knowledge = None
            
        repo_paths = {}
    
        if self.knowledge and 'source_code_bases' in self.knowledge and 'source_file_info' in self.knowledge:
            for code_base in self.knowledge['source_code_bases']:
                # Extract org/repo from URL
                url_parts = code_base.split('/')
                if len(url_parts) >= 5:
                    org = url_parts[3]
                    repo_with_branch = url_parts[4]
                    
                    # Handle branch part and .git extension
                    if ':' in repo_with_branch:
                        repo_part, branch = repo_with_branch.split(':')
                    else:
                        repo_part = repo_with_branch
                        branch = 'master'
                    
                    if repo_part.endswith('.git'):
                        repo_part = repo_part[:-4]
                    
                    # Dynamically find the base path by examining file paths
                    org_repo_pattern = f"{org}/{repo_part}"
                    
                    # Find first file matching this repository
                    for file_path, file_info in self.knowledge['source_file_info'].items():
                        if 'code-base-name' in file_info and file_info['code-base-name'] == code_base:
                            # Extract the base path by finding the org/repo pattern in the path
                            path_parts = file_path.split('/')
                            base_path = ""
                            
                            # Find where org/repo pattern starts in the path
                            for i in range(len(path_parts) - 1):
                                if path_parts[i] == org and path_parts[i+1] == repo_part:
                                    # Found org/repo pattern, construct base path up to repo
                                    base_path = '/'.join(path_parts[:i+2])
                                    break
                            
                            # If we couldn't find org/repo exactly, look for partial matches
                            if not base_path:
                                for i in range(len(path_parts)):
                                    if org in path_parts[i] or repo_part in path_parts[i]:
                                        # Go up until we find a common ancestor path
                                        # that contains both org and repo in some form
                                        ancestor_path = '/'.join(path_parts[:i+1])
                                        if org in ancestor_path and repo_part in ancestor_path:
                                            base_path = ancestor_path
                                            break
                            
                            if base_path:
                                print(f"Found base path for {code_base}: {base_path}")
                                lock_file = os.path.join(base_path, '.knowledge', '.vector_db', '.milvus.db.lock')
    
                                # Check if lock file exists and remove it
                                if os.path.exists(lock_file):
                                    try:
                                        os.remove(lock_file)
                                        print(f"Successfully removed {lock_file}")
                                    except Exception as e:
                                        print(f"Error removing lock file: {e}")
                                
                                repo_paths[code_base] = base_path
                                break
                    
                    # If we couldn't find the base path, use a default path format
                    if code_base not in repo_paths:
                        # Try to construct from file paths directly
                        for file_path in self.knowledge['source_file_info']:
                            # Extract common prefix that would represent the repo root
                            path_segments = file_path.split('/')
                            # Look for rdkcentral/rialto-gstreamer pattern
                            for i in range(len(path_segments) - 1):
                                if path_segments[i] == org or path_segments[i+1] == repo_part:
                                    base_path = '/'.join(path_segments[:i+2])
                                    repo_paths[code_base] = base_path
                                    
                                    lock_file = os.path.join(base_path, '.knowledge', '.vector_db', '.milvus.db.lock')
    
                                    # Check if lock file exists and remove it
                                    if os.path.exists(lock_file):
                                        try:
                                            os.remove(lock_file)
                                            print(f"Successfully removed {lock_file}")
                                        except Exception as e:
                                            print(f"Error removing lock file: {e}")
                                        
                                    print(f"Constructed base path for {code_base}: {base_path}")
                                    break
                            if code_base in repo_paths:
                                break
                        
                        # If still not found, use the first file's directory as a fallback
                        if code_base not in repo_paths and self.knowledge['source_file_info']:
                            first_file = next(iter(self.knowledge['source_file_info']))
                            last_dir_index = first_file.rfind('/')
                            if last_dir_index > 0:
                                base_path = first_file[:last_dir_index]
                                
                                lock_file = os.path.join(base_path, '.knowledge', '.vector_db', '.milvus.db.lock')
    
                                # Check if lock file exists and remove it
                                if os.path.exists(lock_file):
                                    try:
                                        os.remove(lock_file)
                                        print(f"Successfully removed {lock_file}")
                                    except Exception as e:
                                        print(f"Error removing lock file: {e}")
                                        
                                repo_paths[code_base] = base_path
                                print(f"Fallback base path for {code_base}: {base_path}")
            
            print("DEBUG EMBEDDING IN KG TOOL", repo_paths)
            
            
            # Initialize embeddings with repository paths
            self.embeddings = KnowledgeEmbeddings.get_instance(
                self.session_id,
                repo_paths
            )
            
    def get_keys(self,):
        keys = self.knowledge.get('metadata', {}).get('keys', self.knowledge.get('keys', []))
        key_descriptions = self.knowledge.get('metadata', {}).get('key_descriptions', self.knowledge.get('key_descriptions', []))
        
        result = {
            "status": "SUCCESS",
            "keys": {"keys": keys, "descriptions": key_descriptions}
        }
        return result

    def get_key_value(self, key):
        
        knowledge = self.knowledge
        status = "ERROR"
        value = None
        
        # Implement the key lookup logic
        if key == 'source-file-list':
            value = []
            for file in knowledge.get('source_files', []):
                description = knowledge.get('source_file_info', {}).get(file, {}).get('description', "No description")
                value.append({"name": file, "description": description})
            status = "SUCCESS" if value else "ERROR"
        elif key == 'source-languages':
            value = knowledge.get('source_languages', [])
            status = "SUCCESS" if value is not None else "ERROR"
        elif key == 'search-terms':
            value = knowledge.get('search_terms', [])
            status = "SUCCESS" if value is not None else "ERROR"
        elif key == 'code-bases':
            value = knowledge.get('source_code_bases', [])
            status = "SUCCESS" if value is not None else "ERROR"
        elif key.startswith('source-languages-'):
            code_base_name = key[len('source-languages-'):]
            # Find languages for the specific code base
            for file in knowledge.get('source_files', []):
                file_info = knowledge.get('source_file_info', {}).get(file, {})
                if file_info.get('code-base-name') == code_base_name:
                    if 'format' in file_info and file_info['format'] not in value:
                        if value is None:
                            value = []
                        value.append(file_info['format'])
            status = "SUCCESS" if value is not None else "ERROR"
        elif key.startswith('search-terms-'):
            code_base_name = key[len('search-terms-'):]
            # Find search terms for the specific code base
            value = []
            for file in knowledge.get('source_files', []):
                file_info = knowledge.get('source_file_info', {}).get(file, {})
                if file_info.get('code-base-name') == code_base_name:
                    for term in file_info.get('search-terms', []):
                        if term not in value:
                            value.append(term)
            status = "SUCCESS" if value else "ERROR"
        
        result = {
            "status": status,
            "value": value
        }
        return result

    def find_relevant_files(self, search_terms, and_search, code_base=None):
        """
        Find relevant files matching the search terms with support for both keyword and semantic search.
        
        Args:
            search_terms: List of search terms to find
            and_search: Boolean indicating whether all terms must match (True) or any term (False)
            code_base: Optional name of codebase to limit search to
            
        Returns:
            Dictionary with search results and detailed debug information
        """
        # Initialize variables
        relevant_files = []
        matches_high = []
        matches_medium = []
        matches_low = []
        matches_by_weight = {"high": matches_high, "medium": matches_medium, "low": matches_low}
        matches_per_term = {term: 0 for term in search_terms}
        results_abridged = False
        debug_info = {
            "search_terms": search_terms,
            "and_search": and_search,
            "code_base_filter": code_base,
            "total_files_available": 0,
            "files_after_codebase_filter": 0,
            "files_processed": 0,
            "files_matched": 0,
            "semantic_matches_count": 0,
            "available_codebases": set(),
            "file_states": {"processed": 0, "unprocessed": 0},
            "search_methods_used": [],
            "errors": []
        }
        
        # Validate knowledge
        if not self.knowledge:
            error_msg = "No knowledge base available"
            self.logger.error(error_msg)
            return {
                "status": "ERROR",
                "value": [],
                "error": error_msg,
                "debug_info": debug_info
            }
        
        # Log search parameters
        self.logger.info(f"Starting search - Terms: {search_terms}, AND search: {and_search}, Code base: {code_base}")
        
        # Get source files
        source_files = self.knowledge.get('source_files', [])
        source_file_info = self.knowledge.get('source_file_info', {})
        debug_info["total_files_available"] = len(source_files)
        
        if not source_files:
            error_msg = "No source files available in knowledge base"
            self.logger.error(error_msg)
            debug_info["errors"].append(error_msg)
            return {
                "status": "ERROR",
                "value": [],
                "error": error_msg,
                "debug_info": debug_info
            }
        
        self.logger.info(f"Total source files available: {len(source_files)}")
        
        # Collect available codebases for debugging
        for file_path in source_files:
            file_info = source_file_info.get(file_path, {})
            if 'code-base-name' in file_info:
                debug_info["available_codebases"].add(file_info['code-base-name'])
            
            # Track file states
            state = file_info.get('state', 0)
            if state == 1:
                debug_info["file_states"]["unprocessed"] += 1
            else:
                debug_info["file_states"]["processed"] += 1
        
        debug_info["available_codebases"] = list(debug_info["available_codebases"])
        
        # Filter by codebase if specified
        if code_base:
            original_count = len(source_files)
            source_files = [f for f in source_files if source_file_info.get(f, {}).get('code-base-name') == code_base]
            debug_info["files_after_codebase_filter"] = len(source_files)
            self.logger.info(f"After codebase filter '{code_base}': {len(source_files)} files (from {original_count})")
            
            if not source_files:
                error_msg = f"No files found for codebase '{code_base}'. Available codebases: {debug_info['available_codebases']}"
                self.logger.error(error_msg)
                debug_info["errors"].append(error_msg)
                return {
                    "status": "ERROR",
                    "value": [],
                    "error": error_msg,
                    "debug_info": debug_info
                }
        else:
            debug_info["files_after_codebase_filter"] = len(source_files)
        
        # Get semantic search results if embeddings are available
        semantic_matches = {}
        if self.embeddings:
            try:
                similar_files = self.embeddings.search_similar_files(code_base, search_terms)
                for result in similar_files:
                    semantic_matches[result['file_path']] = True
                debug_info["semantic_matches_count"] = len(semantic_matches)
                debug_info["search_methods_used"].append("semantic")
                self.logger.info(f"Semantic search found {len(semantic_matches)} matches")
            except Exception as e:
                error_msg = f"Semantic search failed: {str(e)}"
                self.logger.warning(error_msg)
                debug_info["errors"].append(error_msg)
        else:
            self.logger.info("No embeddings available for semantic search")
        
        # Process each file
        for file_path in source_files:
            debug_info["files_processed"] += 1
            file_matched = False
            file_info = source_file_info.get(file_path, {})
            
            # Determine initial weight based on semantic search results
            weight = "low"
            if file_path in semantic_matches:
                weight = "medium"  # Boost weight for semantic matches
            
            # Handle unprocessed files (state == 1)
            if file_info.get('state') == 1:
                if hasattr(self, '_check_file') and self.knowledge._check_file(file_path, search_terms, and_search):
                    file_matched = True
                    debug_info["search_methods_used"].append("check_file")
            else:
                # Process with multiple search methods
                term_matches = {term: False for term in search_terms}
                
                for search_term in search_terms:
                    term_matched = False
                    
                    # Method 1: Description search
                    if 'description' in file_info and file_info['description']:
                        if self._check_against_file_description(file_info, search_term):
                            term_matched = True
                            weight = "high"
                            if "description" not in debug_info["search_methods_used"]:
                                debug_info["search_methods_used"].append("description")
                    
                    # Method 2: Search terms
                    if not term_matched and 'search-terms' in file_info and file_info['search-terms']:
                        if self._check_against_file_search_keys(file_info, search_term):
                            term_matched = True
                            weight = max(weight, "medium")
                            if "search_terms" not in debug_info["search_methods_used"]:
                                debug_info["search_methods_used"].append("search_terms")
                    
                    # Method 3: Method descriptions
                    if not term_matched and 'methods' in file_info and file_info['methods']:
                        if self._check_against_method_descriptions(file_info, search_term):
                            term_matched = True
                            weight = max(weight, "medium")
                            if "methods" not in debug_info["search_methods_used"]:
                                debug_info["search_methods_used"].append("methods")
                    
                    # Method 4: Class descriptions
                    if not term_matched and 'classes' in file_info and file_info['classes']:
                        if self._check_against_class_descriptions(file_info, search_term):
                            term_matched = True
                            weight = max(weight, "medium")
                            if "classes" not in debug_info["search_methods_used"]:
                                debug_info["search_methods_used"].append("classes")
                    
                    # Method 5: CTags
                    if not term_matched and 'ctags' in file_info and file_info['ctags']:
                        if self._check_against_ctags(file_info, search_term):
                            term_matched = True
                            weight = max(weight, "medium")
                            if "ctags" not in debug_info["search_methods_used"]:
                                debug_info["search_methods_used"].append("ctags")
                    
                    # Method 6: External files
                    if not term_matched and 'external_files' in file_info and file_info['external_files']:
                        if self._check_against_externals(file_info, search_term):
                            term_matched = True
                            weight = max(weight, "medium")
                            if "externals" not in debug_info["search_methods_used"]:
                                debug_info["search_methods_used"].append("externals")
                    
                    # Method 7: Path matching
                    if not term_matched:
                        if self._check_against_path(file_path, search_term):
                            term_matched = True
                            # Keep weight as is for path matches
                            if "path" not in debug_info["search_methods_used"]:
                                debug_info["search_methods_used"].append("path")
                    
                    if term_matched:
                        matches_per_term[search_term] += 1
                        term_matches[search_term] = True
                    
                    # For AND search, if any term doesn't match, file doesn't match
                    if and_search and not term_matched:
                        break
                
                # Determine if file matches based on search mode
                if and_search:
                    file_matched = all(term_matches.values())
                else:
                    file_matched = any(term_matches.values())
            
            # Handle semantic-only matches that didn't match keywords
            if not file_matched and file_path in semantic_matches:
                file_matched = True
                weight = "medium"
            
            # Add to results if matched
            if file_matched:
                debug_info["files_matched"] += 1
                
                # Get filename using the knowledge method if available
                if hasattr(self.knowledge, '_get_item_filename'):
                    filename = self.knowledge._get_item_filename(file_info)
                else:
                    filename = os.path.basename(file_path)
                
                # Get description
                if file_info.get('state') == 1:
                    description = 'Description not yet available. Call get_source_file_knowledge to get description'
                else:
                    description = file_info.get('description', 'No description available')
                
                # Check if we should add this match (avoid too many results)
                should_add = True
                for term in search_terms:
                    if matches_per_term[term] >= 60:
                        results_abridged = True
                        should_add = False
                        break
                
                if should_add:
                    match_entry = {
                        "name": filename,
                        "description": description,
                        "file_path": file_path,
                        "weight": weight
                    }
                    
                    if 'code-base-name' in file_info:
                        match_entry['code-base'] = file_info['code-base-name']
                    
                    if file_path in semantic_matches:
                        match_entry['semantic_match'] = True
                    
                    matches_by_weight[weight].append(match_entry)
        
        # Combine results with priority to higher weights
        for weight in ["high", "medium", "low"]:
            for match in matches_by_weight[weight]:
                if len(relevant_files) < 120:
                    relevant_files.append(match)
        
        # Add abridged notice if needed
        if results_abridged:
            relevant_files.append({
                "name": "...",
                "description": "Search results have been abridged due to too many matches"
            })
        
        # Final debug information
        debug_info["matches_per_term"] = matches_per_term
        debug_info["results_by_weight"] = {
            weight: len(matches) for weight, matches in matches_by_weight.items()
        }
        
        # Log results
        if relevant_files:
            self.logger.info(f"Search completed successfully: {len(relevant_files)} files found")
            status = "SUCCESS"
            error = None
        else:
            error_msg = "No files matched the search criteria"
            self.logger.warning(f"{error_msg}. Debug info: {debug_info}")
            status = "ERROR"
            error = error_msg
        
        return {
            "status": status,
            "value": relevant_files,
            "error": error
        }

    # Helper methods for finding relevant files
    def _is_unit_match(self, term, text):
        """Create a pattern with word boundaries around term and search for it in text"""
        pattern = r'\b' + re.escape(term) + r'\b'
        return bool(re.search(pattern, text, re.IGNORECASE))

    def _check_against_file_search_keys(self, file_info, search_term):
        """Check if search term matches any of the file's search terms"""
        match = False
        terms = file_info.get('search-terms', [])
        for term in terms:
            similarity = fuzz.ratio(search_term, term)
            if similarity > 80:
                match = True
                break
        return match

    def _get_base_path(self, path):
        """Get the base path for a given file path"""
        # Since we may not have access to code_bases, we'll extract from the path
        parts = path.split('/')
        # This is a simplified version - in reality we'd need to know the actual base path
        base_path = '/'.join(parts[:3])  # Assuming first 3 parts form the base path
        return base_path

    def _check_against_path(self, path, search_term):
        """Check if search term matches any part of the file path"""
        match = False
        base_path = self._get_base_path(path)
        # Using replace instead of removeprefix for compatibility
        path_of_interest = path.replace(base_path + '/', '', 1)
        path_elements = path_of_interest.split('/')
        for path_element in reversed(path_elements):
            similarity = fuzz.ratio(search_term, path_element)
            if similarity > 70:
                match = True
                break
        return match

    def _check_against_file_description(self, file_info, search_term):
        """Check if search term matches the file description"""
        match = False
        description = file_info.get('description', '')
        if self._is_unit_match(search_term, description):
            match = True
        return match

    def _check_against_method_descriptions(self, file_info, search_term):
        """Check if search term matches any method description"""
        match = False
        methods = file_info.get('methods', [])
        for method in methods:
            if 'description' in method:
                if self._is_unit_match(search_term, method['description']):
                    match = True
                    break
        return match

    def _check_against_ctags(self, file_info, search_term):
        """Check if search term matches any ctag pattern"""
        match = False
        ctags = file_info.get('ctags', [])
        for tag in ctags:
            if self._is_unit_match(search_term, tag.get('pattern', '')):
                match = True
                break
        return match

    def _check_against_externals(self, file_info, search_term):
        """Check if search term matches any external file reference"""
        match = False
        external_files = file_info.get('external_files', [])
        for external_file in external_files:
            if self._is_unit_match(search_term, external_file):
                match = True
                break
        return match

    def _check_against_class_descriptions(self, file_info, search_term):
        """Check if search term matches any class description"""
        match = False
        classes = file_info.get('classes', [])
        for klass in classes:
            if 'description' in klass:
                if self._is_unit_match(search_term, klass['description']):
                    match = True
                    break
        return match

    def get_source_file_description(self,file_path):
        status = "ERROR"
        description = "No such file"
        
        file_info = self.knowledge.get('source_file_info', {}).get(file_path)
        if file_info:
            description = file_info.get('description', "No description available")
            status = "SUCCESS"
            
        result = {
            "status": status,
            "value": description
        }
        return result

    def get_source_files_knowledge(self, file_paths):
        results = {}
        
        for file_path in file_paths:
            status = "ERROR"
            file_info = self.knowledge.get('source_file_info', {}).get(file_path)
            if file_info:
                description = file_info.get('description', "No description available")
                status = "SUCCESS"

            results[file_path] = {
                "status": status,
                "value": description
            }
        
        return results

    def read_file(self, file_path):
        """
        Reads the contents of a file using the base_path.
        If the file is not found, returns a suggestion to use find_relevant_files.
        
        Args:
            file_path: The fully qualified path to the file.
            
        Returns:
            Dictionary with status, file contents or error message with suggestion.
        """
        try:
            with open(file_path, 'r') as file:
                contents = file.read()
                return {
                    "status": "SUCCESS",
                    "content": contents
                }
        except FileNotFoundError:
            return {
                "status": "ERROR",
                "error": "File not found",
                "suggestion": f"File '{file_path}' not found. Try using find_relevant_files with search terms from the filename to locate the correct path.",
                "recommended_action": {
                    "function": "find_relevant_files",
                    "parameters": {
                        "search_terms": [os.path.basename(file_path).replace("_", " ").replace("-", " ").replace(".", " ").split()],
                        "and_search": False
                    }
                }
            }
        except Exception as e:
            return {
                "status": "ERROR",
                "error": str(e)
            }