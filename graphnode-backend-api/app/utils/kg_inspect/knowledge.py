import base64
import copy
import json
import os
import logging
import re
import hashlib
import threading
import asyncio
import time
import nest_asyncio
from rapidfuzz import fuzz
from abc import ABC, abstractmethod
from code_generation_core_agent.config import Config<PERSON>andler
from git import Repo

from app.telemetry.logger_config import set_task_id
from app.utils.kg_inspect.debug_knowledge import debug_embeddings_object
from app.utils.kg_inspect.knowledge_embeddings import KnowledgeEmbeddings


_KNOWLEDGE_FORMAT_VERSION = 4
_LAST_COMPATIBLE_KNOWLEDGE_FORMAT_VERSION = 3

_STATE_NONE = 0
_STATE_INITIAL = 1
_STATE_COMPLETE = 2
_STATE_PENDING = 3

_default_ingestible_filetypes = [
    # Web Development and Frontend
    '.js', '.jsx', '.ts', '.tsx', '.html', '.htm', '.xml', '.xhtml', 
    '.css', '.scss', '.less', 

    # Data and Configuration
    '.json', '.yaml', '.yml', 

    # JavaScript Modules
    '.mjs', '.cjs',

    # Python
    '.py', '.pyi', '.ipynb',

    # C/C++ Family
    '.c', '.h', '.cpp', '.cc', '.cxx', '.c++', '.hpp', '.hxx', '.hh',

    # Modern Systems Programming
    '.rs', 

    # JVM Languages
    '.java', '.kt', '.kts', '.scala', '.sc',

    # Apple Development
    '.swift', '.m', '.mm',

    # Other Major Languages
    '.cs', '.go', 

    # PHP
    '.php', '.php3', '.php4', '.php5', '.phps', '.phtml',

    # Ruby
    '.rb', '.erb', '.rhtml',

    # Perl
    '.pl', '.pm', '.t',

    # Shell and Scripting
    '.sh', '.bash', '.zsh', '.fish', '.csh', '.tcsh', '.ksh',

    # Build Systems
    '.Makefile', '.makefile', '.mk', '.cmake', '.ninja', '.gradle', '.sbt',

    # Lisp Family
    '.lisp', '.cl', '.el', '.scm', '.rkt',

    # Low-level Programming
    '.asm', '.s', '.a51', '.inc',

    # Functional Programming
    '.hs', '.lhs', '.erl', '.hrl', '.ex', '.exs', '.fs', '.fsi', '.fsx',

    # Other Languages
    '.dart', '.lua', '.r', '.R', '.m', '.mat', '.fig', '.jl', '.pl',
    '.pro', '.cob', '.cbl', '.cpy',

    # Scientific Computing
    '.f', '.for', '.f90', '.f95', '.f03', '.f08',

    # Legacy Systems
    '.pas', '.pp', '.inc', '.dpr', '.dfm',

    # Database
    '.sql',

    # Templates and Configuration
    '.j2', '.jinja', '.jinja2', '.toml', '.ini',

    # Documentation
    '.md', '.rst', '.adoc', '.asciidoc',

    # Special Formats
    '.gitignore', '.prompt', '.ebnf', '.bnf', '.peg'
]

_lock = threading.Lock()

class KnowledgeReporter(ABC):
    @abstractmethod
    def send_agent_message(self, message):
        pass

    @abstractmethod
    def cost_update_callback(self, all_costs, total_cost ):
        pass

class KnowledgeHelpers(ABC):
    @abstractmethod
    def execute_cmd(self, cmd):
        pass

    @abstractmethod
    def check_file_exists(self, filename):
        pass

    @abstractmethod
    def read_file(self, filename):
        pass

    @abstractmethod
    def write_file(self, filename, content):
        pass

    @abstractmethod
    def list_directory(self, directory):
        pass

class KnowledgeCodeBase:
    def __init__(self, base_path, name = None):
        self.name = name
        self.base_path = base_path
        self.source_languages = []
        self.search_terms = []
        self.knowledge = None
        self._lock_context = threading.Lock()
        self.context = {}
        self.knowledge_revision = 0
        self.repo = None
        try:
            self.repo = Repo(self.base_path, search_parent_directories=False)
        except Exception:
            self.repo = None

    def _get_git_revision(self, filename):
        git_hash = ""
        if self.repo:
            try:
                git_hash = self.repo.git.log('-n', '1', '--format=%H', '--', filename).strip()
            except Exception as e:
                self.knowledge.logger.error(f"Exception getting git commit hash for {filename}: {str(e)}")
        return git_hash

    def _assign_file_id(self):
        number = 0
        with self._lock_context:
            number = self.context['next_file_id']
            self.context['next_file_id'] = number+1
        return number

    def _get_revision(self):
        revision = 0
        with self._lock_context:
            revision = self.knowledge_revision
        return revision

    def _get_changes(self, filename, fromGitRev):
        changes = []
        if self.repo:
            try:
                toGitRev = self._get_git_revision(filename)
                diff = self.repo.git.diff(toGitRev, "--", filename)
                if diff:
                    changes.append({"Uncommited changes": diff})
                log = self.repo.git.log("--format=%H", f"{fromGitRev}..{toGitRev}", "--", filename)
                revisions = log.splitlines()[::-1]
                revA = fromGitRev
                for revB in revisions:
                    commit = self.repo.commit(revB)
                    diff = self.repo.git.diff(revA, revB, "--", filename)
                    changes.append({commit.message.strip(): diff})
                    revA = revB
            except Exception as e:
                self.knowledge.logger.error(f"Exception getting git diff for {filename}: {str(e)}")
        return changes

    def _persist_context(self):
        with self._lock_context:
            self.knowledge_revision = self.context['knowledge_revision']
            if self.knowledge._state != _STATE_NONE:
                self.knowledge_revision += 1
                self.context['knowledge_revision'] = self.knowledge_revision
            folder = os.path.join(self.base_path, '.knowledge')
            ctx_path = os.path.join(folder, '.knowledge.json')
            content = json.dumps(self.context)
            self.knowledge.helpers.write_file( ctx_path, content)

    def _load_context(self):
        with self.knowledge._lock:
            folder = os.path.join(self.base_path, '.knowledge')
            ctx_name = '.knowledge.json'
            try:
                data = self.knowledge.helpers.read_file(os.path.join(folder,ctx_name))
                if data:
                    with self._lock_context:
                        self.context = json.loads(data)
                        self.knowledge_revision = self.context['knowledge_revision']
            except Exception as e:
                self.knowledge.logger.error(f"_load_context had exception {str(e)} for {ctx_name}")

            if self.context:
                if self.repo:
                    git_revision = self.repo.head.commit.hexsha
                    if git_revision != self.context.get('git_revision',''):
                        self.context['git_revision'] = git_revision
                self._persist_context()
            else:
                git_revision = ""
                if self.repo:
                    git_revision = self.repo.head.commit.hexsha
                self.context = {
                    'knowledge_revision' : 1,
                    'next_file_id' : 1,
                    'git_revision': f"{git_revision}"
                }
                self._persist_context()

class Knowledge:

    _instances = {}

    class _Worker:
        def __init__(self, knowledge, name):
            self.knowledge : Knowledge = knowledge
            self.name = name
            self.state = _STATE_NONE
            self.thread_worker = None
            self.loop_worker = None
            self.stop_worker = False
            self.worker_stopped = False
            knowledge.logger.info(f'worker {name} created')

        def start(self):
            self.thread_worker = threading.Thread(target=self._worker_loop, daemon=True)
            self.thread_worker.start()

        def stop(self):
            self.stop_worker = True
            if self.thread_worker:
                self.thread_worker.join()

        def _service_ingest_queue(self):
            filename = None
            file_info = None
            knowledge = self.knowledge
            with knowledge._lock:
                if knowledge._ingest_queue:
                    filename = knowledge._ingest_queue[0]
                    knowledge._ingest_queue= knowledge._ingest_queue[1:]
                if filename in knowledge.source_files:
                    file_info = knowledge.source_file_info[filename]
                    file_info["state"] = _STATE_PENDING
                if filename:
                    self.state = _STATE_PENDING
            if filename:
                knowledge.logger.info(f"worker {self.name} pull {filename} from ingest queue")
                code_base = knowledge._get_code_base_from_filename(filename)
                if not file_info:
                    file_info = {
                        "is_source_file": True,
                        "file_id" : code_base._assign_file_id(),
                        "git_revision" : code_base._get_git_revision(filename),
                        "state": _STATE_INITIAL,
                        "description": "",
                        "external_files": [],
                        "external_methods": [],
                        "published": [],
                        "classes": [],
                        "methods": [],
                        "calls": [],
                        "search-terms": []
                    }
                else:
                    try:
                        knowledge.logger.info(f"Thread {self.name} - File {os.path.basename(filename)} - Remaining files in queue: {len(knowledge._ingest_queue)}")
                        file_info_prev = file_info
                        file_info = self.loop_worker.run_until_complete(asyncio.wait_for( knowledge._ingest_file(filename), knowledge.timeout))
                        if not file_info:
                            file_info = file_info_prev
                        file_info["state"] = _STATE_COMPLETE
                        for key in ['file_id','knowledge_revision','git_revision','revision_history']:
                            if key in file_info_prev:
                                file_info[key] = file_info_prev[key]
                    except Exception as e:
                        knowledge.logger.error(f"_service_ingest_queue had exception {str(e)} for file {filename}", exc_info=True)
                if file_info:
                    knowledge._get_ctags(filename, file_info)
                    knowledge._process_file_info(filename, file_info)

        def _worker_loop(self):
            knowledge = self.knowledge
            knowledge.logger.info(f"knowledge worker {self.name} start")
            self.loop_worker = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop_worker)

            while True:
                try:
                    self._service_ingest_queue()
                except Exception as e:
                    knowledge.logger.error(f"Exception servicing ingest queue {str(e)}")

                if knowledge._state == _STATE_INITIAL:
                    with knowledge._lock:
                        if knowledge._state == _STATE_INITIAL:
                            if not knowledge._ingest_queue:
                                self.state = _STATE_COMPLETE
                                if all(worker.state != _STATE_PENDING for worker in knowledge.thread_worker):
                                    knowledge._state = _STATE_COMPLETE
                            if knowledge._state == _STATE_COMPLETE:
                                knowledge.logger.info("Knowledge creation complete")
                                knowledge.reporter.send_agent_message("Knowledge creation complete")
                                if knowledge.synchronous:
                                    self.worker_stopped = True
                                    break

                if knowledge._state == _STATE_COMPLETE:
                    if knowledge.synchronous:
                        self.worker_stopped = True
                        break
                    else:                    
                        time.sleep(1)

                if self.stop_worker:
                    self.worker_stopped = True
                    break
            knowledge.logger.info(f"knowledge worker {self.name} done")
            return None


    def __init__(self, id, config : dict):
        self.processing_start_time = time.time() 
        self.id = id
        self.config = config
        self.base_path = config['base_path']
        print("DEBUG EMBEDDINGS", self.base_path)
        self.model = config['model']
        self.code_bases = config.get('code_bases', [KnowledgeCodeBase(self.base_path, 'default')])
        
        print("DEBUG EMBEDDINGS ", self.code_bases)
        self.ingestible_filetypes = config.get('file_types', _default_ingestible_filetypes)
        self.chunk_size = config.get('chunk_size',64*1024)
        self.num_ingest_threads = config.get('num_ingest_threads', 7)
        self.other_files = []
        self.source_files = []
        self.document_files = []
        self.source_code_bases = []
        self.source_languages = []
        self.search_terms = []
        self.source_file_info = {}
        self.document_info = {}
        self.task_keys = {}
        self.task_knowledge = {}
        self.descriptions_cache = {}
        self.timeout = config['timeout']
        self.reporter = config['reporter']
        self.helpers = config['helpers']
        self.cost_tracer = config['cost_tracer']
        self.logger = self._setup_logger("Knowledge", self.base_path)
        self.keys = ["source-languages","search-terms"]
        self.key_descriptions = [
            "List of source code languages used by the codebase",
            "List of search terms relevant to the codebase. The list is not exhaustive. Any search terms can be used with the find_relevant_files tool."
        ]
        if len(self.code_bases) > 1:
            self.keys.append("code-bases")
            self.key_descriptions.append("List of available codebases")
            for base in self.code_bases:
                self.keys.append("source-languages-"+base.name)
                self.key_descriptions.append(f"List of source code languages used by the {base.name} codebase")
                self.keys.append("search-terms-"+base.name)
                self.key_descriptions.append(f"List of search terms relevant to the {base.name} codebase. The list is not exhaustive. Any search terms can be used with the find_relevant_files tool.")
        self._lock = threading.Lock()
        self._state = _STATE_NONE
        self._ingest_queue = []
        self.loop_nested = None
        self.thread_nested = None
        self.thread_worker = []
        self.stop_nested = False
        self.nested_stopped = False
        code_bases = {}
        for code_base in self.code_bases:
            code_base.knowledge = self
            code_bases[code_base.name] = code_base.base_path
            
        # Check environment variable for embeddings feature flag
        embeddings_enabled = ConfigHandler().getboolean('KNOWLEDGE', 'semantic_search_enabled', fallback=False)
        self.embeddings = None
        if embeddings_enabled:
            print("DEBUG EMBEDDINGS : ", id)
            print("DEBUG CODEBASE : ", code_bases)
            
            # Record start time
            start_time = time.time()
            
            # Get embeddings instance
            self.embeddings = KnowledgeEmbeddings.get_instance(id, code_bases)
            
            # Calculate elapsed time
            elapsed_time = time.time() - start_time
            print(f"DEBUG EMBEDDINGS INITIALIZATION TIME: {elapsed_time:.2f} seconds")
            
            # Debug the embeddings object
            debug_embeddings_object(self.embeddings)
        set_task_id(self.id)
        return
    
    def get_file_count(self, base_path):
        total_count = 0
        items = self.helpers.list_directory(base_path)
        for item in items:
            item_path = os.path.join(base_path, item)
            nest = self.helpers.list_directory(item_path + "/")
            if len(nest) > 0:
                skip_folder = False
                if item.startswith('.'):
                    skip_folder = True
                if item_path.endswith('/coverage'):
                    skip_folder = True
                if item_path.endswith('/node_modules'):
                    skip_folder = True
                if not skip_folder:
                    total_count += self.get_file_count(item_path)
            else:
                add = self._is_ingestible(item_path)
                if add:
                    total_count += 1

        return total_count


    def get_kg_progress(self, total_files):
        set_task_id(self.id)
        print("Self get_kg_progress")
        progress_by_codebase = {}
        overall_estimated_time = 0
        
        print("STATE VALUES")
        print(_STATE_NONE)
        print(_STATE_INITIAL)
        print(_STATE_COMPLETE)
        print("---------------")
        
        # Group files by codebase/build_id
        for code_base in self.code_bases:
            build_id = code_base.name  # This is the build_id as per your setup
            base_path = code_base.base_path
            
            # Initialize counters for this codebase
            codebase_files = {
                'total_files': total_files,
                'files_ready': 0,
                'files_processed': 0,
                'percentage_complete': 0
            }
            
            # Count source files for this codebase
            for file in self.source_files:
                if file.startswith(base_path):
                    file_info = self.source_file_info.get(file, {})
                    if file_info.get('state') == _STATE_COMPLETE:
                        codebase_files['files_processed'] += 1
                    elif file_info.get('state') == _STATE_INITIAL:
                        codebase_files['files_ready'] += 1
                    else:
                        print("STATE NONE")
                        print(_STATE_NONE)
                        
            # Count other files for this codebase
            for file in self.other_files:
                if file.startswith(base_path):
                    codebase_files['files_processed'] += 1
            
            # Calculate percentage for this codebase
            if codebase_files['total_files'] > 0:
                codebase_files['percentage_complete'] = round(
                    (codebase_files['files_processed'] / codebase_files['total_files']) * 100, 
                    2
                )
            
            # Calculate estimated time remaining for this codebase
            if hasattr(self, 'processing_start_time') and codebase_files['files_processed'] > 0:
                current_time = time.time()
                elapsed_time = current_time - self.processing_start_time
                avg_time_per_file = elapsed_time / codebase_files['files_processed']
                remaining_files = codebase_files['total_files'] - codebase_files['files_processed']
                estimated_time_remaining = avg_time_per_file * remaining_files
                
                # Convert to minutes
                estimated_time_remaining_minutes = estimated_time_remaining / 60
                estimated_time_remaining_seconds = round((estimated_time_remaining_minutes - int(estimated_time_remaining_minutes)) * 60)
                time_unit = "minute" if estimated_time_remaining_minutes <= 1 else "minutes"
                
                if(estimated_time_remaining_minutes < 1): 
                    formatted_time = f"{round(estimated_time_remaining_seconds)} seconds"
                else:
                    formatted_time = f"{int(estimated_time_remaining_minutes)} {time_unit} {round(estimated_time_remaining_seconds)} seconds"
                
                # Store both raw minutes and formatted string
                codebase_files["estimated_time_remaining_minutes"] = estimated_time_remaining_minutes
                codebase_files["estimated_time_remaining"] = formatted_time
                
                # Add to overall estimation
                overall_estimated_time += estimated_time_remaining_minutes
            
            # Add to progress dictionary
            progress_by_codebase[build_id] = codebase_files
            
            # Print progress for this codebase
            print(f"\nBuild ID: {build_id}")
            print(f"Total Files: {codebase_files['total_files']}")
            print(f"\n Files Ready: {codebase_files['files_ready']}")
            print(f"Files Processed: {codebase_files['files_processed']}")
            print(f"Percentage: {codebase_files['percentage_complete']}%")
            if 'estimated_time_remaining' in codebase_files:
                print(f"Estimated Time Remaining: {codebase_files['estimated_time_remaining']}")
        
        # Calculate overall progress
        total_files = sum(cb['total_files'] for cb in progress_by_codebase.values())
        total_processed = sum(cb['files_processed'] for cb in progress_by_codebase.values())
        total_ready = sum(cb['files_ready'] for cb in progress_by_codebase.values())
        
        # Format overall estimated time
        time_unit = "minute" if overall_estimated_time <= 1 else "minutes"
        if(overall_estimated_time == 0):
            formatted_overall_time = "-"
        else:
            overall_remaining_seconds = round((overall_estimated_time - int(overall_estimated_time)) * 60)
            if(overall_estimated_time < 1): 
                formatted_overall_time = f"{round(overall_remaining_seconds)} seconds"
            else:
                formatted_overall_time = f"{int(overall_estimated_time)} {time_unit} {round(overall_remaining_seconds)} seconds"
        
        overall_progress = {
            "estimated_time_remaining": formatted_overall_time,
            "total_files": total_files,
            "files_ready": total_ready,
            "files_processed": total_processed,
            "percentage_complete": round((total_processed / total_files * 100), 2) if total_files > 0 else 0,
            "progress_by_codebase": progress_by_codebase
        }
        
        return overall_progress
    
    def start(self, synchronous=False):
        
        set_task_id(self.id)
        self.synchronous = synchronous  # Store the synchronous flag
        if not self.thread_nested and not self.thread_worker:
            for base in self.code_bases:
                base._load_context()
            self._load_persisted_knowledge()
            self._load_persisted_docinfo()
            self._state = _STATE_INITIAL
            for base in self.code_bases:
                with self._lock:
                    self._ingest_queue.extend(self._list_important_files(base.base_path))

            # If there are no files to ingest, check source files state
            if not self._ingest_queue:
                for file in self.source_files:
                    file_info = self.source_file_info[file]
                    if file_info['state'] == _STATE_INITIAL:
                        self._ingest_queue.append(file)
                if not self._ingest_queue:
                    self.state = _STATE_COMPLETE

            # set the number of threads for indexing, by default 1
            num_workers = 1
            if self._ingest_queue:
                #  if there are source files to ingest use more threads
                num_workers = min(self.num_ingest_threads, len(self._ingest_queue))

            if synchronous:
                # Run the nested event loop synchronously
                self.loop_nested = asyncio.new_event_loop()
                asyncio.set_event_loop(self.loop_nested)
                self._run_nested_event_loop_synchronous()
            else:
                # Run the nested event loop asynchronously
                self.loop_nested = asyncio.new_event_loop()
                self.thread_nested = threading.Thread(target=self._run_nested_event_loop, daemon=True)
                self.thread_nested.start()

            # Run worker loops asynchronously
            worker_index = 0
            while worker_index < num_workers:
                worker_index += 1
                thread_worker = self._Worker(self, f"kw{worker_index}")
                thread_worker.start()
                self.thread_worker.append(thread_worker)

            if synchronous:
                # Wait for all worker threads to finish
                for worker in self.thread_worker:
                    worker.thread_worker.join()

    def _run_nested_event_loop_synchronous(self):
        nest_asyncio.apply()
        asyncio.run(self._nested_event_loop())

    async def _nested_event_loop(self):
        while True:
            await asyncio.sleep(1)
            if self.stop_nested or all(worker.worker_stopped for worker in self.thread_worker):
                self.nested_stopped = True
                break

    def _run_nested_event_loop(self):
        asyncio.set_event_loop(self.loop_nested)
        while True:
            self.loop_nested.run_until_complete(asyncio.sleep(1))
            if self.stop_nested:
                self.nested_stopped = True
                break

    def _post_process_file_info(self, file_info):
        ctags = file_info.get('ctags')
        if ctags:
            for tag in ctags:
                tag_type = tag.get('kind')
                if tag_type == 'member' and 'signature' in tag:
                    tag_type = 'method'
                if tag_type in ['function','method']:
                    name = tag.get('name')
                    if name:
                        if not 'typeref' in tag and not 'signature' in tag:
                            type = tag.get('pattern').removeprefix('/^').removesuffix('$/').strip()
                        else:
                            type = tag.get('typeref','typename:').removeprefix('typename:')
                        signature = tag.get('signature','')
                        scope = tag.get('scope','')
                        scopekind = tag.get('scopeKind','')
                        function = type+' '+name+signature
                        function = function.strip()
                        found = False
                        for method in file_info['methods']:
                            if name == method['name'] or function == method['name']:
                                method['name'] = function
                                method['scope'] = scope
                                method['scopeKind'] = scopekind
                                found = True
                                break
                        if not found:
                            file_info['methods'].append({'name': function, 'scope': scope, 'scopeKind': scopekind, 'description': 'unavailable'})
                elif tag_type in ['member','variable','field','property','interface']:
                    name = tag.get('name')
                    if name:
                        if not 'typeref' in tag:
                            type = tag.get('pattern').removeprefix('/^').removesuffix('$/').strip()
                            member = type
                        else:
                            type = tag.get('typeref','typename:').removeprefix('typename:')
                            member = type+' '+name
                        scope = tag.get('scope','')
                        scopekind = tag.get('scopeKind','')
                        if 'fields' not in file_info:
                            file_info['fields'] = []
                        found = False
                        for field in file_info['fields']:
                            if member == field['name']:
                                found = True
                                break
                        if not found:
                            file_info['fields'].append({'name': member, 'scope': scope, 'scopeKind': scopekind, 'description': 'unavailable'})
                elif not tag_type in ['namespace','macro','struct','typedef','class','enum','enumerator','constant','alias','generator','unknown']:
                    self.logger.info(f'Unexpected ctag tag type: {tag_type}')


    def _process_file_info(self, filename, file_info, persist=None):
        self.logger.info(f"process ingested knowledge for {filename}")
        file_info['filename'] = filename
        if not persist == False:
            file_info['hash'] = self._get_file_hash(filename)
        file_info['format-version'] = _KNOWLEDGE_FORMAT_VERSION
        code_base = None
        code_base_name = self._get_code_base_name(filename)
        if code_base_name:
            code_base = self._get_codebase(code_base_name)
            file_info['code-base-name'] = code_base_name
            if not code_base_name in self.source_code_bases:
                self.source_code_bases.append(code_base_name)
        with self._lock:
            if 'is_source_file' in file_info:
                if file_info['is_source_file']:
                        if filename in self.other_files:
                            self.other_files.remove(filename)
                        if filename in self.source_files:
                            self.source_files.remove(filename)
                        self.source_files.append(filename)
                        if 'format' in file_info:
                            language = file_info['format']
                            if language not in self.source_languages:
                                self.source_languages.append(language)
                            if code_base:
                                if language not in code_base.source_languages:
                                    code_base.source_languages.append(language)
                        search_terms = file_info['search-terms']
                        for search_term in search_terms:
                            if search_term not in self.search_terms:
                                self.search_terms.append(search_term)
                            if code_base:
                                if search_term not in code_base.search_terms:
                                    code_base.search_terms.append(search_term)
                        self.source_file_info[filename] = file_info
                else:
                    if filename in self.other_files:
                        self.other_files.remove(filename)
                    if filename in self.source_files:
                        self.source_files.remove(filename)
                    self.other_files.append(filename)
                if persist == None:
                    persist = True
        if file_info['state'] == _STATE_COMPLETE:
            self._post_process_file_info(file_info)
        if persist:
            self._persist_knowledge( file_info)
            if file_info['state'] == _STATE_INITIAL:
                self.addToIngestQueue(filename)

    def _get_base_path(self, filename):
        base_path = ''
        for base in self.code_bases:
            if filename.startswith(base.base_path):
                if len(base.base_path) > len(base_path):
                    base_path = base.base_path
        return base_path

    def _get_code_base_name(self, filename):
        name = None
        base_path = self._get_base_path(filename)
        for base in self.code_bases:
            if base_path == base.base_path:
                name = base.name
                break
        return name

    def _get_code_base_from_filename(self, filename):
        code_base = None
        base_path = self._get_base_path(filename)
        for base in self.code_bases:
            if base_path == base.base_path:
                code_base = base
                break
        return code_base

    def _get_codebase(self, code_base_name):
        code_base = None
        for base in self.code_bases:
            if code_base_name == base.name:
                code_base = base
                break
        return code_base

    def _persist_docinfo(self, doc_info):
        filename = doc_info['filename']
        self.logger.info(f"persist docinfo for {filename}")
        code_base = self._get_code_base_from_filename(filename)
        folder = os.path.join(code_base.base_path, '.knowledge')
        folder = os.path.join(folder, '.documents')
        persistent_filename = os.path.join( folder, filename.replace( '/', '_' )+'.json')
        content = json.dumps(doc_info)
        self.helpers.write_file( persistent_filename, content)

    def _load_persisted_docinfo(self):
        self.logger.info("load persisted docinfo: begin")
        for base in self.code_bases:
            folder_name = os.path.join(base.base_path,'.knowledge')
            folder_name = os.path.join(folder_name,'.documents')
            self._preprocess_knowledge_files(folder_name)
            files = self.helpers.list_directory(folder_name)
            for file in files:
                doc_info = None
                if file.startswith('.'):
                    continue
                try:
                    data = self.helpers.read_file(os.path.join(folder_name,file))
                    doc_info = json.loads(data)
                except Exception as e:
                    self.logger.error(f"_load_peristed_knowledge had exception {str(e)} for file {file}")
                    pass
                if doc_info:
                    filename = doc_info['filename']
                    with self._lock:
                        if self.helpers.check_file_exists(filename):
                            if not filename in self.document_files:
                                self.document_files.append(filename)
                            self.document_info[filename] = doc_info
                        else:
                            self.logger.info(f'file has been deleted {filename}: removing persisted doc_info')
                            self.helpers.execute_cmd(f'rm {filename}')
        self.logger.info("load persisted docinfo: end")

    def _preprocess_knowledge_files(self,folder_name):
        changes_made = False
        files = self.helpers.list_directory(folder_name)
        for file in files:
            if file.startswith('.'):
                continue
            if not file.endswith('.json') or (file.endswith('.json') and not file.endswith('.json.json')):
                changes_made = True
                new_file = f"{file}.json"
                self.helpers.execute_cmd(f'mv {os.path.join(folder_name,file)} {os.path.join(folder_name,new_file)}')
        if changes_made:
            self.logger.info(f"Persisted knowledge files in {folder_name} have been renamed to use .json")
            
    def _persist_knowledge(self, file_info):
        self.logger.info(f"persist ingested knowledge for {file_info['filename']}")
        base_path = self._get_base_path(file_info['filename'])
        with self._lock:
            filename = file_info['filename']
            code_base = self._get_code_base_from_filename(filename)
            code_base._persist_context()
            file_info['knowledge_revision'] = code_base._get_revision()
            state = file_info['state']
            revision_history = file_info.get('revision_history',[])
            if ( state == _STATE_COMPLETE ):
                file_info['git_revision'] = code_base._get_git_revision(filename)
                revision_history.append({file_info['knowledge_revision']:file_info['git_revision']})
                file_info['revision_history'] = revision_history
            folder = os.path.join(base_path, '.knowledge')
            persistent_filename = os.path.join( folder, file_info['filename'].replace( '/', '_' ))
            content = json.dumps(file_info)
            self.helpers.write_file( persistent_filename, content)
            search_terms = file_info['search-terms']
            code_base_name = code_base.name
            # Only store embeddings if enabled
            if search_terms and self.embeddings and not self.embeddings.has_file_embeddings(code_base_name, filename):
                embedding = self.embeddings.create_embedding_for_terms(search_terms)
                self.embeddings.store_file_search_terms(filename, code_base_name, search_terms, embedding)
                self.logger.info(f"persist search terms embedding for {filename}")

    def _load_persisted_knowledge(self):
        set_task_id(self.id)
        self.logger.info("load persisted knowledge: begin")
        for base in self.code_bases:
            folder_name = os.path.join(base.base_path,'.knowledge')
            self._preprocess_knowledge_files(folder_name)
            files = self.helpers.list_directory(folder_name)
            for file in files:
                if file.startswith('.'):
                    continue
                try:
                    data = self.helpers.read_file(os.path.join(folder_name,file))
                    file_info = json.loads(data)
                except Exception as e:
                    self.logger.error(f"_load_peristed_knowledge had exception {str(e)} for file {file}")
                    pass
                format_version = file_info['format-version']
                filename = file_info['filename']
                if not 'file_id' in file_info:
                    code_base = self._get_code_base_from_filename(filename)
                    file_info['file_id'] = code_base._assign_file_id()
                    file_info['knowledge_revision'] = code_base._get_revision()
                    file_info['git_revision'] = code_base._get_git_revision(filename)
                    file_info['revision_history'] = [{code_base._get_revision():code_base._get_git_revision(filename)}]
                    self._persist_knowledge(file_info)
                if format_version >= _LAST_COMPATIBLE_KNOWLEDGE_FORMAT_VERSION:
                    update_persisted = False
                    if format_version == 3:
                        # update ctag data to new format
                        update_persisted = True
                        self._get_ctags(filename,file_info)
                    if self.helpers.check_file_exists(filename):
                        self._process_file_info(filename, file_info, update_persisted)
                        if 'hash' in file_info:
                            hash = self._get_file_hash(filename)
                            if hash and hash != file_info['hash']:
                                self.logger.info('modified file detected: hash mismatch')
                                self.addToIngestQueue(filename)
                    else:
                        self.logger.info(f'file has been deleted {filename}: removing persisted knowledge')
                        self.helpers.execute_cmd(f'rm {os.path.join(folder_name,file)}')
        if self.source_file_info:
            self.reporter.send_agent_message("Knowlege loading complete")
        self.logger.info("load persisted knowledge: end")
    
    def _get_file_hash(self, filename):
        hash = None
        content = self.helpers.read_file(filename)
        if content:
            hash = hashlib.md5(content.encode("utf-8")).hexdigest()
        return hash

    def _get_ctags(self, filename, file_info):
        use_ctags = False
        for suffix in [
            '.js','.jsx','.ts','.tsx','.py','.c','.cpp','.c++','.cc','.h','.hpp','.cob','.cbl','.rs']:
            if filename.lower().endswith(suffix):
                use_ctags = True
                break
        if not use_ctags:
            return
        ctags = []
        language = None
        cmd = f'ctags -R --fields=+lS --excmd=number --output-format=json -f- {filename}'
        output, returncode = self.helpers.execute_cmd(cmd)
        if returncode == 0:
            datalen = 0
            language = None
            for line in output.split('\n'):
                try:
                    entry = json.loads(line)
                except json.JSONDecodeError:
                    entry = None
                if entry:
                    if not language and 'language' in entry:
                        language = entry.get('language')
                    if datalen + len(line)  < 30000:
                        datalen += len(line)
                        ctags.append(entry)
        file_info['ctags'] = ctags
        if language and not 'format' in file_info:
            file_info['format'] = language

    def _is_ingestible(self, item_path):
        codebase = self._get_code_base_from_filename(item_path)
        if not codebase:
            return False
        item = os.path.basename(item_path)
        ingestible = True
        if item.startswith('.'):
            ingestible = False
        if item_path.endswith('.log'):
            ingestible = False
        if item_path.endswith('files.yaml'):
            ingestible = False
        if item_path.endswith('package-lock.json'):
            ingestible = False
        good_suffix = False
        for suffix in self.ingestible_filetypes:
            if item_path.lower().endswith(suffix):
                good_suffix = True
                break
        if not good_suffix:
            ingestible = False
        return ingestible

    def _list_important_files(self, base_folder):
        result = ""
        filelist = []

        def list_files_in_folder(folder_path):
            files = []
            items = self.helpers.list_directory(folder_path)
            for item in items:
                item_path = os.path.join(folder_path, item)
                nest = self.helpers.list_directory(item_path + "/")
                if len(nest) > 0:
                    skip_folder = False
                    if item.startswith('.'):
                        skip_folder = True
                    for suffix in ['/coverage','/node_modules','venv']:
                        if item_path.endswith(suffix):
                            skip_folder = True
                    if not skip_folder:
                        files.extend(list_files_in_folder(item_path))
                else:
                    add = self._is_ingestible(item_path)
                    if add:
                        # if there is an ingestible file that we do not yet know
                        # about, add it to the list of important files
                        if (not item_path in self.source_files
                            and not item_path in self.other_files
                            and not item_path in self._ingest_queue
                        ):
                            files.append(os.path.join(folder_path, item))
            return files

        filelist.extend(list_files_in_folder(base_folder))
        return filelist

    def _check_file(self, filename, search_terms, and_search):
        match = False
        for term in search_terms:
            cmd = f"grep -l {term} {filename}"
            output, returncode = self.helpers.execute_cmd(cmd)
            if returncode == 0  and filename in output:
                match = True
                if not and_search:
                    break
            elif and_search:
                match = False
                break
        return match

    def _getTaskKnowledgeDescriptions(self, name:str, items:dict):
        descriptions = None
        hash = f'{hashlib.md5(json.dumps(items).encode("utf-8")).hexdigest()}'
        with self._lock:
            if hash in self.descriptions_cache:
                descriptions = self.descriptions_cache[hash]
        if not descriptions:
            descriptions = self._execute_ingest_task_knowledge(name,items)
            if descriptions:
                with self._lock:
                    self.descriptions_cache[hash] = descriptions
        return descriptions

    def _execute_ingest_task_knowledge(self, name:str, items:dict):
        timeout = self.timeout
        try:
            future = asyncio.run_coroutine_threadsafe( self._ingest_task_knowledge(name,items), self.loop_nested)
            items_descriptions = future.result(timeout=timeout)
            return items_descriptions
        except Exception as e:
            self.logger.error(f"Error: _execute_ingest_task_knowledge had exception {str(e)}")
        return None

    async def _ingest_task_knowledge(self, name:str, items:dict):
        import litellm
        items_descriptions = None
        self.logger.info(f"start ingest for task knowledge: {name}")
        model_name = self.model
        system_prompt = "You are an expert software engineer."
        user_prompt = "Your task is to examine the supplied JSON object and for each " \
                      "key in the JSON object generate a brief (1 to 4 sentences) description of " \
                      "the value for that key.  The purpose of the description is to allow a future " \
                      "software engineer to read the description to decide if this key supplies informaton " \
                      "that would be useful for a particular task.\n" \
                      "Please format your final reponse as a JSON object with the following structure:\n " \
                      "{{ " \
                      "   '<key from input JSON>': '<description of value>' \n" \
                      "}} \n" \
                      f"The JSON object to examine is: {items}.\n"
        try:
            response = litellm.completion(
                model=model_name,
                response_format={"type": "json_object"},
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ]
            )
            output = response.choices[0].message.content

            prompt_tokens = int(response.usage.prompt_tokens)
            completion_tokens = int(response.usage.completion_tokens)

            if self.cost_tracer:
                self.cost_tracer.add_cost_by_tokens("Knowledge", model_name, prompt_tokens, completion_tokens)

                if self.reporter.cost_update_callback:
                    all_costs = self.cost_tracer.get_all_costs()
                    total_cost = self.cost_tracer.get_total_cost()
                    self.reporter.cost_update_callback(all_costs, total_cost)

            try:
                items_descriptions = json.loads(output)
            except json.JSONDecodeError:
                pass
        except Exception as e:
            self.logger.error(f"_ingest_file had exception {str(e)} processing {name}")
            pass

        self.logger.info(f"done ingest for task knowledge: {name}")
        return items_descriptions

    def _summarize_changes(self, filename, changes):
        import litellm
        summary = []
        self.logger.info(f"start summarizing changes for {filename}")
        doc_info = self.document_info.get(filename)
        if doc_info:
            description = doc_info.get('description')

            model_name = self.model
            system_prompt = "You are an expert software engineer and technical writer.  You are highly skilled at examining diffs to source code and producing a detailed explanation of the chnages."
            for key, value in changes.items():
                user_prompt = "Your task is to read the supplied list of changes to codebase source files. The changes are in the form " \
                              "of a dict where the keys are fully qualified pathnames of codebase source files and the values are " \
                              "a list of changes with each entry consisting of a commit message followed by a diff of the changes. " \
                              "The list of changes are for a set of codebase files that were used as sources of information for a " \
                              f"document {filename}.  The description of this document is ({description}).\n " \
                              "These changes have been made to the codebase after the last time the document has been updated and so " \
                              "the document may no longer be entirely accurate or complete.\n " \
                              "Examine the changes to each file, considering the commit message and the change diffs, and produce a " \
                              "synopsis of the changes focusing on aspects of the changes that are relevant to the document and would " \
                              "be helpful to a technical writer tasked with updating the document.\n " \
                              "The synopsis of changes should consist of full descriptive paragraphs and bullet lists. \n " \
                              f"Here is are the changes: ({key}:{value})\n  " \
                              "If your response contains multi-line text be sure to format it properly for JSON.\n " \
                              "Please format your final response as a JSON object with the following structure:\n" \
                              "{{ " \
                              "    '<fully qualified source filename>': '<synposis of changes>' " \
                              "}}\n"
                try:
                    synopsis = None
                    response = litellm.completion(
                        model=model_name,
                        response_format={"type": "json_object"},
                        messages=[
                            {"role": "system", "content": system_prompt},
                            {"role": "user", "content": user_prompt}
                        ]
                    )
                    output = response.choices[0].message.content
                    try:
                        synopsis = json.loads(output)
                        if synopsis:
                            summary.append(synopsis)
                    except json.JSONDecodeError:
                        pass
                except Exception as e:
                    self.logger.error(f"_summarize_changes had exception {str(e)} processing changes for file {filename}")
                    pass
        self.logger.info(f"done summarizing changes for {filename}")
        return summary

    def _execute_ingest(self, filename):
        timeout = self.timeout
        try:
            future = asyncio.run_coroutine_threadsafe( self._ingest_file(filename), self.loop_nested)
            file_info = future.result(timeout=timeout)
            return file_info
        except Exception as e:
            self.logger.error(f"Error: _ingest_command had exception {str(e)}")
        return None

    def _merge_file_info(self, file_info1:dict, file_info2:dict):
        for key in ['external_files','external_methods','published','calls','search-terms','classes','methods']:
            if key in file_info2:
                items = file_info2[key]
                if not key in file_info1:
                    file_info1[key] = items
                else:
                    for item in items:
                        if not item in file_info1[key]:
                            file_info1[key].append(item)

    async def _ingest_file(self, filename):
        import litellm
        self.logger.info(f"start ingest for {filename}")
        file_info = None
        file_info_master = None
        file_data = self.helpers.read_file(filename)
        model_name = self.model
        system_prompt = "You are an expert software engineer."

        file_len = len(file_data)
        chunks = []
        chunk_overlap_size = 1024
        chunk_start = 0
        while chunk_start < file_len:
            chunk = ''
            chunk_end =  chunk_start + self.chunk_size
            if chunk_end > file_len:
                chunk_end = file_len
            chunk = file_data[chunk_start:chunk_end]
            chunk_start += len(chunk)
            if chunk_start < file_len and chunk_start > chunk_overlap_size:
                chunk_start -= chunk_overlap_size
            chunks.append(chunk)

        num_chunks = len(chunks)
        chunk_number = 1
        file_info_prior = None
        for chunk in chunks:
            user_prompt = "Your task is to read the supplied file which is some sort of source file " \
                        "for a software project and to report information about it.\n" \
                        "First decide if the file is actually a source file and set is_source_file to true or false. " \
                        "If it is a source file set the remaining response fields appropriately, otherwise leave them empty. " \
                        "For search-terms pick terms that distinguish the file within the project not terms that are shared by " \
                        "files in the project in general.\n" \
                        "Please format your final response as a JSON object with the following structure:\n" \
                        "{{ " \
                        "   'is_source_file': <true or false>,\n" \
                        "   'format': '<the format or language of the file>', \n" \
                        "   'description': '<description of file contents>', \n" \
                        "   'external_files': [<list of external files referenced>], \n" \
                        "   'external_methods': [<list of external methods referenced>], \n" \
                        "   'published': [<list of symbols defined in this file that are visible to other files>], \n" \
                        "   'classes': [ #list of classes defined in file, if applicable\n" \
                        "       {{ \n" \
                        "         'name': '<class name>',\n" \
                        "         'description': '<description of class>', \n" \
                        "          ...\n" \
                        "       }}, \n" \
                        "     ], " \
                        "   'methods': [ #list of methods defined in file, if applicable\n" \
                        "       {{ \n" \
                        "         'name': '<method name>', \n" \
                        "         'description': '<description of method>', \n" \
                        "          ...\n" \
                        "       }}, \n" \
                        "     ], " \
                        "   'calls': [<list of methods called made in file, if applicable, use fully qualified names if possible], \n" \
                        "   'search-terms': [<list of search keys relevant to this file>] \n" \
                        "}}\n"
            if num_chunks > 1:
                user_prompt += f"The file is being divided into chunks.  This is chunk {chunk_number} of {num_chunks}.\n"
            if file_info_prior:
                user_prompt += f"From examining prior chunks you have established the following: {file_info_prior}\n" \
                                "Take this into consideration when examining this new chunk and add new extracted information to " \
                                "what you have already determined from prior chunks so that no information is lost and the final " \
                                "response for the last chunk represents the results for the entire file.\n"
            user_prompt += f"The fully qualified filename of the file to examine is: {filename}.\n" \
                        f"The contents of the file to examine is: ({chunk}).\n"
            try:
                response = litellm.completion(
                    model=model_name,
                    response_format={"type": "json_object"},
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_prompt}
                    ]
                )
                output = response.choices[0].message.content

                prompt_tokens = int(response.usage.prompt_tokens)
                completion_tokens = int(response.usage.completion_tokens)

                if self.cost_tracer:
                    self.cost_tracer.add_cost_by_tokens("Knowledge", model_name, prompt_tokens, completion_tokens)

                    if self.reporter.cost_update_callback:
                        all_costs = self.cost_tracer.get_all_costs()
                        total_cost = self.cost_tracer.get_total_cost()
                        self.reporter.cost_update_callback(all_costs, total_cost)

                try:
                    file_info = json.loads(output)
                except json.JSONDecodeError:
                    pass
            except Exception as e:
                self.logger.error(f"_ingest_file had exception {str(e)} processing file {filename}")
                pass
            file_info_prior = file_info
            chunk_number += 1
            if num_chunks > 1:
                if not file_info_master:
                    file_info_master = copy.deepcopy(file_info)
                else:
                    self._merge_file_info(file_info_master,file_info)
                file_info = file_info_master

        self.logger.info(f"done ingest for {filename}")
        return file_info

    def _setup_logger(self, name, base_path, log_level=logging.INFO):
        """
        Set up a logger with a file handler, preventing logs from going to the console.

        :param name: Name of the logger and the log file
        :param base_path: Base path for the log directory
        :param log_level: Logging level (default: logging.INFO)
        :return: Configured logger
        """
        logger = logging.getLogger(name)
        logger.setLevel(log_level)

        # Remove any existing handlers (including the default StreamHandler)
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        # Prevent the logger from propagating messages to the root logger
        logger.propagate = False

        # Create logs directory if it doesn't exist
        log_dir = os.path.join(base_path, "logs")
        os.makedirs(log_dir, exist_ok=True)

        # Create file handler
        file_handler = logging.FileHandler(os.path.join(log_dir, f"{name}.log"))
        file_handler.setLevel(log_level)

        # Create formatter
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)

        # Add file handler to logger
        logger.addHandler(file_handler)
        logger.propagate = False

        return logger

    def _finish_ingest(self, file_info):
        try:
            filename = file_info['filename']
            self.logger.info(f"need to finish ingest for {filename}")
            file_info = self._execute_ingest(filename)
            file_info["state"] = _STATE_COMPLETE
            self._get_ctags(filename, file_info)
            self._process_file_info(filename, file_info)
        except Exception as e:
            self.logger.error(f"_finish_ingest for {filename} had exception {str(e)} ")
            pass
        return file_info

    def getSourceFileList(self):
        list = []
        files = self.source_files
        for file in files:
            description = self.source_file_info[file]['description']
            list.append( {"name": f"{file}", "description": f"{description}"})
        return list

    def getSourceFileDescription(self, file_path):
        description = "No such file"
        if file_path in self.source_file_info:
            file_info = self.source_file_info[file_path]
            if file_info['state'] == _STATE_INITIAL:
                file_info = self._finish_ingest(file_info)
            description = file_info['description']
        return description

    def _is_unit_match(self, term, text):
        # Create a pattern with word boundaries around term
        pattern = r'\b' + re.escape(term) + r'\b'
        
        # Search for the pattern in text, ignoring case
        return bool(re.search(pattern, text, re.IGNORECASE))

    def _check_against_file_search_keys(self, file_info, search_term):
        match = False
        terma = search_term
        terms = file_info['search-terms']
        for termb in terms:
            similarity = fuzz.ratio(terma, termb)
            if similarity > 80:
                match = True
                break
        return match

    def _check_against_path(self, path, search_term):
        match = False
        base_path = self._get_base_path(path)
        path_of_interest = path.removeprefix(base_path+'/')
        path_elements = path_of_interest.split('/')
        for path_element in reversed(path_elements):
            similarity = fuzz.ratio(search_term,path_element)
            if similarity > 70:
                match = True
                break
        return match

    def _check_against_file_description(self, file_info, search_term):
        match = False
        if self._is_unit_match(search_term, file_info['description']):
            match = True
        return match

    def _check_against_method_descriptions(self, file_info, search_term):
        match = False
        for method in file_info['methods']:
            if 'description' in method:
                if self._is_unit_match(search_term, method['description']):
                    match = True
                    break
        return match

    def _check_against_ctags(self, file_info, search_term):
        match = False
        ctags = file_info['ctags']
        for tag in ctags:
            if self._is_unit_match(search_term, tag.get('pattern','')):
                match = True
                break
        return match

    def _check_against_externals(self, file_info, search_term):
        match = False
        external_files = file_info['external_files']
        for external_file in external_files:
            if self._is_unit_match(search_term,external_file):
                match = True
                break
        return match

    def _check_against_class_descriptions(self, file_info, search_term) -> dict:
        match = False
        for klass in file_info['classes']:
            if 'description' in klass:
                if self._is_unit_match( search_term, klass['description']):
                    match = True
                    break
        return match

    def getKnowledgeRevisions(self,code_base_names:list[str] = None):
        revisions = []
        with self._lock:
            for code_base in self.code_bases:
                if code_base_names:
                    if not code_base.name in code_base_names:
                        continue
                revisions.append({code_base.name: code_base._get_revision()})
        return revisions

    def getFilenameFromNumber(self,code_base_name:str,number:int) -> str:
        name = None
        with self._lock:
            for file_info in self.source_file_info.values():
                if file_info['file_id'] == number and file_info['code-base-name'] == code_base_name:
                    name = file_info['filename']
                    break
        return name

    def findRelevantTaskKeys(self, search_terms, and_search):
        relevant_task_keys = []

        for key in self.task_keys:
            matchWithKey = False
            parts = key.split('--')
            domain_name = parts[0]
            domain_key = parts[1]
            domain = self.task_knowledge[domain_name]
            value = domain[domain_key]

            def search_value(key, value, term):
                match = False
                if isinstance(value,dict):
                    domain = value
                    domain_keys = domain.keys()
                    for key in domain_keys:
                        match = search_value(key, domain[key], term)
                        if match:
                            break
                elif isinstance(value,list):
                    match = self._is_unit_match(term,f"{value}")
                elif isinstance(value,str):
                    match = self._is_unit_match(term,value)
                if match:
                    self.logger.debug(f'task knowledge match key {key} with term {term}')
                return match

            for term in search_terms:
                match = search_value(key,value,term)
                matchWithKey |= match
                if and_search and not match:
                    matchWithKey = False
                    break
                if not and_search and match:
                    break

            if matchWithKey:
                if not key in relevant_task_keys:
                    relevant_task_keys.append(key)

        results = []
        for key in relevant_task_keys:
            results.append( {"key":key, "description":self.task_keys[key]} )

        return results

    def findRelevantFiles(self, search_terms, and_search, code_base):
        relevant_files = []
        matches_high = []
        matches_medium = []
        matches_low = []
        matches_by_weight = { "high": matches_high, "medium": matches_medium, "low": matches_low}
        matches_per_term = {}
        results_abridged = False

        # Get semantic search results only if embeddings are enabled
        semantic_matches = {}
        if self.embeddings:
            similar_files = self.embeddings.search_similar_files(code_base, search_terms)
            for result in similar_files:
                semantic_matches[result['file_path']] = True

        for search_term in search_terms:
            matches_per_term[search_term] = 0
        source_files = copy.deepcopy(self.source_files)
        for file in source_files:
            matchInFile = False
            file_info = self.source_file_info[file]
            if code_base and 'code-base-name' in file_info and file_info['code-base-name'] != code_base:
                continue

            # Determine initial weight based on semantic search results
            weight = "low"
            if file in semantic_matches:
                weight = "medium"  # Boost weight for semantic matches

            if file_info['state'] == _STATE_INITIAL:
                if self._check_file(file, search_terms, and_search):
                    matchInFile = True
            else:
                # Get keyword search results
                for search_term in search_terms:
                    match = False
                    for method in ["description", "terms", "method", "classes",
                                "ctags", "external", "path" ]:
                        if method == "description" and 'description' in file_info:
                            match |= self._check_against_file_description(file_info, search_term)
                            weight = "high"
                        elif method == "terms" and 'search-terms' in file_info and file_info['search-terms']:
                            match |= self._check_against_file_search_keys(file_info, search_term)
                            weight = "medium"
                        elif method == "method" and 'methods' in file_info:
                            match |= self._check_against_method_descriptions(file_info, search_term)
                            weight = "medium"
                        elif method == "classes" and 'classes' in file_info:
                            match |= self._check_against_class_descriptions(file_info, search_term)
                            weight = "medium"
                        elif method == "ctags" and 'ctags' in file_info:
                            match |= self._check_against_ctags(file_info, search_term)
                            weight = "medium"
                        elif method == "external" and 'external_files' in file_info:
                            match |= self._check_against_externals(file_info, search_term)
                            weight = "medium"
                        elif method == "path":
                            match |= self._check_against_path(file, search_term)
                            weight = "low"

                        if match:
                            matches_per_term[search_term] += 1
                            break

                    matchInFile |= match

                    if and_search and not match:
                        matchInFile = False
                        break
                    if not and_search and match:
                        break

            # Add semantic-only matches that didn't match keywords
            if (not matchInFile or weight == "low") and file in semantic_matches:
                matchInFile = True
                weight = "medium"

            if matchInFile:
                filename = file_info['filename']
                if file_info['state'] == _STATE_INITIAL:
                    description = 'Description not yet available.  Call get_source_file_knowledge to get description'
                else:
                    description = file_info['description']
                match_bin = matches_by_weight[weight]
                if matches_per_term[search_term] < 60:
                    match = {"name": f"{filename}", "description": f"{description}"}
                    if 'code-base-name' in file_info:
                        match['code-base'] = file_info['code-base-name']
                    if file in semantic_matches:
                        match['semantic_match'] = True
                    match_bin.append(match)
                else:
                    results_abridged = True

        # Combine results with priority to higher weights
        for weight in ["high", "medium", "low"]:
            match_bin = matches_by_weight[weight]
            for match in match_bin:
                if len(relevant_files) < 120:
                    relevant_files.append(match)

        if results_abridged:
            relevant_files.append( {"name": "...", "description": "search results have been abridged due to too many matches"} )

        return relevant_files

    def getSourceFileKnowledge(self, file_path):
        knowledge = "No such file"
        if file_path in self.source_file_info:
            file_info = self.source_file_info[file_path]
            if file_info['state'] == _STATE_INITIAL:
                file_info = self._finish_ingest(file_info)
            knowledge = copy.deepcopy(file_info)
            for key in ['filename','format-version','hash','is_source_file','knowledge_revision','git_revision','revision_history']:
                knowledge.pop(key)
        return knowledge

    def findMethods(self, method_name):
        matches = []
        method_name = method_name.lower()
        for filename in self.source_files:
            file_info = self.source_file_info.get(filename)
            if file_info:
                methods = file_info.get('methods')
                if methods:
                    for method in methods:
                        name = method.get('name','none').lower()
                        if method_name in name:
                            matches.append( {'filename': filename, 'code-base-name': file_info.get('code-base-name','default'), 'method': method})
        return matches

    def findFields(self, field_name):
        matches = []
        field_name = field_name.lower()
        for filename in self.source_files:
            file_info = self.source_file_info.get(filename)
            if file_info:
                fields = file_info.get('fields')
                if fields:
                    for field in fields:
                        name = field.get('name','none').lower()
                        if field_name in name:
                            matches.append( {'filename': filename, 'code-base-name': file_info.get('code-base-name','default'), 'field': field})
        return matches

    def getKeys(self):
        return { "keys": self.keys, "descriptions": self.key_descriptions }
    
    def getKeyValue(self, key):
        value = None
        with self._lock:
            if key in self.keys:
                if key == 'source-file-list':
                    value = self.getSourceFileList()
                elif key == 'source-languages':
                    value = self.source_languages
                elif key == 'search-terms':
                    value = self.search_terms
                elif key == 'code-bases':
                    value = self.source_code_bases
                elif key.startswith('source-languages-'):
                    code_base_name = key.removeprefix('source-languages-')
                    if code_base_name != key:
                        code_base = self._get_codebase(code_base_name)
                        if code_base:
                            value = code_base.source_languages
                elif key.startswith('search-terms-'):
                    code_base_name = key.removeprefix('search-terms-')
                    if code_base_name != key:
                        code_base = self._get_codebase(code_base_name)
                        if code_base:
                            value = code_base.search_terms
        return value

    def getTaskKeys(self):
        items = []
        for key,desc in self.task_keys.items():
            items.append({"key":key,"description":desc})
        return items


    def getTaskKeyValues(self, keys):
        values = []
        with self._lock:
            for key in keys:
                if key in self.task_keys:
                    parts = key.split('--')
                    if len(parts) == 2:
                        domain = parts[0]
                        domain_key = parts[1]
                        if domain in self.task_knowledge:
                            domain_dict = self.task_knowledge[domain]
                            if domain_key in domain_dict:
                                value = domain_dict[domain_key]
                                if value:
                                    values.append( f"{key}: [{value}]")

        return ', '.join(values)

    def documentCreated(self, filename, description, sources):
        doc_info = None
        with self._lock:
            codebase = self._get_code_base_from_filename(filename)
            if not codebase:
                return
            if filename in self.document_files:
                doc_info = self.document_info[filename]
                doc_info['description'] = description
            else:
                doc_info = {
                    'filename' : filename,
                    'description' : description,
                    'sources' : []
                }
            for source in sources:
                file_info = self.source_file_info.get(source)
                if file_info:
                    codebase = self._get_code_base_from_filename(filename)
                    if codebase:
                        file_id = file_info['file_id']
                        knowledge_revision = file_info['knowledge_revision']
                        git_revision = file_info['git_revision']
                        current_sources = doc_info.get('sources')
                        new_source = True
                        if current_sources:
                            for current_source in current_sources:
                                if codebase.name == current_source.get('code_base') and file_id == current_source.get('file_id'):
                                    current_source['knowledge_revision'] = knowledge_revision
                                    current_source['git_revision'] = git_revision
                                    new_source = False
                                    break
                        if new_source:
                            source_info = {
                                'code_base' : codebase.name,
                                'file_id' : file_id,
                                'knowledge_revision' : knowledge_revision,
                                'git_revision': git_revision
                            }
                            doc_info['sources'].append(source_info)
            self._persist_docinfo(doc_info)
            if not filename in self.document_files:
                self.document_files.append(filename)
                self.document_info[filename] = doc_info

    def getDocuments(self):
        documents = []
        for filename in self.document_files:
            doc_info = self.document_info[filename]
            if doc_info:
                documents.append( {'filename':filename, 'description': doc_info.get('description',"")} )
        return documents

    def getDocumentSources(self, filename):
        document_sources = []
        doc_info = self.document_info.get(filename)
        if doc_info:
            sources = doc_info.get('sources')
            for source in sources:
                codebase_name = source.get('code_base')
                codebase = self._get_codebase(codebase_name)
                source_file_id = source.get('file_id')
                source_filename = self.getFilenameFromNumber(codebase.name, source_file_id)
                document_sources.append(source_filename)
        return document_sources

    def getChangesToDocumentSources(self, filename):
        changes = {}
        doc_info = self.document_info.get(filename)
        if doc_info:
            sources = doc_info.get('sources')
            for source in sources:
                codebase_name = source.get('code_base')
                codebase = self._get_codebase(codebase_name)
                source_file_id = source.get('file_id')
                source_filename = self.getFilenameFromNumber(codebase.name, source_file_id)
                source_git_revision = source.get('git_revision')
                source_changes = codebase._get_changes(source_filename,source_git_revision)
                changes[source_filename] = source_changes
        return changes

    def getSummaryOfChangesToDocumentSources(self, filename):
        summary = {}
        changes = self.getChangesToDocumentSources(filename)
        if changes:
            summary = self._summarize_changes(filename,changes)
        return summary

    def addToIngestQueue(self, filename):
        if self._is_ingestible(filename):
            with self._lock:
                if filename not in self._ingest_queue:
                    self.logger.info(f"adding {filename} to ingest queue")
                    self._ingest_queue.append(filename)

    def addTaskKnowledge(self, name:str, items:dict):
        self.logger.info(f"adding task knowledge: {name}")
        items_descriptions = self._getTaskKnowledgeDescriptions(name,items)
        with self._lock:
            keys_added = False
            keys = items.keys()
            for key in keys:
                value = items.get(key,None)
                if value:
                    new_key = '--'.join([name,f"{key}"])
                    if items_descriptions and key in items_descriptions:
                        new_description = items_descriptions[key]
                    else:
                        new_description = f"{name} key for {key} information"
                    self.task_keys[new_key] = new_description
                    keys_added = True
            if keys_added:
                self.task_knowledge[name] = items

    def removeTaskKnowledge(self, name:str):
        result = False
        self.logger.info(f"removing task knowledge: {name}")
        with self._lock:
            if name in self.task_knowledge:
                items = self.task_knowledge.pop(name)
                keys = items.keys()
                for key in keys:
                    remove_key = '--'.join([name,f"{key}"])
                    if remove_key in self.task_keys:
                        self.task_keys.pop(remove_key)
                result = True
        return result

    @staticmethod
    def getKnowledge(config : dict = None, id: str = "default"):
        instance = None
        with _lock:
            if not id in Knowledge._instances:
                if config:
                    required_keys = ["base_path", "model", "timeout", "cost_tracer", "reporter", "helpers" ]
                    for key in required_keys:
                        if key not in config:
                            raise ValueError(f"Missing required key: {key}")
                    Knowledge._instances[id] = Knowledge(id, config)
            if id in Knowledge._instances:
                instance = Knowledge._instances[id]
        return instance

    @staticmethod
    def releaseKnowledge(id: str):
        with _lock:
            if id in Knowledge._instances:
                instance = Knowledge._instances[id]

                # Stop any running threads
                for thread_worker in instance.thread_worker:
                    if thread_worker:
                        thread_worker.stop_worker = True
                if instance.thread_nested:
                    instance.stop_nested = True
                    time.sleep(1)

                # Close event loops
                for thread_worker in instance.thread_worker:
                    if thread_worker.loop_worker:
                        while not thread_worker.worker_stopped:
                            time.sleep(1)
                        thread_worker.loop_worker.stop()
                        thread_worker.loop_worker.close()
                if instance.loop_nested:
                    while not all(worker.worker_stopped for worker in instance.thread_worker):
                        time.sleep(1)
                    instance.loop_nested.stop()
                    instance.loop_nested.close()

                # Release embeddings if they exist
                if instance.embeddings:
                    KnowledgeEmbeddings.release_instance(id)

                # Remove the instance
                Knowledge._instances.pop(id)
