import os
from app.telemetry.logger_config import set_discussion_id
from app.utils.conversation_utils import ConversationUtils
from app.connection.establish_db_connection import get_mongo_db, get_node_db
from app.conversation.conversation_finder import ConversationFinder
from app.models.chat_model import Chat<PERSON>ontext
from app.utils.logs_utils import get_path
from llm_wrapper.utils.base_tools import DynamicToolFactory, ToolRegistry
from llm_wrapper.core.llm_interface import LLMInterface
from app.utils.kg_inspect.kg_tool import KgTools
from app.utils.kg_inspect.knowledge_cli import get_model_name
from app.utils.datetime_utils import generate_timestamp
import json
import litellm
import asyncio
from uuid import uuid4
import boto3
from botocore.exceptions import ClientError

async def get_vertex_secret():

        secret_name = "preprod/vertext_ai/service_account"
        region_name = "us-east-1"
        
        aws_access_key_id = os.getenv("CODEGEN_AWS_ACCESS_KEY_ID", os.getenv("AWS_ACCESS_KEY_ID"))
        aws_secret_access_key = os.getenv("CODEGEN_AWS_SECRET_ACCESS_KEY", os.getenv("AWS_SECRET_ACCESS_KEY"))
        session = boto3.session.Session(
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key
        )

        client = session.client(
            service_name='secretsmanager',
            region_name=region_name
        )

        try:
            get_secret_value_response = client.get_secret_value(
                SecretId=secret_name
            )
        except ClientError as e:
            # For a list of exceptions thrown, see
            # https://docs.aws.amazon.com/secretsmanager/latest/apireference/API_GetSecretValue.html
            raise e

        secret = get_secret_value_response['SecretString']
        print("AWS SECRETS : ", secret)
        return secret
    
def answer_question_sync(reporter, session_id, project_id, user_id, question, discussion_id=None):
    try:
        print("Starting answer_question_sync")
        asyncio.run(answer_question(reporter, session_id, project_id, user_id, question, discussion_id))
    except Exception as e:
        print(f"Exception {e}")

async def answer_question(reporter, session_id, project_id, user_id, question, message_uuid, actual_username=None, reporterObj=None, discussion_id=None ):
    try:
        print("Starting answer_question : ", discussion_id)
        
        context = ChatContext(
            project_id=project_id,
            user_id=user_id,
            message=question,
            discussion_id=discussion_id,
            agent_name='code_query'
        )
        
        _utils = ConversationUtils(context)
        _chat = ConversationFinder(context)
        node_db = get_node_db()
        user_prompt = question
        
        system_prompt = '''You are an expert software engineer and technical writer. Your task is to answer questions about a software project and generate comprehensive documentation when requested. When analyzing code or creating documentation:

        1. Write in clear and proper markdown format, flowing prose paragraphs rather than bullet points and use the correct numbers for sections and give a tab space before the sublist item.
        2. Provide detailed explanations of how components work and interact
        3. Use technical language while maintaining readability
        4. Explain the reasoning and context behind technical decisions
        5. Focus on system relationships and interactions rather than isolated features
        6. Include diagrams wherever appropriate. Use mermaid charts for diagrams and Title of the charts, unless other mechanisms were requested by the user.

        **Use of Tools:**

        - Use the provided tools to gather detailed information about the project codebase and ensure accuracy in your documentation.
        - Use the KnowledgeTools to understand the codebase and the architecture.
        - Use the KnowledgeTools tools to get information about the project codebase, and to determine what files in the project are relevant to your task.  
        - Use the KnowledgeTools tools to get file knowledge about relevant files in the project.
        - If the information you gather suggests there may be additional sources of information then do additional find_relevant_files searches and reads so your decisions are based on comprehensive information.  
        - If find_relevant_files results include an entry stating that the results are abridged, consider using separate searches with fewer search terms in each, or enabling and_search.
        - Before executing the read_file search the file in find_relevant_files bases on user input. Always use the base path for while reading the file.
        '''

        docs_prompt = '''
        In necessary place always generate a single document in proper markdown format without any preliminary statements:

        1. Write in continuous prose paragraphs. Avoid bullet points, lists, or abbreviated descriptions.
        2. Organize the document into sections and provide suitable headers.
        3. Each section should flow naturally into the next, with clear transitions.
        4. Fully develop ideas and concepts, explaining not just what exists but how it works and why it matters.
        5. Use a professional technical tone while maintaining clarity and readability.
        6. Include relevant technical details within the narrative rather than as separate lists.
        7. When adding a sublist under any parent item (ordered list, unordered list, or any other case), give a tab space before the sublist item.
        8. Ensure proper markdown format for the entire document.
        Format the document as:

        -------------------
        \nDocument:

        [Title on its own line]

        [Your comprehensive documentation in flowing prose paragraphs with proper line breaks between sections]
        -------------------

        Remember: Use \nDocument: for document always, No bullet points, no abbreviated lists, and no shallow descriptions. Every feature or component should be thoroughly explained in proper paragraph form. Ensure proper line breaks between the title, sections, and paragraphs.'''

        
                    
        if discussion_id:
            messages = await _utils._load_discussion(int(context.discussion_id))      
            
        else:
            messages = []
            discussion_id = await _chat._create_conversation('code_query', context.user_id ,actual_username, session_id)
            print("DISCUSSION ID : ", discussion_id)
        # Generate message UUID for user's question
        messages.append({
            "role": "system",
            "content": system_prompt + docs_prompt
        })
        
        if discussion_id:
            set_discussion_id(discussion_id)
                
        try:
            
            litellm.drop_params=True

            model_name = await get_model_name("code_inspection",user_id)
                    
            general_registry = ToolRegistry()
            general_registry.register_tool("KgTools", KgTools)
            
            general_factory = DynamicToolFactory(general_registry)
            general_exec_agent = general_factory.create_dynamic_tool(["KgTools"])
            kg_tool = general_exec_agent('/test', logger=None, user_id=session_id)
            
            llm = LLMInterface(str(get_path()), 'knowledge', user_id, int(project_id), 'code_query', mongo_handler=get_mongo_db())
            cp = messages.copy()
            messages.append({
                "role": "user",
                "content": user_prompt,
                "timestamp": generate_timestamp()
            })
            
            # print(messages)
        
            print("Getting into LLM wrapper")
            
            completion = await llm.llm_interaction_wrapper(
                messages=cp,
                user_prompt=user_prompt,
                system_prompt=None,
                model=model_name,
                stream=True,
                response_format={"type": "json_object"},
                function_schemas=kg_tool.function_schemas,
                function_executor=kg_tool.function_executor
            )   
            
            
            print(completion)
            
            llm_response = ""
            STREAM_DELAY = 0.1  # 100ms delay between chunks
            
            async for res in completion:
                if reporterObj.stop_streaming:
                    print("stop streaming")
                    reporterObj.stop_straeming = True
                    break

                if "Checking the data" in res:
                    yield f"data: {json.dumps({'content': 'Your code-related insights will be available shortly...', 'message_uuid': message_uuid})}\n\n"
                    await asyncio.sleep(STREAM_DELAY)
                    
                elif "Reading File:" in res:
                    await reporter.send_message("code_query", {
                        "message": res,
                        "message_uuid": message_uuid
                    })
                    yield f"data: {json.dumps({'content': res, 'message_uuid': message_uuid})}\n\n"
                    await asyncio.sleep(STREAM_DELAY)
                    
                else:
                    llm_response += res
                    await reporter.send_message("code_query", {
                        "message": llm_response,
                        "discussion_id": discussion_id,
                        "message_uuid": message_uuid
                    })
                    yield f"data: {json.dumps({'content': llm_response, 'discussion_id': discussion_id, 'message_uuid': message_uuid})}\n\n"
                    await asyncio.sleep(STREAM_DELAY)

            
            def add_document_end_marker(message):
                if '-------------------\nDocument:' in message and not message.endswith('-------------------'):
                    return message + '\n\n-------------------'
                return message

            await reporter.send_message("code_query", {
                "message_end": True,
                "message": add_document_end_marker(llm_response),
                "message_uuid": message_uuid
            })
            
            # Generate message UUID for assistant's response
            assistant_message_uuid = str(uuid4())
            messages.append({
                "role": "assistant",
                "content": llm_response,
                "timestamp": generate_timestamp(),
            })
            
            await node_db.update_node_by_id(int(discussion_id), {'Discussion': json.dumps(messages)})            
                
        except Exception as e:
            print(f"Exception {e}")
            await reporter.send_message("code_query", {
                        "message": e,
                        "message_uuid": message_uuid
                    })
    except Exception as e:
        print(f"Exception {e}")
        await reporter.send_message("code_query", {
                        "message": e,
                        "message_uuid": message_uuid
                    })
    finally:
        reporter.is_answering_question = False
