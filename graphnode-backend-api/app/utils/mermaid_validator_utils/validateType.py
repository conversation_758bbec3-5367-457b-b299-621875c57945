import json
import asyncio
import mermaid as md
from mermaid.graph import Graph
from datetime import datetime
import time
from typing import List, Dict, Tuple
from concurrent.futures import ThreadPoolExecutor
from tqdm import tqdm

def validate_mermaid_code(mermaid_code: str) -> tuple[bool, str]:
    """
    Validates Mermaid diagram code by attempting to create a Graph and Mermaid object.
    Returns a tuple of (is_valid, error_message)
    """
    try:
        graph = Graph("test-graph", mermaid_code)
        mermaid = md.Mermaid(graph)
        
        if mermaid.svg_response.status_code == 200:
            return True, ""
        return False, f"SVG generation failed with status code: {mermaid.svg_response.text}"
    except Exception as e:
        return False, str(e)

async def validate_diagram(diagram: Dict, executor: ThreadPoolExecutor, pbar: tqdm) -> Tuple[bool, str, Dict, float]:
    """
    Wrapper function to run validate_mermaid_code asynchronously with timing
    """
    start_time = time.time()
    loop = asyncio.get_event_loop()
    is_valid, error_message = await loop.run_in_executor(
        executor, 
        validate_mermaid_code, 
        diagram['diagram']
    )
    execution_time = time.time() - start_time
    pbar.update(1)
    return is_valid, error_message, diagram, execution_time

async def read_json_file(filename: str) -> Dict:
    try:
        with open(filename, 'r') as file:
            return json.load(file)
    except FileNotFoundError:
        print(f"Error: File {filename} not found")
        return None
    except json.JSONDecodeError:
        print("Error: Invalid JSON format")
        return None

async def process_diagrams(diagrams: List[Dict]) -> List[Dict]:
    invalid_diagrams = []
    total_time = 0
    max_time = 0
    min_time = float('inf')
    
    total_diagrams = len(diagrams)
    
    print(f"\nProcessing {total_diagrams} diagrams...")
    
    with ThreadPoolExecutor() as executor:
        with tqdm(total=total_diagrams, desc="Validating diagrams", unit="diagram") as pbar:
            tasks = [validate_diagram(diagram, executor, pbar) for diagram in diagrams]
            
            start_total = time.time()
            results = await asyncio.gather(*tasks)
            total_parallel_time = time.time() - start_total
            
            validation_results = []
            for is_valid, error_message, diagram, execution_time in results:
                validation_results.append({
                    'title': diagram['title'],
                    'valid': is_valid,
                    'time': execution_time,
                    'error': error_message if not is_valid else None
                })
                
                total_time += execution_time
                max_time = max(max_time, execution_time)
                min_time = min(min_time, execution_time)
                
                if not is_valid:
                    invalid_diagrams.append({
                        'title': diagram['title'],
                        'diagram': diagram['diagram']
                    })

    # Print detailed results
    print("\nDetailed Results:")
    print("=" * 80)
    for result in validation_results:
        status = "✓" if result['valid'] else "✗"
        print(f"{status} {result['title']:<50} {result['time']:.2f}s")
        if result['error']:
            print(f"   Error: {result['error']}")
    print("=" * 80)

    # Print timing statistics
    avg_time = total_time / total_diagrams if total_diagrams else 0
    
    print("\nTiming Statistics:")
    print("-" * 40)
    print(f"Total parallel execution time:      {total_parallel_time:.2f}s")
    print(f"Sum of individual execution times:  {total_time:.2f}s")
    print(f"Average time per diagram:          {avg_time:.2f}s")
    print(f"Maximum execution time:            {max_time:.2f}s")
    print(f"Minimum execution time:            {min_time:.2f}s")
    print(f"Speed improvement from parallel:    {(total_time/total_parallel_time):.2f}x")
    print("-" * 40)
    
    return invalid_diagrams

async def main():
    print("Starting Mermaid diagram validation...")
    overall_start = time.time()
    
    file_data = await read_json_file('/home/<USER>/kavia-ai/graphnode-backend-api/examples/mermaid_code/container_diagrams.json')
    
    if file_data:
        invalid_diagrams = await process_diagrams(file_data['container_diagrams'])
        
        with open('container_diagrams.json', 'w') as f:
            json.dump({'invalid_container_diagram': invalid_diagrams}, f, indent=4)
            
        print(f"\nTotal invalid diagrams found: {len(invalid_diagrams)}")
    
    overall_time = time.time() - overall_start
    print(f"\nTotal script execution time: {overall_time:.2f} seconds")
    print("Validation complete!")

if __name__ == "__main__":
    asyncio.run(main())