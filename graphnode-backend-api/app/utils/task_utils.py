from app.connection.establish_db_connection import get_mongo_db
from app.utils.node_utils import add_parent_node_id, get_child_node_count
from app.core.constants import TASK_FRAMEWORK_COLLECTION_NAME as tasks_collection_name, TaskStatus, USER_PODS_COLLECTION, KUBERNETES_MONITOR_COLLECTION_NAME as DB_NAME
from app.core.Settings import settings
from pymongo import MongoClient
import uuid
from pydantic import BaseModel, Field
from typing import List
from datetime import datetime, timedelta

# Initialize MongoDB client for pod assignment
MONGO_URI = settings.MONGO_CONNECTION_URI

# Create MongoDB client
client = MongoClient(
    MONGO_URI,
    maxPoolSize=10,
    connect=False,
    socketTimeoutMS=2000,
    serverSelectionTimeoutMS=2000
)
mongo_db_init = client[DB_NAME]

class CheckPoint(BaseModel):
    """
    Pydantic model representing a git commit checkpoint.
    """
    hash: str = Field(description="The git commit hash")
    message: str = Field(description="The commit message")
    date: str = Field(description="The date of the commit")
    author: str = Field(description="The author of the commit")
    path: str = Field(description="The path of the commit")

class CheckPoints(BaseModel):
    """
    Pydantic model containing a list of git commit checkpoints.
    """
    check_points: List[CheckPoint] = Field(description="The list of git commit checkpoints")


def generate_task_id(prefix:str):
    return f"{prefix}{uuid.uuid4().hex[:8]}"


def build_hierarchy(data):
    hierarchy = {}
    
    for node_id, node in data.items():
        parent_id = node.get("parent_node_id")
        if parent_id is None:
            continue
        parent_id = str(parent_id)
        if parent_id not in hierarchy:
            hierarchy[parent_id] = []
        hierarchy[parent_id].append(node_id)
    return hierarchy
    
def format_response(data):
    hierarchy = build_hierarchy(data)

    skipped = []
    printed = []
    final_format = {
        
    }
                
    def check_for_children(node_id, key ,hierarchy,level=1)-> list:
        if str(node_id) not in hierarchy:
            return []
        children = hierarchy[node_id]
        
        # print( node_id, f"level {level}->",children)
        final_format[node_id]=children
        for child in children:
            printed.append(child)
            check_for_children(child, key ,hierarchy,level+1)

    for key, value in data.items():
        
        if not hierarchy.get(key):
            skipped.append(key)
            continue
        
        if key in printed:
            continue
        # print(key,"children 0 ->" , hierarchy.get(key))
        if key not in final_format:
            final_format[key]={
                
            }
        final_format[key]= hierarchy.get(key)
        if hierarchy.get(key):

            for id in hierarchy.get(key):
                printed.append(id)
                children = check_for_children(id,key,hierarchy,level=1)

    skipped = list(set(skipped).difference(set(printed)))
    for id in skipped:
        if id not in final_format:
            final_format[id]=[]
        
    return final_format

def format_title(data:dict) -> str:
    title= f""
    try:
        keys = list(data.keys())
        if (len(keys) > 0):
            node = data[keys[0]]
            title = f"{node.get('node_type')}: {node.get('title')}"
        return title
    except Exception as e:
        print ("Exception in formatting title",e)
        return title
    
    
CONFIG_TIMING_BY_PATTERN = {
    "monolithic": {
        "project_autoconfig": 40,
        "requirements_autoconfig": 40,
        "epic_autoconfig": 320,
        "user_story_autoconfig": 1600,
        "testcase_autoconfig": 1600,
        "component_testcase_autoconfig": 320,
        "architectural_requirements_autoconfig": 40,
        "system_context_autoconfig": 40,
        "container_autoconfig": 640,  # Simpler for monolithic
        "components_autoconfig": 1280,  # Less complex for monolithic
        "interface_autoconfig": 320,
        "design_autoconfig": 640,  # Simpler design for monolithic
        "documentation_autoconfig": 320,
        "termination": 0
    },
    "adaptive": {
        "project_autoconfig": 40,
        "requirements_autoconfig": 40,
        "epic_autoconfig": 320,
        "user_story_autoconfig": 1600,
        "testcase_autoconfig": 1600,
        "component_testcase_autoconfig": 40,
        "architectural_requirements_autoconfig": 40,
        "system_context_autoconfig": 40,
        "container_autoconfig": 120,  # More complex for adaptive
        "components_autoconfig": 40,  # Much more complex for adaptive
        "interface_autoconfig": 40,
        "design_autoconfig": 160,  # Much more complex design for adaptive
        "documentation_autoconfig": 320,
        "termination": 0
    }}

DEFAULT_CONFIG_TIMING = {
    "project_autoconfig": 3,
    "deployment_autoconfig": 4,
    "work_item_autoconfig": 2,
    "requirements_autoconfig": 5,
    "epic_autoconfig": 4,
    "user_story_autoconfig": 3,
    "testcase_autoconfig": 6,
    "component_testcase_autoconfig": 5,
    "architectural_requirements_autoconfig": 7,
    "system_context_autoconfig": 8,
    "container_autoconfig": 10,
    "components_autoconfig": 12,
    "interface_autoconfig": 6,
    "container_ops_autoconfig": 4,
    "design_autoconfig": 15,
    "documentation_autoconfig": 8,
    "termination": 0
}

async def get_project_architecture_pattern(project_id: int) -> str:
    """
    Get the architecture pattern from the project node
    
    Args:
        project_id: The project node ID
        
    Returns:
        Architecture pattern string (defaults to 'adaptive' if not found)
    """
    try:
        from app.connection.establish_db_connection import get_node_db
        db = get_node_db()
        
        project_node = await db.get_node_by_id(project_id)
        if project_node and 'properties' in project_node:
            architecture_pattern = project_node['properties'].get('ArchitecturePattern', 'adaptive')
            return architecture_pattern.lower()
        
        return 'adaptive'  # Default fallback
        
    except Exception as e:
        print(f"Error getting architecture pattern: {str(e)}")
        return 'adaptive'  # Default fallback

def get_config_duration_seconds(config_type: str, architecture_pattern: str = 'adaptive') -> int:
    """
    Get estimated duration in seconds for a config type based on architecture pattern
    
    Args:
        config_type: The configuration type
        architecture_pattern: The architecture pattern (monolithic, adaptive, etc.)
        
    Returns:
        Duration in seconds
    """
    if 'epic_autoconfig_' in config_type:
        config_type = 'epic_autoconfig'
    pattern_timing = CONFIG_TIMING_BY_PATTERN.get(architecture_pattern, CONFIG_TIMING_BY_PATTERN['adaptive'])
    return pattern_timing.get(config_type, 300)  # Default 5 minutes if not found

async def calculate_eta_info(config_types: list, completed_configs: list = None, project_id: int = None) -> dict:
    """
    Calculate simple ETA by subtracting completed config times from total
    
    Args:
        config_types: List of config types to be executed
        completed_configs: List of already completed config types
        project_id: Project ID to get architecture pattern
    
    Returns:
        Dict with ETA information
    """

    if completed_configs is None:
        completed_configs = []

    # Get architecture pattern from project
    architecture_pattern = 'adaptive'  # Default
    if project_id:
        architecture_pattern = await get_project_architecture_pattern(project_id)

    total_seconds_progress = sum(get_config_duration_seconds(config, architecture_pattern) for config in config_types)
    completed_seconds_progress = sum(get_config_duration_seconds(config, architecture_pattern) 
                           for config in completed_configs if config in config_types)

    config_types = [
        config for config in config_types
        if not (config.startswith('epic_autoconfig_'))
    ]
    
    total_seconds = sum(get_config_duration_seconds(config, architecture_pattern) for config in config_types)
    completed_seconds = sum(get_config_duration_seconds(config, architecture_pattern) 
                           for config in completed_configs if config in config_types)
    remaining_seconds = total_seconds - completed_seconds
    
    progress_percentage = (completed_seconds_progress / total_seconds_progress * 100) if total_seconds > 0 else 0
    
    return {
        "architecture_pattern": architecture_pattern,
        "total_seconds": total_seconds,
        "remaining_seconds": remaining_seconds,
        "completed_seconds": completed_seconds,
        "total_minutes": round(total_seconds / 60, 1),
        "remaining_minutes": round(remaining_seconds / 60, 1),
        "completed_minutes": round(completed_seconds / 60, 1),
        "progress_percentage": round(progress_percentage, 1),
        "formatted_eta": format_eta_time(remaining_seconds)
    }

def format_eta_time(seconds: int) -> str:
    """Format seconds into human-readable time"""
    if seconds <= 0:
        return "Completing..."
    elif seconds < 60:
        return f"{seconds} seconds"
    elif seconds < 3600:  # Less than 1 hour
        minutes = seconds // 60
        remaining_seconds = seconds % 60
        if remaining_seconds == 0:
            return f"{minutes} minute{'s' if minutes != 1 else ''}"
        else:
            return f"{minutes}m {remaining_seconds}s"
    else:  # 1 hour or more
        hours = seconds // 3600
        remaining_minutes = (seconds % 3600) // 60
        if remaining_minutes == 0:
            return f"{hours} hour{'s' if hours != 1 else ''}"
        else:
            return f"{hours}h {remaining_minutes}m"

async def configuration_update(task_id: str):
    """Enhanced configuration_update with architecture pattern-based ETA"""
    db = get_mongo_db().db
    result = db[tasks_collection_name].find_one({"_id": task_id})
    if result is None:
        return None
    
    # Get basic task info (existing code)
    progress = result.get('progress', 0)
    task_status = result.get('status', 'In Progress')
    cancel = result.get('cancel', False)
    run_completed = result.get('run_completed', False)
    configuration_status = result.get("configuration_status", {})
    
    title = format_title(configuration_status)
    configuration_status = await add_parent_node_id(configuration_status)
    formatted_response = format_response(configuration_status)
    
    # Get project ID and config types
    project_id = result.get("node_id")  # Assuming this is the project ID
    selected_configs = result.get("config_types", [])
    
    # Determine completed configs from configuration_status (existing logic)
    completed_configs = []
    
    # Mapping between node_types and config_types (existing code)
    config_mapping = {
        'Project': 'project_autoconfig',
        'Requirement': 'requirements_autoconfig',
        'ArchitecturalRequirement': 'architectural_requirements_autoconfig',
        'SystemContext': 'system_context_autoconfig',
        'Container': 'container_autoconfig',
        'Component': 'components_autoconfig',
        'Epic': 'epic_autoconfig',
        'UserStory': 'user_story_autoconfig',
        'Interface': 'interface_autoconfig',
        'Architecture': 'design_autoconfig',
        'Documentation': 'documentation_autoconfig'
    }
    
    # Special cases for configs that use different keys in configuration_status
    special_config_fields = {
        'architectural_requirements_autoconfig': 'architecture_requirement',
        'interface_autoconfig': 'design_details',
        'design_autoconfig': 'design_details'
    }
    
    # Keep track of which config types have been processed
    processed_configs = set()
    
    # Check configuration_status for completed configs (existing logic)

    epic_count = 0
    if 'epic_autoconfig' in selected_configs:
        _project_id = result.get('project_id','') or result.get('node_id','')
        epic_count = await get_child_node_count(int(_project_id), 'HAS_CHILD', 'RequirementRoot', 'Epic')

        for n in range(epic_count) :
            conf = 'epic_autoconfig' + '_' + str(n)
            if conf not in selected_configs:
                selected_configs.append(conf)        

    conf_epic_count = 0 
    for node_id, node_data in configuration_status.items() :
        node_type = node_data.get('node_type')
        if node_type not in config_mapping:
            continue
            
        config_type = config_mapping[node_type]
        
        if config_type not in selected_configs:
            continue
        config_field = special_config_fields.get(config_type, 'configuration')
        
        if node_data.get(config_field) == 'configured':
            if node_type == 'Epic' :
                # if node_data.get('title', 'None') not in processed_configs:
                #    _count += 1
                conf = config_type + '_' + str(conf_epic_count)  
                conf_epic_count += 1

                if conf not in processed_configs:
                    completed_configs.append(conf)
                    processed_configs.add(conf)

                # for n in range(epic_count) :
                #     conf = config_type + '_' + str(n)
                #     if conf not in processed_configs:
                #         completed_configs.append(conf)
                #         processed_configs.add(conf)
                
            if config_type not in processed_configs:
                completed_configs.append(config_type)
                processed_configs.add(config_type)
    
    # NEW: Calculate ETA information with architecture pattern
    eta_info = await calculate_eta_info(selected_configs, completed_configs, project_id)
    # Use ETA progress if it's more accurate than the existing calculation
    if eta_info['progress_percentage'] > 0:
        progress = eta_info['progress_percentage']

        # Fallback to existing progress calculation
        total_configs = len(selected_configs)+1
        if epic_count :
            total_configs = total_configs + epic_count
        completed_count = len(completed_configs)

        print(f"PROGRESS CALCULATION 378: {completed_count}/{total_configs} SELECTED: {selected_configs} COMPLETED: {completed_configs} ")

    elif selected_configs:
        total_configs = len(selected_configs)
        if epic_count :
            total_configs = total_configs + epic_count

        completed_count = len(completed_configs)
        
        if total_configs > 0:
            # Scale progress so maximum is 90% (not 100%)
            # This ensures that even when all configs are complete, progress is 90%
            base_progress = (completed_count / total_configs) * 90  # Scale to 90% instead of 100%
            
            # Set minimum progress to 5% once started, but don't exceed the scaled maximum
            if completed_count > 0:
                progress = max(base_progress, 5)
            else:
                progress = 0
                
            progress = int(round(progress))  # Round to nearest integer
            
            # Log progress calculation for debugging
            print(f"PROGRESS CALCULATION 404: {completed_count}/{total_configs} configs completed = {base_progress:.1f}% (final: {progress}%)")

    # Handle terminal states (existing code)
    if run_completed or task_status.casefold() in [TaskStatus.FAILED.casefold(), TaskStatus.CANCELLED.casefold()]:
        if result.get('task_status',"") == TaskStatus.FAILED:
            task_status = TaskStatus.FAILED
        elif result.get('task_status',"") == TaskStatus.COMPLETE:
            progress = 100
            task_status = TaskStatus.COMPLETE
            eta_info.update({
                "remaining_seconds": 0,
                "remaining_minutes": 0,
                "progress_percentage": 100,
                "formatted_eta": "Completed"
            })
        elif result.get('task_status',"") == TaskStatus.CANCELLED:
            progress = 100
            task_status = TaskStatus.CANCELLED
        else:
            progress = 100
            task_status = TaskStatus.COMPLETE

    # Save ETA info to database
    try:
        db[tasks_collection_name].update_one(
            {"_id": task_id},
            {
                "$set": {
                    "eta_info": eta_info,
                    "eta_last_updated": datetime.now().isoformat()
                }
            }
        )
        print(f"ETA info saved for task {task_id}")
    except Exception as e:
        print(f"Error saving ETA info: {str(e)}")
    # Return response with architecture pattern-based ETA information
    return {
        "task_id": task_id,
        "title": title,
        "configuration_status": configuration_status,
        "formatted_response": formatted_response,
        "progress": progress,
        "status": task_status,
        "cancel": cancel,
        "run_completed": run_completed,
        "config_types": selected_configs,
        "completed_configs": completed_configs,
        "eta_info": eta_info,
        "has_eta": True
    }

def get_codegen_pod(tenant_id: str, project_id: int, stage: str):
    """
    Get the codegen pod for a given tenant and project.
    
    Args:
        tenant_id: The tenant ID
        project_id: The project ID
        stage: The deployment stage
        
    Returns:
        str: The codegen pod identifier
    """
    unique_id = tenant_id + str(project_id)
    existing_assignment = mongo_db_init[USER_PODS_COLLECTION].find_one({"unique_id": unique_id})
    pod_name = existing_assignment.get("pod_name")
    pod_parts = pod_name.split("-")
    codegen_pod = pod_parts[0]
        
    return codegen_pod

def get_codegen_url(stage: str, pod_prefix: str):
    """
    Generate the codegen URL based on tenant ID, project ID, and stage.
    
    Args:
        tenant_id: The tenant ID
        project_id: The project ID  
        stage: The deployment stage
        
    Returns:
        str: The codegen URL
    """
    codegen_pod = pod_prefix
    
    # Handle None or empty stage - default to 'dev'
    if not stage:
        stage = "dev"
    
    if stage == "dev":
        return f'http://internal-{codegen_pod}-dev.duploservices-k-dev01.svc.cluster.local:8003/api/code_gen'
    elif stage == "qa":
        return f'http://internal-{codegen_pod}-qa.duploservices-k-qa01.svc.cluster.local:8003/api/code_gen'
    elif stage == "pre_prod":
        return f'http://internal-{codegen_pod}-beta.duploservices-k-beta01.svc.cluster.local:8003/api/code_gen'
    
    return f'http://internal-{codegen_pod}-{stage}.duploservices-k-dev01.svc.cluster.local:8003/api/code_gen'

