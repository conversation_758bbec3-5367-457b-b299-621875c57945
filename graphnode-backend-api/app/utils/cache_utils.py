from cachetools import cached, TTL<PERSON>ache
from functools import wraps
import hashlib
import pickle

def async_ttl_cache(ttl=0.1, maxsize=128):
    cache = TTLCache(maxsize=maxsize, ttl=ttl)
    
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Create a more reliable cache key
            try:
                # For database methods, we want to cache based on:
                # 1. Function name
                # 2. Actual arguments (excluding 'self' or database object)
                # 3. Keyword arguments
                
                # Skip the first argument if it's likely a 'self' or database object
                cache_args = args[1:] if args and hasattr(args[0], '__dict__') else args
                
                # Create a hashable key
                key_parts = [
                    func.__name__,
                    str(cache_args),
                    str(sorted(kwargs.items()))
                ]
                key = '|'.join(key_parts)
                
                # Use hash for very long keys to avoid memory issues
                if len(key) > 250:
                    key = hashlib.md5(key.encode()).hexdigest()
                
            except Exception:
                # Fallback: create a simple hash-based key
                try:
                    key_data = (func.__name__, args[1:], tuple(sorted(kwargs.items())))
                    key = str(hash(key_data))
                except Exception:
                    # Final fallback: just use function name and argument count
                    key = f"{func.__name__}_{len(args)}_{len(kwargs)}"
            
            # Check cache
            if key in cache:
                return cache[key]
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            cache[key] = result
            return result
            
        return wrapper
    return decorator