import time
from app.connection.establish_db_connection import get_mongo_db
from app.connection.establish_db_connection import get_node_db, NodeDB ,get_vector_db  , connect_mongo_db, get_tenant_id
from app.connection.llm_init import get_llm_interface
from app.utils.auth_utils import get_current_user
from fastapi import APIRouter, Depends, Body , Query, Request, responses, HTTPException , BackgroundTasks
from typing import List, Union, Annotated, Dict, Optional
from fastapi.responses import J<PERSON>NResponse, StreamingResponse
from app.utils.k8_job import K8JobManager
from app.utils.node_utils import node_types, root_node_types, clone_node, create_node as node_creation 
from app.utils.node_utils import get_node_type
from datetime import datetime
import json
from app.core.constants import TASKS_COLLECTION_NAME as tasks_collection_name
from app.models.node_model import CreateNodeRequest, PropertyUpdate
from app.core.constants import NodeType, Node<PERSON>abel
from app.utils.user_utils import track_project_usage , delete_project_usage , delete_notifications
from app.core.function_schema_generator import load_json_file
from pydantic import BaseModel
from enum import Enum
from app.core.data_model_helper import data_model
from app.routes.users_route import get_user_by_id
from app.classes.Ec2Handler import Ec2Handler
from app.core.Settings import settings
from app.utils.project_utils import get_stage, safe_name
from typing import Dict, List, Any
from collections import defaultdict
from app.utils.datetime_utils import generate_timestamp
from typing import AsyncGenerator
from llm_wrapper.core.llm_interface import LLMInterface
import os
from app.utils.hash import decrypt_data
import asyncio
from app.utils.stream_utils import format_response_for_stream
from app.routes.batch_route import stream_start_workspace_status
from pydantic import BaseModel, Field
from typing import Optional
from app.models.project_model import ProjectVisibilityRequest, PublicProjectModel, ProjectInfoManual, ProjectInfoDirectCodeGen, ArcitectureInfo, ContainerDetails, ProjectOverview, ContainerInfo, StartProjectInit, EnhancedProjectStructure
from app.classes.MongoDB import MongoDBHandler
from app.connection.tenant_middleware import KAVIA_ROOT_DB_NAME
from app.utils.k8.create_project_v2 import create_project_for_dev, create_project_for_qa, create_project_for_beta
from app.utils.logs_utils import get_path
from app.models.user_model import LLMModel
from fastapi.responses import RedirectResponse, HTMLResponse
from code_generation_core_agent.agents.project_welcome_page import create_project_welcome_page, ContainerType, ProjectSchema
from app.utils.code_generation_utils import get_efs_manifest_path, convert_manifest_to_yaml_string
from app.utils.hash import decrypt_string

_SHOW_NAME = "node"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)




llm_interface = get_llm_interface()

class QueryType(str, Enum):
    REQUIREMENT = "requirement"
    ARCHITECTURE = "architecture"
    DEPLOYMENT = "deployment"

class Framework(str, Enum):
    REACT = "react"
    # ANGULAR = "angular"
    VUE = "vue"
    # NEXTJS = "nextjs"
    # SVELTE = "svelte"
    # LARAVEL = "laravel"

class Platform(str, Enum):
    COMMON = "common"
    WEB = "web"
    ANDROID = "android"
    IOS = "ios"

class ProjectProperties(BaseModel):
    """Model representing the properties of a project."""
    created_by: Optional[str] = None
    Description: Optional[str] = None
    is_active: Optional[bool] = None
    status: Optional[str] = None
    Requirement: Optional[str] = None
    Type: Optional[str] = None
    created_at: Optional[str] = None
    Title: Optional[str] = None


class ProjectNodes(BaseModel):
    """Model representing a project node from the database."""
    id: Optional[int] = None
    labels: Optional[List[str]] = None
    properties: Optional[ProjectProperties] = None

class FeaturesInfo(BaseModel):
    id: str
    name: str
    description: str
    isEnabled: bool
class InitProjectData(BaseModel):
    """Model representing a project node from the database."""
    description: Optional[str] = None
    features: List[FeaturesInfo] = None
    layoutDescription: Optional[str] = None

class TechStack(BaseModel):
    frontend: List[str]
    backend: List[str]
    language: List[str]

class Colors(BaseModel):
    primary: str
    secondary: str
    accent: str

class Blueprint(BaseModel):
    projectInfo: Optional[ProjectNodes] = None
    id: Optional[str] = None
    name: Optional[str] = None
    description: Optional[str] = None
    features: Optional[List[FeaturesInfo]] = None
    techStack: Optional[TechStack] = None
    colors: Optional[Colors] = None
    theme: Optional[str] = None
    estimatedTime: Optional[str] = None
    complexity: Optional[str] = None
    layoutDescription: Optional[str] = None

class ProjectRequest(BaseModel):
    requirement: str = Field(description="Project requirement", examples=["Create a todo list application"])
    framework: str = Framework.REACT.value
    platform: str = Platform.WEB.value
    Supabase_project_id: Optional[str] = None
    advanced_applications: bool = Field(default=False, description="Flag for advanced applications processing")
    blueprint: Optional[Blueprint] = Field(default=None, description="Blueprint info Init_project_info , projectInfo")
    encrypted_scm_id: Optional[str] = Field(default=None, description="Github connection ID, projectInfo")

class CloneNode(BaseModel):
    title: Optional[str] = None

class CountResponse(BaseModel):
    counts: Dict[str, int]
@router.get("/")
async def get_nodes(node_type:str, db = Depends(get_node_db) ):
    print("Received query for node type:", node_type)
    if node_type not in node_types and node_type not in root_node_types:
        raise HTTPException(status_code=400, detail="Invalid node type")

    try:
        nodes = await db.get_nodes_by_label(node_type)
        return nodes
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

from typing import Dict, List, Any
from collections import defaultdict
from pydantic import BaseModel
from typing import Literal

def format_flowline_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Formats flowline data by sorting sections and transforming structure from {Root, Title} to [title, title].
    
    Args:
        data: Dictionary containing architecture components and other data
        
    Returns:
        Dictionary with sorted and restructured elements
    """
    
    if "architecture" not in data:
        return data
    def transform_and_sort_section(items: List[Dict[str, str]]) -> List[List[str]]:
        """Helper function to transform and sort items"""
        grouped_items = defaultdict(list)
        
        for item in items:
            root = item['Root'].lower()
            # Transform from {Root, Title} to [title, title]
            transformed_item = item['Title']
            grouped_items[root].append(transformed_item)
            
        sorted_roots = sorted(grouped_items.keys())
        sorted_items = []
        for root in sorted_roots:
            sorted_items.extend(grouped_items[root])
            
        return sorted_items

    def sort_container_list(containers: List[str]) -> List[str]:
        """Helper function to sort container list"""
        return sorted(containers, key=str.lower)

    # Create a deep copy of the input data
    result = data.copy()
    result['architecture'] = data['architecture'].copy()
    # Transform and sort sections
    if 'component' in result['architecture'] and result['architecture']['component']!=[]:
        result['architecture']['component'] = transform_and_sort_section(data['architecture']['component'])
    # Handle container section - filter None values before sorting
    if 'container' in result['architecture']:
        # First filter out None values
        result['architecture']['container'] = [
            c for c in result['architecture']['container'] 
            if c is not None
        ]
        # Then sort the remaining valid containers
        if result['architecture']['container']:  # Only sort if list is not empty
            result['architecture']['container'] = sort_container_list(
                result['architecture']['container']
            )
    if 'Requirement' in result['architecture'] and result['architecture']['Requirement']!=[]:
        result['architecture']['Requirement'] = {
            'Description': data['architecture']['Requirement']['Description'],
            'functional_requirements': data['architecture']['Requirement']['functional_requirements'],
            'architectural_requirements': data['architecture']['Requirement']['architectural_requirements']
        }
    if 'systemContext' in result['architecture']:
        result['architecture']['systemContext'] = {
            'Description': data['architecture']['systemContext']['Description'],
            'Users': data['architecture']['systemContext']['Users'],
            'External Systems': data['architecture']['systemContext']['ExternalSystems'],
            'System ContextDiagram': data['architecture']['systemContext']['SystemContextDiagram'],
            'ContainerDiagram': data['architecture']['systemContext']['ContainerDiagram'],

        }    
    if 'interface' in result['architecture'] and result['architecture']['interface']!=[]:
        result['architecture']['interface'] = transform_and_sort_section(data['architecture']['interface'])
    
    if 'design' in result['architecture'] and result['architecture']['design']!=[]:
        result['architecture']['design'] = transform_and_sort_section(data['architecture']['design'])
    print(data)
    return result

class ProjectContainerRequest(BaseModel):
    project_id: str
    env_name: str
    action: Literal["create", "delete"]
    
@router.post("/project-container")
async def manage_project_container(
    request: ProjectContainerRequest,
    current_user = Depends(get_current_user)
):
    """
    Create or delete a project container using Kubernetes jobs
    """
    try:
        k8_job_manager = K8JobManager()
        job_name = k8_job_manager.create_project_container_job(
            project_id=request.project_id,
            env_name=request.env_name,
            action=request.action
        )
        
        if job_name:
            return {
                "status": "success",
                "message": f"Job {job_name} created successfully",
                "job_name": job_name
            }
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to create Kubernetes job"
            )
            
    except ValueError as ve:
        raise HTTPException(
            status_code=400,
            detail=str(ve)
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred: {str(e)}"
        )
        
@router.get("/properties/{node_id}", summary="Fetch node properties")
async def fetch_node_properties(
    node_id: int,
    node_type: str,
    db: NodeDB = Depends(get_node_db),
    current_user = Depends(get_current_user)
):
    print(f"Fetching properties for node with ID: {node_id}")
    node_type = get_node_type(node_type)
    if node_type not in NodeType.__members__.values():
        raise HTTPException(status_code=400, detail="Invalid node type")
    
    try:
        node = await db.get_node_properties_by_id(node_id, node_type=node_type)
        if node:
            node["ui_metadata"] = data_model["model"][node_type]["ui_metadata"]
            return node
        else:
            return JSONResponse(content={"message": "Node not found"}, status_code=404)
    except Exception as e:
        print("Exception: ", e)
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")
    
    
@router.get("/{node_id}")  # Adjust response_model as necessary
async def get_node(
    node_id: int, 
    node_type: str, 
    db: NodeDB =Depends(get_node_db), 
    current_user=Depends(get_current_user),
    background_tasks: BackgroundTasks = BackgroundTasks()  # Added for background tasks
):
    print("Fetching node with ID:", node_id)
    node_type = get_node_type(node_type)

    if node_type not in NodeType.__members__.values():
        
        raise HTTPException(status_code=400, detail="Invalid node type")
    try:
        node = await db.get_node_by_id(node_id, node_type = node_type)
        if node:
            # Trigger project usage tracking in the background (optional)
            if get_node_type(node_type) in ["Project", "Product"] and current_user:
                try:
                    background_tasks.add_task(
                        track_project_usage, 
                        user_id=current_user.get("cognito:username"),
                        project_id=node_id,
                        project_name=node.get("properties", {}).get("Title",node.get("properties", {}).get("Name"))
                    )
                    # ec2_handler = Ec2Handler()
                    # print("Invoking ec2 instance")
                    # tenant_id = current_user.get("custom:tenant_id")
                    # project_id = node_id
                    # stage = get_stage() 
                    # instance_name =f"{tenant_id}-{project_id}-{stage}"
                    # background_tasks.add_task(
                    #     ec2_handler.wake_up_instance,
                    #     instance_name=instance_name)
                except Exception as e:
                    print("Error tracking project usage: ", str(e))
                    pass
                    
                
            # print(node.get("properties"))

            node["ui_metadata"] = data_model["model"][node_type]["ui_metadata"]
            return node
        else:
            return JSONResponse(content={"message": "Node not found"})
    except Exception as e:
        print("Exception: ", e)
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")

@router.get("/v2/data_model/{node_id}")
async def get_node_v2(
    node_id: int, 
    node_type: str, 
    exclude_relationships: bool = Query(False, description="Exclude relationships from the response"),
    skip_ui_metadata: bool = Query(False, description="Skip UI metadata from the response"),
    db: NodeDB = Depends(get_node_db),
    current_user = Depends(get_current_user),
    background_tasks: BackgroundTasks = BackgroundTasks()  # Added for background tasks
    ):
    node_type = get_node_type(node_type)
    if node_type not in NodeType.__members__.values():
        raise HTTPException(status_code=400, detail="Invalid node type")
    
    # if get_node_type(node_type) in ["Project", "Product"]:
    #     try:
    #         ec2_handler = Ec2Handler()
    #         print("Invoking ec2 instance")
    #         tenant_id = current_user.get("custom:tenant_id")
    #         project_id = node_id
    #         stage = get_stage() 
    #         instance_name =f"{tenant_id}-{project_id}-{stage}"
    #         background_tasks.add_task(
    #             ec2_handler.wake_up_instance,
    #             instance_name=instance_name)
    #     except Exception as e:
    #         print("Error tracking project usage: ", str(e))
    #         pass
    node = await db.get_node_based_on_data_model(node_id, node_type, data_model, exclude_relationships=exclude_relationships,skip_ui_metadata=skip_ui_metadata)
    
    if node is None:
        raise HTTPException(status_code=404, detail="Node not found")
    
    return node
        
@router.get("/list_projects/")
async def get_projects(
    page: int = Query(default=1, ge=1, description="Page number (starts from 1)"),
    page_size: int = Query(default=10, ge=1, le=100, description="Number of items per page"),
    search: Optional[str] = Query(default=None, description="Search term for project titles"),
    creator_filter: Optional[str] = Query(default=None, description="Filter by creator name"),
    db: NodeDB = Depends(get_node_db), 
    current_user = Depends(get_current_user)
):
    """
    Get projects with pagination, search, and filtering
    
    Query Parameters:
    - page: Page number (default: 1)
    - page_size: Items per page (default: 10, max: 100)
    - search: Search term for project titles
    - creator_filter: Filter by creator name
    """
    try:
        tenant_id = current_user.get("custom:tenant_id")
        if tenant_id.startswith("default"):
            tenant_id = settings.KAVIA_B2C_CLIENT_ID
        
        email = current_user.get("email")
        
        # Calculate skip value for pagination
        skip = (page - 1) * page_size
        
        # For B2C users, add email filter to the database query
        email_filter = None
        if tenant_id == settings.KAVIA_B2C_CLIENT_ID:
            email_filter = email
        
        # Get projects with pagination (now includes email filtering)
        result = await db.get_projects_with_pagination(
            skip=skip, 
            limit=page_size, 
            search=search, 
            creator_filter=creator_filter,
            email_filter=email_filter  # Pass email filter to DB query
        )
        
        return {
            "success": True,
            "data": {
                "projects": result["projects"],
                "pagination": result["pagination"]
            }
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "data": {
                "projects": [],
                "pagination": {
                    "total_count": 0,
                    "total_pages": 0,
                    "current_page": page,
                    "page_size": page_size,
                    "has_next": False,
                    "has_previous": False,
                    "start_index": 0,
                    "end_index": 0
                }
            }
        }
@router.get("/project-created-users/")
async def get_project_creators(
    db: NodeDB = Depends(get_node_db), 
    current_user = Depends(get_current_user)
):
    """
    Get unique list of project creators for filtering
    """
    try:
        tenant_id = current_user.get("custom:tenant_id")
        if tenant_id.startswith("default"):
            tenant_id = settings.KAVIA_B2C_CLIENT_ID
        
        email = current_user.get("email")
        
        query = """
        MATCH (p:Project)
        WHERE p.is_active = True
        OPTIONAL MATCH (u:User) WHERE u.Username = p.created_by
        WITH DISTINCT u.Name AS creator_name, u.Email AS creator_email
        WHERE creator_name IS NOT NULL
        RETURN creator_name
        ORDER BY creator_name
        """
        
        result = await db.async_run(query)
        creators_data = result.data() if result else []
        
        creators = [item["creator_name"] for item in creators_data]
        
        # Filter for B2C users - only show current user as creator
        if tenant_id == settings.KAVIA_B2C_CLIENT_ID:
            user_query = """
            MATCH (u:User) WHERE u.Email = $email
            RETURN u.Name AS creator_name
            """
            user_result = await db.async_run(user_query, email=email)
            user_data = user_result.data()
            creators = [user_data[0]["creator_name"]] if user_data and user_data[0]["creator_name"] else []
        
        return {
            "success": True,
            "data": {
                "creators": creators
            }
        }
        
    except Exception as e:
        print(f"Error in get_project_creators route: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"Failed to fetch creators: {str(e)}"
        )
  
# Route for change visibility
  
@router.post("/")
async def create_node(request: dict, current_user = Depends(get_current_user)):
    print(type(request))
    name = request.get('name')
    description = request.get('description')
    node_type = request.get('node_type')
    properties = request.get('properties', {})
    
    properties['created_by'] = current_user.get('cognito:username')
    properties['created_at'] = generate_timestamp()
    
    if node_type not in NodeType.__members__.values():
        raise HTTPException(status_code=400, detail="Invalid node type")

    try:
        return await node_creation(node_type, name, description, properties)
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")

@router.post("/v2/")
async def create_node_v2(request: CreateNodeRequest, db:NodeDB = Depends(get_node_db)):
  
    
    properties = request.model_dump()
    node_type = properties.pop('node_type')
    node_type = get_node_type(node_type)
    parent_id = properties.pop('parent_id', None)
    
    properties['created_at'] = generate_timestamp()
    properties['updated_at'] = generate_timestamp()
    
    labels = []
    if node_type == "Design":
        # There must be only one design node per architecture
        get_child_nodes = await db.get_child_nodes(parent_id, "Design")
        if get_child_nodes:
            return JSONResponse(content="Design node already exists", status_code=400)
                
    if node_type == "Epic":
        labels = NodeLabel.EPIC.value
    elif node_type == "UserStory":
        labels = NodeLabel.USERSTORY.value
    elif node_type == "Task":
        labels = NodeLabel.TASK.value
    else:
        labels = [node_type]
        
    labels = list(set(labels))
    result = await db.create_node( labels , properties, parent_id)
    return result
     
@router.patch("/{node_id}")
async def update_node(node_id: int, request: dict, current_user = Depends(get_current_user)):
    db = get_node_db()
    vector_db = get_vector_db()
    print("Received request to update node", node_id)
    node_type = request.get('node_type')
    name = request.get('name')
    description = request.get('description')
    scope = request.get('scope')
    node_types_to_omit = ['Discussion', 'User'] # Nodes that should not be updated in the vector database FIXME: This should be a configuration
    if not node_id or not node_type:
        raise HTTPException(status_code=400, detail='Node id or type not provided')

    if node_type not in node_types:
        raise HTTPException(status_code=400, detail='Invalid node type')
    properties = {
        'Name': name,
        'Title': name, 
        'Description': description,
        'Scope':scope
    }
    
    print("properties", properties)
    properties['updated_by'] = current_user.get('cognito:username')
    properties['updated_at'] = generate_timestamp()
    
    node = await db.update_node_by_id(node_id, properties, node_type)

    # Update the node in the vector database. For that first get the full set of properties for the node
    if node_type not in node_types_to_omit:
        # await db.add_embedding_to_node(node_id, node.get('properties'))
        await vector_db.update_node_in_vector_db(node_id, node.get('properties'), node_type)

    if node:

        return node

    raise HTTPException(status_code=500, detail='Failed to update node')

@router.patch("/v2/{node_id}")
async def update_node_properties(
    node_id: int,
    node_type: str,
    properties: dict = Body(...),
    current_user: dict = Depends(get_current_user)
):
    db = get_node_db()
    
    if not node_id or not node_type:
        raise HTTPException(status_code=400, detail='Node id or type not provided')
    node_type = get_node_type(node_type)
    
    if node_type not in NodeType.__members__.values():
        raise HTTPException(status_code=400, detail="Invalid node type")
    
    properties['updated_by'] = current_user.get('cognito:username')
    properties['updated_at'] = generate_timestamp()
    
    node = await db.update_node_by_id(node_id, properties, node_type)
    
    return node


# Architecture pattern to container mapping

ARCHITECTURE_CONTAINER_MAPPING = {

    "monolithic-application": {

        "containers": ["application"],

        "description": "Single unified application container"

    },

    "monolithic-service": {

        "containers": ["service"],

        "description": "Single unified service container with API exposure"

    },

    "multi-container-single-component": {

        "containers": ["frontend", "backend", "database"],

        "description": "Multiple containers, each with single focused component"

    },

    "multi-container-service": {

        "containers": ["frontend", "backend", "database"],

        "description": "Multiple service containers with well-defined interfaces"

    },

    "adaptive": {

        "containers": ["frontend", "backend", "database", "api-gateway", "cache"],

        "description": "Flexible multi-container architecture"

    }

}



def _build_enhanced_system_prompt(request: StartProjectInit) -> str:

    """Build the enhanced system prompt with architecture-specific container guidance."""

    

    # Determine architecture pattern

    architecture_pattern = _determine_architecture_pattern(request)

    container_guidance = _get_container_guidance(architecture_pattern)

    

    base_prompt = f"""You are an expert software architect. 

Your task is to review the input given by the user about a project and generate a structured Python dictionary that describes the project in detail.



ARCHITECTURE PATTERN: {architecture_pattern}

{container_guidance}



The dictionary should contain the following keys based on the architecture pattern: "overview", and container-specific keys.



REQUIRED STRUCTURE:

- "overview": Always required with project metadata

- Container keys: Based on architecture pattern ({', '.join(ARCHITECTURE_CONTAINER_MAPPING[architecture_pattern]['containers'])})



Each container should follow this structure:

{{

    "container_name": "descriptive-name-kebab-case",

    "description": "Clear description of container purpose",

    "interfaces": "Description of how this container interfaces with others",

    "dependent_containers": ["list", "of", "container", "names", "this", "depends", "on"],

    "container": {{ // Only for frontend containers

        "features": ["list", "of", "features"],

        "colors": {{

            "primary": "#HEX",

            "secondary": "#HEX", 

            "accent": "#HEX",

            "background": "#HEX"

        }},

        "theme": "light|dark|custom|selectable",

        "layoutDescription": "UI layout description",

        "style": "modern|minimalistic|material|etc"

    }}

}}



CONTAINER DEPENDENCY RULES:

- Frontend typically depends on: ["backend"]

- Backend typically depends on: ["database"] 

- Database typically depends on: []

- API Gateway depends on: ["backend"]

- Cache can depend on: ["database"] or be standalone"""

    

    # Add framework context if available

    # if request.overview_data:

    if hasattr(request, 'overview_data') and request.overview_data:    

        framework_context = f"""

        

FRAMEWORK PREFERENCES:

- Frontend: {request.overview_data.get('frontend_framework', 'Auto-select')}

- Backend: {request.overview_data.get('backend_framework', 'Auto-select')}  

- Database: {request.overview_data.get('database_framework', 'Auto-select')}

        """

        base_prompt += framework_context

    

    return base_prompt



def _determine_architecture_pattern(request: StartProjectInit) -> str:

    """Determine architecture pattern based on request type and complexity."""

    if request.type == 'new_project_manual':

        # For manual projects, default to multi-container-service for flexibility

        return "multi-container-service"

    

    # Analyze user input for complexity indicators

    user_input = request.usercomment.lower()

    complexity_indicators = {

        'simple': ['simple', 'basic', 'minimal', 'single page'],

        'api': ['api', 'service', 'backend', 'rest', 'graphql'],

        'multi': ['dashboard', 'admin', 'complex', 'microservice', 'scalable']

    }

    

    if any(indicator in user_input for indicator in complexity_indicators['multi']):

        return "multi-container-service"

    elif any(indicator in user_input for indicator in complexity_indicators['api']):

        return "multi-container-single-component"

    elif any(indicator in user_input for indicator in complexity_indicators['simple']):

        return "monolithic-application"

    else:

        return "multi-container-single-component"  # Default



def _get_container_guidance(architecture_pattern: str) -> str:

    """Get specific guidance for container structure based on architecture pattern."""

    guidance_map = {

        "monolithic-application": """

CONTAINER STRUCTURE:

- Single "application" container containing all functionality

- No external API exposure, direct UI interactions only

- Self-contained with embedded database if needed""",

        

        "monolithic-service": """

CONTAINER STRUCTURE:

- Single "service" container with unified functionality

- Must expose well-defined external APIs

- Designed for service integration""",

        

        "multi-container-single-component": """

CONTAINER STRUCTURE:

- "frontend" container: User interface and client-side logic

- "backend" container: Business logic and API layer

- "database" container: Data persistence layer

- Each container has exactly one focused component

- Clear interfaces between containers""",

        

        "multi-container-service": """

CONTAINER STRUCTURE:

- "frontend" container: Complete frontend service with UI/UX

- "backend" container: Business logic service with API exposure

- "database" container: Data persistence service

- Each container is a complete service with well-defined provider interfaces

- Independent deployment and scaling per service""",

        

        "adaptive": """

CONTAINER STRUCTURE:

- "frontend" container: User interface service

- "backend" container: Core business logic service  

- "database" container: Data persistence service

- "api-gateway" container: Request routing and authentication

- "cache" container: Performance optimization layer

- Flexible component organization within containers"""

    }

    

    return guidance_map.get(architecture_pattern, guidance_map["multi-container-single-component"])



def _get_project_name(request: StartProjectInit) -> str:

    """Generate appropriate project name based on request type."""

    if request.type == 'new_project_manual' and request.title:

        return request.title

    elif request.overview_data and request.overview_data.get('project_name'):

        return request.overview_data['project_name']

    else:

        return f"Project-{generate_timestamp()}"



def _build_initial_properties(request: StartProjectInit, current_user: Dict, temp_project_name: str) -> Dict[str, Any]:

    """Build initial node properties."""

    return {

        'Title': temp_project_name,

        'Description': request.usercomment,

        'Type': NodeType.PROJECT.value.lower(),

        'created_by': current_user.get('cognito:username'),

        'created_at': generate_timestamp(),

        'Requirement': request.usercomment,

        'status': 'initializing',

        'project_type': request.type

    }



def _update_project_properties(project_node: Dict[str, Any], project_structure: Dict[str, Any]) -> None:

    """Update project node properties for response."""

    if 'overview' in project_structure:

        overview = project_structure['overview']

        project_node['properties']['Title'] = overview.get('project_name', project_node['properties']['Title'])

        project_node['properties']['Description'] = overview.get('description', project_node['properties']['Description'])



async def _create_project_node(node_db: NodeDB, properties: Dict[str, Any]) -> Dict[str, Any]:

    """Create project node asynchronously."""

    return await node_db.create_node([NodeType.PROJECT.value], properties)



def _build_user_prompt(request: StartProjectInit) -> str:

    """Build comprehensive user prompt with all available context."""

    prompt_parts = [request.usercomment]

    

    # FIXED: Safe attribute access

    if hasattr(request, 'overview_data') and request.overview_data:

        overview_info = "\n\nAdditional Project Information:"

        for key, value in request.overview_data.items():

            if value and value.strip():

                overview_info += f"\n- {key.replace('_', ' ').title()}: {value}"

        prompt_parts.append(overview_info)

    

    return "\n".join(prompt_parts)



async def _generate_project_structure(

    llm: LLMInterface, 

    request: StartProjectInit, 

    system_prompt: str, 

    response_format: type, 

    model: str

) -> str:

    """Generate project structure using LLM."""

    try:

        user_prompt = _build_user_prompt(request)

        

        return await llm.llm_interaction_wrapper(

            messages=[],

            user_prompt=user_prompt,

            system_prompt=system_prompt,

            response_format=response_format,

            model=model,

            stream=False

        )

    

    except Exception as e:

        print('error---<', e)





async def _process_enhanced_structure(project_structure: Dict[str, Any]) -> Dict[str, Any]:

    """Process the enhanced project structure for database storage."""

    update_data = {

        'enhanced_project_structure': json.dumps(project_structure),

        'configuration_state': 'structured'

    }

    

    # Extract overview data

    if 'overview' in project_structure:

        overview = project_structure['overview']

        update_data.update({

            'Title': overview.get('project_name', ''),

            'Description': overview.get('description', ''),

            'frontend_framework': overview.get('frontend_framework', ''),

            'backend_framework': overview.get('backend_framework', ''),

            'database_framework': overview.get('database_framework', '')

        })

    

    # Store container information

    for container_type in ['frontend', 'backend', 'database']:

        if container_type in project_structure:

            container_data = project_structure[container_type]

            update_data[f'{container_type}_container'] = json.dumps(container_data)

    

    return update_data



def _extract_container_info(project_structure: Dict[str, Any]) -> Dict[str, Any]:

    """Extract container summary information for response."""

    containers = {}

    for container_type in ['frontend', 'backend', 'database']:

        if container_type in project_structure:

            container = project_structure[container_type]

            containers[container_type] = {

                'name': container.get('container_name', ''),

                'description': container.get('description', ''),

                'dependencies': container.get('dependent_containers', [])

            }

    return containers



# Update the main endpoint to include architecture pattern


def _generate_container_summary(project_structure: Dict[str, Any], architecture_pattern: str) -> Dict[str, Any]:
    """Generate a summary of containers and their relationships."""
    summary = {
        "pattern": architecture_pattern,
        "containers": [],
        "dependencies": {},
        "deployment_units": len([k for k in project_structure.keys() if k != 'overview'])

    }

    for container_type in ['frontend', 'backend', 'database', 'api-gateway', 'cache']:
        if container_type in project_structure:
            container = project_structure[container_type]
            container_info = {
                "type": container_type,
                "name": container.get('container_name', ''),
                "description": container.get('description', ''),
                "interfaces": container.get('interfaces', ''),
                "dependencies": container.get('dependent_containers', [])
            }

            summary["containers"].append(container_info)
            summary["dependencies"][container_type] = container.get('dependent_containers', [])

    return summary


@router.patch("/property/{node_id}")
async def update_property_in_node(
    node_id: int, 
    update_data: PropertyUpdate,
    db: NodeDB = Depends(get_node_db)
):
    """
    Update a field in a node using request body
    """
    response = await db.update_property_by_id(
        node_id, 
        update_data.property_name, 
        update_data.property_value,
        update_data.session_id
    )
    if response:
        return JSONResponse(content="success", status_code=200)
    
    return JSONResponse(content="failed", status_code=404)

@router.delete("/{node_id}")
async def delete_node(node_id: int, node_type: str, node_db = Depends(get_node_db), background_tasks: BackgroundTasks = BackgroundTasks()):
    vector_db = get_vector_db()
    print("Received request to delete node", node_id)
    project_id = node_id
    
    if not node_id or not node_type:
        raise HTTPException(status_code=400, detail='Node id or type not provided')

    if node_type not in node_types and node_type not in root_node_types:
        raise HTTPException(status_code=400, detail='Invalid node type')

    if node_type == "Project":
        deleted_node_ids = await node_db.delete_associates_and_deactivate_project(node_id, ['HAS_CHILD'])
    else:
        # Delete the node and all its 'HAS_CHILD' nodes recursively
        deleted_node_ids = await node_db.delete_node_and_associates(node_id, ['HAS_CHILD'])
    
    # if not deleted_node_ids or len(deleted_node_ids) == 0:
        # If no nodes were deleted then return an error
        # raise HTTPException(status_code=404, detail='Node not found')
    
    # Move vector DB deletions to background tasks
    for deleted_node_id in deleted_node_ids:
        background_tasks.add_task(vector_db.delete_node_from_vector_db, deleted_node_id)

    # Move conversation deletion to background tasks
    background_tasks.add_task(node_db.delete_conversations, project_id)
    
    if get_node_type(node_type) in ["Project", "Product"]:
        # Delete project usage tracking in the background (optional)
        
        background_tasks.add_task(delete_project_usage, project_id)
        background_tasks.add_task(delete_notifications, project_id)
    
    return deleted_node_ids


@router.delete("/")
async def delete_multiple_nodes(
    node_ids: List[int] = Query(..., description="List of node IDs to delete"),
    node_type: str = Query(..., description="Type of nodes to delete"),
    node_db: NodeDB = Depends(get_node_db),
    vector_db = Depends(get_vector_db),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    print(f"Received request to delete multiple nodes of type {node_type}: {node_ids}")

    if not node_ids or not node_type:
        raise HTTPException(status_code=400, detail='Node ids or type not provided')

    if node_type not in node_types and node_type not in root_node_types:
        raise HTTPException(status_code=400, detail='Invalid node type')

    deleted_node_ids = []
    for node_id in node_ids:
        try:
            # Delete the node and all its 'HAS_CHILD' nodes recursively
            deleted_ids = await node_db.delete_node_and_associates(node_id, ['HAS_CHILD', 'HAS_USER'])
            deleted_node_ids.extend(deleted_ids)

            if get_node_type(node_type) in ["Project", "Product"]:
                # Delete project usage tracking in the background (optional)
                background_tasks.add_task(delete_project_usage, node_id)
                background_tasks.add_task(delete_notifications, node_id)

            # Delete from vector database
            for deleted_id in deleted_ids:
                await vector_db.delete_node_from_vector_db(deleted_id)

        except Exception as e:
            print(f"Error deleting node {node_id}: {str(e)}")
            # Continue with other nodes even if one fails

    if not deleted_node_ids:
        raise HTTPException(status_code=404, detail='No nodes were deleted')

    return {"deleted_node_ids": deleted_node_ids}

@router.get("/get_child_nodes/")
async def get_child_nodes(node_id: int , node_type: str, child_node_type: str, db = Depends(get_node_db)):
    print("Received request to get child nodes for node", node_id)
    if (not node_id and node_id!=0 ) or not node_type or not child_node_type:
        raise HTTPException(status_code=400, detail='Node id, type, and child node type not provided')
    # Fetch child nodes
    nodes = await db.get_child_nodes(node_id, child_node_type)
    
    if nodes:
        return nodes
    else:
        return []
    
@router.get("/get_node_tree/")
async def get_node_tree(node_id: int, node_type: str, db = Depends(get_node_db)):
    if not node_id or not node_type:
        raise HTTPException(status_code=400, detail='Node id and type not provided')

    # Get the node tree
    node_tree = await db.get_node_tree(node_id, node_type)

    if node_tree:
        return node_tree
    else:
        raise HTTPException(status_code=404, detail='Node not found')

@router.get("/get_similar_nodes/")
async def get_similar_nodes(node_id: int, max_results: int = Query(10, ge=1), db = Depends(get_node_db), vector_db = Depends(get_vector_db)):
    if not node_id:
        raise HTTPException(status_code=400, detail='Node id not provided')

    # Assuming 'db.get_node_by_id' fetches the node and its properties
    parent_node = await db.get_node_by_id(node_id)
    if not parent_node:
        raise HTTPException(status_code=404, detail='Parent node not found')

    # Use the vector DB to find similar nodes
    similar_node_ids = await vector_db.find_similar_nodes(parent_node['properties'], max_results=max_results)

    similar_nodes = await db.get_nodes_by_ids(similar_node_ids)

    return similar_nodes

@router.get("/get_connected_nodes/")
async def get_connected_nodes(node_id: int, node_type: str, linked_node_type: str, db = Depends(get_node_db)):
    if not node_id or not node_type or not linked_node_type:
        raise HTTPException(
            status_code=400, 
            detail='Node id, type, and linked node type not provided'
        )
    
    # Make sure that the node is something that we allow to fetch
    if node_type not in node_types:
        raise HTTPException(
            status_code=400, 
            detail='Invalid node type'
        )

    nodes = await db.get_connected_nodes(node_id, linked_node_type)
    if nodes:
        return nodes
    else:
        return []

@router.post("/create_node_association/")
async def create_node_association(message_body: dict):
    db = get_node_db()
    start_node_id = message_body.get('start_node_id')
    end_node_id = message_body.get('end_node_id')
    relationship_type = message_body.get('relationship_type')
    
    if not start_node_id or not end_node_id or not relationship_type:
        raise HTTPException(status_code=400, detail="Invalid request")
    
    # Call your asynchronous function to create node association here
    relationship_type = await db.create_relationship(int(start_node_id), int(end_node_id), relationship_type)

    return {'relationship_type': relationship_type}

@router.get("/get_associated_nodes/")
async def get_associated_nodes(node_id: int, node_type: str, associated_node_type: str, relationship_type: str):
    db = get_node_db()
    allowed_types = ['Product', 'Project']
    if node_type not in allowed_types or associated_node_type not in allowed_types:
        raise HTTPException(status_code=400, detail="Invalid node type")

    # Call your asynchronous function to get associated nodes here
    node = await db.get_associated_item(node_id, node_type, associated_node_type, relationship_type)
    if node:
        return node
    return []

@router.get("/get_likely_associated_nodes/")
async def get_likely_associated_nodes(node_id: int, node_type: str, associated_node_type: str, db = Depends(get_node_db), vector_db = Depends(get_vector_db)):
    allowed_types = ['Product', 'Project']
    if node_type not in allowed_types or associated_node_type not in allowed_types:
        raise HTTPException(status_code=400, detail="Invalid node type")

    # Call your asynchronous function to get likely associated nodes here
    node = await db.get_node_by_id(node_id)
    node['properties']['node_id'] = node['id']
    similar_nodes = await vector_db.find_similar_nodes(node['properties'])
    nodes = await db.get_nodes_by_ids(similar_nodes)
    filtered_nodes = [node for node in nodes if associated_node_type in node['labels']]
    
    if filtered_nodes:
        return filtered_nodes
    return []
 
@router.get("/get_graph_diagram/{node_id}")
async def get_graph_diagram(node_id: int ,node_types: Annotated[Union[List[str], None], Query()] = None , levels: int = Query(999, ge=0, le=999), node_db: NodeDB = Depends(get_node_db)):
    print(node_types)
    if node_types:
        node_types = node_types[0].split(',')
    result = await node_db.get_graph_nodes(node_id, node_types , levels)    

    if result:
        return JSONResponse(result, status_code=200)
    else:
        return JSONResponse({'message': 'Id not found'}, status_code= 404)

@router.get("/tasks_count/", response_description="Get Total Number of Task")
async def get_tasks_count(
    project_id: int,
    db = Depends(get_node_db)
):
    try:
        return await db.get_tasks_count(project_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    

async def project_counts_v2(project_id: int, query_type: QueryType, node_db: NodeDB):
    queries = {
        QueryType.REQUIREMENT: f"""
            MATCH (p:Project)
            WHERE ID(p) = {project_id}
            WITH p
            OPTIONAL MATCH (p)-[:HAS_CHILD|CONTAINS*]->(epic:Epic)
            OPTIONAL MATCH (epic)-[:HAS_CHILD|CONTAINS*]->(story:UserStory)
            OPTIONAL MATCH (story)-[:HAS_CHILD|CONTAINS*]->(task:Task)
            RETURN 
                COUNT(DISTINCT epic) as epicCount,
                COUNT(DISTINCT story) as userStoryCount,
                COUNT(DISTINCT task) as taskCount
        """,
        QueryType.ARCHITECTURE: f"""
            MATCH (p:Project)
            WHERE ID(p) = {project_id}
            WITH p
            OPTIONAL MATCH (p)-[:HAS_CHILD|CONTAINS*]->(sc:SystemContext)
            WHERE NOT sc:Discussion
            OPTIONAL MATCH (sc)-[:HAS_CHILD|CONTAINS*]->(c:Container)
            WHERE NOT c:Discussion
            OPTIONAL MATCH (c)-[:HAS_CHILD|CONTAINS*]->(comp:Component)
            WHERE NOT comp:Discussion
            OPTIONAL MATCH (c)-[:HAS_CHILD|CONTAINS*]->(des:Design)
            WHERE NOT des:Discussion
            OPTIONAL MATCH (c)-[:HAS_CHILD|CONTAINS*]->(i:Interface)
            WHERE NOT i:Discussion
            OPTIONAL MATCH (c)-[:HAS_CHILD|CONTAINS*]->(d:Deployment)
            WHERE NOT d:Discussion
            RETURN
                COUNT(DISTINCT sc) as systemContextCount,
                COUNT(DISTINCT c) as containerCount,
                COUNT(DISTINCT comp) as componentCount,
                COUNT(DISTINCT des) as designCount,
                COUNT(DISTINCT i) as interfaceCount,
                COUNT(DISTINCT d) as deploymentCount
        """,
        QueryType.DEPLOYMENT: f"""
            MATCH (p:Project)
            WHERE ID(p) = {project_id}
            WITH p
            OPTIONAL MATCH (p)-[:HAS_CHILD|CONTAINS*]->(sc:SystemContext)
            WHERE NOT sc:Discussion
            OPTIONAL MATCH (sc)-[:HAS_CHILD|CONTAINS*]->(c:Container)
            WHERE NOT c:Discussion
            OPTIONAL MATCH (c)-[:HAS_CHILD|CONTAINS*]->(d:Deployment)
            WHERE NOT d:Discussion
            RETURN COUNT(DISTINCT d) as deploymentCount
        """
    }
    
    try:
        cursor = await node_db.async_run(queries[query_type])
        
        # Try to get the first result from cursor
        record = None
        try:
            for row in cursor:
                record = row
                break
        except:
            pass

        if query_type == QueryType.REQUIREMENT:
            result = {
                "epicCount": record["epicCount"] if record else 0,
                "userStoryCount": record["userStoryCount"] if record else 0,
                "taskCount": record["taskCount"] if record else 0
            }
        elif query_type == QueryType.ARCHITECTURE:
            result = {
                "systemContextCount": record["systemContextCount"] if record else 0,
                "containerCount": record["containerCount"] if record else 0,
                "componentCount": record["componentCount"] if record else 0,
                "designCount": record["designCount"] if record else 0,
                "interfaceCount": record["interfaceCount"] if record else 0,
                "deploymentCount": record["deploymentCount"] if record else 0
            }
        else:  # DEPLOYMENT
            result = {
                "deploymentCount": record["deploymentCount"] if record else 0
            }

        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Database query error: {str(e)}"
        )
        
@router.get("/project_counts/{project_id}/{query_type}", response_model=CountResponse)
async def project_counts(
    project_id: int,
    query_type: QueryType,
    node_db = Depends(get_node_db)
) -> CountResponse:
    try:
        counts = await project_counts_v2(project_id, query_type, node_db)
        return CountResponse(counts=counts)
    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )
    

def convert_counts_to_boolean(data: dict) -> dict:
    """
    Convert nested count values to boolean based on whether they're greater than 0.
    
    Args:
        data (dict): Nested dictionary containing count values
        
    Returns:
        dict: Dictionary with count values converted to boolean
    """
    result = {}
    
    for key, value in data.items():
        if isinstance(value, dict):
            # Recursively convert nested dictionaries
            result[key] = convert_counts_to_boolean(value)
        elif isinstance(value, (int, float)):
            # Convert numbers to boolean based on whether they're greater than 0
            result[key] = bool(value > 0)
        else:
            # Keep other values as is
            result[key] = value
            
    return result


async def flowline(project_id: int, node_db: NodeDB):
    unified_query = """
        MATCH (p:Project) 
        WHERE ID(p) = $project_id
        WITH p, ID(p) as pid, properties(p) as pprops

        // Get work item count directly
        OPTIONAL MATCH (p)-[:HAS_CHILD]->(wir:WorkItemRoot)
        WITH p, pid, pprops, wir
        OPTIONAL MATCH (wir)-[:HAS_CHILD]->(w:WorkItem)
        WITH p, pid, pprops, COUNT(w) as workItemCount

        // Rest of your existing query...
        OPTIONAL MATCH (p)-[:HAS_CHILD|CONTAINS*]->(epic:Epic)
        WHERE NOT epic:Discussion
        OPTIONAL MATCH (epic)-[:HAS_CHILD|CONTAINS*]->(story:UserStory)
        WHERE NOT story:Discussion  
        OPTIONAL MATCH (story)-[:HAS_CHILD|CONTAINS*]->(task:Task)
        WHERE NOT task:Discussion

        // Get requirements
        OPTIONAL MATCH (p)-[:HAS_CHILD|CONTAINS*]->(requirement:Requirement)
        WHERE NOT requirement:Discussion

        // Get architectural components
        OPTIONAL MATCH (p)-[:HAS_CHILD*]->(ar:ArchitecturalRequirement)
        WHERE NOT ar:Discussion

        // Get system context and its children
        OPTIONAL MATCH (p)-[:HAS_CHILD|CONTAINS*]->(sc:SystemContext)
        WHERE NOT sc:Discussion
        OPTIONAL MATCH (sc)-[:HAS_CHILD|CONTAINS*]->(c:Container)
        WHERE NOT c:Discussion
        OPTIONAL MATCH (c)-[:HAS_CHILD|CONTAINS*]->(comp:Component)
        WHERE NOT comp:Discussion AND comp.Type = 'Component'
        OPTIONAL MATCH (c)-[:HAS_CHILD|CONTAINS*]->(des:Design)
        WHERE NOT des:Discussion

        // Get interfaces from containers and components
        OPTIONAL MATCH (c)-[:HAS_CHILD]->(interface:Interface)
        WHERE NOT interface:Discussion
        OPTIONAL MATCH (comp)-[:HAS_CHILD]->(comp_interface:Interface)
        WHERE NOT comp_interface:Discussion

        WITH DISTINCT pid, pprops, workItemCount,
            COLLECT(DISTINCT epic) as epics,
            COLLECT(DISTINCT story) as stories,
            COLLECT(DISTINCT task) as tasks,
            COLLECT(DISTINCT requirement) as requirements,
            COLLECT(DISTINCT ar) as architecturalRequirements,
            COLLECT(DISTINCT sc) as systemContexts,
            COLLECT(DISTINCT c) as containers,
            COLLECT(DISTINCT comp) as components,
            COLLECT(DISTINCT des) as designs,
            COLLECT(DISTINCT interface) as containerInterfaces,
            COLLECT(DISTINCT comp_interface) as componentInterfaces

        RETURN {
            project_setup: {
                description: pprops.Description IS NOT NULL,
                objective: pprops.Objective IS NOT NULL,
                scope: pprops.Scope IS NOT NULL,
                architecturePattern: pprops.ArchitecturePattern IS NOT NULL,
                architectureStrategy: pprops.ArchitectureStrategy IS NOT NULL,
                teamComposition: pprops.TeamComposition IS NOT NULL,
                additionalDetails: pprops.AdditionalDetails IS NOT NULL
            },
            workItems: {
                task: workItemCount
            },
            architecture: {
                Requirement: {
                    Description: ANY(r IN requirements WHERE r.Description IS NOT NULL),
                    functional_requirements: ANY(r IN requirements WHERE r.functional_requirements IS NOT NULL),
                    architectural_requirements: ANY(r IN requirements WHERE r.architectural_requirements IS NOT NULL)
                },
                systemContext: {
                    Description: ANY(sc IN systemContexts WHERE sc.Description IS NOT NULL),
                    Users: ANY(sc IN systemContexts WHERE sc.Users IS NOT NULL),
                    ExternalSystems: ANY(sc IN systemContexts WHERE sc.ExternalSystems IS NOT NULL),
                    SystemContextDiagram: ANY(sc IN systemContexts WHERE sc.SystemContextDiagram IS NOT NULL),
                    ContainerDiagram: ANY(sc IN systemContexts WHERE sc.ContainerDiagram IS NOT NULL)
                },
                container: [c IN containers | c.Title],
                component: [comp IN components | {
                    Title: comp.Title,
                    Root: [c IN containers WHERE (c)-[:HAS_CHILD|CONTAINS*]->(comp)][0].Title
                }],
                design: REDUCE(arr = [], des IN designs | 
                    CASE 
                        WHEN NOT des.Title IN arr 
                        THEN arr + {
                            Title: des.Title,
                            Root: [c IN containers WHERE (c)-[:HAS_CHILD|CONTAINS*]->(des)][0].Title
                        }
                        ELSE arr 
                    END
                ),
                interface: REDUCE(arr = [], interface IN (containerInterfaces + componentInterfaces) | 
                    CASE 
                        WHEN interface IS NOT NULL AND NOT interface.Title IN arr 
                        THEN arr + {
                            Title: interface.Title,
                            Root: [c IN containers WHERE (c)-[:HAS_CHILD|CONTAINS*]->(interface)][0].Title
                        }
                        ELSE arr 
                    END
                )
            },
            requirements: {
                epic: SIZE(epics),
                userStory: SIZE(stories)
            }
        } as data
    """

    try:
        cursor = await node_db.async_run(
            unified_query, 
            parameters={"project_id": project_id}
        )
        
        record = next(cursor, None)
        if not record:
            # Return empty data structure that matches expected format
            return {
                "project_setup": {
                    "description": False,
                    "objective": False,
                    "scope": False,
                    "architecturePattern": False,
                    "architectureStrategy": False,
                    "teamComposition": False,
                    "additionalDetails": False
                },
                "workItems": {
                    "task": 0
                },
                "architecture": {
                    "Requirement": {
                        "Description": False,
                        "functional_requirements": False,
                        "architectural_requirements": False
                    },
                    "systemContext": {
                        "Description": False,
                        "Users": False,
                        "ExternalSystems": False,
                        "SystemContextDiagram": False,
                        "ContainerDiagram": False
                    },
                    "container": [],
                    "component": [],
                    "design": [],
                    "interface": []
                },
                "requirements": {
                    "epic": 0,
                    "userStory": 0
                }
            }
            
        return record["data"] if record else {}
        
    except Exception as e:
        print(f"Error in flowline query: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Database query error: {str(e)}"
        )
@router.get("/flowline/{project_id}", response_model=dict)
async def get_timeline(
    project_id: int,
    node_db: NodeDB = Depends(get_node_db)
) -> dict:
    """Get flowline data for a project, including node counts."""
    try:
            mongo_handler = get_mongo_db(
                db_name=settings.MONGO_DB_NAME,
                collection_name='project_repositories'
            )

            _result = await mongo_handler.get_one(
                filter={
                    'project_id': project_id,
                },
                db=mongo_handler.db
            )
                
            if not _result:
                repositories = []
            else:
                repositories = _result.get('repositories', [])
            data =await flowline(project_id, node_db)
            # print(data)
            data['project_assets']= {"repo":len(repositories)}
            mongo_handler = get_mongo_db(
                db_name=settings.MONGO_DB_NAME,
                collection_name=tasks_collection_name
            ) 
            _result = list(mongo_handler.db[tasks_collection_name].find(
                {
                    'project_id': project_id,
                }
            ).sort("start_time", -1).limit(1) )  
            print(_result)
            if not _result:
                status="pending"
            else:
                status = _result[0].get('status',"pending")
            data['codegen_status']=status
            # responses = convert_counts_to_boolean(data)
            return format_flowline_data(data)

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch timeline data: {str(e)}"
        )
    

@router.get("/get_related_nodes/{node_id}/{node_type}/{relationship_type}")
async def get_related_nodes(
    node_id: int,
    node_type: str,
    relationship_type: str,
    node_db: NodeDB = Depends(get_node_db)
):
    try:
        # Verify node exists
        node = await node_db.get_node_by_id(node_id)
        if not node:
            raise HTTPException(status_code=404, detail="Node not found")
        
        # Construct and execute query to find related nodes with specified relationship
        query = f"""
        MATCH (p:{node_type})-[r:{relationship_type}]-(q) 
        WHERE ID(p) = $node_id AND NOT 'Unused' IN LABELS(q)
        RETURN ID(q) AS id, LABELS(q) AS labels, properties(q) AS properties, 
               TYPE(r) AS relationship_type, properties(r) AS relationship_properties
        """
        
        result = await node_db.async_run(query, node_id=node_id)
        related_nodes = result.data()
        
        return related_nodes
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get related nodes: {str(e)}")
    
def get_supabase_credentials(project_id):
    """
    Retrieve Supabase credentials from MongoDB.
    
        
    """
    try:
        # Get MongoDB connection
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
        collection = mongo_db.db["supabase_user_credentials"]
        project_id = str(project_id)
        # Query for Supabase credentials
        query = {
            "project_id": project_id, # Only get active connections
        }
        
        projection = {
            "api_url": 1,
            "db_url": 1,
            "anon_key": 1,
            "_id": 0
        }
        
        supabase_record = collection.find_one(query, projection)
        
        
    # Decrypt the encrypted fields
    
        decrypted_db_url = decrypt_data(supabase_record["db_url"])
        decrypted_anon_key = decrypt_data(supabase_record["anon_key"])
        api_url = supabase_record["api_url"]
        
        return {
            "decrypted_db_url":decrypted_db_url,
            "decrypted_anon_key":decrypted_anon_key,
            "api_url":api_url
        }
            
    except Exception as e:
        print(f"Error retrieving Supabase credentials: {str(e)}")
def _update_temp_data_from_blueprint(temp_data: dict, blueprint) -> dict:
    """Update TempData based on blueprint changes while preserving existing structure"""
    
    # Make a deep copy to avoid modifying the original
    import copy
    updated_data = copy.deepcopy(temp_data)
    
    # Update project overview information
    if hasattr(blueprint, 'name') and blueprint.name:
        if "overview" not in updated_data:
            updated_data["overview"] = {}
        updated_data["overview"]["project_name"] = blueprint.name
    
    if hasattr(blueprint, 'description') and blueprint.description:
        if "overview" not in updated_data:
            updated_data["overview"] = {}
        updated_data["overview"]["description"] = blueprint.description
    
    # Update tech stack information in containers
    if hasattr(blueprint, 'techStack') and blueprint.techStack:
        _update_containers_tech_stack(updated_data, blueprint.techStack)
    
    # Update features in container details
    if hasattr(blueprint, 'features') and blueprint.features:
        _update_containers_features(updated_data, blueprint.features)
    
    # Update design information (colors, theme, layout)
    if hasattr(blueprint, 'colors') or hasattr(blueprint, 'theme') or hasattr(blueprint, 'layoutDescription'):
        _update_frontend_design(updated_data, blueprint)
    
    # Update project metadata
    if hasattr(blueprint, 'estimatedTime'):
        updated_data["estimatedTime"] = blueprint.estimatedTime
    
    if hasattr(blueprint, 'complexity'):
        updated_data["complexity"] = blueprint.complexity
    
    return updated_data


def _update_containers_tech_stack(temp_data: dict, tech_stack):
    """Update container frameworks based on tech stack"""
    
    if "containers" not in temp_data:
        return
    
    for container in temp_data["containers"]:
        container_type = container.get("container_type", "")
        
        # Update frontend framework
        if container_type == "frontend" and hasattr(tech_stack, 'frontend') and tech_stack.frontend:
            # Map frontend frameworks
            framework_mapping = {
                "React JS": "react",
                "Vue.js": "vue",
                "Angular": "angular",
                "Next.js": "nextjs"
            }
            frontend_tech = tech_stack.frontend[0] if tech_stack.frontend else ""
            container["framework"] = framework_mapping.get(frontend_tech, frontend_tech.lower().replace(" ", ""))
        
        # Update backend framework
        elif container_type == "backend" and hasattr(tech_stack, 'backend') and tech_stack.backend:
            backend_tech = tech_stack.backend[0] if tech_stack.backend else ""
            if backend_tech and backend_tech != "None":
                framework_mapping = {
                    "FastAPI": "fastapi",
                    "Django": "django",
                    "Flask": "flask",
                    "Express.js": "express"
                }
                container["framework"] = framework_mapping.get(backend_tech, backend_tech.lower().replace(" ", ""))


def _update_containers_features(temp_data: dict, features):
    """Update container features based on blueprint features"""
    
    if "containers" not in temp_data:
        return
    
    # Convert blueprint features to the format expected in container_details
    container_features = []
    for feature in features:
        if hasattr(feature, 'name'):
            container_features.append(feature.name)
        elif isinstance(feature, str):
            container_features.append(feature)
    
    # Update features in frontend and backend containers
    for container in temp_data["containers"]:
        if not isinstance(container.get("container_details"), dict):
            container["container_details"] = {}
        
        # For frontend containers, update features
        if container.get("container_type") == "frontend":
            container["container_details"]["features"] = container_features
        
        # For backend containers, you might want to filter or process differently
        elif container.get("container_type") == "backend":
            # You can add backend-specific feature processing here
            container["container_details"]["features"] = container_features


def _update_frontend_design(temp_data: dict, blueprint):
    """Update frontend design elements (colors, theme, layout)"""
    
    if "containers" not in temp_data:
        return
    
    for container in temp_data["containers"]:
        if container.get("container_type") == "frontend":
            if not isinstance(container.get("container_details"), dict):
                container["container_details"] = {}
            
            # Update colors
            if hasattr(blueprint, 'colors') and blueprint.colors:
                colors_dict = {}
                if hasattr(blueprint.colors, 'primary'):
                    colors_dict["primary"] = blueprint.colors.primary
                if hasattr(blueprint.colors, 'secondary'):
                    colors_dict["secondary"] = blueprint.colors.secondary
                if hasattr(blueprint.colors, 'accent'):
                    colors_dict["accent"] = blueprint.colors.accent
                
                container["container_details"]["colors"] = colors_dict
            
            # Update theme
            if hasattr(blueprint, 'theme') and blueprint.theme:
                container["container_details"]["theme"] = blueprint.theme
            
            # Update layout description
            if hasattr(blueprint, 'layoutDescription') and blueprint.layoutDescription:
                container["container_details"]["layout_description"] = blueprint.layoutDescription
            
            break  # Only update the first frontend container


@router.post("/start-project")  # Removed response_class=StreamingResponse
async def start_project(
    request: ProjectRequest,
    db: NodeDB = Depends(get_node_db),
    current_user = Depends(get_current_user),
    llm_service = Depends(get_llm_interface),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    try:
        """
        Start a new project from a requirement string.
        Streams updates back to the client as the project is created and analyzed.
        
        Request body should contain:
        - requirement: string - The project requirement
        """
        Supabase_project_id = request.Supabase_project_id
        print(f"Supabase_project_id ---> {Supabase_project_id}.  {request}")
        requirement = request.requirement
        framework = request.framework
        platform = request.platform
        blueprint = request.blueprint

        print(f"BluePrint======{blueprint}")
        advanced_applications = request.advanced_applications

        # Skip Supabase processing when advanced_applications is True
        if advanced_applications:
            print("Advanced applications mode enabled - skipping Supabase processing")
            Supabase_project_id = None

        project_node = None
        init_project_info = {}
        if blueprint is not None and blueprint.projectInfo is not None:
            print('Using projectInfo from blueprint')
            project_node = blueprint.projectInfo.model_dump()

        if blueprint is not None:
            # Create a dictionary to store all blueprint data
            blueprint_data = blueprint.model_dump()
            
            # Remove projectInfo from blueprint_data to avoid duplication
            if 'projectInfo' in blueprint_data:
                del blueprint_data['projectInfo']
                
            init_project_info.update(blueprint_data)    


        print(f"Requirement: {project_node}")  # Debugging
        node_db = get_node_db()
        start_time = time.time()

        # manifest creation
        try:
                project = await db.get_node_by_id(project_node['id'], node_type="Project")
                print(f"Project ------> {project}")
                print(f"Type_of_project: {type(project)}")
                
                # Access TempData from the properties dictionary
                if 'properties' in project and 'TempData' in project['properties']:
                    data_to_convert = json.loads(project['properties']['TempData'])
                else:
                    print("TempData not found in project properties, using default structure")
                    data_to_convert = {"backend": {}}
                
                print(f"data_to_convert ----> {data_to_convert}")

                if blueprint is not None:
                    data_to_convert = _update_temp_data_from_blueprint(data_to_convert, blueprint)
                    print(f"Updated data_to_convert after blueprint ----> {data_to_convert}")
                    for container in data_to_convert.get('containers', []):
                        if container.get('container_type') == 'frontend' and platform == 'mobile':
                            container['container_type'] = "mobile"
    
                
                if Supabase_project_id:
                    print("Inside the print statement")
                    user_id = current_user.get("cognito:username")
                    project_id = project_node['id']
                    try:
                        supabase_env_details = get_supabase_credentials(project_id)
                        db_url = supabase_env_details['decrypted_db_url']
                        print(f"supabase_env_details --> {supabase_env_details}")
                        anon_key = supabase_env_details['decrypted_anon_key']
                        api_url = supabase_env_details['api_url']

                        # Ensure containers structure exists
                        if 'containers' not in data_to_convert:
                            data_to_convert['containers'] = []

                        # Find or create backend container in the containers array
                        backend_container = None

                        for container in data_to_convert['containers']:
                            if container.get('container_type') == 'backend':
                                backend_container = container
                                break

                        # If no backend container exists, create one
                        if backend_container is None:
                            backend_container = {
                                'container_type': 'backend',
                                'container_name': 'backend',
                                'description': 'Backend container',
                                'interfaces': '',
                                'framework': 'fastapi',
                                'dependent_containers': []
                            }
                            data_to_convert['containers'].append(backend_container)

                        # Add environment variables to the backend container only
                        backend_container['env'] = {
                            "SUPABASE_URL":api_url,
                            "SUPABASE_KEY": anon_key,
                            "SUPABASE_DB_URL": db_url,
                        }
                        if "third_party_services" not in data_to_convert:
                            data_to_convert["third_party_services"] = []
                        supabase_service = {
                            "name": "Supabase",
                            "env": {
                                "SUPABASE_URL":api_url,
                                "SUPABASE_KEY": anon_key,
                                "SUPABASE_DB_URL": db_url,
                            }
                        }
                        data_to_convert["third_party_services"].append(supabase_service)

                    except Exception as e:
                        print(f"Error while processing Supabase credentials: {e}")
                manifest_response = db.create_initial_manifest(data_to_convert)
                print(f"Manifest response -----> {manifest_response}")
                node = await db.update_node_by_id(project_node['id'], {'Manifest': json.dumps(manifest_response)})
                print(f"Node ---> {node}")
        except Exception as e:
                print(f"Error while processing manifest creation : {e}")

        if not requirement:
            raise HTTPException(status_code=400, detail="Requirement string is required")
        
        # Handle advanced_applications mode - skip stream_project_creation
        if advanced_applications:
            print("Advanced applications mode - updating project blueprint and returning")
            
            # Update the project node with advanced_applications flag
            if project_node:
                try:
                    updated_properties = {
                        'advanced_applications': True,
                        'status': 'advanced_configured',
                        'updated_at': generate_timestamp()
                    }
                    
                    # Update blueprint information if provided
                    if init_project_info:
                        updated_properties['Init_project_info'] = json.dumps(init_project_info)
                        updated_properties['configuration_state'] = 'advanced_configured'
                    
                    updated_node = await db.update_node_by_id(
                        project_node['id'], 
                        updated_properties, 
                        NodeType.PROJECT.value.lower()
                    )
                    
                    print(f"Successfully updated project node: {updated_node}")
                    
                    # Return the updated project blueprint as JSONResponse
                    response_data = {
                        "status": "success",
                        "message": "Project blueprint updated for advanced applications",
                        "project_id": project_node['id'],
                        "advanced_applications": True,
                        "timestamp": generate_timestamp()
                    }
                    
                    print(f"Returning response: {response_data}")
                    return JSONResponse(
                        status_code=200,
                        content=response_data
                    )
                    
                except Exception as e:
                    print(f"Error updating project for advanced applications: {e}")
                    import traceback
                    print(f"Traceback: {traceback.format_exc()}")
                    return JSONResponse(
                        status_code=500,
                        content={
                            "status": "error",
                            "message": f"Failed to update project blueprint: {str(e)}",
                            "timestamp": generate_timestamp()
                        }
                    )
            else:
                return JSONResponse(
                    status_code=400,
                    content={
                        "status": "error",
                        "message": "Project node is required for advanced applications mode",
                        "timestamp": generate_timestamp()
                    }
                )
        
        # Regular mode - proceed with stream_project_creation
        async def stream_project_creation(project_node, init_project_info) -> AsyncGenerator[bytes, None]:
            try:
                yield format_response_for_stream({
                    "status": "processing", 
                    "message": "Analyzing requirement and generating project structure..."
                })
                
                # OPTIMIZATION: Create project node with a temporary name immediately
                # This allows us to start working with the project ID right away
                temp_project_name = f"Project-{generate_timestamp()}"
                
                # Create project node with temporary name
                properties = {
                    'Title': temp_project_name,
                    'Description': requirement,
                    'Type': NodeType.PROJECT.value.lower(),
                    'created_by': current_user.get('cognito:username'),
                    'created_at': generate_timestamp(),
                    'Requirement': requirement,
                    'status': 'initializing'  # Mark as initializing until LLM response
                }

                if not project_node :
                    # Create the project node first to get an ID to work with
                    print('Creating new project node')
                    project_node = await node_db.create_node([NodeType.PROJECT.value], properties)

                if init_project_info and isinstance(init_project_info, dict):
                    keymap = {
                        "layoutDescription": "Layout_Description", 
                        "colors": "Colors", 
                        "features": "Features",
                        "projectTitle": "Title",
                        "description": "Description",
                        "techStack":"Tech_Stack",
                        "theme": "Theme",
                        "estimatedTime": "Estimated_Time",
                        "complexity": "Complexity",
                    }

                    update_data = {
                        'Init_project_info': json.dumps(init_project_info),
                        'configuration_state': 'configured'
                    }

                    for key, value in init_project_info.items():
                        db_key = keymap.get(key, key)
                        if key == 'id' or key == 'name':
                            continue
                        if isinstance(value, dict) or isinstance(value, list):
                            # Convert complex objects (dict/list) to JSON strings
                            update_data[db_key] = json.dumps(value)
                        elif value is None:
                            # Handle None values
                            update_data[db_key] = ""
                        else:    
                            update_data[db_key] = value

                    resp = await node_db.update_node_by_id(project_node['id'], update_data, NodeType.PROJECT.value.lower())

                if not project_node:
                    yield format_response_for_stream({
                        "status": "error",
                        "message": "Failed to create project node",
                        "end": True
                    })
                    return
                    
                # Extract project ID from the response
                project_id = project_node["id"]
                # create a asynio task
                stage = get_stage(settings)
                safe_name = str(get_tenant_id()) + str(project_id)
                if stage == "dev":
                    # Create project in the background
                    background_tasks.add_task(create_project_for_dev, safe_name)
                elif stage == "qa":
                    background_tasks.add_task(create_project_for_qa, safe_name)
                elif stage == "pre_prod":
                    background_tasks.add_task(create_project_for_beta, safe_name)
                    
                yield format_response_for_stream({
                    "status": "processing", 
                    "message": f"Project created with ID: {project_id}",
                    "project_id": project_id,
                    "project_name": temp_project_name
                })
                
                # OPTIMIZATION: Only use LLM to generate the project name - SINGLE STREAMLINED LLM CALL
                system_prompt = "You are an expert system architect who creates concise and relevant project names."
                user_prompt = f"""
                Based on the following requirement, generate a suitable project name.

                Requirement: "{requirement}"

                Format your response as a JSON with the following structure:
                {{
                    "project_name": "Generated Project Name"
                }}
                """
                
                # Start creating basic structure concurrently with LLM call
                yield format_response_for_stream({
                    "status": "processing", 
                    "message": "Creating basic project structure ..."
                })
                
                # Launch LLM call - don't await it yet
                llm_task = asyncio.create_task(
                    llm_service.llm_interaction_wrapper(
                        messages=[],
                        user_prompt=user_prompt,
                        system_prompt=system_prompt,
                        response_format={'type': 'json_object'},
                        model='gpt-4.1-nano',
                        stream=False
                    )
                )
                    
                # OPTIMIZATION: Prepare and execute all node creation tasks in parallel
                # Level 1 nodes - directly connected to project
                level1_nodes_properties = [
                    # 1. RequirementRoot
                    {
                        "labels": ["RequirementRoot", "Requirement"],
                        "properties": {
                            "Title": f"Root requirement for {temp_project_name}",
                            "Description": requirement,
                            "Type": "RequirementRoot",
                            "created_by": current_user.get('cognito:username'),
                            "created_at": generate_timestamp(),
                            "project_id": project_id
                        }
                    },
                    # 2. ArchitectureRoot
                    {
                        "labels": ["ArchitectureRoot", "Architecture"],
                        "properties": {
                            "Title": f"Root architecture for {temp_project_name}",
                            "Description": requirement,
                            "Type": "ArchitectureRoot",
                            "created_by": current_user.get('cognito:username'),
                            "created_at": generate_timestamp(),
                            "project_id": project_id
                        }
                    },
                    # 3. WorkItemRoot
                    {
                        "labels": ["WorkItemRoot", "WorkItem"],
                        "properties": {
                            "Title": f"Root work item for {temp_project_name}",
                            "Description": requirement,
                            "Type": "WorkItemRoot",
                            "created_by": current_user.get('cognito:username'),
                            "created_at": generate_timestamp(),
                            "project_id": project_id
                        }
                    },
                    # 4. SystemContext - TEMPLATED INSTEAD OF LLM GENERATED
                    {
                        "labels": ["SystemContext"],
                        "properties": {
                            "Title": f"System Context for {temp_project_name}",
                            "Description": f"Description of system context for {temp_project_name}",
                            "Type": "SystemContext",
                            "created_by": current_user.get('cognito:username'),
                            "created_at": generate_timestamp(),
                            "project_id": project_id
                        }
                    }
                ]
                
                # Create Level 1 nodes
                level1_node_tasks = []
                for node_props in level1_nodes_properties:
                    level1_node_tasks.append(node_db.create_node(node_props["labels"], node_props["properties"]))
                
                # Execute level 1 node creation in parallel
                level1_nodes = await asyncio.gather(*level1_node_tasks)
                
                # Extract level 1 nodes
                requirement_node = level1_nodes[0]
                architecture_node = level1_nodes[1]
                work_item_node = level1_nodes[2]
                system_context_node = level1_nodes[3]
                
                # Create level 1 relationships in parallel
                level1_relationship_tasks = [
                    node_db.create_relationship(project_id, requirement_node["id"], "HAS_CHILD"),
                    node_db.create_relationship(project_id, architecture_node["id"], "HAS_CHILD"),
                    node_db.create_relationship(project_id, work_item_node["id"], "HAS_CHILD"),
                    node_db.create_relationship(project_id, system_context_node["id"], "HAS_CHILD"),
                ]
                
                # Execute level 1 relationships in parallel
                await asyncio.gather(*level1_relationship_tasks)
                
                yield format_response_for_stream({
                    "status": "processing", 
                    "message": "Created base project structure, ..."
                })
                
                # Level 2 nodes' properties - we'll create these after getting LLM response
                # Wait for LLM response now
                try:
                    # Now await the LLM task to get the response
                    completion_response = await llm_task
                    
                    # Extract the text content from the response
                    if hasattr(completion_response, 'choices') and len(completion_response.choices) > 0:
                        if hasattr(completion_response.choices[0], 'message'):
                            response_text = completion_response.choices[0].message.content
                        else:
                            response_text = completion_response.choices[0].text
                    else:
                        response_text = str(completion_response)
                    
                    # Parse the JSON response
                    try:
                        llm_data = json.loads(response_text)
                        # Extract the LLM-generated project name
                        base_project_name = llm_data.get("project_name", "Project")
                        # Create unique project name by combining LLM name with project ID
                        project_name = f"{base_project_name}-{project_id}"
                    except json.JSONDecodeError:
                        # Fallback if JSON parsing fails
                        project_name = f"Project-{project_id}"
                    
                    # Update the project node with the unique combined name
                    await node_db.update_node_by_id(
                        project_id,
                        properties={"Title": project_name, "status": "active"}
                    )
                    # Update all level 1 nodes to use the real project name
                    level1_update_tasks = [
                        # Update RequirementRoot node
                        node_db.update_node_by_id(
                            requirement_node["id"],
                            properties={"Title": f"Root requirement for {base_project_name}"}
                        ),
                        # Update ArchitectureRoot node
                        node_db.update_node_by_id(
                            architecture_node["id"],
                            properties={"Title": f"Root architecture for {base_project_name}"}
                        ),
                        # Update WorkItemRoot node
                        node_db.update_node_by_id(
                            work_item_node["id"],
                            properties={"Title": f"Root work item for {base_project_name}"}
                        ),
                        # Update SystemContext node
                        node_db.update_node_by_id(
                            system_context_node["id"],
                            properties={"Title": f"System Context for {base_project_name}","Description": f"Description of system context for {base_project_name}"}
                        )
                    ]
                    
                    # Execute all level 1 node updates in parallel
                    await asyncio.gather(*level1_update_tasks)
                    yield format_response_for_stream({
                        "status": "processing", 
                        "message": f"Updated project name to: {project_name}",
                        "project_name": project_name
                    })
                    # Extract containers from data_to_convert
                    containers_data = data_to_convert.get('containers', [])

                    # Prepare container nodes properties
                    container_nodes_properties = []
                    for container in containers_data:
                        # Extract container information
                        container_name = container.get('container_name', 'Unknown Container')
                        container_description = container.get('description', '')
                        container_type = container.get('container_type', 'backend')
                        framework = container.get('framework', 'fastapi')
                        ports = container.get('ports', 3000)
                        interfaces = container.get('interfaces', '')
                        workspace = container.get('workspace', '')
                        
                        # Handle container_details - can be string or dict
                        container_details = container.get('container_details', '')
                        if isinstance(container_details, dict):
                            # If it's a dict, extract relevant information
                            features = container_details.get('features', [])
                            colors = container_details.get('colors', {})
                            theme = container_details.get('theme', '')
                            layout_description = container_details.get('layout_description', '')
                            style = container_details.get('style', '')
                            
                            # Create a comprehensive description
                            details_description = f"Features: {', '.join(features) if features else 'Standard features'}"
                            if colors:
                                details_description += f" | Colors: Primary {colors.get('primary', '')}, Secondary {colors.get('secondary', '')}"
                            if theme:
                                details_description += f" | Theme: {theme}"
                            if layout_description:
                                details_description += f" | Layout: {layout_description}"
                        else:
                            # If it's a string, use as is
                            details_description = str(container_details)
                        
                        # Build comprehensive description
                        full_description = f"{container_description}"
                        if details_description:
                            full_description += f" | {details_description}"
                        if interfaces:
                            full_description += f" | Interfaces: {interfaces}"
                        
                        # Create container node properties
                        container_props = {
                            "labels": ["Container"],
                            "properties": {
                                "Title": f"{container_name}",
                                "Description": full_description,
                                "Type": "Container",
                                "project_name": project_name,
                                "container_name": container_name,
                                "container_type": container_type,
                                "framework": framework,
                                "ports": ports,
                                "workspace": workspace,
                                "interfaces": interfaces,
                                "created_by": current_user.get('cognito:username'),
                                "created_at": generate_timestamp(),
                                "project_id": project_id
                            }
                        }
                        
                        # Add platform if available from original framework/platform variables
                        if 'platform' in locals():
                            container_props["properties"]["platform"] = platform.value if isinstance(platform, Platform) else platform
                        
                        container_nodes_properties.append(container_props)
                    # Level 2 nodes' properties
                    level2_nodes_properties = [
                        # 1. ArchitectureRequirement - TEMPLATED
                        {
                            "labels": ["ArchitecturalRequirement"],
                            "properties": {
                                "Title": f"Architecture Requirement for {base_project_name}",
                                "Description": f"Architectural requirements for {base_project_name}",
                                "Type": "ArchitecturalRequirement",
                                "created_by": current_user.get('cognito:username'),
                                "created_at": generate_timestamp(),
                                "project_id": project_id
                            }
                        },
                        # 2. Container - TEMPLATED
                        {
                            "labels": ["Container"],
                            "properties": {
                                "Title": f"Main Container for {base_project_name}",
                                "Description": f"Primary container for {base_project_name} implementation: {requirement}",
                                "Type": "Container",
                                "project_name": project_name,
                                "created_by": current_user.get('cognito:username'),
                                "created_at": generate_timestamp(),
                                "project_id": project_id,
                                "framework": framework.value if isinstance(framework, Framework) else framework,
                                "platform": platform.value if isinstance(platform, Platform) else platform,
                                "created_by": current_user.get('cognito:username'),
                                "created_at": generate_timestamp(),
                                
                            }
                        },
                        # 3. Component - TEMPLATED
                        {
                            "labels": ["Component", "Architecture"],
                            "properties": {
                                "Title": f"Core Component for {base_project_name}",
                                "Description": f"{requirement}",
                                "is_architectural_leaf": "yes",
                                "Type": "Component",
                                "project_name": project_name,
                                "created_by": current_user.get('cognito:username'),
                                "created_at": generate_timestamp(),
                                "project_id": project_id
                            }
                        }
                    ]
                    
                    # Design node properties - TEMPLATED INSTEAD OF LLM GENERATED
                    design_properties = {
                        "labels": ["Design"],
                        "properties": {
                            "Title": f"Design for Core Component",
                            "Description": f"{requirement}",
                            "Type": "Design",
                            "project_name": project_name,
                            "component_name": "Core Component",
                            "interfaces": "Default user interface elements and interaction points",
                            "algorithms": "Default implementation algorithms and approaches",
                            "pseudocode": "",
                            "framework": framework.value if isinstance(framework, Framework) else framework,
                            "platform": platform.value if isinstance(platform, Platform) else platform,
                            "created_by": current_user.get('cognito:username'),
                            "created_at": generate_timestamp(),
                            "project_id": project_id
                        }
                    }
                    
                    yield format_response_for_stream({
                        "status": "processing", 
                        "message": "Creating detailed project structure..."
                    })
                    
                    # Create Level 2 nodes in parallel
                    level2_node_tasks = []
                    for node_props in level2_nodes_properties:
                        level2_node_tasks.append(node_db.create_node(node_props["labels"], node_props["properties"]))
                    
                    # Create design node in parallel with others
                    design_node_task = node_db.create_node(design_properties["labels"], design_properties["properties"])
                    
                    # Execute level 2 node creation in parallel
                    level2_results = await asyncio.gather(*level2_node_tasks, design_node_task)
                    
                    # Extract nodes from results
                    architecture_requirement_node = level2_results[0]
                    # container_node = level2_results[1]
                    # component_node = level2_results[2]
                    # design_node = level2_results[3]
                    
                    # Build level 2 relationship creation tasks
                    level2_relationship_tasks = [
                        # Level 2 relationships
                        node_db.create_relationship(architecture_node["id"], architecture_requirement_node["id"], "HAS_CHILD"),
                        # node_db.create_relationship(system_context_node["id"], container_node["id"], "HAS_CHILD"),
                        # node_db.create_relationship(container_node["id"], component_node["id"], "HAS_CHILD"),
                        
                        # # Design relationship
                        # node_db.create_relationship(component_node["id"], design_node["id"], "HAS_CHILD")
                    ]
                    
                    # Execute all level 2 relationships in parallel
                    await asyncio.gather(*level2_relationship_tasks)
                    container_node_tasks = []
                    async def container_node_task(container_props):
                        container_node = await node_db.create_node(container_props["labels"], container_props["properties"])
                        return container_node
                    
                    for container_props in container_nodes_properties:
                        container_node_tasks.append(container_node_task(container_props))

                    # Execute container node creation in parallel
                    container_nodes = await asyncio.gather(*container_node_tasks)

                    # Create relationships: system_context -> containers
                    container_relationship_tasks = []
                    for container_node in container_nodes:
                        container_relationship_tasks.append(
                            node_db.create_relationship(system_context_node["id"], container_node["id"], "HAS_CHILD")
                        )

                    # Execute container relationships in parallel
                    await asyncio.gather(*container_relationship_tasks)
                    yield format_response_for_stream({
                        "status": "processing", 
                        "message": "Created project structure with Architectural Requirement, System Context, Container and Component"
                    })
                    
                    workspace_start_time = time.time()
                    container_node_ids = []
                    for container_node in container_nodes:
                        container_id = container_node["id"]
                        container_node_ids.append(container_id)

                    async for message in stream_start_workspace_status(
                        project_id=project_id, 
                        container_ids=container_node_ids,  # Pass list instead of single ID
                        session_name=project_name, 
                        description=requirement, 
                        mongo_db=get_mongo_db(), 
                        node_db=node_db, 
                        current_user=current_user, 
                        new_repo_creation=True,
                        resume=False,
                        resume_task_id='',
                        encrypted_scm_id = request.encrypted_scm_id
                    ):
                        yield message

                    # Calculate elapsed time after workspace is created
                    workspace_end_time = time.time()
                    workspace_elapsed_time = workspace_end_time - workspace_start_time
                    workspace_elapsed_seconds = int(workspace_elapsed_time)

                    before_workspace = workspace_start_time - start_time
                    project_creation_seconds = int(before_workspace)
                    
                    yield format_response_for_stream({
                        "status": "complete", 
                        "message": "Project creation complete with full design hierarchy",
                        "project_id": project_id,
                        "project_name": project_name,
                        "project_creation_time_seconds": project_creation_seconds,
                        "workspace_creation_time_seconds": workspace_elapsed_seconds

                    })
                    
                except json.JSONDecodeError as e:
                    yield format_response_for_stream({
                        "status": "error", 
                        "message": f"Failed to parse project name from LLM: {str(e)}"
                    })
                    return
                except Exception as e:
                    yield format_response_for_stream({
                        "status": "error", 
                        "message": f"Error processing project creation: {str(e)}"
                    })
                    return
                
            except Exception as e:
                error_message = str(e)
                print(f"Error in stream_project_creation: {error_message}")
                yield format_response_for_stream({
                    "status": "error", 
                    "message": f"Error creating project: {error_message}"
                })
        
        return StreamingResponse(
            stream_project_creation(project_node, init_project_info),
            media_type="text/event-stream",
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'X-Accel-Buffering': 'no'  # This is for Nginx specifically
            }
        )
    except Exception as e:
        import traceback
        print('error', e)
        print('traceback.format--->', traceback.format_exc())

@router.get("/project/visibility/{tenant_id}/{project_id}", summary="Check if a project is public")
async def get_project_visibility(
    tenant_id: str,
    project_id: int,
    current_user = Depends(get_current_user),
    
):
    
    if tenant_id.startswith("default"):
        tenant_id = settings.KAVIA_B2C_CLIENT_ID
 
    """
    Check if a specific project is public or private.
    """
    # Connect to the public_projects collection
    mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME, collection_name="public_projects")
    mongo_handler = mongo_db
    
    # Verify project exists
    db = get_node_db(tenant_id=tenant_id)
    project = await db.get_node_by_id(project_id, node_type="Project")
    print(f"Project: {project}")
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
     
    # Check if project is in the public projects collection
    try:
        public_project = await mongo_handler.get_one(
                filter={"project_id": project_id, "tenant_id": tenant_id},
                db=mongo_handler.db
            )
    except Exception as e:
        print(f"Error getting public project: {e}")
        public_project = {}

    # if tenant_id.startswith("")
    
    # Check visibility status - project is public if it exists and visibility is "public"
    is_public = bool(public_project and public_project.get("visibility", "") != "private")
    
    # Serialize ObjectId to string if public_project exists
    if public_project and '_id' in public_project:
        public_project['_id'] = str(public_project['_id'])
        # Convert any other ObjectId that might be in the document
        for key, value in list(public_project.items()):
            if hasattr(value, '_type_marker') and str(value._type_marker) == 'ObjectId':
                public_project[key] = str(value)
    else:
        if public_project==None:
            if tenant_id==settings.KAVIA_B2C_CLIENT_ID: 
                return {
                "project_id": project_id,
                "tenant_id": tenant_id,
                "is_public": True,
                "visibility": "public",
                "public_info": None
                }
            else:
                return {
                "project_id": project_id,
                "tenant_id": tenant_id,
                "is_public": False,
                "visibility": "private",
                "public_info": None
                }

    return {
        "project_id": project_id,
        "tenant_id": tenant_id,
        "is_public": is_public,
        "visibility": "public" if is_public else "private",
        "public_info": public_project if is_public else None
    }


@router.post("/project/visibility", summary="Change project visibility (public/private)")
async def change_project_visibility(
    request: ProjectVisibilityRequest,
    current_user = Depends(get_current_user),
):
    """
    Change a project's visibility by updating its status in the public_projects collection.
    Setting make_public=true adds or updates the project with public visibility,
    setting it to false updates it with private visibility.
    """
    project_id = request.project_id
    make_public = request.make_public
    tenant_id = get_tenant_id()
    
    if tenant_id.startswith("default"):
        tenant_id = settings.KAVIA_B2C_CLIENT_ID
    
    mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME, collection_name="public_projects")
    
    # Connect to the public_projects collection
    mongo_handler = mongo_db
    
    # Get project details from Neo4j with creator information
    db = get_node_db()
    
    # Query project with creator information
    query = f"""
    MATCH (p:Project) WHERE ID(p) = {project_id}
    OPTIONAL MATCH (u:User) WHERE u.Username = p.created_by
    RETURN ID(p) AS id, p.Title AS Title, p.Description AS Description, 
           p.created_at AS created_at, p.created_by AS created_by,
           u.Name AS creator_name, u.Email AS creator_email, u.Picture AS creator_picture
    """
    
    result = await db.async_run(query)
    project_data = result.data()
    
    if not project_data or len(project_data) == 0:
        raise HTTPException(status_code=404, detail="Project not found")
    
    project = {
        "id": project_data[0]["id"],
        "properties": {
            "Title": project_data[0]["Title"],
            "Description": project_data[0]["Description"],
            "created_at": project_data[0]["created_at"],
            "created_by": project_data[0]["created_by"],
            "creator_name": project_data[0]["creator_name"],
            "creator_email": project_data[0]["creator_email"],
            "creator_picture": project_data[0]["creator_picture"]
        }
    }
    
    # Check if project is already in the public projects collection
    existing_public_project = await mongo_handler.get_one(
        filter={"project_id": project_id, "tenant_id": tenant_id},
        db=mongo_handler.db
    )
    
    current_timestamp = generate_timestamp()
    current_username = current_user.get("cognito:username")
    
    # If make_public is True, add or update the project with public visibility
    if make_public:
        # If the project doesn't exist in the collection, create it
        if not existing_public_project:
            # Create a public project record
            public_project = PublicProjectModel(
                project_id=project_id,
                title=project["properties"].get("Title", ""),
                tenant_id=tenant_id,
                description=project["properties"].get("Description", ""),
                created_at=project["properties"].get("created_at", ""),
                created_by=project["properties"].get("created_by", ""),
                creator_name=project["properties"].get("creator_name", ""),
                creator_email=project["properties"].get("creator_email", ""),
                creator_picture=project["properties"].get("creator_picture", ""),
                made_public_at=current_timestamp,
                made_public_by=current_username,
                visibility="public"
            )
            
            # Insert into MongoDB
            result = await mongo_handler.insert(
                element=public_project.model_dump(),
                db=mongo_handler.db
            )
            
            return {"status": "success", "message": f"Project {project_id} is now public", "made_public": True}
        else:
            # Project exists, update its visibility to public
            update_data = {
                "visibility": "public",
                "made_public_at": current_timestamp,
                "made_public_by": current_username
            }
            
            # Update MongoDB record
            result = await mongo_handler.update_one(
                filter={"project_id": project_id, "tenant_id": tenant_id},
                element=update_data,
                db=mongo_handler.db
            )
            
            return {"status": "success", "message": f"Project {project_id} is now public", "made_public": True}
    
    # If make_public is False, update the visibility to private if it exists
    else:
        # If the project exists in the collection, update its visibility
        if existing_public_project:
            update_data = {
                "visibility": "private",
                "made_private_at": current_timestamp,
                "made_private_by": current_username
            }
            
            # Update MongoDB record
            result = await mongo_handler.update_one(
                filter={"project_id": project_id, "tenant_id": tenant_id},
                element=update_data,
                db=mongo_handler.db
            )
            
            return {"status": "success", "message": f"Project {project_id} is now private", "made_public": False}
        else:
            public_project = PublicProjectModel(
                project_id=project_id,
                title=project["properties"].get("Title", ""),
                tenant_id=tenant_id,
                description=project["properties"].get("Description", ""),
                created_at=project["properties"].get("created_at", ""),
                created_by=project["properties"].get("created_by", ""),
                creator_name=project["properties"].get("creator_name", ""),
                creator_email=project["properties"].get("creator_email", ""),
                creator_picture=project["properties"].get("creator_picture", ""),
                made_public_at=current_timestamp,
                made_public_by=current_username,
                visibility="private"
            )
            result = await mongo_handler.insert(
                element=public_project.model_dump(),
                db=mongo_handler.db
            )
            
            # Project doesn't exist in the collection, so it's already effectively private
            return {"status": "success", "message": f"Project {project_id} is already private", "made_public": False}
        
        
@router.get("/projects/public", summary="Get list of public projects")
async def get_public_projects(
    skip: int = 0,
    limit: int = 20,
    current_user = Depends(get_current_user),
):
    """
    Retrieve a paginated list of all public projects.
    """
    public_projects = []
    mongo_handler = get_mongo_db(
        db_name=KAVIA_ROOT_DB_NAME, 
        collection_name="public_projects"
    )
    
    # Define the filter condition for public projects
    filter_condition = {"visibility": "public"}
    email = current_user.get("email")
    try:
        # Get paginated results
        cursor = await mongo_handler.get_all_documents(
            filter=filter_condition,
            db=mongo_handler.db
        )
        for doc in cursor:
            serialized_doc = doc.copy()
            if '_id' in serialized_doc:
                serialized_doc['_id'] = str(serialized_doc['_id'])
            
            # Convert any ObjectId fields to strings
            for key, value in serialized_doc.items():
                if hasattr(value, '_type_marker') and str(value._type_marker) == 'ObjectId':
                    serialized_doc[key] = str(value)
            
            public_projects.append(serialized_doc)
        # Convert cursor to list
        node_db = get_node_db(tenant_id=settings.KAVIA_B2C_CLIENT_ID)
        projects = await node_db.get_projects()
        for project in projects:
            project['tenant_id'] = settings.KAVIA_B2C_CLIENT_ID
            public_projects.append(project)

        print(f"Retrieved {len(projects)} projects from B2C tenant")
        print(f"Projects: {projects}")
        # Process and serialize the results
        if email:
            print(f"Filtering projects by email: {email}")
            # Filter projects by creator email
            filtered_projects = [project for project in public_projects if project.get("creator_email") != email]
        else:
            filtered_projects = public_projects 
            
        return {
        "public_projects": filtered_projects,
        "pagination": {
            "total": len(filtered_projects),
            "skip": skip,
            "limit": limit
        }
    }

        
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"Error fetching public projects: {str(e)}"
        )


@router.post("/clone_source_node")
async def create_node(source_node_id: int, request: CloneNode, current_user = Depends(get_current_user)):
    try:
        response = await clone_node(source_node_id, current_user, request.title)
        return {"message":"Project cloned successfully","node_info":response}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")

from app.services.supabase_management_service import SupabaseManagementService

# Initialize the management service
supabase_mgmt_service = SupabaseManagementService()

def _convert_to_manual_project_format(llm_response_obj: dict) -> dict:
    """Convert nested LLM response to flattened format for manual project creation"""
    
    def safe_get(obj, *keys, default=None):
        """Safely navigate nested dictionary keys"""
        for key in keys:
            if isinstance(obj, dict) and key in obj:
                obj = obj[key]
            else:
                return default
        return obj
    
    # Extract overview data
    overview = safe_get(llm_response_obj, 'overview', default={})
    project_name = overview.get('project_name', '')
    description = overview.get('description', '')
    
    # Extract frontend container data
    frontend_container = safe_get(llm_response_obj, 'frontend', 'container', default={})
    frontend_features = frontend_container.get('features', [])
    colors = frontend_container.get('colors', {})
    layout_description = frontend_container.get('layoutDescription', '')
    
    # Extract backend container data
    backend_container = safe_get(llm_response_obj, 'backend', 'container', default={})
    backend_features = backend_container.get('features', [])
    
    # Combine and normalize features
    all_features = frontend_features + backend_features
    normalized_features = []
    
    for idx, feature in enumerate(all_features, start=1):
        if isinstance(feature, str):
            normalized_features.append({
                'name': feature,
                'description': feature,
                'isEnabled': True
            })
        elif isinstance(feature, dict):
            normalized_features.append({
                'name': feature.get('name', f'Feature {idx}'),
                'description': feature.get('description', feature.get('name', f'Feature {idx}')),
                'isEnabled': feature.get('isEnabled', True)
            })
        else:
            normalized_features.append({
                'name': str(feature),
                'description': str(feature),
                'isEnabled': True
            })
    
    # Remove background color if present
    colors_copy = colors.copy() if isinstance(colors, dict) else {}
    colors_copy.pop('background', None)
    
    # Determine architecture pattern (you may need to adjust this logic)
    architecture_pattern = "monolithic-application"  # Default value
    
    return {
        'description': description,
        'features': normalized_features,
        'layoutDescription': layout_description,
        'colors': colors_copy,
        'architecturePattern': architecture_pattern
    }
@router.post('/start_project_init')
async def start_project_init(
    request: StartProjectInit,
    current_user = Depends(get_current_user),
    node_db: NodeDB = Depends(get_node_db)
):
    try:
        # Configuration based on request type
        config = _get_project_config(request)
        
        # Determine platform list
        platform_list = request.platform if isinstance(request.platform, list) else [request.platform]
        
        # Determine platform list
        platform_list = request.platform if isinstance(request.platform, list) else [request.platform]
        
        # Prepare base node properties with conditional framework fields
        base_properties = {
            'Title': config['temp_project_name'],
            'Description': request.usercomment + "only give backend frameworks if backend is mentioned in the requirement",
            'Type': NodeType.PROJECT.value.lower(),
            'created_by': current_user.get('cognito:username'),
            'created_at': generate_timestamp(),
            'status': 'initializing',
            'work_item_type': config.get('work_item_type',''),
        }
        
        # Add backend_framework if provided
        if request.backend_frameworks:
            base_properties['backend_framework'] = request.backend_frameworks
        
        # Add appropriate framework field based on platform
        if platform_list and len(platform_list) > 0 and request.frontend_frameworks:
            if platform_list[0] == 'mobile':
                base_properties['frontend_framework'] = request.frontend_frameworks
            elif platform_list[0] == 'web':
                base_properties['frontend_framework'] = request.frontend_frameworks
                
        # Run node creation and project schema creation concurrently
        project_node, project_schema = await asyncio.gather(
            _create_project_node(node_db, base_properties),
            create_project_welcome_page(base_properties)
        )
        
        print(f"Project_Schema -----> {project_schema}")
        
        # Process project schema containers
        for container in project_schema.containers:
            name: str = container.container_name
            name = name.replace(" ", "_")
            container.workspace = name + "_workspace"
            container.container_root = f"{container.workspace}/{container.container_name}"
        # Delete existing manifest file if it exists
        if os.path.exists('/tmp/kavia/workspace/.project_manifest.yaml'): 
            os.remove('/tmp/kavia/workspace/.project_manifest.yaml')
        
        # Configure backend containers
        bcs = project_schema.get_containers_by_type(ContainerType.BACKEND)
        if bcs:
            bcs[0].ports = settings.BACKEND_PORT  # Set default backend port
            bcs[0].apiSpec = "openapi.json"  # Set default API spec for backend

        # Configure frontend containers
        fcs = project_schema.get_containers_by_type(ContainerType.FRONTEND)
        
        if fcs:
            fcs[0].ports = settings.FRONTEND_PORT  # Set default frontend port

        yaml_content = convert_manifest_to_yaml_string(project_schema)
        
        
        
        # Convert project_schema to the exact LLM response format the frontend expects
        llm_response_obj = _convert_schema_to_exact_llm_format(project_schema)
        
        # Process LLM response and update node (keeping original logic)
        update_data = await _process_llm_response(llm_response_obj, json.dumps(llm_response_obj), request.type)
        
        await node_db.update_node_by_id(
            project_node['id'], 
            update_data,
            NodeType.PROJECT.value.lower()
        )
        
        # Update project node properties for response (keeping original logic)
        _update_project_node_properties(project_node, llm_response_obj, config['temp_project_name'])
        
        if request.type == 'new_project_manual':
            manual_llm_response = _convert_to_manual_project_format(llm_response_obj)
            return {
                "llmResponse": manual_llm_response, 
                "projectNodeInfo": project_node
            }

        # Apply all the same transformations as the original code
        def safe_dict(value):
            return value if value is not None else {}
        
        llm_response_obj['projectTitle'] = safe_dict(llm_response_obj.get('overview')).get('project_name', '')
        llm_response_obj['description'] = safe_dict(llm_response_obj.get('overview')).get('description', '')
        llm_response_obj['layoutDescription'] = safe_dict(safe_dict(llm_response_obj.get('frontend')).get('container')).get('layoutDescription', '')
        llm_response_obj['colors'] = safe_dict(safe_dict(llm_response_obj.get('frontend')).get('container')).get('colors', {})

        if isinstance(llm_response_obj['colors'], dict):
            llm_response_obj['colors'].pop('background', None)

       # Replace the existing feature combination logic with this:
        front_end_feat = safe_dict(safe_dict(llm_response_obj.get('frontend')).get('container')).get('features', [])
        back_end_feat = safe_dict(safe_dict(llm_response_obj.get('backend')).get('container')).get('features', [])

        combined_feat = front_end_feat + back_end_feat

        # Ensure each feature has all required fields and proper ID
        for idx, item in enumerate(combined_feat, start=1):
            if isinstance(item, str):
                # Convert string features to dict format with all required fields
                combined_feat[idx-1] = {
                    'id': str(idx),
                    'name': item,
                    'description': item,
                    'isEnabled': True
                }
            elif isinstance(item, dict):
                # Ensure dict has all required fields
                item['id'] = str(idx)
                if 'description' not in item:
                    item['description'] = item.get('name', '')
                if 'isEnabled' not in item:
                    item['isEnabled'] = True
            else:
                # Fallback for any other type
                combined_feat[idx-1] = {
                    'id': str(idx),
                    'name': str(item),
                    'description': str(item),
                    'isEnabled': True
                }

        llm_response_obj['features'] = combined_feat


        print('llm_response_obj--->>>', llm_response_obj)
        
        # Use the original transform function logic but with project_schema data
        data_to_Convert = transform_to_containers_list(llm_response_obj)
        
        print('data_to_Convert', data_to_Convert)

        # Store project_schema in TempData instead of the transformed data
        project_schema_dict = _convert_project_schema_to_dict(project_schema)
        
        node = await node_db.update_node_by_id(
            project_node['id'], 
            {'TempData': json.dumps(project_schema_dict),'manifest': yaml_content}
        )

        print('node--->', node)

        return {
            "llmResponse": llm_response_obj, 
            "projectNodeInfo": project_node
        }
    
    except Exception as e:
        return {
            "status": "error", 
            "message": f"Error creating project: {str(e)}"
        }


def _convert_schema_to_exact_llm_format(project_schema) -> dict:
    """Convert ProjectSchema to the exact format that mimics the original LLM response"""
    
    # Get containers by type
    backend_containers = project_schema.get_containers_by_type(ContainerType.BACKEND)
    frontend_containers = project_schema.get_containers_by_type(ContainerType.FRONTEND)
    
    # Create the exact structure the frontend expects
    llm_response_obj = {
        'overview': {
            'project_name': project_schema.overview.project_name,
            'description': project_schema.overview.description
        }
    }
    
    # Frontend container data
    if frontend_containers:
        frontend_container = frontend_containers[0]
        frontend_details = frontend_container.container_details if isinstance(frontend_container.container_details, dict) else {}
        
        # Process frontend features to ensure they have all required fields
        frontend_features = frontend_details.get('features', [])
        processed_frontend_features = _process_features_for_validation(frontend_features)
        
        llm_response_obj['frontend'] = {
            'container': {
                'features': processed_frontend_features,
                'colors': frontend_details.get('colors', {}),
                'layoutDescription': frontend_details.get('layout_description', ''),
                'framework': frontend_container.framework
            }
        }
    
    # Backend container data
    if backend_containers:
        backend_container = backend_containers[0]
        backend_details = backend_container.container_details if isinstance(backend_container.container_details, dict) else {}
        
        # Process backend features to ensure they have all required fields
        backend_features = backend_details.get('features', [])
        processed_backend_features = _process_features_for_validation(backend_features)
        
        llm_response_obj['backend'] = {
            'container': {
                'features': processed_backend_features,
                'framework': backend_container.framework
            }
        }
    
    return llm_response_obj


def _process_features_for_validation(features: list) -> list:
    """Process features to ensure they have all required fields for downstream validation"""
    processed_features = []
    
    for feature in features:
        if isinstance(feature, str):
            # Convert string to dict with all required fields
            processed_feature = {
                'name': feature,
                'description': feature,  # Use the feature name as description
                'isEnabled': True  # Default to enabled
            }
        elif isinstance(feature, dict):
            # Ensure dict has all required fields
            processed_feature = {
                'name': feature.get('name', ''),
                'description': feature.get('description', feature.get('name', '')),  # Use name as fallback
                'isEnabled': feature.get('isEnabled', True)  # Default to enabled
            }
        else:
            # Fallback for any other type
            processed_feature = {
                'name': str(feature),
                'description': str(feature),
                'isEnabled': True
            }
        
        processed_features.append(processed_feature)
    
    return processed_features


def _convert_project_schema_to_dict(project_schema) -> dict:
    """Convert ProjectSchema object to dictionary for JSON serialization in TempData"""
    
    schema_dict = {
        'overview': {
            'project_name': project_schema.overview.project_name,
            'description': project_schema.overview.description,
            'third_party_services': project_schema.overview.third_party_services,
        },
        'containers': []
    }
    
    for container in project_schema.containers:
        container_dict = {
            'container_name': container.container_name,
            'description': container.description,
            'interfaces': container.interfaces,
            'container_type': container.container_type.value,
            'dependent_containers': container.dependent_containers,
            'workspace': container.workspace,
            'ports': container.ports,
            'framework': container.framework,
            'type': container.type,
            'buildCommand': container.buildCommand,
            'startCommand': container.startCommand,
            'lintCommand': container.lintCommand,
            'container_details': container.container_details,
            'lintConfig': container.lintConfig,
            'routes': container.routes,
            'apiSpec': container.apiSpec,
            'auth': container.auth,
            'schema': container.schema,
            'migrations': container.migrations,
            'seed': container.seed
        }
        schema_dict['containers'].append(container_dict)
    
    return schema_dict
def transform_to_containers_list_old(input_data: dict) -> dict:
    """
    Transform project structure with enhanced safety for missing/None containers.
    """
    if not input_data:
        return {}
    
    # Create a copy to avoid mutating the original
    output = input_data.copy()
    
    # Container keys to move into the containers list
    container_keys = ['frontend', 'backend', 'database']
    
    # Initialize containers list
    output['containers'] = []
    
    # Move each valid container into the list
    for key in container_keys:
        container_data = output.get(key)
        if container_data and isinstance(container_data, dict):
            container = container_data.copy()
            container['container_type'] = key
            output['containers'].append(container)
        
        # Clean up from root level regardless
        if key in output:
            del output[key]
    
    return output

def transform_to_containers_list(input_data: dict) -> dict:
    """
    Transform project structure with enhanced safety for missing/None containers.
    Excludes keys with None values from the output.
    """
    if not input_data:
        return {}
    
    # Create a copy to avoid mutating the original
    output = input_data.copy()
    
    # Container keys to move into the containers list
    container_keys = ['frontend', 'backend', 'database']
    
    # Initialize containers list
    output['containers'] = []
    
    # Move each valid container into the list
    for key in container_keys:
        container_data = output.get(key)
        if container_data and isinstance(container_data, dict):
            # Filter out None values and add container_type
            container = {k: v for k, v in container_data.items() if v is not None}
            container['container_type'] = key
            output['containers'].append(container)
        
        # Clean up from root level regardless
        if key in output:
            del output[key]
    
    return output

# Helper functions for better code organization
def _get_project_config(request: StartProjectInit) -> Dict[str, Any]:
    """Get configuration based on project type."""
    if request.type == 'direct_code_gen':
        return {
            'temp_project_name': f"Project-{generate_timestamp()}",
            'response_format': ProjectInfoDirectCodeGen,
            'system_prompt': _get_direct_code_gen_system_prompt(),
            'work_item_type':'project'
        }
    elif request.type == 'new_project_manual':
        return {
            'temp_project_name': request.title,
            'response_format': ProjectInfoManual,
            'system_prompt': _get_manual_project_system_prompt(),
            'work_item_type':'container'
        }
    else:
        return {
            'temp_project_name': f"Project-{generate_timestamp()}",
            'response_format': ProjectInfoDirectCodeGen,
            'system_prompt': _get_direct_code_gen_system_prompt(),
            'work_item_type':'project'
        }

def _get_direct_code_gen_system_prompt() -> str:
    """Get system prompt for direct code generation."""
    return """Consider the user input while deriving the properties. Make assumptions only for required fields when there is lack of sufficient information.
If the input from user is very basic/simple use only what is needed frontend or backend or Database (or combination only when necessary, atleast one should be selected).
# At least for one in frontend, backend , database should be selected and necessary field data should be populated. 

Share data in proper response_format as requested."""

# For the project structure:
# 1. Create an overview with project_name, description, and framework selections (use 'None' if framework not needed)
# 2. Generate frontend container if UI/frontend is needed with features, colors, theme, layout, and style
# 3. Generate backend container if server-side logic is needed
# 4. Generate database container if data persistence is needed
# 5. Set dependent_containers appropriately (e.g., frontend depends on backend, backend depends on database)


def _get_manual_project_system_prompt() -> str:
    """Get system prompt for manual project."""
    return f"""Consider the user input while deriving the properties. Make assumptions only for required fields when there is lack of sufficient information (optional can be skipped).

Share data in proper response_format as requested

When selecting architecturePattern refer this info:
{ArcitectureInfo}"""

async def _create_project_node(node_db: NodeDB, properties: Dict[str, Any]):
    """Create project node asynchronously."""
    return await node_db.create_node([NodeType.PROJECT.value], properties)

async def _get_llm_response(llm, user_comment: str, config: Dict[str, Any], model: str) -> str:
    """Get LLM response asynchronously."""
    return await llm.llm_interaction_wrapper(
        messages=[],
        user_prompt=user_comment,
        system_prompt=config['system_prompt'],
        response_format=config['response_format'],
        model=model,
        stream=False
    )

async def _process_llm_response(llm_response_obj: Dict[str, Any], llm_response: str, request_type: str) -> Dict[str, Any]:
    """Process LLM response and prepare update data."""
    update_data = {'Init_project_info': llm_response}
    
    if request_type == 'direct_code_gen':
        # Handle new structure for direct code gen
        if 'overview' in llm_response_obj:
            overview = llm_response_obj['overview']
            update_data.update({
                'Title': overview.get('project_name', ''),
                'Description': overview.get('description', ''),
                'Backend_Framework': overview.get('backend_framework', ''),
                'Frontend_Framework': overview.get('frontend_framework', ''),
                'Database_Framework': overview.get('database_framework', '')
            })
        
        # Handle container information
        for container_type in ['frontend', 'backend', 'database', 'mobile']:
            if container_type in llm_response_obj:
                container_data = llm_response_obj[container_type]
                update_data[f'{container_type.capitalize()}_Container'] = json.dumps(container_data)
    else:
        # Handle legacy format for other types
        keymap = {
            "layoutDescription": "Layout_Description", 
            "colors": "Colors", 
            "features": "Features",
            "projectTitle": "Title",
            "description": "Description",
            "architecturePattern": "ArchitecturePattern"
        }
        
        for key, value in llm_response_obj.items():
            db_key = keymap.get(key, key)
            if isinstance(value, (dict, list)):
                update_data[db_key] = json.dumps(value)
            elif value is None:
                update_data[db_key] = ""
            else:    
                update_data[db_key] = value
    
    return update_data

def _update_project_node_properties(project_node: Dict[str, Any], llm_response_obj: Dict[str, Any], temp_project_name: str):
    """Update project node properties for response."""
    if 'overview' in llm_response_obj:
        overview = llm_response_obj['overview']
        project_node['properties']['Description'] = overview.get('description', '')
        project_node['properties']['Title'] = overview.get('project_name', temp_project_name)
    else:
        # Fallback for legacy format
        project_node['properties']['Description'] = llm_response_obj.get('description', '')
        project_node['properties']['Title'] = llm_response_obj.get('projectTitle', temp_project_name)

#--------------------------------------

# Architecture pattern to container mapping
ARCHITECTURE_CONTAINER_MAPPING = {
    "monolithic-application": {
        "containers": ["application"],
        "description": "Single unified application container"
    },
    "monolithic-service": {
        "containers": ["service"],
        "description": "Single unified service container with API exposure"
    },
    "multi-container-single-component": {
        "containers": ["frontend", "backend", "database"],
        "description": "Multiple containers, each with single focused component"
    },
    "multi-container-service": {
        "containers": ["frontend", "backend", "database"],
        "description": "Multiple service containers with well-defined interfaces"
    },
    "adaptive": {
        "containers": ["frontend", "backend", "database", "api-gateway", "cache"],
        "description": "Flexible multi-container architecture"
    }
}

def _determine_architecture_pattern(request: StartProjectInit) -> str:
    """Determine architecture pattern based on request type and complexity."""
    if request.type == 'new_project_manual':
        # For manual projects, default to multi-container-service for flexibility
        return "multi-container-service"
    
    # Analyze user input for complexity indicators
    user_input = request.usercomment.lower()
    complexity_indicators = {
        'simple': ['simple', 'basic', 'minimal', 'single page'],
        'api': ['api', 'service', 'backend', 'rest', 'graphql'],
        'multi': ['dashboard', 'admin', 'complex', 'microservice', 'scalable']
    }
    
    if any(indicator in user_input for indicator in complexity_indicators['multi']):
        return "multi-container-service"
    elif any(indicator in user_input for indicator in complexity_indicators['api']):
        return "multi-container-single-component"
    elif any(indicator in user_input for indicator in complexity_indicators['simple']):
        return "monolithic-application"
    else:
        return "multi-container-single-component"  # Default

def _get_container_guidance(architecture_pattern: str) -> str:
    """Get specific guidance for container structure based on architecture pattern."""
    guidance_map = {
        "monolithic-application": """
CONTAINER STRUCTURE:
- Single "application" container containing all functionality
- No external API exposure, direct UI interactions only
- Self-contained with embedded database if needed""",
        
        "monolithic-service": """
CONTAINER STRUCTURE:
- Single "service" container with unified functionality
- Must expose well-defined external APIs
- Designed for service integration""",
        
        "multi-container-single-component": """
CONTAINER STRUCTURE:
- "frontend" container: User interface and client-side logic
- "backend" container: Business logic and API layer
- "database" container: Data persistence layer
- Each container has exactly one focused component
- Clear interfaces between containers""",
        
        "multi-container-service": """
CONTAINER STRUCTURE:
- "frontend" container: Complete frontend service with UI/UX
- "backend" container: Business logic service with API exposure
- "database" container: Data persistence service
- Each container is a complete service with well-defined provider interfaces
- Independent deployment and scaling per service""",
        
        "adaptive": """
CONTAINER STRUCTURE:
- "frontend" container: User interface service
- "backend" container: Core business logic service  
- "database" container: Data persistence service
- "api-gateway" container: Request routing and authentication
- "cache" container: Performance optimization layer
- Flexible component organization within containers"""
    }
    
    return guidance_map.get(architecture_pattern, guidance_map["multi-container-single-component"])


async def _create_project_node(node_db: NodeDB, properties: Dict[str, Any]) -> Dict[str, Any]:
    """Create project node asynchronously."""
    return await node_db.create_node([NodeType.PROJECT.value], properties)

def _build_user_prompt(request: StartProjectInit) -> str:
    """Build comprehensive user prompt with all available context."""
    prompt_parts = [request.usercomment]
    
    # FIXED: Safe attribute access
    if hasattr(request, 'overview_data') and request.overview_data:
        overview_info = "\n\nAdditional Project Information:"
        for key, value in request.overview_data.items():
            if value and value.strip():
                overview_info += f"\n- {key.replace('_', ' ').title()}: {value}"
        prompt_parts.append(overview_info)
    
    return "\n".join(prompt_parts)

async def _generate_project_structure(
    llm: LLMInterface, 
    request: StartProjectInit, 
    system_prompt: str, 
    response_format: type, 
    model: str
) -> str:
    """Generate project structure using LLM."""
    try:
        user_prompt = _build_user_prompt(request)
        
        return await llm.llm_interaction_wrapper(
            messages=[],
            user_prompt=user_prompt,
            system_prompt=system_prompt,
            response_format=response_format,
            model=model,
            stream=False
        )
    
    except Exception as e:
        print('error---<', e)


async def _process_enhanced_structure(project_structure: Dict[str, Any]) -> Dict[str, Any]:
    """Process the enhanced project structure for database storage."""
    update_data = {
        'enhanced_project_structure': json.dumps(project_structure),
        'configuration_state': 'structured'
    }
    
    # Extract overview data
    if 'overview' in project_structure:
        overview = project_structure['overview']
        update_data.update({
            'Title': overview.get('project_name', ''),
            'Description': overview.get('description', ''),
            'frontend_framework': overview.get('frontend_framework', ''),
            'backend_framework': overview.get('backend_framework', ''),
            'database_framework': overview.get('database_framework', '')
        })
    
    # Store container information
    for container_type in ['frontend', 'backend', 'database']:
        if container_type in project_structure:
            container_data = project_structure[container_type]
            update_data[f'{container_type}_container'] = json.dumps(container_data)
    
    return update_data

def _extract_container_info(project_structure: Dict[str, Any]) -> Dict[str, Any]:
    """Extract container summary information for response."""
    containers = {}
    for container_type in ['frontend', 'backend', 'database']:
        if container_type in project_structure:
            container = project_structure[container_type]
            containers[container_type] = {
                'name': container.get('container_name', ''),
                'description': container.get('description', ''),
                'dependencies': container.get('dependent_containers', [])
            }
    return containers


def _generate_container_summary(project_structure: Dict[str, Any], architecture_pattern: str) -> Dict[str, Any]:
    """Generate a summary of containers and their relationships."""
    summary = {
        "pattern": architecture_pattern,
        "containers": [],
        "dependencies": {},
        "deployment_units": len([k for k in project_structure.keys() if k != 'overview'])
    }
    
    for container_type in ['frontend', 'backend', 'database', 'api-gateway', 'cache']:
        if container_type in project_structure:
            container = project_structure[container_type]
            container_info = {
                "type": container_type,
                "name": container.get('container_name', ''),
                "description": container.get('description', ''),
                "interfaces": container.get('interfaces', ''),
                "dependencies": container.get('dependent_containers', [])
            }
            summary["containers"].append(container_info)
            summary["dependencies"][container_type] = container.get('dependent_containers', [])
    
    return summary