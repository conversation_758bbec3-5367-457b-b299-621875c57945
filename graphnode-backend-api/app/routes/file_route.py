import uuid
import mimetypes
import json
import yaml
import os
import time
import asyncio
from fastapi import APIRouter, UploadFile, File, HTTPException, Form,Depends,BackgroundTasks
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel
from app.telemetry.logger_config import get_logger
from app.utils.aws_utils import extract_text_with_textract, upload_file_to_s3, get_s3_file_url
from app.utils.file_utils.pdf_utils import extract_text_from_pdf
from app.utils.file_utils.excel_utils import extract_text_from_excel
from app.utils.file_utils.text_utils import extract_text_from_text_file
from app.utils.file_utils.image_utils import extract_text_from_image, generate_preview, generate_thumbnail, get_primary_color, image_to_bytes, image_to_base64url
from app.utils.file_utils.upload_utils import upload_and_process, create_thumbnail
from PIL import Image
from io import BytesIO, StringIO
import io
import logging
from app.connection.tenant_middleware import get_tenant_id
from app.utils.prodefn.projdefn import ProjDefn, ProjDefnReporter, ProjDefnDocSpecifier
from app.utils.prodefn.projdefn_helper import Reporter, Helpers, ProjDefn_Helper
from app.utils.prodefn.docs_session_manager import get_or_create_session
from app.utils.auth_utils import get_current_user
from app.utils.file_utils.upload_utils import upload_and_process, s3_client, get_tenant_bucket
from app.utils.file_utils.upload_utils import generate_document_presigned_url
from app.connection.establish_db_connection import get_mongo_db
from datetime import datetime
from app.core.Settings import Settings
from typing import List
from app.telemetry.logger_config import get_logger
from werkzeug.utils import secure_filename
import threading
from io import BytesIO

from app.core.constants import CODE_GEN_ATTACHMENT_COLLECTION
_SHOW_NAME = "file"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)

settings = Settings()
update_logger = get_logger(__name__)

# Add these constants and functions
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB in bytes
ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'py', 'js', 'json', 'yaml', 'yml'}
base_path_for_attachments = "/app/data"

if os.environ.get("LOCAL_DEBUG"):
    base_path_for_attachments = "/tmp"

def allowed_file(filename):
    is_allowed = '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS
    update_logger.debug(f"File extension check for {filename}: {is_allowed}")
    return is_allowed

def validate_file(file_content, filename, file_size):
    update_logger.info(f"Validating file: {filename}, size: {file_size}")
    
    # Check file size
    if file_size > MAX_FILE_SIZE:
        update_logger.warning(f"File size {file_size} exceeds limit of {MAX_FILE_SIZE}")
        return False, "File size exceeds limit"
    
    # Check file type
    if not allowed_file(filename):
        update_logger.warning(f"File type not allowed for {filename}")
        return False, "File type not allowed"
    
    # Basic content validation
    try:
        # Check for executable content (first 1024 bytes)
        content_sample = file_content[:1024]
        if content_sample.startswith(b'MZ') or content_sample.startswith(b'#!'):
            update_logger.warning(f"Executable content detected in {filename}")
            return False, "Executable files not allowed"
            
    except Exception as e:
        update_logger.error(f"File validation error for {filename}: {str(e)}")
        return False, f"File validation error: {str(e)}"
    
    update_logger.info(f"File validation successful for {filename}")
    return True, "File is valid"

def get_file_type(filename):
    file_type = mimetypes.guess_type(filename)[0] or 'application/octet-stream'
    update_logger.debug(f"Detected file type for {filename}: {file_type}")
    return file_type

def initialize_projdefn(base_path, current_user, session_id, tenant_id, project_id):
    """Initialize the project definition handler"""
    update_logger.info(f"Initializing project definition handler for session {session_id}")

    try:
        reporter = Reporter()
        update_logger.info(f"📊 Reporter created: {reporter}")
        helper = ProjDefn_Helper(
            reporter=reporter,
            base_path=base_path,
            session_id=session_id,
            tenant_id=tenant_id,
            project_id=project_id
        )
        update_logger.info(f"🛠️ Helper created: {helper}")
        configuration = {
            "base_path": base_path,
            "model": "gpt-4.1-nano-2025-04-14",
            "timeout": 60,
            "chunk_size": 64*1024,
            "cost_tracer": None,
            "reporter": reporter,
            "helpers": helper
        }
        update_logger.info(f"⚙️ Configuration: {configuration}")
        # Get or create ProjDefn instance with session
        projdefn = ProjDefn.getInstance(configuration, f"default_{session_id}")
        update_logger.info(f"🏭 ProjDefn instance created: {projdefn}")
        projdefn.start()
        update_logger.info(f"🚀 ProjDefn started successfully")
        update_logger.info(f"Project definition handler initialized successfully for session {session_id}")
        return projdefn
    except Exception as e:
        update_logger.error(f"Error initializing project definition handler: {str(e)}")
        update_logger.info(f"Error initializing project definition handler: {str(e)}")
        raise

def wait_for_processing_complete(reporter, timeout=300):
    """Wait for document processing to complete"""
    update_logger.info(f"Waiting for document processing to complete (timeout: {timeout}s)")
    start_time = time.time()
    while not reporter.is_ready() and (time.time() - start_time) < timeout:
        time.sleep(1)
    is_ready = reporter.is_ready()
    update_logger.info(f"Document processing {'completed' if is_ready else 'timed out'}")
    return is_ready

@router.post("/upload")
async def upload(file: UploadFile = File(...), discussion_id: str = Form(...)):
    update_logger.info(f"Processing upload request for file: {file.filename}, discussion_id: {discussion_id}")
    tenant_id = get_tenant_id()
    content = await file.read()
    
    # Check file type using mimetypes
    file_type = get_file_type(file.filename)
    
    if not file_type.startswith('image/'):
        update_logger.warning(f"Invalid file type: {file_type} for file {file.filename}")
        raise HTTPException(status_code=400, detail="Only image files are allowed")

    try:
        # Generate a UUID for the file
        file_uuid = str(uuid.uuid4())
        update_logger.info(f"Generated UUID for file {file.filename}: {file_uuid}")
        
        # Process the image
        with Image.open(BytesIO(content)) as img:
            width, height = img.size
            update_logger.debug(f"Image dimensions - width: {width}, height: {height}")
            
            # Create thumbnail
            thumbnail = create_thumbnail(img.copy())
            thumbnail_io = BytesIO()
            thumbnail.save(thumbnail_io, format=img.format)
            thumbnail_io.seek(0)
            
            # Create preview (full-size image)
            preview_io = BytesIO()
            img.save(preview_io, format=img.format)
            preview_io.seek(0)
        
        # Upload original file
        update_logger.info(f"Uploading original file to S3 for {file_uuid}")
        original_data = upload_and_process(file_uuid, content, f"original_{file.filename}", file_type, tenant_id=tenant_id)

        # Generate base64 URLs
        original_url = image_to_base64url(content, file_type)
        thumbnail_url = image_to_base64url(thumbnail_io.getvalue(), file_type)
        preview_url = image_to_base64url(preview_io.getvalue(), file_type)
        
        # Construct the response
        response = {
            "file_kind": "image",
            "file_uuid": file_uuid,
            "file_name": file.filename,
            "file_type": file_type,
            "original_url": original_url,
            "thumbnail_url": thumbnail_url,
            "preview_url": preview_url,
            "thumbnail_asset": {
                "url": thumbnail_url,
                "file_variant": "thumbnail",
                "primary_color": "ffffff",
                "image_width": min(400, width),
                "image_height": int(min(400, width) * height / width)
            },
            "preview_asset": {
                "url": preview_url,
                "file_variant": "preview",
                "primary_color": "ffffff",
                "image_width": width,
                "image_height": height
            },
            "discussion_id": discussion_id,
            "s3_location": original_data['s3_location']
        }
        
        update_logger.info(f"Successfully processed and uploaded file {file.filename} with UUID {file_uuid}")
        return JSONResponse(content=response, status_code=200)
    
    except HTTPException as he:
        update_logger.error(f"HTTP Exception during file upload: {str(he)}")
        raise he
    except Exception as e:
        update_logger.error(f"Error processing file upload: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    
async def get_document_handler():
    """Get MongoDB handler for ingested_documents collection"""
    return get_mongo_db(
        db_name=settings.MONGO_DB_NAME,
        collection_name='ingested_documents'
    )


async def update_document_status(file_uuid: str, status_update: dict):
    try:
        """Update the document status in the ingested_documents collection"""
        # Get MongoDB handler
        doc_handler = await get_document_handler()
        # Create the processing_status update
        processing_status = {
            "status": status_update.get("status", "processing"),
            "updated_at": datetime.now()
        }
        
        # Add additional fields if they exist
        if "error" in status_update:
            processing_status["error"] = status_update["error"]
        if "error_details" in status_update:
            processing_status["error_details"] = status_update["error_details"]
        if "progress" in status_update:
            processing_status["progress"] = status_update["progress"]
        if "s3_location" in status_update:
            processing_status["s3_location"] = status_update["s3_location"]
        if "end_time" in status_update:
            processing_status["end_time"] = status_update["end_time"]
        if "start_time" in status_update:
            processing_status["start_time"] = status_update["start_time"]

        # First try to find the document
        existing_doc = await doc_handler.get_one(
            {"document_record.file_uuid": file_uuid},
            db=doc_handler.db
        )

        if existing_doc:
            print(f"Updating existing document for file_uuid: {file_uuid}")
            # Update only processing status for existing document
            update_data = {"processing_status": processing_status}
            result = await doc_handler.update_one(
                {"document_record.file_uuid": file_uuid},
                update_data,
                db=doc_handler.db
            )
        else:
            print(f"Creating new document for file_uuid: {file_uuid}")
            # Create complete document for new entry
            document_record = {
                "project_id": status_update.get("project_id"),
                "file_name": status_update.get("filename", "Unknown"),
                "file_uuid": file_uuid,
                "file_type": status_update.get("file_type", "application/pdf"),
                "file_extension": status_update.get("file_extension", ""),
                "file_size": status_update.get("file_size", 0),
                "uploaded_by": {
                    "user_id": status_update.get("user_id"),
                    "user_name": status_update.get("user_name", "Unknown")
                },
                "tenant_id": status_update.get("tenant_id"),
                "session_id": status_update.get("session_id"),
                "created_at": datetime.now()
            }

            new_document = {
                "document_record": document_record,
                "processing_status": processing_status
            }

            # Insert new document
            result = await doc_handler.insert(new_document, db=doc_handler.db)
            print(f"Created new document with ID: {result}")

    except Exception as e:
        print('error with update_document_status', e)  

async def process_document_background(
    file_path: str, 
    file_uuid: str, 
    session_id: str, 
    filename: str, 
    project_id: int,
    tenant_id: str,
    user_id: str,
    user_name: str
):
    """Background task to process the uploaded document"""
    try:
        print(f"Starting document processing for {filename}")
        update_logger.info(f"Starting document processing for {filename}")
        update_logger.info(f"File details - Path: {file_path}, UUID: {file_uuid}, Session: {session_id}")
        
        # Check if file actually exists
        if not os.path.exists(file_path):
            update_logger.info(f"File does not exist at path: {file_path}")
            return

        # Get file size for timeout calculation
        file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
        update_logger.info(f"File size: {file_size} bytes ({file_size/1024:.2f} KB)")
        file_size_kb = file_size / 1024
        
        # Calculate timeout based on file size (1 second per KB, max 1800 seconds)
        size_factor = 0.3 # 1 second per KB
        process_timeout = min(file_size_kb * size_factor, 1800)
        process_timeout = max(process_timeout, 60)  # Minimum 60 seconds
        
        update_logger.info(f"Calculated timeout: {process_timeout} seconds")

        # Create initial document record with progress tracking
        await update_document_status(file_uuid, {
            "status": "processing",
            "start_time": datetime.now(),
            "filename": filename,
            "project_id": project_id,
            "tenant_id": tenant_id,
            "user_id": user_id,
            "user_name": user_name,
            "session_id": session_id,
            "file_type": "application/pdf",
            "file_extension": filename.split(".")[-1],
            "file_size": file_size,
            "timeout_value": process_timeout,  # Store the calculated timeout
            "progress": {
                "elapsed_time": 0,
                "percentage": 0,
                "is_active": True
            }
        })

        # Set up directories
        base_path = "/tmp/doc_ingestion/dir"
        projdefn_dir = os.path.join(base_path, '.projdefn')
        os.makedirs(projdefn_dir, exist_ok=True)

        update_logger.info(f"Base path created: {base_path}")

        # Initialize project definition handler
        update_logger.info(f"🔧 Initializing ProjDefn handler...")
        try:
            projdefn = initialize_projdefn(base_path, "default_user", session_id, tenant_id, project_id)
            update_logger.info(f"✅ ProjDefn handler initialized successfully")
        except Exception as init_error:
            update_logger.info(f"❌ ProjDefn initialization failed: {str(init_error)}")
            raise init_error
        
        try:

            # === ENHANCED PATH DEBUGGING ===
            update_logger.info(f"📥 Adding document to ingest queue...")
            update_logger.info(f"📂 File path: {file_path}")
            update_logger.info(f"📂 Base path: {base_path}")
            update_logger.info(f"📂 File exists: {os.path.exists(file_path)}")

            # List directory contents
            update_logger.info(f"📁 Directory contents of {base_path}:")
            try:
                contents = os.listdir(base_path)
                for item in contents:
                    item_path = os.path.join(base_path, item)
                    is_file = os.path.isfile(item_path)
                    size = os.path.getsize(item_path) if is_file else 0
                    update_logger.info(f"  📄 {item} ({'file' if is_file else 'dir'}, {size} bytes)")
            except Exception as list_error:
                update_logger.info(f"❌ Cannot list directory: {list_error}")

            try:
                relative_path = os.path.relpath(file_path, base_path)
                update_logger.info(f"📂 Relative path: {relative_path}")
            except Exception as rel_error:
                update_logger.info(f"❌ Cannot get relative path: {rel_error}")
            
            # Add document to ingest queue
            update_logger.info(f"📥 Adding document to ingest queue...")
            docspec = ProjDefnDocSpecifier(file_path, filename, file_uuid)
            update_logger.info(f"📋 DocSpec created: {docspec}")
            projdefn.addToIngestQueueWithSpec(docspec)
            update_logger.info(f"✅ Document added to ingest queue successfully")

            # Check queue status immediately after adding
            queue_status = projdefn.getQueueStatus() if hasattr(projdefn, 'getQueueStatus') else "Unknown"
            update_logger.info(f"📊 Queue status after adding: {queue_status}")
                
        except Exception as queue_error:
            update_logger.info(f"❌ Failed to add document to queue: {str(queue_error)}")
            raise queue_error        

        # Set up S3 paths
        bucket_name = get_tenant_bucket(tenant_id)
        prefix = f"extracted-docs-{tenant_id}/project-{project_id}/digested-files/doc__{filename}.json"
        
        # Wait for processing to complete with timeout
        # process_timeout = 3600
        start_time = time.time()
        file_exists = False
        processing_complete = False

        # Update the completion check function
        processed_operations = 0
        total_operations = 0
        def is_criteria_processing_complete():
            print(f"🔢 Checking completion status - processed: {processed_operations}, total: {total_operations}")
            update_logger.info(f"🔢 Checking completion status - processed: {processed_operations}, total: {total_operations}")
            
            if processed_operations == 0 and total_operations == 0:
                return False
            return processed_operations >= total_operations and total_operations > 0

        print(f"⏳ Waiting for document processing to complete (timeout: {process_timeout}s)")
        update_logger.info(f"⏳ Waiting for document processing to complete (timeout: {process_timeout}s)")

        update_logger.info(f"⏳ Starting processing wait loop...")

        while not processing_complete and (time.time() - start_time) < process_timeout:
            current_time = time.time()
            elapsed_time = current_time - start_time
            try:
                # Check if criteria processing is complete
                if is_criteria_processing_complete():
                    processing_complete = True
                    print(f"🎉 Document processing completed for {filename}!")
                    update_logger.info(f"🎉 Document processing completed for {filename}!")

                    bucket_name = get_tenant_bucket(tenant_id)
                    prefix = f"extracted-docs-{tenant_id}/project-{project_id}/digested-files/doc__{filename}.json"

                    status_update = {
                        "status": "completed",
                        "end_time": datetime.now(),
                        "s3_location": f"{bucket_name}/{prefix}",
                        "progress": {
                            "elapsed_time": round(elapsed_time, 2),
                            "percentage": 100,
                            "is_active": False,
                            "timeout_value": process_timeout,
                            "start_time": datetime.fromtimestamp(start_time).isoformat()
                        }
                    }

                    await update_document_status(file_uuid, status_update)

                    return
                else:
                    # Not complete yet, continue waiting and updating progress
                    progress_metrics = projdefn.reporter.calculate_progress_metrics()
                    progress_value = progress_metrics.get("progress_percent",0)
                    processed_operations = progress_metrics.get("processed_operations",0)
                    total_operations = progress_metrics.get("total_operations",0)


                    if int(elapsed_time) % 10 == 0:  # Log every 10 seconds
                        print(f"⏱️ Processing in progress: {progress_value:.1f}% complete, elapsed: {elapsed_time:.1f}s")
                        update_logger.info(f"⏱️ Processing in progress: {progress_value:.1f}% complete, elapsed: {elapsed_time:.1f}s")

                    status_update = {
                        "status": "processing",
                        "percentage": round(progress_value, 1),
                        "progress": {
                            "elapsed_time": round(elapsed_time, 2),
                            "percentage": round(progress_value, 1),
                            "is_active": True,
                            "timeout_value": process_timeout,
                            "start_time": datetime.fromtimestamp(start_time).isoformat()
                        }
                    }

                    await update_document_status(file_uuid, status_update)

                await asyncio.sleep(4)

            except Exception as e:
                print(f"❌ Document exception occurred: {str(e)}")
                update_logger.info(f"❌ Document exception occurred: {str(e)}")
                await asyncio.sleep(1)  # Add delay to avoid tight loop on errors

    except Exception as e:
        error_msg = str(e)
        print(f"Processing failed with error: {error_msg}")
        update_logger.info(f"Processing failed with error: {error_msg}")
        
        # Update MongoDB with failure status but still mark progress as 100%
        await update_document_status(file_uuid, {
            "status": "failed",
            "end_time": datetime.now(),
            "error": error_msg,
            "error_details": error_msg,
            "progress": {
                "percentage": 100,
                "is_active": False
            }
        })
    finally:
        if 'projdefn' in locals():
            print("Releasing ProjDefn instance")
            update_logger.info("Releasing ProjDefn instance")
            ProjDefn.releaseInstance(f"default_{session_id}")

@router.post("/v1/extract_text/")
async def extract_text(file: UploadFile = File(...)):
    content = await file.read()
    # Generate unique identifiers
    file_uuid = str(uuid.uuid4())
    file_type = get_file_type(file.filename)
    file_extension = file.filename.split('.')[-1].lower()

    try:
        # Extract text based on file type
        if file_extension == 'pdf':
            text = extract_text_from_pdf(content)
            if text is None:
                raise HTTPException(status_code=500, detail="Failed to extract text from PDF")
        elif file_extension in ['xlsx', 'xls']:
            text = extract_text_from_excel(content)
        elif file_type.startswith('text/') or file_extension in ['txt', 'csv', 'log', 'py', 'js']:
            text = extract_text_from_text_file(content)
        elif file_extension in ['json']:
            try:
                json_data = json.loads(content.decode('utf-8'))
                text = json.dumps(json_data, indent=2)
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="Invalid JSON file")
        elif file_extension in ['yml', 'yaml']:
            try:
                yaml_data = yaml.safe_load(content)
                text = yaml.dump(yaml_data, default_flow_style=False)
            except yaml.YAMLError:
                raise HTTPException(status_code=400, detail="Invalid YAML file")
        elif file_extension in ['xml']:
            # You might want to add XML parsing logic here
            text = content.decode('utf-8')
        else:
            raise HTTPException(status_code=400, detail="Unsupported file type")

        response = {
            "file_name": file.filename,
            "file_size": len(content),
            "file_type": file_type,
            "extracted_content": text
        }

        return JSONResponse(content=response, status_code=200)
    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.post("/extract_text")
async def extract_text(background_tasks: BackgroundTasks, project_id, files: List[UploadFile] = File(...), session_id: str = None, current_user: dict = Depends(get_current_user)):
    update_logger.info(f"Starting text extraction for project_id: {project_id}, session_id: {session_id}")
    
    tenant_id = get_tenant_id()
    base_path = "/tmp/doc_ingestion/dir"
    os.makedirs(base_path, exist_ok=True)
    
    responses = []
    for file in files:
        try:
            update_logger.info(f"Processing file: {file.filename}")
            content = await file.read()
            file_uuid = str(uuid.uuid4())
            file_type = get_file_type(file.filename)
            file_extension = file.filename.split('.')[-1].lower()
            project_id = int(project_id)

            update_logger.debug(f"File details - UUID: {file_uuid}, Type: {file_type}, Extension: {file_extension}")

            # Create a temporary file path using full absolute path
            safe_filename = file.filename.replace(" ", "_")  # Replace spaces with underscores
            temp_file_path = os.path.abspath(os.path.join(base_path, safe_filename))
            update_logger.info(f"Writing file to: {temp_file_path}")
            
            # Create the file
            with open(temp_file_path, "wb") as temp_file:
                temp_file.write(content)
                
            update_logger.debug(f"File created successfully at {temp_file_path}")

            base_path = f"extracted-docs-{tenant_id}"
            project_folder = f"project-{project_id}"
            digested_folder = "digested-file-content"
            original_file_path = f"{base_path}/{project_folder}"

            result = upload_and_process(
                identifier=project_folder,
                file_content=content,
                file_name=file.filename,
                content_type=file_type,
                tenant_id=tenant_id,
                folder_name=base_path
            )

            user_id = current_user.get("cognito:username")
            user_name = current_user.get("custom:Name")
            
            # Create ingest session
            update_logger.info(f"Creating ingest session for user: {user_id}")
            session_info = await get_or_create_session(user_id, project_id)
            session_id = session_info["session_id"]

            # Add background task
            update_logger.info(f"Adding background processing task for file: {file.filename}")
            background_tasks.add_task(
                process_document_background,
                temp_file_path,
                file_uuid,
                session_id,
                file.filename,
                project_id,
                tenant_id,
                user_id,
                user_name
            )

            responses.append({
                "file_name": file.filename,
                "file_size": len(content),
                "file_type": file_type,
                "status": "processing",
                "file_uuid": file_uuid
            })

            update_logger.info(f"Successfully queued file {file.filename} for processing")

        except Exception as e:
            update_logger.error(f"Error processing file {file.filename}: {str(e)}")
            responses.append({
                "file_name": file.filename,
                "status": "failed",
                "error": str(e)
            })

    return JSONResponse(content={
        "message": "Document processing started",
        "files": responses
    })

@router.post("/extract_text_discussion")
async def extract_text(file: UploadFile = File(...)):
    content = await file.read()
    # Generate unique identifiers
    file_uuid = str(uuid.uuid4())
    file_type = get_file_type(file.filename)
    file_extension = file.filename.split('.')[-1].lower()

    try:
        # Extract text based on file type
        if file_extension == 'pdf':
            text = extract_text_from_pdf(content)
            if text is None:
                raise HTTPException(status_code=500, detail="Failed to extract text from PDF")
        elif file_extension in ['xlsx', 'xls']:
            text = extract_text_from_excel(content)
        elif file_type.startswith('text/') or file_extension in ['txt', 'csv', 'log', 'py', 'js']:
            text = extract_text_from_text_file(content)
        elif file_extension in ['json']:
            try:
                json_data = json.loads(content.decode('utf-8'))
                text = json.dumps(json_data, indent=2)
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="Invalid JSON file")
        elif file_extension in ['yml', 'yaml']:
            try:
                yaml_data = yaml.safe_load(content)
                text = yaml.dump(yaml_data, default_flow_style=False)
            except yaml.YAMLError:
                raise HTTPException(status_code=400, detail="Invalid YAML file")
        elif file_extension in ['xml']:
            # You might want to add XML parsing logic here
            text = content.decode('utf-8')
        else:
            raise HTTPException(status_code=400, detail="Unsupported file type")

        response = {
            "file_name": file.filename,
            "file_size": len(content),
            "file_type": file_type,
            "extracted_content": text
        }

        return JSONResponse(content=response, status_code=200)
    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/convert-word-to-txt")
async def convert_word_to_txt(file: UploadFile = File(...)):
    """
    Convert Word document (.doc or .docx) to .txt file

    This endpoint accepts a Word document and returns the extracted text as a downloadable .txt file.
    """
    update_logger.info(f"Converting Word document to TXT: {file.filename}")

    try:
        # Read file content
        content = await file.read()
        file_extension = file.filename.split('.')[-1].lower()

        # Validate file type
        if file_extension not in ['doc', 'docx']:
            raise HTTPException(
                status_code=400,
                detail="Only .doc and .docx files are supported for conversion"
            )

        # Validate file size
        file_size = len(content)
        if file_size > MAX_FILE_SIZE:
            raise HTTPException(
                status_code=400,
                detail=f"File size ({file_size} bytes) exceeds maximum allowed size ({MAX_FILE_SIZE} bytes)"
            )

        # Convert to TXT
        txt_content_bytes = convert_word_to_txt_bytes(content, file_extension)

        if txt_content_bytes is None:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to extract text from {file_extension.upper()} file"
            )

        # Generate output filename with new format: filename(extension).txt
        txt_filename = generate_txt_filename(file.filename)

        update_logger.info(f"Successfully converted {file.filename} to TXT format")

        # Return the TXT content as a response with download headers
        from fastapi.responses import Response

        return Response(
            content=txt_content_bytes,
            media_type='text/plain',
            headers={
                "Content-Disposition": f"attachment; filename={txt_filename}",
                "Content-Type": "text/plain; charset=utf-8"
            }
        )

    except HTTPException as he:
        update_logger.error(f"HTTP Exception during Word to TXT conversion: {str(he)}")
        raise he
    except Exception as e:
        update_logger.error(f"Error converting Word document to TXT: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Conversion failed: {str(e)}")


@router.post("/convert-word-to-txt-json")
async def convert_word_to_txt_json(file: UploadFile = File(...)):
    """
    Convert Word document (.doc or .docx) to text and return as JSON response

    This endpoint accepts a Word document and returns the extracted text in JSON format.
    """
    update_logger.info(f"Converting Word document to text (JSON response): {file.filename}")

    try:
        # Read file content
        content = await file.read()
        file_extension = file.filename.split('.')[-1].lower()

        # Validate file type
        if file_extension not in ['doc', 'docx']:
            raise HTTPException(
                status_code=400,
                detail="Only .doc and .docx files are supported for conversion"
            )

        # Validate file size
        file_size = len(content)
        if file_size > MAX_FILE_SIZE:
            raise HTTPException(
                status_code=400,
                detail=f"File size ({file_size} bytes) exceeds maximum allowed size ({MAX_FILE_SIZE} bytes)"
            )

        # Extract text
        extracted_text = extract_text_from_word_document(content, file_extension)

        if extracted_text is None:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to extract text from {file_extension.upper()} file"
            )

        # Generate output filename with new format: filename(extension).txt
        txt_filename = generate_txt_filename(file.filename)

        update_logger.info(f"Successfully extracted text from {file.filename}")

        # Return JSON response with extracted text
        response = {
            "original_filename": file.filename,
            "converted_filename": txt_filename,
            "file_size": file_size,
            "file_type": file_extension,
            "extracted_text": extracted_text,
            "text_length": len(extracted_text),
            "status": "success"
        }

        return JSONResponse(content=response, status_code=200)

    except HTTPException as he:
        update_logger.error(f"HTTP Exception during Word to text conversion: {str(he)}")
        raise he
    except Exception as e:
        update_logger.error(f"Error converting Word document to text: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Conversion failed: {str(e)}")


# @router.get("/list-project-files/{project_id}")
# async def list_project_files(project_id: int, current_user: dict = Depends(get_current_user)):
#     try:
#         tenant_id = get_tenant_id()
#         bucket_name = get_tenant_bucket(tenant_id)
        
#         # Construct the prefix for the project folder
#         prefix = f"extracted-docs-{tenant_id}/project-{project_id}/"
        
#         # List objects in the S3 bucket with the specified prefix
#         response = s3_client.list_objects_v2(
#             Bucket=bucket_name,
#             Prefix=prefix
#         )

#         doc_handler = await get_document_handler()
        
#         files = []
#         if 'Contents' in response:
#             for obj in response['Contents']:
#                 if obj['Key'].lower().endswith('.pdf'):
#                     filename = os.path.basename(obj['Key'])
#                     user_name = 'Unknown'
#                     status = 'null'

#                     #get file details from mongo
#                     file_details_in_db = await doc_handler.get_one(
#                         {
#                             'document_record.file_name': filename,
#                             'document_record.project_id': project_id
#                         }
#                     , doc_handler.db)

#                     if file_details_in_db:
#                         document_record = file_details_in_db.get('document_record',{})
#                         uploaded_by = document_record.get('uploaded_by')
#                         user_name = uploaded_by.get('user_name')

#                         processing_status = file_details_in_db.get('processing_status')
#                         status = processing_status.get('status')
                    
#                     # Generate URLs for viewing and downloading
#                     view_url = generate_document_presigned_url(
#                         bucket=bucket_name,
#                         key=obj['Key'],
#                         disposition='inline'
#                     )
                    
#                     download_url = generate_document_presigned_url(
#                         bucket=bucket_name,
#                         key=obj['Key'],
#                         disposition=f'attachment; filename="{filename}"'
#                     )
                    
#                     files.append({
#                         "filename": filename,
#                         "size": obj['Size'],
#                         "last_modified": obj['LastModified'].isoformat(),
#                         "download_url": download_url,
#                         "view_url": view_url,
#                         "s3_location": f"{bucket_name}/{obj['Key']}",
#                         "uploaded_by": user_name,
#                         "status": status
#                     })
        
#         return JSONResponse(content={
#             "project_id": project_id,
#             "file_count": len(files),
#             "files": files
#         })
        
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=str(e))

@router.get("/list-project-files/{project_id}")
async def list_project_files(project_id: int, current_user: dict = Depends(get_current_user)):
    update_logger.info(f"Listing files for project_id: {project_id}")
    try:
        tenant_id = get_tenant_id()
        bucket_name = get_tenant_bucket(tenant_id)
        
        # Construct the prefix for the project folder
        prefix = f"extracted-docs-{tenant_id}/project-{project_id}/"
        update_logger.debug(f"Listing S3 objects with prefix: {prefix}")
        
        # List objects in the S3 bucket with the specified prefix
        response = s3_client.list_objects_v2(
            Bucket=bucket_name,
            Prefix=prefix
        )

        doc_handler = await get_document_handler()
        
        files = []
        if 'Contents' in response:
            update_logger.info(f"Found {len(response['Contents'])} files in S3")
            for obj in response['Contents']:
                filename = os.path.basename(obj['Key'])
                user_name = 'Unknown'
                status = 'null'

                # Get file details from mongo
                update_logger.debug(f"Fetching details for file: {filename}")
                file_details_in_db = await doc_handler.get_one(
                    {
                        'document_record.file_name': filename,
                        'document_record.project_id': project_id,
                        '$or': [
                            {'isDeleted': {'$exists': False}},
                            {'isDeleted': False}
                        ]
                    },
                    doc_handler.db
                )

                if file_details_in_db:
                    document_record = file_details_in_db.get('document_record', {})
                    uploaded_by = document_record.get('uploaded_by')
                    file_uuid = document_record.get('file_uuid')
                    user_name = uploaded_by.get('user_name')

                    processing_status = file_details_in_db.get('processing_status')
                    status = processing_status.get('status')
                    
                    # Generate URLs for viewing and downloading
                    update_logger.debug(f"Generating presigned URLs for file: {filename}")
                    view_url = generate_document_presigned_url(
                        bucket=bucket_name,
                        key=obj['Key'],
                        disposition='inline'
                    )
                    
                    download_url = generate_document_presigned_url(
                        bucket=bucket_name,
                        key=obj['Key'],
                        disposition=f'attachment; filename="{filename}"'
                    )
                    
                    processing_status = file_details_in_db.get('processing_status', {})
                    status = processing_status.get('status')
                    percentage = processing_status.get('percentage', 0)
                    progress = processing_status.get('progress', {})

                    files.append({
                        "filename": filename,
                        "file_uuid": file_uuid,
                        "size": obj['Size'],
                        "last_modified": obj['LastModified'].isoformat(),
                        "download_url": download_url,
                        "view_url": view_url,
                        "s3_location": f"{bucket_name}/{obj['Key']}",
                        "uploaded_by": user_name,
                        "status": status,
                        "percentage": percentage,
                        "progress": progress
                    })
        
        update_logger.info(f"Successfully listed {len(files)} files for project {project_id}")
        return JSONResponse(content={
            "project_id": project_id,
            "file_count": len(files),
            "files": files
        })
        
    except Exception as e:
        update_logger.error(f"Error listing project files: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    
class DeleteFileRequest(BaseModel):
    project_id: int
    file_uuid: str

    
@router.post("/delete-file")
async def delete_project_file(
    delete_file_request: DeleteFileRequest,
    current_user: dict = Depends(get_current_user)
):
    try:
        project_id = delete_file_request.project_id
        file_uuid = delete_file_request.file_uuid
        update_logger.info(f"Deleting file with UUID {file_uuid} from project {project_id}")
        
        tenant_id = get_tenant_id()
        bucket_name = get_tenant_bucket(tenant_id)
        
        # Get the document handler instance
        doc_handler = await get_document_handler()

        # Find the document in the database based on project_id and file_uuid
        file_details_in_db = await doc_handler.get_one(
            {
                'document_record.file_uuid': file_uuid,
                'document_record.project_id': project_id
            },
            doc_handler.db
        )

        if file_details_in_db and isinstance(file_details_in_db, dict) and 'document_record' in file_details_in_db:
            document_record = file_details_in_db['document_record']
            file_name = document_record.get('file_name')
            
            # Mark as deleted in MongoDB
            update_data = {"isDeleted": True}
            update_result = await doc_handler.update_one(
                {"document_record.file_uuid": file_uuid},
                update_data,
                db=doc_handler.db
            )
            update_logger.info(f"Successfully marked file {file_uuid} as deleted in MongoDB")
            
            # Clean up S3 files related to this document
            if file_name:
                try:
                    # Delete the processed/digested file from S3
                    digested_file_key = f"extracted-docs-{tenant_id}/project-{project_id}/digested-files/doc__{file_name}.json"
                    
                    update_logger.info(f"Attempting to delete S3 object: {digested_file_key}")
                    
                    # Check if the file exists before trying to delete
                    try:
                        s3_client.head_object(Bucket=bucket_name, Key=digested_file_key)
                        # File exists, delete it
                        s3_client.delete_object(Bucket=bucket_name, Key=digested_file_key)
                        update_logger.info(f"Successfully deleted S3 digested file: {digested_file_key}")
                    except s3_client.exceptions.NoSuchKey:
                        update_logger.info(f"S3 digested file not found (already deleted): {digested_file_key}")
                    
                    # Also delete the original uploaded file if it exists
                    original_file_key = f"extracted-docs-{tenant_id}/project-{project_id}/{file_name}"
                    try:
                        s3_client.head_object(Bucket=bucket_name, Key=original_file_key)
                        s3_client.delete_object(Bucket=bucket_name, Key=original_file_key)
                        update_logger.info(f"Successfully deleted S3 original file: {original_file_key}")
                    except s3_client.exceptions.NoSuchKey:
                        update_logger.info(f"S3 original file not found: {original_file_key}")
                        
                except Exception as s3_error:
                    update_logger.error(f"Error deleting S3 files for {file_name}: {str(s3_error)}")
                    # Don't fail the entire operation if S3 cleanup fails
                    
        else:
            update_logger.warning(f"File {file_uuid} not found in database")
            raise HTTPException(status_code=404, detail="File not found")

        return JSONResponse(content={
            "status": 200,
            "message": "File deleted successfully",
            "file_uuid": file_uuid,
            "project_id": project_id
        })

    except HTTPException as he:
        update_logger.error(f"HTTP Exception deleting file: {str(he)}")
        raise he
    except Exception as e:
        update_logger.error(f"Error deleting file: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get("/document-progress/{file_uuid}")
async def get_document_progress(file_uuid: str, current_user: dict = Depends(get_current_user)):
    """Get the progress of a document being processed"""
    update_logger.info(f"Checking progress for document {file_uuid}")
    try:
        # Get MongoDB handler
        doc_handler = await get_document_handler()
        
        # Find the document in the database
        document = await doc_handler.get_one(
            {"document_record.file_uuid": file_uuid},
            db=doc_handler.db
        )
        
        if not document:
            update_logger.warning(f"Document {file_uuid} not found")
            raise HTTPException(status_code=404, detail="Document not found")
            
        # Extract processing status and document record
        processing_status = document.get("processing_status", {})
        document_record = document.get("document_record", {})
        
        # Get current status
        status = processing_status.get("status", "unknown")
        stage = processing_status.get("stage", "unknown")
        start_time = processing_status.get("start_time")
        
        # Calculate progress percentage based on elapsed time and timeout
        progress_percentage = 0
        elapsed_time = 0
        estimated_time_remaining = 0
        
        # Get file size
        file_size = document_record.get("file_size", 0)
        file_size_kb = file_size / 1024
        
        # Calculate max timeout based on file size
        size_factor = 1  # 1 second per KB
        max_timeout = min(file_size_kb * size_factor, 1800)
        max_timeout = max(max_timeout, 60)  # Minimum 60 seconds
        
        # Calculate elapsed time if start_time is available
        if start_time:
            start_time_dt = start_time
            if isinstance(start_time, str):
                start_time_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            
            elapsed_time = (datetime.now() - start_time_dt).total_seconds()
            
            # Calculate progress percentage based on elapsed time vs max timeout
            if status == "completed" or status == "failed":
                progress_percentage = 100
            elif status == "processing":
                # Check if file exists in S3
                s3_location = processing_status.get("s3_location", "")
                if s3_location:
                    progress_percentage = 100
                else:
                    progress_percentage = min((elapsed_time / max_timeout) * 100, 99)
                    
                # Calculate estimated time remaining
                if progress_percentage < 99:
                    estimated_time_remaining = max(0, max_timeout - elapsed_time)
        
        # Make sure progress is 100% for completed documents
        if status == "completed" or status == "failed":
            progress_percentage = 100
            estimated_time_remaining = 0
        
        update_logger.info(f"Document {file_uuid} progress: {progress_percentage}%, status: {status}")
        return JSONResponse(content={
            "file_uuid": file_uuid,
            "status": status,
            "stage": stage,
            "progress_percentage": round(progress_percentage, 1),
            "elapsed_time": round(elapsed_time, 2),
            "estimated_time_remaining": round(estimated_time_remaining, 2),
            "max_timeout": max_timeout,
            "file_size_kb": round(file_size_kb, 2)
        })
        
    except HTTPException as he:
        update_logger.error(f"HTTP Exception checking document progress: {str(he)}")
        raise he
    except Exception as e:
        update_logger.error(f"Error checking document progress: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))



async def get_attachment_metadata(db, tenant_id, project_id):
    """Get attachment metadata from the database"""
    
    # Find attachments for this tenant and project
    attachments = db[CODE_GEN_ATTACHMENT_COLLECTION].find({"tenant_id": tenant_id, "project_id": project_id, "isDeleted": {"$ne": True}})
    # Convert to list
    attachment_list = list(attachments)
    # Return as dictionary
    return {"attachments": {item["attachment_id"]: item for item in attachment_list}}

async def save_attachment_metadata(db, metadata_item):
    """Save attachment metadata to the database"""
    attachment_handler = db[CODE_GEN_ATTACHMENT_COLLECTION]
    
    # Check if attachment already exists
    existing = attachment_handler.find_one({"attachment_id": metadata_item["attachment_id"]})
    
    if existing:
        # Update existing record
        attachment_handler.update_one(
            {"attachment_id": metadata_item["attachment_id"]},
            metadata_item
        )
    else:
        # Insert new record
        attachment_handler.insert_one(
            metadata_item
        )
        
async def delete_attachment_metadata(db, attachment_id):
    """Mark attachment as deleted in the database"""
    attachment_handler = db[CODE_GEN_ATTACHMENT_COLLECTION]
    
    # Soft delete by setting isDeleted flag
    attachment_handler.update_one(
        {"attachment_id": attachment_id},
        {"isDeleted": True}
    )

@router.post("/upload-attachment")
async def upload_attachment(
    file: UploadFile = File(...),
    project_id: int = Form(...),
    convert_word_to_txt: bool = Form(True),
    current_user: dict = Depends(get_current_user)
):
    """Upload attachment files to EFS path"""
    update_logger.info(f"Processing attachment upload for project_id: {project_id}, file: {file.filename}")
    
    db = get_mongo_db().db
    tenant_id = get_tenant_id()
    content = await file.read()
    file_size = len(content)
    
    try:
        # Validate the file
        is_valid, message = validate_file(content, file.filename, file_size)
        if not is_valid:
            update_logger.warning(f"File validation failed: {message}")
            raise HTTPException(status_code=400, detail=message)
        
        # Add timestamp to filename to prevent duplicates
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_")
        original_filename = file.filename
        safe_filename = secure_filename(original_filename)
        unique_filename = timestamp + safe_filename
        
        # Create full path structure
        base_path = "/app/data"
        if os.environ.get("LOCAL_DEBUG"):
            base_path = "/tmp"
        attachment_path = f"{base_path}/{tenant_id}/{project_id}/workspace/attachments"
        
        # Ensure directory exists
        os.makedirs(attachment_path, exist_ok=True)
        
        # Full file path
        file_path = os.path.join(attachment_path, unique_filename)
        
        # Get file type
        file_type = get_file_type(original_filename)
        
        update_logger.debug(f"Writing attachment to path: {file_path}")
        # Handle Word to TXT conversion first
        file_extension = original_filename.split('.')[-1].lower()
        converted_to_txt = False
        final_attachment_id = None
        final_metadata = None

        if convert_word_to_txt and file_extension in ['doc', 'docx']:
            try:
                update_logger.info(f"Auto-converting Word document {original_filename} to TXT (convert_word_to_txt={convert_word_to_txt})")

                # Extract text from Word document
                extracted_text = extract_text_from_word_document(content, file_extension)

                if extracted_text:
                    # Generate TXT filename with new format: filename(extension).txt
                    txt_filename_base = generate_txt_filename(original_filename)
                    txt_filename = f"{timestamp}{txt_filename_base}"
                    txt_file_path = os.path.join(attachment_path, txt_filename)

                    # Write TXT file instead of original Word file
                    with open(txt_file_path, "w", encoding='utf-8') as txt_file:
                        txt_file.write(extracted_text)

                    # Generate TXT attachment ID
                    final_attachment_id = str(uuid.uuid4())

                    # Create metadata for TXT file (this becomes the primary file)
                    final_metadata = {
                        "attachment_id": final_attachment_id,
                        "original_filename": txt_filename_base,
                        "stored_filename": txt_filename,
                        "file_path": txt_file_path,
                        "file_type": "text/plain",
                        "file_size": len(extracted_text.encode('utf-8')),
                        "tenant_id": tenant_id,
                        "project_id": project_id,
                        "uploaded_by": {
                            "user_id": current_user.get("cognito:username"),
                            "user_name": current_user.get("custom:Name", "Unknown")
                        },
                        "upload_date": datetime.now().isoformat(),
                        "url_path": f"/attachments/{tenant_id}/{project_id}/{txt_filename}",
                        "isDeleted": False,
                        "converted_from": original_filename,
                        "conversion_type": "word_to_txt"
                    }

                    converted_to_txt = True
                    update_logger.info(f"Successfully converted {original_filename} to {txt_filename} - original Word file will not be saved")
                else:
                    update_logger.warning(f"Failed to extract text from {original_filename} - saving original file instead")

            except Exception as conversion_error:
                update_logger.error(f"Error converting Word document to TXT: {str(conversion_error)} - saving original file instead")

        # If conversion failed or file is not a Word document, save the original file
        if not converted_to_txt:
            # Write original file to EFS
            with open(file_path, "wb") as f:
                f.write(content)

            # Generate attachment ID for original file
            final_attachment_id = str(uuid.uuid4())

            # Create metadata for the original attachment
            final_metadata = {
                "attachment_id": final_attachment_id,
                "original_filename": original_filename,
                "stored_filename": unique_filename,
                "file_path": file_path,
                "file_type": file_type,
                "file_size": file_size,
                "tenant_id": tenant_id,
                "project_id": project_id,
                "uploaded_by": {
                    "user_id": current_user.get("cognito:username"),
                    "user_name": current_user.get("custom:Name", "Unknown")
                },
                "upload_date": datetime.now().isoformat(),
                "url_path": f"/attachments/{tenant_id}/{project_id}/{unique_filename}",
                "isDeleted": False
            }

        # Save metadata to database
        update_logger.info(f"Saving attachment metadata for ID: {final_attachment_id}")
        await save_attachment_metadata(db, final_metadata)
        
        # Prepare response
        response_data = {
            "success": True,
            "attachment_id": final_attachment_id,
            "filename": final_metadata["stored_filename"],
            "originalName": original_filename,
            "path": final_metadata["file_path"],
            "size": final_metadata["file_size"],
            "url": final_metadata["url_path"],
            "file_type": final_metadata["file_type"]
        }

        # Add conversion info if Word document was converted
        if converted_to_txt:
            response_data["converted_from_word"] = True
            response_data["original_word_filename"] = original_filename
            response_data["conversion_type"] = "word_to_txt"
        else:
            response_data["converted_from_word"] = False

        return JSONResponse(content=response_data, status_code=200)
    
    except HTTPException as he:
        update_logger.error(f"HTTP Exception during attachment upload: {str(he)}")
        raise he
    except Exception as e:
        update_logger.error(f"Error uploading attachment: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/attachments/{tenant_id}/{project_id}/{filename}")
async def serve_attachment(
    tenant_id: str,
    project_id: int,
    filename: str,
    current_user: dict = Depends(get_current_user)
):
    """Serve an uploaded attachment file"""
    update_logger.info(f"Serving attachment: {filename} for project: {project_id}")
    try:
        attachment_path = f"/app/data/{tenant_id}/{project_id}/workspace/attachments/{filename}"
        
        # Check if file exists
        if not os.path.exists(attachment_path):
            update_logger.warning(f"Attachment not found at path: {attachment_path}")
            raise HTTPException(status_code=404, detail="Attachment not found")
        
        update_logger.debug(f"Serving file from path: {attachment_path}")
        # Return file response
        return FileResponse(
            path=attachment_path,
            filename=filename,
            media_type=get_file_type(filename)
        )
    except HTTPException as he:
        update_logger.error(f"HTTP Exception serving attachment: {str(he)}")
        raise he
    except Exception as e:
        update_logger.error(f"Error serving attachment: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/list-attachments/{project_id}")
async def list_attachments(
    project_id: int,
    current_user: dict = Depends(get_current_user)
):
    """List all attachments for a project"""
    update_logger.info(f"Listing attachments for project: {project_id}")
    tenant_id = get_tenant_id()
    db = get_mongo_db().db
    try:
        # Get attachments from database
        attachment_handler = db[CODE_GEN_ATTACHMENT_COLLECTION]
        
        # Find all non-deleted attachments for this tenant and project
        query = {
            "tenant_id": tenant_id,
            "project_id": project_id,
            "isDeleted": {"$ne": True}
        }

        projection = {
            "_id": 0
        }
        
        attachments = attachment_handler.find(query, projection)
        
        # Convert to list
        attachment_list = list(attachments)
        update_logger.info(f"Found {len(attachment_list)} attachments for project {project_id}")
        
        return JSONResponse(content={
            "success": True,
            "project_id": project_id,
            "attachment_count": len(attachment_list),
            "attachments": attachment_list
        })
    
    except Exception as e:
        update_logger.error(f"Error listing attachments: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/delete-attachment/{project_id}/{attachment_id}")
async def delete_attachment(
    project_id: int,
    attachment_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Delete an attachment"""
    update_logger.info(f"Deleting attachment {attachment_id} from project {project_id}")
    tenant_id = get_tenant_id()
    db = get_mongo_db().db
    try:
        # Get attachment handler
        attachment_handler = db[CODE_GEN_ATTACHMENT_COLLECTION]
        
        # Check if attachment exists
        attachment = attachment_handler.find_one(
            {
                "attachment_id": attachment_id,
                "tenant_id": tenant_id,
                "project_id": project_id,
                "isDeleted": {"$ne": True}
            },
            db=attachment_handler.db
        )
        
        if not attachment:
            update_logger.warning(f"Attachment {attachment_id} not found")
            raise HTTPException(status_code=404, detail="Attachment not found")
        
        # Get attachment info
        file_path = attachment["file_path"]
        
        # Delete the file if it exists
        if os.path.exists(file_path):
            update_logger.debug(f"Removing file from path: {file_path}")
            os.remove(file_path)
        
        # Mark as deleted in database
        update_logger.info(f"Marking attachment {attachment_id} as deleted in database")
        await delete_attachment_metadata(db, attachment_id)
        
        return JSONResponse(content={
            "success": True,
            "message": "Attachment deleted successfully",
            "attachment_id": attachment_id
        })
    
    except HTTPException as he:
        update_logger.error(f"HTTP Exception deleting attachment: {str(he)}")
        raise he
    except Exception as e:
        update_logger.error(f"Error deleting attachment: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))