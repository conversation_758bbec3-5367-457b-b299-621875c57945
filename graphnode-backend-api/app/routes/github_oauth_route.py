import logging
from typing import List, Optional, Union
from fastapi import APIRouter, HTTPException, Request, Depends , Header , status
from fastapi.responses import RedirectResponse
import requests
import os
import secrets
from dotenv import load_dotenv
from datetime import datetime
from app.connection.tenant_middleware import get_tenant_id, tenant_context
from app.routes.scm_route import SCMManager
from app.utils.oauth_utils import get_github_client_details, exchange_github_code_for_token, get_user_info, get_user_repos
from app.connection.establish_db_connection import connect_git_mongo_db  # Your MongoDB connection
from app.utils.auth_utils import get_current_user
from pydantic import BaseModel
import json
import httpx
from app.connection.establish_db_connection import get_mongo_db
from app.core.Settings import settings
from fastapi.responses import JSONResponse
from app.utils.hash import decrypt_string

load_dotenv()
_SHOW_NAME = "oauth/github"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=["GitHub OAuth"],
    responses={404: {"description": "Not found"}},
)

# Load GitHub credentials
GITHUB_CLIENT_ID, GITHUB_CLIENT_SECRET, GITHUB_REDIRECT_URI = get_github_client_details()

# GitHub Login Route
@router.get("")
async def github_login():
    """
    Redirect the user to GitHub's OAuth page.
    """
    # state_string = secrets.token_urlsafe(32)
    github_auth_url = (
        f"https://github.com/login/oauth/authorize"
        f"?client_id={GITHUB_CLIENT_ID}"
        f"&scope=repo read:org admin:org"
        # f"&state={state_string}"
        f"&redirect_uri={GITHUB_REDIRECT_URI}"
    )  

    # print(mongo_result)
    # Log the URL to verify that the client ID and redirect URI are correctly set
    print(f"GitHub Auth URL: {github_auth_url}")
    
    return {"url": github_auth_url}  # Return the URL as part of a JSON response

@router.get("/callback")
async def github_callback(request: Request):
    """
    
    GitHub callback URL to exchange code for an access token.
    """
    code = request.query_params.get("code")
    userId = request.query_params.get("userId")
    tenant_id = request.query_params.get("tenantId")
    tenant_context.set(tenant_id)
    
    if not code:
        raise HTTPException(status_code=400, detail="Code not found")


    # Exchange the code for an access token
    access_token = exchange_github_code_for_token(code)
    if not access_token:
        raise HTTPException(status_code=400, detail="Access token not found")

    # Get the user information from GitHub
    user_info = get_user_info(access_token)
    if not user_info:
        raise HTTPException(status_code=400, detail="Unable to fetch user info from GitHub")

    # If the token is valid, set status to true
    status = "true"

    # Get MongoDB instance
    mongo_handler = get_mongo_db(
        db_name=settings.MONGO_DB_NAME,
        collection_name='users_github'
    )

    # Prepare user data
    user_data = {
        "github_id": user_info["id"],
        "username": user_info["login"],
        "userId" : userId,
        "status": status,
        "email": user_info.get("email"),
        "name": user_info.get("name"),
        "access_token": access_token,
        "avatar_url": user_info.get("avatar_url"),
        "profile_url": user_info.get("html_url"),
        "updated_at": datetime.utcnow(),
    }

    # Check if the user already exists
    existing_user = await mongo_handler.git_get_by(user_info["id"])

    if existing_user:
        # User exists, update their information
        update_result = await mongo_handler.git_update(user_info["id"], user_data)
        if update_result:
            print(f"User updated: {user_data['username']}")
            mongo_result = await mongo_handler.git_get_by(user_info["id"])  # Get the updated user data
        else:
            raise HTTPException(status_code=500, detail="Failed to update user in database")
    else:
        # User doesn't exist, create a new entry
        user_data["created_at"] = datetime.utcnow()
        mongo_result = await mongo_handler.git_create(user_data)
        if mongo_result:
            print(f"User created: {mongo_result['username']}")
        else:
            raise HTTPException(status_code=500, detail="Failed to create user in database")

    # Return response
    return {"Message": f" You can close this window - {userId , status}"}


class BranchResponse(BaseModel):
    name: str
    protected: bool
    commit: dict

class BranchRequest(BaseModel):
    user_id: str | None
    repo_name: str
    owner: str
    org: Optional[Union[str, bool]] = False
    limit: Optional[bool] = False
    
@router.post("/repo-branches", response_model=List[BranchResponse])
async def get_branches(payload: BranchRequest):
    """
    Get branches of a specific repository.
    For org=True, treats the request as an organization account.
    For org=False (default), treats it as a normal user account.
    """
    try:
        # Initialize variables
        token = None
        
        # Get MongoDB connection
        mongo_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='users_github'
        )
        
        # Handle token retrieval based on account type
        if payload.org:
            # Organization account - get token from tenant org
            scm = SCMManager()
            token = scm.get_token_from_tenant_org(payload.org)
        else:
            if(payload.user_id):
                # Normal user account - get token from user data
                user_data = await mongo_handler.git_get_by_user_id(payload.user_id)
                
                if not user_data:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="User not found, please login again"
                    )
            
                # Validate token
                token = user_data.get("access_token")
                if not token:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="Access token not found"
                    )
            else:
                token = settings.GITHUB_ACCESS_TOKEN
        
        # Make GitHub API request with pagination handling
        url = f"https://api.github.com/repos/{payload.owner}/{payload.repo_name}/branches?per_page=100&page=1"
        headers = {
            "Authorization": f"Bearer {token}",
        }
        
        more_pages = True
        final_data = []
        
        while more_pages:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(url, headers=headers)
                
                if response.status_code == 404:
                    # Repository not found or no access
                    final_data = []
                    break
                    
                response.raise_for_status()
                final_data.extend(response.json())
                
                # Check for pagination
                if 'link' in response.headers and 'rel="next"' in response.headers['link'] and not payload.limit:
                    next_links = [link for link in response.headers['link'].split(',') if 'rel="next"' in link]
                    if next_links:
                        angular_url = next_links[0].split(';')[0].strip()
                        url = angular_url[1:-1]  # Remove < and > characters
                else:
                    more_pages = False
        
        return final_data
        
    except httpx.HTTPStatusError as e:
        logging.error(f"GitHub API error: {str(e)}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"GitHub API error: {e.response.json()}"
        )
    except httpx.RequestError as e:
        logging.error(f"Network error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Unable to reach GitHub API"
        )
    except Exception as e:
        logging.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    
@router.get("/repo-details")
async def get_repo_details(request: Request, current_user: dict = Depends(get_current_user)):
    owner = request.query_params.get("owner")
    repo_name = request.query_params.get("repo_name")
    github_token = settings.GITHUB_ACCESS_TOKEN #only using default access token becuase this endpoint is only for public repo. If private repo needs to access this api in the future, change the token retrieval using user_id

    url = f"https://api.github.com/repos/{owner}/{repo_name}"
    headers = {
        "Authorization": f"Bearer {github_token}",
    }

    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(url, headers=headers)

            if response.status_code == 404:
                # Repository not found or no access
                return {}
            
            response.raise_for_status()
            return response.json()
        
    except httpx.HTTPStatusError as e:
        logging.error(f"GitHub API error: {str(e)}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"GitHub API error: {e.response.json()}"
        )
    except httpx.RequestError as e:
        logging.error(f"Network error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Unable to reach GitHub API"
        )
    except Exception as e:
        logging.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    
class SingleBranchCheck(BaseModel):
    owner: str
    repo_name: str
    branch_name: str
    user_id: str | None

@router.post("/get-branch-details")
async def getBranchDetails(payload: SingleBranchCheck):
    print("Entered get branch details")
    github_token = settings.GITHUB_ACCESS_TOKEN
    url = f"https://api.github.com/repos/{payload.owner}/{payload.repo_name}/branches/{payload.branch_name}"
    headers = {
        "Authorization": f"Bearer {github_token}",
    }

    try:
        response = requests.get(url, headers=headers)
        if response.status_code == 404:
                # Branch not found or no access
                return {}
        
        return response.json()
    except Exception as e:
        logging.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

class CodeRequest(BaseModel):
    code: str

@router.get("/repositories")
async def github_repositories(
    request: Request, 
    current_user: dict = Depends(get_current_user)
):
    scm_id = request.query_params.get("scm_id")
    user_id = request.query_params.get("userId")
    token = None

    # If user_id not provided, get from current user
    if not user_id:
        user_id = current_user.get("cognito:username")

    # Get user data from MongoDB
    mongo_handler = get_mongo_db(
        db_name=settings.MONGO_DB_NAME,
        collection_name='users_github'
    )
    user_data = await mongo_handler.git_get_by_user_id(user_id)

    if not user_data and not scm_id:
        return JSONResponse(
            status_code=400,
            content={"detail": "Token not found, please login again"}
        )

    # If scm_id is provided, use it to get token
    if scm_id:
        decrypted_scm_id = decrypt_string(scm_id)
        scm_mongo_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='scm_configurations'
        )
        scm_data = await scm_mongo_handler.git_get_by_scm_id(decrypted_scm_id)
        if scm_data:
            token = scm_data['credentials']['access_token']
    
    # If no token from scm_id, use token from user_data
    if not token:
        token = user_data.get("access_token")

    if not token:
        return JSONResponse(
            status_code=400,
            content={"detail": "Access token not found for the user"}
        )

    repos = get_user_repos(token)
    if not repos:
        return JSONResponse(
            status_code=400,
            content={"detail": "Error fetching repositories"}
        )

    transformed_repos = []
    for repo in repos:
        transformed_repo = {
            "id": repo.get("id"),
            "name": repo.get("name"),
            "description": repo.get("description"),
            "languages": [repo.get("language")] if repo.get("language") else [],
            "selected": False,
            "path": repo.get("html_url"),
            "lastUpdated": repo.get("updated_at"),
            "branch": repo.get("default_branch"),
        }
        transformed_repos.append(transformed_repo)

    return transformed_repos


@router.get("/logout")
async def logout(request: Request,current_user: dict = Depends(get_current_user)):
    """
    Log the user out by clearing the session and redirecting to the home page or GitHub logout.
    """
    userId = current_user.get("cognito:username")
    # Clear the user's session or token
    request.session.clear()  # Clear all session data
    if userId:
        try:
            mongo_handler = get_mongo_db(
                db_name=settings.MONGO_DB_NAME,
                collection_name='users_github'
            )
            delete_result = await mongo_handler.git_delete_by_userId(userId,'users_github' )
            
            if delete_result.deleted_count == 0:
                return JSONResponse(
                    status_code=404,
                    content={"detail": "User not found in database"}
                )
        except Exception as e:
            return JSONResponse(
                status_code=500,
                content={"detail": f"Error deleting user: {str(e)}"}
            )

    # Optionally redirect to GitHub's logout page (not required but a good practice)
    github_logout_url = "https://github.com/logout"
    
    # Redirecting to a specific URL after logging out
    return {"logouturl": github_logout_url}  # Return the URL as part of a JSON response


@router.get("/git_connected_status")
async def git_connected_status(user_id: str= None,
                            current_user: dict = Depends(get_current_user)):
    """
    Check if a user is connected to GitHub by their user_id.
    """
    if not user_id:
        user_id = current_user.get("cognito:username")
    # Get MongoDB instance
    mongo_handler = get_mongo_db(
        db_name=settings.MONGO_DB_NAME,
        collection_name='users_github'
    )

    try:
        # Fetch user data from the database using user_id
        user_data = await mongo_handler.git_get_by_user_id(user_id)

        print(user_data)

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error accessing database: {str(e)}"
        )

    if not user_data:
        # If the user does not exist in the database
        return JSONResponse(
            status_code=200,
            content={"git_connected": False, "message": "GitHub user not connected"}
        )

    # If the user exists, return the connection status
    return {
        "git_connected": True,
        "message": "GitHub user already connected",
        "status": user_data.get("status"),
        "username": user_data.get("username"),
    }
