from fastapi import APIRouter, Depends, HTTPException, status, Request, Body
from app.connection.establish_db_connection import get_mongo_db
from app.connection.tenant_middleware import KAVIA_ROOT_DB_NAME
import logging
from datetime import datetime
from typing import Optional, Dict, Any, List, Union
from pydantic import BaseModel, Field

# Set up logging
logger = logging.getLogger(__name__)

_SHOW_NAME = "project"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}},
)

# Define models with explicit integer validation for project_id
class ProjectGuidanceStep(BaseModel):
    project_id: int = Field(..., description="Project ID (must be an integer)")
    step_name: str
    status: str = "initiated"
    data: Optional[Dict[str, Any]] = None
    
class EpicConfigurationData(BaseModel):
    epic_id: int = Field(..., description="Epic ID (must be an integer)")
    type: str = "Epic"
    status: str
    
class ProjectGuidanceUpdate(BaseModel):
    project_id: int = Field(..., description="Project ID (must be an integer)")
    step_name: str
    status: str
    data: Optional[Dict[str, Any]] = None

@router.post("/guidance-flow", summary="Create or update project guidance flow status")
async def update_project_guidance_flow(
    step_data: ProjectGuidanceStep
):
    """
    Create or update the project guidance flow document in MongoDB.
    
    For new projects, this creates an initial record.
    For existing projects, it updates the specified step with its status.
    
    Note: project_id must be an integer.
    """
    try:
        # Ensure project_id is an integer
        project_id = int(step_data.project_id)
        
        # Initialize MongoDB connection
        mongo_handler = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME, collection_name="project_guidance_flow")
        
        # Check if document already exists for this project
        existing_doc = await mongo_handler.get_one(
            filter={"project_id": project_id},
            db=mongo_handler.db
        )
        
        now = datetime.utcnow()
        
        # Special handling for epics - look for "epic" in step_name
        if "epic" in step_data.step_name.lower():
            # Check if data includes epic_id
            if step_data.data and isinstance(step_data.data, dict) and "epic_id" in step_data.data:
                epic_id = int(step_data.data["epic_id"])
                epic_status = step_data.data.get("status", "pending")
                epic_type = step_data.data.get("type", "Epic")
                
                if existing_doc:
                    # First, we need to handle the case where steps.epics might not be an object
                    # Get the current document to check the type of steps.epics
                    current_doc = await mongo_handler.get_one(
                        filter={"project_id": project_id},
                        db=mongo_handler.db
                    )
                    
                    # Initialize the steps object with its current value or an empty dict
                    steps = current_doc.get("steps", {}) if current_doc else {}
                    
                    # Initialize or update the epics object
                    if "epics" not in steps or not isinstance(steps["epics"], dict):
                        steps["epics"] = {}
                    
                    # Add the epic to the epics object
                    steps["epics"][str(epic_id)] = {
                        "id": epic_id,
                        "type": epic_type,
                        "status": epic_status,
                        "updated_at": now
                    }
                    
                    # Update the document with the entire steps object
                    update_fields = {
                        "steps": steps,
                        "updated_at": now
                    }
                    
                    # Use update_one method to update the entire steps field
                    result = await mongo_handler.update_one(
                        filter={"project_id": project_id},
                        element=update_fields,
                        db=mongo_handler.db
                    )
                    
                    return {
                        "status": "success",
                        "message": f"Updated epic {epic_id} in project {project_id}",
                        "project_id": project_id,
                        "epic_id": epic_id
                    }
                else:
                    # Document doesn't exist, create a new one with epics
                    new_doc = {
                        "project_id": project_id,
                        "created_at": now,
                        "updated_at": now,
                        "steps": {
                            "epics": {
                                str(epic_id): {
                                    "id": epic_id,
                                    "type": epic_type, 
                                    "status": epic_status,
                                    "updated_at": now
                                }
                            }
                        }
                    }
                    
                    # Insert the document
                    await mongo_handler.insert(new_doc, mongo_handler.db)
                    
                    return {
                        "status": "success",
                        "message": f"Created new guidance flow with epic {epic_id} for project {project_id}",
                        "project_id": project_id,
                        "epic_id": epic_id
                    }
            elif step_data.data and isinstance(step_data.data, dict) and "skipped_epics" in step_data.data:
                # Handle bulk skipped epics
                skipped_epics = step_data.data["skipped_epics"]
                
                if existing_doc:
                    # Get the current document to check the type of steps.epics
                    current_doc = await mongo_handler.get_one(
                        filter={"project_id": project_id},
                        db=mongo_handler.db
                    )
                    
                    # Initialize the steps object with its current value or an empty dict
                    steps = current_doc.get("steps", {}) if current_doc else {}
                    
                    # Initialize the epics object if it doesn't exist or isn't a dict
                    if "epics" not in steps or not isinstance(steps["epics"], dict):
                        steps["epics"] = {}
                    
                    # Update specific epic status for each skipped epic
                    for epic_id in skipped_epics:
                        epic_id = int(epic_id)
                        steps["epics"][str(epic_id)] = {
                            "id": epic_id,
                            "type": "Epic",
                            "status": "skipped",
                            "updated_at": now
                        }
                    
                    # Update the document with the entire steps object
                    update_fields = {
                        "steps": steps,
                        "updated_at": now
                    }
                    
                    # Update the document
                    result = await mongo_handler.update_one(
                        filter={"project_id": project_id},
                        element=update_fields,
                        db=mongo_handler.db
                    )
                    
                    return {
                        "status": "success",
                        "message": f"Updated skipped epics for project {project_id}",
                        "project_id": project_id
                    }
                else:
                    # Document doesn't exist, create a new one with skipped epics
                    epics_dict = {}
                    for epic_id in skipped_epics:
                        epic_id = int(epic_id)
                        epics_dict[str(epic_id)] = {
                            "id": epic_id,
                            "type": "Epic",
                            "status": "skipped",
                            "updated_at": now
                        }
                    
                    new_doc = {
                        "project_id": project_id,
                        "created_at": now,
                        "updated_at": now,
                        "steps": {
                            "epics": epics_dict
                        }
                    }
                    
                    # Insert the document
                    await mongo_handler.insert(new_doc, mongo_handler.db)
                    
                    return {
                        "status": "success",
                        "message": f"Created new guidance flow with skipped epics for project {project_id}",
                        "project_id": project_id
                    }
                    
        # Special handling for containers - look for "container" in step_name
        elif "container" in step_data.step_name.lower():
            # Check if data includes container_id
            if step_data.data and isinstance(step_data.data, dict) and "container_id" in step_data.data:
                container_id = int(step_data.data["container_id"])
                container_status = step_data.data.get("status", "pending")
                container_type = step_data.data.get("type", "Container")
                
                if existing_doc:
                    # First, we need to handle the case where steps.containers might not be an object
                    # Get the current document to check the type of steps.containers
                    current_doc = await mongo_handler.get_one(
                        filter={"project_id": project_id},
                        db=mongo_handler.db
                    )
                    
                    # Initialize the steps object with its current value or an empty dict
                    steps = current_doc.get("steps", {}) if current_doc else {}
                    
                    # Initialize or update the containers object
                    if "containers" not in steps or not isinstance(steps["containers"], dict):
                        steps["containers"] = {}
                    
                    # Add the container to the containers object
                    steps["containers"][str(container_id)] = {
                        "id": container_id,
                        "type": container_type,
                        "status": container_status,
                        "updated_at": now
                    }
                    
                    # Update the document with the entire steps object
                    update_fields = {
                        "steps": steps,
                        "updated_at": now
                    }
                    
                    # Use update_one method to update the entire steps field
                    result = await mongo_handler.update_one(
                        filter={"project_id": project_id},
                        element=update_fields,
                        db=mongo_handler.db
                    )
                    
                    return {
                        "status": "success",
                        "message": f"Updated container {container_id} in project {project_id}",
                        "project_id": project_id,
                        "container_id": container_id
                    }
                else:
                    # Document doesn't exist, create a new one with containers
                    new_doc = {
                        "project_id": project_id,
                        "created_at": now,
                        "updated_at": now,
                        "steps": {
                            "containers": {
                                str(container_id): {
                                    "id": container_id,
                                    "type": container_type, 
                                    "status": container_status,
                                    "updated_at": now
                                }
                            }
                        }
                    }
                    
                    # Insert the document
                    await mongo_handler.insert(new_doc, mongo_handler.db)
                    
                    return {
                        "status": "success",
                        "message": f"Created new guidance flow with container {container_id} for project {project_id}",
                        "project_id": project_id,
                        "container_id": container_id
                    }
            elif step_data.data and isinstance(step_data.data, dict) and "skipped_containers" in step_data.data:
                # Handle bulk skipped containers
                skipped_containers = step_data.data["skipped_containers"]
                
                if existing_doc:
                    # Get the current document to check the type of steps.containers
                    current_doc = await mongo_handler.get_one(
                        filter={"project_id": project_id},
                        db=mongo_handler.db
                    )
                    
                    # Initialize the steps object with its current value or an empty dict
                    steps = current_doc.get("steps", {}) if current_doc else {}
                    
                    # Initialize the containers object if it doesn't exist or isn't a dict
                    if "containers" not in steps or not isinstance(steps["containers"], dict):
                        steps["containers"] = {}
                    
                    # Update specific container status for each skipped container
                    for container_id in skipped_containers:
                        container_id = int(container_id)
                        steps["containers"][str(container_id)] = {
                            "id": container_id,
                            "type": "Container",
                            "status": "skipped",
                            "updated_at": now
                        }
                    
                    # Update the document with the entire steps object
                    update_fields = {
                        "steps": steps,
                        "updated_at": now
                    }
                    
                    # Update the document
                    result = await mongo_handler.update_one(
                        filter={"project_id": project_id},
                        element=update_fields,
                        db=mongo_handler.db
                    )
                    
                    return {
                        "status": "success",
                        "message": f"Updated skipped containers for project {project_id}",
                        "project_id": project_id
                    }
                else:
                    # Document doesn't exist, create a new one with skipped containers
                    containers_dict = {}
                    for container_id in skipped_containers:
                        container_id = int(container_id)
                        containers_dict[str(container_id)] = {
                            "id": container_id,
                            "type": "Container",
                            "status": "skipped",
                            "updated_at": now
                        }
                    
                    new_doc = {
                        "project_id": project_id,
                        "created_at": now,
                        "updated_at": now,
                        "steps": {
                            "containers": containers_dict
                        }
                    }
                    
                    # Insert the document
                    await mongo_handler.insert(new_doc, mongo_handler.db)
                    
                    return {
                        "status": "success",
                        "message": f"Created new guidance flow with skipped containers for project {project_id}",
                        "project_id": project_id
                    }
        
        # Special handling for architecture configuration
        elif "architecture" in step_data.step_name.lower():
            # Check if data includes architecture_id
            if step_data.data and isinstance(step_data.data, dict) and "architecture_id" in step_data.data:
                arch_id = int(step_data.data["architecture_id"])
                arch_status = step_data.data.get("status", "pending")
                arch_type = step_data.data.get("type", "ArchitecturalRequirement")
                
                if existing_doc:
                    # Get the current document
                    current_doc = await mongo_handler.get_one(
                        filter={"project_id": project_id},
                        db=mongo_handler.db
                    )
                    
                    # Initialize the steps object with its current value or an empty dict
                    steps = current_doc.get("steps", {}) if current_doc else {}
                    
                    # Update the architecture_configuration step
                    steps["architecture_configuration"] = {
                        "status": step_data.status,
                        "updated_at": now,
                        "data": {
                            "id": arch_id,
                            "type": arch_type,
                            "status": arch_status
                        }
                    }
                    
                    # Update the document with the entire steps object
                    update_fields = {
                        "steps": steps,
                        "updated_at": now
                    }
                    
                    # Update the document
                    result = await mongo_handler.update_one(
                        filter={"project_id": project_id},
                        element=update_fields,
                        db=mongo_handler.db
                    )
                    
                    return {
                        "status": "success",
                        "message": f"Updated architecture configuration for project {project_id}",
                        "project_id": project_id,
                        "architecture_id": arch_id
                    }
                else:
                    # Document doesn't exist, create a new one with architecture configuration
                    new_doc = {
                        "project_id": project_id,
                        "created_at": now,
                        "updated_at": now,
                        "steps": {
                            "architecture_configuration": {
                                "status": step_data.status,
                                "updated_at": now,
                                "data": {
                                    "id": arch_id,
                                    "type": arch_type,
                                    "status": arch_status
                                }
                            }
                        }
                    }
                    
                    # Insert the document
                    await mongo_handler.insert(new_doc, mongo_handler.db)
                    
                    return {
                        "status": "success",
                        "message": f"Created new guidance flow with architecture configuration for project {project_id}",
                        "project_id": project_id,
                        "architecture_id": arch_id
                    }
        
        # Special handling for system context configuration
        elif "system_context" in step_data.step_name.lower():
            # Check if data includes system_context_id
            if step_data.data and isinstance(step_data.data, dict) and "system_context_id" in step_data.data:
                sys_ctx_id = int(step_data.data["system_context_id"])
                sys_ctx_status = step_data.data.get("status", "pending")
                sys_ctx_type = step_data.data.get("type", "SystemContext")
                
                if existing_doc:
                    # Get the current document
                    current_doc = await mongo_handler.get_one(
                        filter={"project_id": project_id},
                        db=mongo_handler.db
                    )
                    
                    # Initialize the steps object with its current value or an empty dict
                    steps = current_doc.get("steps", {}) if current_doc else {}
                    
                    # Update the system_context_configuration step
                    steps["system_context_configuration"] = {
                        "status": step_data.status,
                        "updated_at": now,
                        "data": {
                            "id": sys_ctx_id,
                            "type": sys_ctx_type,
                            "status": sys_ctx_status
                        }
                    }
                    
                    # Update the document with the entire steps object
                    update_fields = {
                        "steps": steps,
                        "updated_at": now
                    }
                    
                    # Update the document
                    result = await mongo_handler.update_one(
                        filter={"project_id": project_id},
                        element=update_fields,
                        db=mongo_handler.db
                    )
                    
                    return {
                        "status": "success",
                        "message": f"Updated system context configuration for project {project_id}",
                        "project_id": project_id,
                        "system_context_id": sys_ctx_id
                    }
                else:
                    # Document doesn't exist, create a new one with system context configuration
                    new_doc = {
                        "project_id": project_id,
                        "created_at": now,
                        "updated_at": now,
                        "steps": {
                            "system_context_configuration": {
                                "status": step_data.status,
                                "updated_at": now,
                                "data": {
                                    "id": sys_ctx_id,
                                    "type": sys_ctx_type,
                                    "status": sys_ctx_status
                                }
                            }
                        }
                    }
                    
                    # Insert the document
                    await mongo_handler.insert(new_doc, mongo_handler.db)
                    
                    return {
                        "status": "success",
                        "message": f"Created new guidance flow with system context configuration for project {project_id}",
                        "project_id": project_id,
                        "system_context_id": sys_ctx_id
                    }
        
        # Extract only essential data for regular steps
        minimal_data = {
            "id": project_id,
            "type": "project",
            "status": step_data.status
        }
        
        # If any data is provided for non-epic steps, extract just the id and type
        if step_data.data and isinstance(step_data.data, dict):
            if "project_details" in step_data.data and isinstance(step_data.data["project_details"], dict):
                project_details = step_data.data["project_details"]
                if "id" in project_details:
                    minimal_data["id"] = int(project_details["id"])
                # Don't store other details
        
        # Default handling for non-epic and non-architecture steps
        if existing_doc:
            # Document exists, update the specific step
            logger.info(f"Updating guidance flow for project {project_id}, step: {step_data.step_name}")
            
            # Get the current document
            current_doc = await mongo_handler.get_one(
                filter={"project_id": project_id},
                db=mongo_handler.db
            )
            
            # Initialize the steps object with its current value or an empty dict
            steps = current_doc.get("steps", {}) if current_doc else {}
            
            # Update the specific step
            steps[step_data.step_name] = {
                "status": step_data.status,
                "updated_at": now,
                "data": minimal_data
            }
            
            # Update the document with the entire steps object
            update_fields = {
                "steps": steps,
                "updated_at": now
            }
            
            # Update the document
            result = await mongo_handler.update_one(
                filter={"project_id": project_id},
                element=update_fields,
                db=mongo_handler.db
            )
            
            if result.modified_count > 0:
                return {
                    "status": "success",
                    "message": f"Updated guidance flow step '{step_data.step_name}' for project {project_id}",
                    "project_id": project_id
                }
            else:
                logger.warning(f"No changes made to document for project {project_id}")
                return {
                    "status": "unchanged",
                    "message": "No changes were made to the document",
                    "project_id": project_id
                }
        else:
            # Document doesn't exist, create a new one
            logger.info(f"Creating new guidance flow for project {project_id}")
            
            # Create initial document with the first step and minimal data
            new_doc = {
                "project_id": project_id,
                "created_at": now,
                "updated_at": now,
                "steps": {
                    step_data.step_name: {
                        "status": step_data.status,
                        "updated_at": now,
                        "data": minimal_data
                    }
                }
            }
            
            # Insert the document - using the correct method from MongoDBHandler
            await mongo_handler.insert(new_doc, mongo_handler.db)
            
            return {
                "status": "success",
                "message": f"Created new guidance flow document for project {project_id}",
                "project_id": project_id
            }
            
    except Exception as e:
        logger.error(f"Error updating project guidance flow: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating project guidance flow: {str(e)}"
        )

@router.get("/guidance-flow/{project_id}", summary="Get project guidance flow status")
async def get_project_guidance_flow(project_id: int):
    """
    Retrieve the current project guidance flow document for a specific project.
    
    Note: project_id must be an integer.
    """
    try:
        # Ensure project_id is an integer
        project_id = int(project_id)
        
        # Initialize MongoDB connection
        mongo_handler = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME, collection_name="project_guidance_flow")
        
        # Get the document
        doc = await mongo_handler.get_one(
            filter={"project_id": project_id},
            db=mongo_handler.db
        )
        
        if not doc:
            return {
                "status": "not_found",
                "message": f"No guidance flow document found for project {project_id}",
                "project_id": project_id,
                "steps": {}
            }
        
        # Convert ObjectId to string for JSON serialization
        if "_id" in doc:
            doc["_id"] = str(doc["_id"])
            
        return doc
        
    except Exception as e:
        logger.error(f"Error getting project guidance flow: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting project guidance flow: {str(e)}"
        )

@router.post("/node-configured", summary="Mark project node as configured")
async def mark_project_node_configured(
    project_data: Dict[str, Any] = Body(...),
):
    """
    Update the project guidance flow to mark the project node as configured.
    This is called after fetchNodeById confirms the configuration_state is 'configured'.
    
    Note: project_id must be an integer.
    """
    try:
        # Always ensure project_id is an integer
        project_id = int(project_data.get("id"))
        configuration_state = project_data.get("properties", {}).get("configuration_state")
        
        if not project_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Project ID is required"
            )
            
        # Initialize MongoDB connection
        mongo_handler = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME, collection_name="project_guidance_flow")
        
        # Check if document already exists for this project
        existing_doc = await mongo_handler.get_one(
            filter={"project_id": project_id},
            db=mongo_handler.db
        )
        
        now = datetime.utcnow()
        
        # Extract only essential data
        minimal_data = {
            "id": project_id,  # Ensure this is an integer
            "type": project_data.get("labels", ["project"])[0].lower() if "labels" in project_data else "project",
            "status": "configured" if configuration_state == "configured" else "pending"
        }
        
        if existing_doc:
            # Get the current document
            current_doc = await mongo_handler.get_one(
                filter={"project_id": project_id},
                db=mongo_handler.db
            )
            
            # Initialize the steps object with its current value or an empty dict
            steps = current_doc.get("steps", {}) if current_doc else {}
            
            # Update the projectnode step
            steps["projectnode"] = {
                "status": "configured" if configuration_state == "configured" else "pending",
                "updated_at": now,
                "data": minimal_data
            }
            
            # Update the document with the entire steps object
            update_fields = {
                "steps": steps,
                "updated_at": now
            }
            
            # Update the document
            result = await mongo_handler.update_one(
                filter={"project_id": project_id},
                element=update_fields,
                db=mongo_handler.db
            )
            
            if result.modified_count > 0:
                return {
                    "status": "success",
                    "message": f"Updated project node configuration status for project {project_id}",
                    "project_id": project_id,
                    "configuration_state": configuration_state
                }
            else:
                logger.warning(f"No changes made to document for project {project_id}")
                return {
                    "status": "unchanged",
                    "message": "No changes were made to the document",
                    "project_id": project_id
                }
        else:
            # Document doesn't exist, create a new one
            logger.info(f"Creating new guidance flow with project node status for project {project_id}")
            
            # Create initial document with the projectnode step and minimal data
            new_doc = {
                "project_id": project_id,
                "created_at": now,
                "updated_at": now,
                "steps": {
                    "projectnode": {
                        "status": "configured" if configuration_state == "configured" else "pending",
                        "updated_at": now,
                        "data": minimal_data
                    }
                }
            }
            
            # Insert the document - using the correct method from MongoDBHandler
            await mongo_handler.insert(new_doc, mongo_handler.db)
            
            return {
                "status": "success",
                "message": f"Created new guidance flow document with project node status for project {project_id}",
                "project_id": project_id,
                "configuration_state": configuration_state
            }
            
    except Exception as e:
        logger.error(f"Error updating project node configuration status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating project node configuration status: {str(e)}"
        )