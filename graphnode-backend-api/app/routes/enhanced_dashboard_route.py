# app/routes/enhanced_dashboard_route.py
from fastapi import APIRouter, Query, HTTPException
from typing import List, Dict
from app.services.enhanced_tracking_service import enhanced_tracking_service
from app.connection.tenant_middleware import KAVIA_ROOT_DB_NAME

router = APIRouter(prefix="/dashboard", tags=["dashboard"])

@router.get("/overview/{tenant_id}")
async def get_dashboard_overview(
    tenant_id: str,
    days: int = Query(7, description="Number of days to analyze", ge=1, le=90)
):
    """Get comprehensive dashboard overview for a tenant with location data"""
    try:
        data = await enhanced_tracking_service.get_dashboard_data(tenant_id, days)
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching dashboard data: {str(e)}")

@router.get("/tenants")
async def get_all_tenants():
    """Get list of all tenants with basic stats"""
    try:
        tenants = await enhanced_tracking_service.get_all_tenants()
        return {"tenants": tenants}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching tenants: {str(e)}")

@router.get("/geographic/{tenant_id}")
async def get_geographic_analytics(
    tenant_id: str,
    days: int = Query(7, description="Number of days to analyze", ge=1, le=90)
):
    """Get geographic analytics for a tenant"""
    try:
        dashboard_data = await enhanced_tracking_service.get_dashboard_data(tenant_id, days)
        return {
            "tenant_id": tenant_id,
            "period": dashboard_data["period"],
            "geographic_data": dashboard_data.get("geographic_data", []),
            "summary": {
                "total_locations": len(dashboard_data.get("geographic_data", [])),
                "top_country": dashboard_data.get("geographic_data", [{}])[0].get("_id", {}).get("country") if dashboard_data.get("geographic_data") else None
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching geographic data: {str(e)}")