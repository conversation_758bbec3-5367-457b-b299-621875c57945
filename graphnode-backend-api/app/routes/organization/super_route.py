from fastapi import API<PERSON><PERSON><PERSON>, BackgroundTasks, HTTPException, Request
from pydantic import BaseModel, EmailStr, validator
from app.models.organization_models import Plan
from app.models.organization_models import Organization 
from app.models.organization_models import generate_org_id
from app.models.organization_models import Configurations, Settings
from app.utils.aws.cognito_main import TenantService
from app.utils.aws.cognito_user_manager import CognitoUserManager
from app.models.organization_models import User
from app.models.organization_models import initialize_default_plans
from app.routes.authentication_route import add_user_node_db
from app.utils.aws.cognito_user_manager import CognitoUserManager
from app.utils.aws.cognito_userpool import CognitoUser<PERSON>ool<PERSON>reator
import re
from pymongo import MongoClient
from pymongo.errors import PyMongoError
from bson import ObjectId
import random
import string
from fastapi import HTTPException
from app.core.Settings import settings, STAGE
from fastapi import Depends
from typing import Optional, List, Set
from app.connection.tenant_middleware import get_tenant_id
from fastapi import Query
from fastapi import Request
from app.utils.hash import encrypt_tenant_id
import urllib.parse
from app.classes.SESHandler import ses_handler, EmailTemplates
from datetime import datetime
from app.connection.establish_db_connection import get_mongo_db
from app.connection.tenant_middleware import KAVIA_ROOT_DB_NAME
from datetime import datetime, timedelta
import urllib.parse
import zipfile
import os
from fastapi.responses import StreamingResponse, JSONResponse
import io
from app.core.constants import KUBERNETES_MONITOR_COLLECTION_NAME, USER_PODS_COLLECTION, PODS_COLLECTION
from app.utils.k8.delete_pod import cleanup_codegen_pods
from app.utils.k8.delete_project import delete_kubernetes_deployment
from app.utils.task_utils import get_codegen_url
import requests
from app.models.auth_model import  ReferralCodeResponse, ReferralStats, ValidateReferralResponse
from app.routes.authentication_route import ReferralService

from app.core.kubernetes_monitor import KubernetesAvailablePodsManager, get_environment_and_namespace, kubernetes_manager

import asyncio
from concurrent.futures import ThreadPoolExecutor

def cleanup_pod_sync(pod_name: str, env_name: str, namespace: str):
    """Synchronous wrapper for the cleanup function"""
    try:
        delete_kubernetes_deployment(pod_name,env_name)
    except Exception as e:
        print(e)
        
ROOT_TENANT_ID = settings.KAVIA_ROOT_TENANT_ID
DB_NAME = KAVIA_ROOT_DB_NAME
#develop_kaviaroot

router = APIRouter(
    prefix="/manage/super",
    tags=["Organization Admin Management"],
    responses={404: {"description": "Not found"}}
)



def safe_parse_datetime(date_string):
    """Safely parse datetime string to datetime object"""
    if not date_string:
        return None
    
    try:
        # Try parsing ISO format first
        if 'T' in date_string:
            return datetime.fromisoformat(date_string.replace('Z', '+00:00'))
        else:
            # Try parsing as date only
            return datetime.strptime(date_string, '%Y-%m-%d')
    except Exception as e:
        print(f"Error parsing datetime '{date_string}': {e}")
        return None
    
    
# def get_allowed_tenants() -> Set[str]:
#     """
#     Get set of allowed tenants from settings.ALLOWED_TENANTS.
#     Using Set for O(1) lookup performance.
#     """
#     if not settings.ALLOWED_TENANTS:
#         return set()
    
#     # Split by comma, strip whitespace, and filter empty strings
#     tenants = {tenant.strip() for tenant in settings.ALLOWED_TENANTS.split(',') if tenant.strip()}
#     return tenants

MONGO_URI = settings.MONGO_CONNECTION_URI
client = MongoClient(
    MONGO_URI,
    maxPoolSize=10,
    connect=False,
    socketTimeoutMS=2000,
    serverSelectionTimeoutMS=2000
)
def get_allowed_tenants() -> Set[str]:
    """
    Get set of allowed tenants from MongoDB where enable_log_download_pod_crud is True.
    Using Set for O(1) lookup performance.
    
    Returns:
        Set[str]: Set of tenant IDs that have enable_log_download_pod_crud set to True
    """
    try:
        
        mongo_db = client[DB_NAME] 
        collection = mongo_db["organizations"]
        # Query for documents where enable_log_download_pod_crud is True
        # Using projection to only fetch _id field for better performance
        query = {"settings.enable_log_download_pod_crud": True}
        projection = {"_id": 1}
        # Execute query and extract tenant IDs
        cursor = collection.find(query, projection)
        tenant_ids = {doc["_id"] for doc in cursor}
        
        print(f"Found {len(tenant_ids)} allowed tenants")
        return tenant_ids
        
    except PyMongoError as e:
        print(f"MongoDB error while fetching allowed tenants: {e}")
        return set()  # Return empty set on error
    except Exception as e:
        print(f"Unexpected error while fetching allowed tenants: {e}")
        return set()


def check_tenant_authorization() -> None:
    """
    Check if current tenant is authorized to access the resource.
    Raises HTTPException if not authorized.
    """
    current_tenant = get_tenant_id()
    is_root_tenant = current_tenant == ROOT_TENANT_ID
    allowed_tenants = get_allowed_tenants()
    
    if not allowed_tenants and not is_root_tenant:
        raise HTTPException(
            status_code=500,
            detail="No allowed tenants configured"
        )
    
    if current_tenant not in allowed_tenants and not is_root_tenant:
        raise HTTPException(
            status_code=403,
            detail="You are not authorized to access this resource"
        )
    

def random_password():
    return ''.join(random.choices(string.ascii_letters + string.digits, k=10))

class OrganizationCreate(BaseModel):
    tenant_id: str
    name: str
    business_email: str
    industrial_type: str
    company_size: str
    domain: str
    image: str
    plan_id: str
    admin_name: str
    admin_email: str
    admin_contact_number: str
    admin_department: str
    configurations: Configurations
    settings: Settings

class AddAdminUserToOrganization(BaseModel):
    organization_id: str
    name: str
    email: str
    contact_number: str
    department: str

class BulkUserData(BaseModel):
    email: EmailStr
    name: str = None
    department: str = ""
    designation: str = "User"

    @validator('name', always=True)
    def set_name_from_email(cls, v, values):
        if not v and 'email' in values:
            return values['email']
        return v

class BulkAddUsersToOrganization(BaseModel):
    organization_id: str
    users: List[BulkUserData]
    plan_id: str 
class PodBulkDeleteRequest(BaseModel):
    pod_ids: List[str]


    
@router.get("/plans")
async def get_plans():
    try:
        plans = await Plan.get_all()
        if not plans:  # Check if empty
            await initialize_default_plans()
            plans = await Plan.get_all()
        return plans
    except Exception as e:
        if "Collection not found" in str(e):
            await initialize_default_plans() 
            return await Plan.get_all()
        raise e

@router.get("/plan/{plan_id}")
async def get_plan(plan_id: str,):
    return await Plan.get_by_id(plan_id)

@router.post("/create_plan")
async def create_plan(plan: Plan,):
    return await Plan.create(plan)

@router.get("/organizations")
async def get_organizations(latest: Optional[bool] = Query(default=True), type: Optional[str] = Query(default=None)):
    print("Reached here")
    if type == settings.KAVIA_B2C_CLIENT_ID:
        # For B2C organizations, only return organizations that start with "default_"
        return await Organization.get_with_admin_cost(latest=latest, filter_prefix="default_")
    elif type == "all":
        # For type "all", return all organizations
        return await Organization.get_with_admin_cost(latest=latest)
    else:
        # For default organizations, return organizations that don't start with "default_"
        return await Organization.get_with_admin_cost(latest=latest, exclude_prefix="default_")
    
@router.get('/b2c_user_cost_plan')
async def get_b2c_users_plan_cost():
    
    try:
        organization_id = settings.KAVIA_B2C_CLIENT_ID
        print("organization_id",organization_id)

        if not organization_id:
            raise HTTPException(status_code=400, detail="Organization ID not found.")

        # Call the function to get users for the organization
        users = await Organization.get_users_by_organization_id(organization_id)

        return {"organization_id": organization_id, "users": users}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch users: {str(e)}")
    
# Create Organization
@router.post("/create_organization")
async def create_organization(organization: OrganizationCreate, request: Request):
    tenant_id = organization.tenant_id
    name = organization.name
    business_email = organization.business_email
    industrial_type = organization.industrial_type
    company_size = organization.company_size
    domain = organization.domain
    image = organization.image
    plan_id = organization.plan_id
    admin_name = organization.admin_name
    admin_email = organization.admin_email
    admin_contact_number = organization.admin_contact_number
    admin_department = organization.admin_department
    configurations = organization.configurations
    settings = organization.settings
    admin_designation = "Admin"
    # Validate email format
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, business_email):
        raise HTTPException(status_code=400, detail="Invalid business email format")
    if not re.match(email_pattern, admin_email):
        raise HTTPException(status_code=400, detail="Invalid admin email format")
    
  
    organization = await Organization.get(tenant_id)
    if organization != {}:
            raise HTTPException(status_code=400, detail="Organization already exists")


    # Validate tenant ID format - only allow lowercase letters, numbers with single - or _ between
    tenant_pattern = r'^[a-z0-9]+$'
    if not re.match(tenant_pattern, tenant_id):
        raise HTTPException(
            status_code=400, 
            detail="Invalid tenant ID format. Only lowercase letters and numbers allowed"
        )
    
    response = {}
    org_id = tenant_id
    print("ORG ID", org_id)
    tenant_service = TenantService()
    try:
        tenant_cred = await tenant_service.get_or_create_tenant_cred(org_id)
        cognito_user_manager = CognitoUserManager(
            user_pool_id=tenant_cred['user_pool_id'],
            client_id=tenant_cred['client_id']
        )
        ui_url = request.headers.get('origin', str(request.base_url))
        set_password_url = f"{ui_url}/users/set_password?tenant_id={org_id}&email={urllib.parse.quote(admin_email)}"
        login_url = f"{ui_url}/users/login?tenant_id={org_id}&email={urllib.parse.quote(admin_email)}"
        user = cognito_user_manager.create_user(
            email=admin_email,
            temporary_password=f"Temp@2024{random_password()}",
            set_password_url=set_password_url,
            login_url=login_url,
            organization_name=name,
            custom_attributes={
                "tenant_id": org_id,
                "is_admin": True,
                "Department": admin_department,
                "Designation": admin_designation,
                "Name": admin_name
            }
        )
        user_id = user['Username']
        # active_subscription = get_mongo_db(db_name=DB_NAME, collection_name="active_subscriptions")

        # subscription_data = {
        #     "tenant_id": tenant_id,
        #     "price_id": "free_plan",
        #     "credits": 50000,
        #     "created_at": datetime.utcnow().isoformat()
        # }

        # # Insert or update the subscription data
        # await active_subscription.update_one_data(
        #   {"user_id": user_id},
        # {"$set" :subscription_data},
        # upsert=True
        # )

        await add_user_node_db(user_id, org_id)
        org = Organization( 
            _id=org_id,
            name=name,
            business_email=business_email,
            industrial_type=industrial_type,
            company_size=company_size,
            domain=domain,
            image=image,
            plan_id=plan_id,
            admin_id=user_id,
            configurations=configurations,
            group_ids=[],
            settings=settings
        )
        await Organization.create(org.model_dump())
        costs = {
            "organization_id": org_id,
            "organization_cost": "$0.000000",
            "organization_name": name,
            "users": []
        }
        await Organization.defineCosts(costs)
        await User.create({
            "_id": user_id,
            "name": admin_name,
            "email": admin_email,
            "contact_number": admin_contact_number,
            "department": admin_department,
            "designation": admin_designation,
            "organization_id": org_id,
            "group_ids": [],
            "is_admin": True
        })
        response['organization_id'] = org_id
        response['user_id'] = user_id
        return response
    except Exception as e:
        return {"error": str(e)}

@router.post("/update_organization")
async def update_organization(organization: Organization):
    
    print("ORGANIZATION-----", organization)
    return await Organization.update(organization.id, {
        "name": organization.name,
        "business_email": organization.business_email,
        "industrial_type": organization.industrial_type,
        "company_size": organization.company_size,
        "domain": organization.domain,
        "image": organization.image,
        "plan_id": organization.plan_id,
        "admin_id": organization.admin_id,
        "configurations": organization.configurations.model_dump(),
        "group_ids": organization.group_ids,
        "settings": organization.settings.model_dump(),
        "credits": organization.credits
      
    })


@router.post("/bulk_add_users_to_organization")
async def bulk_add_users_to_organization(data: BulkAddUsersToOrganization, request: Request):
    try:
        # Check if organization exists
        organization = await Organization.get(data.organization_id)
        if not organization:
            raise HTTPException(status_code=404, detail="Organization not found")
        
        # Validate that plan_id is one of the Premium plans
        allowed_plans = ["price_1RWBTrCI2zbViAE2WZFApvc8", "price_1RIosGCI2zbViAE25Ny8rOkc", "price_1RIozwCI2zbViAE2beuA7CDk"]
        if data.plan_id not in allowed_plans:
            raise HTTPException(status_code=400, detail="Invalid plan ID. Please select one of the Premium plans.")
        
        # Initialize tenant services
        tenant_service = TenantService()
        tenant_cred = await tenant_service.get_or_create_tenant_cred(data.organization_id)
        cognito_user_manager = CognitoUserManager(
            user_pool_id=tenant_cred['user_pool_id'],
            client_id=tenant_cred['client_id']
        )
        
        ui_url = request.headers.get('origin', str(request.base_url))
        tenant_name = organization["name"]
        
        # Get active_subscriptions collection
        active_subscription_collection = get_mongo_db(db_name=DB_NAME, collection_name="active_subscriptions")
        llm_costs_collection = get_mongo_db(db_name=DB_NAME, collection_name="llm_costs")
        
        results = {
            "success": [],
            "failed": []
        }
        
        # Current timestamp for subscription creation
        current_time = datetime.utcnow().isoformat() + "+00:00"
        
        # Process each user
        for user_data in data.users:
            try:
                # Create URLs
                set_password_url = f"{ui_url}/users/set_password?tenant_id={data.organization_id}&email={urllib.parse.quote(user_data.email)}"
                login_url = f"{ui_url}/users/login?tenant_id={data.organization_id}&email={urllib.parse.quote(user_data.email)}"
                
                # Create user in Cognito
                user = cognito_user_manager.create_user(
                    email=user_data.email,
                    temporary_password=f"Temp@2024{random_password()}",
                    organization_name=tenant_name,
                    set_password_url=set_password_url,
                    login_url=login_url,
                    custom_attributes={
                        "tenant_id": data.organization_id,
                        "is_admin": True,
                        "Department": user_data.department,
                        "Designation": user_data.designation,
                        "Name": user_data.name,
                        "free_user": False
                    }
                )
                
                user_id = user['Username']
                
                # Add user to database
                await add_user_node_db(user_id, data.organization_id)
                await User.create({
                    "_id": user_id,
                    "name": user_data.name,
                    "email": user_data.email,
                    "contact_number": "",
                    "department": user_data.department,
                    "designation": user_data.designation,
                    "organization_id": data.organization_id,
                    "group_ids": [],
                    "is_admin": True,
                    "is_free_user": False
                })
                
                # Create subscription record
                subscription_doc = {
                    "created_at": current_time,
                    "updated_at": current_time,
                    "price_id": data.plan_id,
                    "status": 2,  # Assuming 2 means active
                    "tenant_id": data.organization_id,
                    "user_id": user_id
                }
                
                # Insert subscription record
                await active_subscription_collection.insert(subscription_doc, active_subscription_collection.db)
                if data.organization_id == "b2c":
                    # Find the existing llm_costs document for the b2c organization
                    llm_costs_doc = await llm_costs_collection.get_one(
                        {"organization_id": "b2c"}, 
                        llm_costs_collection.db
                    )
                    
                    # Initialize the new user data for llm_costs
                    new_user_data = {
                        "user_id": user_id,
                        "type": "llm_interaction",
                        "cost": "0.00",
                        "current_plan": data.plan_id
                    }
                    if 'users'  in llm_costs_doc:
                        llm_costs_doc['users'].append(new_user_data)
                     
                    if llm_costs_doc:
                        # Update existing document by appending the new user to the users array
                        await llm_costs_collection.update_one(
                            {"_id": llm_costs_doc["_id"]},
                            llm_costs_doc,
                            upsert=True,
                            db = llm_costs_collection.db
                        )
                    else:
                        # Create new document if it doesn't exist
                        new_llm_costs_doc = {
                            "organization_id": "b2c",
                            "organization_cost": "$0.000",
                            "organization_name": organization.get("name", "Default"),
                            "users": [new_user_data],
                            "cost": "0.00",
                            "current_plan": data.plan_id,
                        }
                        await llm_costs_collection.insert(new_llm_costs_doc, llm_costs_collection.db)
                
                results["success"].append({"email": user_data.email, "user_id": user_id})
                
            except Exception as e:
                results["failed"].append({
                    "email": user_data.email,
                    "reason": str(e)
                })
        
        return {
            "message": f"Processed {len(results['success'])} users successfully, {len(results['failed'])} failed",
            "success_count": len(results['success']),
            "failed_count": len(results['failed']),
            "failed_details": results['failed']
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 
@router.post("/add_admin_user_to_organization")
async def add_admin_user_to_organization(data: AddAdminUserToOrganization, request: Request):
    try:
        # Check if organization exists
        organization = await Organization.get(data.organization_id)
        if not organization:
            raise HTTPException(status_code=404, detail="Organization not found")
        # if data.organization_id == settings.KAVIA_B2C_CLIENT_ID:
        #     raise HTTPException(status_code=400, detail="Cannot add admin user to B2C organization")
        tenant_service = TenantService()
        tenant_cred = await tenant_service.get_or_create_tenant_cred(data.organization_id)
        cognito_user_manager = CognitoUserManager(
            user_pool_id=tenant_cred['user_pool_id'],
            client_id=tenant_cred['client_id']
        )
        ui_url = request.headers.get('origin', str(request.base_url))
        tenant_name = organization["name"]
        set_password_url = f"{ui_url}/users/set_password?tenant_id={data.organization_id}&email={urllib.parse.quote(data.email)}"
        login_url = f"{ui_url}/users/login?tenant_id={data.organization_id}&email={urllib.parse.quote(data.email)}"
        user = cognito_user_manager.create_user(
            email=data.email,
            temporary_password=f"Temp@2024{random_password()}",
            organization_name=tenant_name,
            set_password_url=set_password_url,
            login_url=login_url,
            custom_attributes={
                "tenant_id": data.organization_id,
                "is_admin": True,
                "Department": data.department,
                "Designation": "Admin",
                "Name": data.name,
                "is_free_user": True
            }
        )
        user_id = user['Username'] 
        # Get active_subscriptions collection
        active_subscription_collection = get_mongo_db(db_name=DB_NAME, collection_name="active_subscriptions")
        llm_costs_collection = get_mongo_db(db_name=DB_NAME, collection_name="llm_costs")
        await add_user_node_db(user_id, data.organization_id)
        await User.create({
            "_id": user_id,
            "name": data.name,
            "email": data.email,
            "contact_number": data.contact_number,
            "department": data.department,
            "designation": "Admin",
            "organization_id": data.organization_id,
            "group_ids": [],
            "is_admin": True
        })
         # Current timestamp for subscription creation
        current_time = datetime.utcnow().isoformat() + "+00:00"
        # Create subscription record
        subscription_doc = {
            "created_at": current_time,
            "updated_at": current_time,
            "price_id": "price_1RWBNuCI2zbViAE2N6TkeNVB",
            "status": 2,  # Assuming 2 means active
            "tenant_id": data.organization_id,
            "user_id": user_id
        }
        # Insert subscription record
        await active_subscription_collection.insert(subscription_doc, active_subscription_collection.db)
        if data.organization_id == "b2c":
            # Find the existing llm_costs document for the b2c organization
            llm_costs_doc = await llm_costs_collection.get_one(
                {"organization_id": "b2c"}, 
                llm_costs_collection.db
            )
                    
            # Initialize the new user data for llm_costs
            new_user_data = {
                "user_id": user_id,
                "type": "llm_interaction",
                "cost": "0.00",
                "current_plan": "price_1RWBNuCI2zbViAE2N6TkeNVB"
            }
            if 'users'  in llm_costs_doc:
                llm_costs_doc['users'].append(new_user_data)
                     
            if llm_costs_doc:
                # Update existing document by appending the new user to the users array
                await llm_costs_collection.update_one(
                    {"_id": llm_costs_doc["_id"]},
                    llm_costs_doc,
                    upsert=True,
                    db = llm_costs_collection.db
                )
            else:
                # Create new document if it doesn't exist
                new_llm_costs_doc = {
                 "organization_id": "b2c",
                "organization_cost": "$0.000",
                "organization_name": organization.get("name", "Default"),
                "users": [new_user_data],
                "cost": "0.00",
                "current_plan": "price_1RWBNuCI2zbViAE2N6TkeNVB"
                }
                await llm_costs_collection.insert(new_llm_costs_doc, llm_costs_collection.db)

        
        return {"message": "Admin user added successfully"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/organization/{organization_id}")
async def delete_organization(organization_id: str):
    """
    Delete an organization and all its associated data
    
    Args:
        organization_id (str): The ID of the organization to delete
        
    Returns:
        dict: A message indicating success or failure
        
    Raises:
        HTTPException: If organization not found or deletion fails
    """
    try:
        tenant_id = get_tenant_id()
        if tenant_id != ROOT_TENANT_ID:
            raise HTTPException(
                status_code=403,
                detail="You are not authorized to delete this organization"
            )
        # First check if organization exists
        organization = await Organization.get(organization_id)
        if not organization:
            raise HTTPException(
                status_code=404,
                detail="Organization not found"
            )

        # Delete the organization and its associated data
        deleted = await Organization.delete(organization_id)
        
        # Get tenant credentials before deleting user pool
        tenant_service = TenantService()
        tenant_cred = await tenant_service.get_tenant_cred(organization_id)
        if tenant_cred and 'user_pool_id' in tenant_cred:
            userpool_creator = CognitoUserPoolCreator()
            userpool_creator.delete_user_pool(tenant_cred['user_pool_id'])
        
        if deleted:
            return {
                "message": "Organization and associated data successfully deleted",
                "organization_id": organization_id
            }
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to delete organization"
            )
            
    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred while deleting the organization: {str(e)}"
        )

@router.get("/delete_admin_user")
async def delete_admin_user(user_id: str):
    try:
        if get_tenant_id() != ROOT_TENANT_ID:
            raise HTTPException(
                status_code=403,
                detail="You are not authorized to delete this admin user"
            )

        # Get user details to find organization
        user = await User.get(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="Admin user not found")

        organization_id = user.get('organization_id')
        if not organization_id:
            raise HTTPException(status_code=404, detail="Organization not found for this admin")

        # Count total admin users in the organization
        users = await Organization.get_users(organization_id)
        admin_count = sum(1 for u in users if u.get('is_admin', False))

        if admin_count <= 1 and organization_id != settings.KAVIA_B2C_CLIENT_ID:
            raise HTTPException(
                status_code=400,
                detail="Cannot delete the last admin. At least one admin should be present"
            )

        # Delete from Cognito - continue even if this fails
        try:
            tenant_service = TenantService()
            tenant_cred = await tenant_service.get_tenant_cred(organization_id)
            cognito_user_manager = CognitoUserManager(
                user_pool_id=tenant_cred['user_pool_id'],
                client_id=tenant_cred['client_id']
            )
            cognito_user_manager.delete_user(user_id)
        except Exception as cognito_error:
            # Log the error but continue with database deletion
            print(f"Failed to delete user from Cognito: {str(cognito_error)}")

        # Delete from database
        deleted = await User.delete(user_id)
        if not deleted:
            raise HTTPException(status_code=500, detail="Failed to delete admin user")
        
        return {"message": "Admin user deleted successfully"}
    except HTTPException as he:
        raise he
    except Exception as e:
        print("ERROR", e)
        raise HTTPException(
            status_code=500,
            detail=f"{str(e)}"
        )

@router.get("/download_logs_CGA/{tenant_id}/{project_id}/{task_id}")
async def download_logs(tenant_id: str, project_id: int, task_id: str):
    """
    Download all logs for a specific task as a zip file.
    Provides a browser download experience similar to downloading images from Google.
    
    Args:
        project_id: Project ID
        task_id: Task ID
        tenant_id: Optional tenant ID (defaults to current tenant)
        
    Returns:
        Streaming response with zip file
    """
    try:
        check_tenant_authorization()
        print(f"Tennat_id: {tenant_id}, project_id: {project_id}, task_id: {task_id}")
        
        # override tenant_id
        tenant_id = "T0000"
        
        if os.getenv("LOCAL_DEBUG"):
            log_dir = f"/tmp/{tenant_id}/{project_id}/logs/{task_id}/"
        else:
            log_dir = f"/app/data/{tenant_id}/{project_id}/logs/{task_id}/"
        
        # Check if directory exists
        if not os.path.exists(log_dir):
            raise HTTPException(status_code=404, detail=f"No logs found for task {task_id}")
        
        # Create timestamp for unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"logs_task_{task_id}_{timestamp}.zip"
        
        # Create zip file in memory
        zip_io = io.BytesIO()
        with zipfile.ZipFile(zip_io, mode='w', compression=zipfile.ZIP_DEFLATED) as zipf:
            # Walk through the directory and add all files
            for root, _, files in os.walk(log_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    # Add file to zip with relative path
                    arcname = os.path.relpath(file_path, log_dir)
                    zipf.write(file_path, arcname)
        
        # Reset the pointer to the beginning of the BytesIO object
        zip_io.seek(0)
        
        # Return streaming response with appropriate headers for download
        headers = {
            'Content-Disposition': f'attachment; filename="{filename}"',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        }
        
        return StreamingResponse(
            zip_io, 
            media_type="application/zip",
            headers=headers
        )
        
    except HTTPException:
        raise  # Re-raise HTTP exceptions
    except Exception as e:
        import traceback
        print(f"Error creating log zip file: {str(e)}")
        print(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to create log archive: {str(e)}")
    

@router.get("/is_organization_exists")
async def is_organization_exists(tenant_id: str):
    organization = await Organization.get(tenant_id)
    return {"exists": organization != {}}

@router.post("/update_organization_status")
async def update_organization_status(organization_id: str, status: str):
    """
    Update organization status to active or inactive
    
    Args:
        organization_id (str): The ID of the organization to update
        status (str): The new status ('active' or 'inactive')
        
    Returns:
        dict: A message indicating success or failure
        
    Raises:
        HTTPException: If organization not found, invalid status, or update fails.
    """
    try:
        tenant_id = get_tenant_id()
        # if tenant_id != ROOT_TENANT_ID:
        #     raise HTTPException(
        #         status_code=403,
        #         detail="You are not authorized to update organization status"
        #     )
            
        # Validate status
        if status not in ["active", "inactive"]:
            raise HTTPException(
                status_code=400,
                detail="Status must be either 'active' or 'inactive'"
            )
            
        # First check if organization exists
        organization = await Organization.get(organization_id)
        if not organization:
            raise HTTPException(
                status_code=404,
                detail="Organization not found"
            )

        # Update the organization status
        updated = await Organization.update(organization_id, {"status": status})
        
        if updated:
            try:
                if status == "active":
                    admin_id = organization["admin_id"]
                    # Get admin user details
                    admin_user = await User.get(admin_id)
                    admin_name = admin_user.get("name", "User")
                    
                    # Personalize the email with admin name
                    activation_email_html = open(EmailTemplates.ACTIVATION_TEMPLATE, "r").read().replace(
                        "%name%", admin_name
                    )
                    ses_handler.send_email_smtp(
                        to_address=organization["business_email"],
                        subject="✅ Account Activation Successful",
                        body_text="Your account has been successfully activated.",
                        body_html=activation_email_html
                    )
                elif status == "inactive":
                    admin_id = organization["admin_id"]
                    # Get admin user details
                    admin_user = await User.get(admin_id)
                    admin_name = admin_user.get("name", "User")
                    deactivation_email_html = open(EmailTemplates.DEACTIVATION_TEMPLATE, "r").read().replace(
                        "{{name}}", admin_name
                    )
                    ses_handler.send_email_smtp(
                        to_address=organization["business_email"],
                        subject=" Account Deactivated",
                        body_text="Your account has been deactivated.",
                        body_html=deactivation_email_html
                    )
            except Exception as e:
                print("ERROR", e)
            return {
                "message": f"Organization status updated to {status} successfully",
                "organization_id": organization_id,
                "status": status
            }
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to update organization status"
            )
            
    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred while updating organization status: {str(e)}"
        )

@router.post("/update_organization_public_access")
async def update_organization_public_access(organization_id: str, opentopublic: bool):
    """
    Update organization's public access setting
    
    Args:
        organization_id (str): The ID of the organization to update
        opentopublic (bool): Whether the organization should be open to public
        
    Returns:
        dict: A message indicating success or failure
        
    Raises:
        HTTPException: If organization not found or update fails
    """
    try:
        tenant_id = get_tenant_id()
        print("TENANT ID", tenant_id)
        # if tenant_id != ROOT_TENANT_ID:
        #     raise HTTPException(
        #         status_code=403,
        #         detail="You are not authorized to update organization public access"
        #     )
            
        # First check if organization exists
        organization = await Organization.get(organization_id)
        if not organization:
            # Only create the organization if it's the Kavia super tenant ID
            if organization_id == ROOT_TENANT_ID:
                try:
                    org = Organization( 
                        _id=organization_id,
                        name=f"Super Admin",
                        business_email="<EMAIL>",
                        industrial_type="IT",
                        company_size="small",
                        domain="kavia.ai",
                        image=None,
                        plan_id="",  # Use default or first available plan
                        admin_id="",  # Will be updated later
                        opentopublic=opentopublic,
                        configurations=Configurations(),
                        group_ids=[],
                        settings=Settings()
                    )
                    await Organization.create(org.model_dump())
                    return {
                        "message": f"Organization created with public access set to {opentopublic}",
                        "organization_id": organization_id,
                        "opentopublic": opentopublic
                    }
                except Exception as e:
                    raise HTTPException(
                        status_code=500,
                        detail=f"Failed to create organization: {str(e)}"
                    )
            else:
                # For non-ROOT_TENANT_ID, return error that organization doesn't exist
                raise HTTPException(
                    status_code=404,
                    detail="Organization not found"
                )

        # Update the organization's public access setting
        updated = await Organization.update(organization_id, {"opentopublic": opentopublic})
        
        if updated:
            return {
                "message": f"Organization public access updated to {opentopublic} successfully",
                "organization_id": organization_id,
                "opentopublic": opentopublic
            }
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to update organization public access"
            )
            
    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred while updating organization public access: {str(e)}"
        )

@router.post("/update_user_status")
async def update_user_status(tenant_id: str, email: str, status: str):
    """
    Update a user's status to active or inactive
    
    Args:
        tenant_id (str): The ID of the tenant/organization
        email (str): The email of the user to update
        status (str): The new status ('active' or 'inactive')
        
    Returns:
        dict: A message indicating success or failure
        
    Raises:
        HTTPException: If user not found, invalid status, or update fails
    """
    try:
        # Check if request is from super tenant
        current_tenant_id = get_tenant_id()
        # if current_tenant_id != ROOT_TENANT_ID:
        #     raise HTTPException(
        #         status_code=403,
        #         detail="You are not authorized to update user status"
        #     )
            
        # Validate status
        if status not in ["active", "inactive"]:
            raise HTTPException(
                status_code=400,
                detail="Status must be either 'active' or 'inactive'"
            )
            
        # Check if organization exists
        organization = await Organization.get(tenant_id)
        if not organization:
            raise HTTPException(
                status_code=404,
                detail="Organization not found"
            )

        # Get the user through Cognito by email
        tenant_service = TenantService()
        tenant_cred = await tenant_service.get_tenant_cred(tenant_id)
        cognito_user_manager = CognitoUserManager(
            user_pool_id=tenant_cred['user_pool_id'],
            client_id=tenant_cred['client_id']
        )
        
        try:
            # Update user's enabled status in Cognito
            if status == "active":
                cognito_user_manager.enable_user(email)
            else:  # status == "inactive"
                cognito_user_manager.disable_user(email)
                
            # Update user status in MongoDB
            users_collection = User.get_collection()
            
            # Try to update the user by email first
            result = users_collection.update_one(
                {"email": email, "organization_id": tenant_id},
                {"$set": {"status": status, "updated_at": datetime.utcnow()}}
            )
            
            if result.modified_count == 0:
                # Try by username/ID if update by email wasn't successful
                # Get the user ID from Cognito
                user = cognito_user_manager.get_user_by_identifier(email)
                username = user['Username']
                
                result = users_collection.update_one(
                    {"_id": username, "organization_id": tenant_id},
                    {"$set": {"status": status, "updated_at": datetime.utcnow()}}
                )
                
                if result.modified_count == 0:
                    result = users_collection.update_one(
                        {"cognito_id": username, "organization_id": tenant_id},
                        {"$set": {"status": status, "updated_at": datetime.utcnow()}}
                    )
            
            # Even if MongoDB update fails, we return success as Cognito is the source of truth
            return {
                "message": f"User status updated to {status} successfully",
                "email": email,
                "organization_id": tenant_id,
                "status": status
            }
            
        except Exception as e:
            raise HTTPException(
                status_code=404,
                detail=f"User not found or could not be updated: {str(e)}"
            )
            
    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred while updating user status: {str(e)}"
        )

@router.post("/update_user_status_by_id")
async def update_user_status_by_id(tenant_id: str, user_id: str, status: str):
    """
    Update a user's status to active or inactive using user ID
    
    Args:
        tenant_id (str): The ID of the tenant/organization
        user_id (str): The ID of the user to update
        status (str): The new status ('active' or 'inactive')
        
    Returns:
        dict: A message indicating success or failure
        
    Raises:
        HTTPException: If user not found, invalid status, or update fails
    """
    try:
        # Check if request is from super tenant
        current_tenant_id = get_tenant_id()
        # if current_tenant_id != ROOT_TENANT_ID:
        #     raise HTTPException(
        #         status_code=403,
        #         detail="You are not authorized to update user status"
        #     )
            
        # Validate status
        if status not in ["active", "inactive"]:
            raise HTTPException(
                status_code=400,
                detail="Status must be either 'active' or 'inactive'"
            )
            
        # Check if organization exists
        organization = await Organization.get(tenant_id)
        if not organization:
            raise HTTPException(
                status_code=404,
                detail="Organization not found"
            )

        # Get user from database first to get their email
        user = await User.get(user_id)
        if not user:
            raise HTTPException(
                status_code=404,
                detail="User not found"
            )
        
        email = user.get("email")
        if not email:
            raise HTTPException(
                status_code=404,
                detail="User email not found in database"
            )

        # Get the Cognito user manager
        tenant_service = TenantService()
        tenant_cred = await tenant_service.get_tenant_cred(tenant_id)
        cognito_user_manager = CognitoUserManager(
            user_pool_id=tenant_cred['user_pool_id'],
            client_id=tenant_cred['client_id']
        )
        
        try:
            # Update user's enabled status in Cognito
            if status == "active":
                cognito_user_manager.enable_user(email)
            else:  # status == "inactive"
                cognito_user_manager.disable_user(email)
                
            # Update user status in MongoDB
            result = await User.update(
                user_id, 
                {"status": status}
            )
            
            if not result:
                raise HTTPException(
                    status_code=500,
                    detail="Failed to update user status in database"
                )
            
            return {
                "message": f"User status updated to {status} successfully",
                "user_id": user_id,
                "organization_id": tenant_id,
                "status": status
            }
            
        except Exception as e:
            raise HTTPException(
                status_code=404,
                detail=f"User not found or could not be updated: {str(e)}"
            )
            
    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred while updating user status: {str(e)}"
        )


mongo_db_init = client[KUBERNETES_MONITOR_COLLECTION_NAME]

# Replace the existing get_all_used_pod function with this updated version
@router.get("/get_all_used_pod")
async def get_all_used_pod():
    """Get all used pods using direct Kubernetes API instead of MongoDB."""
    try:
        check_tenant_authorization()
        
        # Create Kubernetes pods manager instance
        environment, namespace = get_environment_and_namespace()
        
        if not environment or not namespace:
            raise HTTPException(
                status_code=500,
                detail="Environment or namespace not configured properly"
            )
        
        
        # Get used pods directly from Kubernetes
        used_pods, count = kubernetes_manager.get_used_codegen_pods()
        
        return {
            "status": "success",
            "data": used_pods,
            "count": count,
            "message": f"Successfully retrieved {count} used pod records from Kubernetes",
            "source": "kubernetes_api",  # Indicate data source
            "environment": kubernetes_manager.environment,
            "namespace": kubernetes_manager.namespace
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error fetching used pods: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch used pods data: {str(e)}"
        )
    
@router.get("/get_all_available_pod")
async def get_all_available_pod():
    """Get all available pods using direct Kubernetes API instead of MongoDB."""
    try:
        check_tenant_authorization()
        
        # Create Kubernetes pods manager instance
        environment, namespace = get_environment_and_namespace()
        
        if not environment or not namespace:
            raise HTTPException(
                status_code=500,
                detail="Environment or namespace not configured properly"
            )
        
        # Get available pods directly from Kubernetes
        available_pods, count = kubernetes_manager.get_available_codegen_pods()
        
        return {
            "status": "success",
            "data": available_pods,
            "count": count,
            "message": f"Successfully retrieved {count} available pod records from Kubernetes",
            "source": "kubernetes_api",  # Indicate data source
            "environment": kubernetes_manager.environment,
            "namespace": kubernetes_manager.namespace
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error fetching available pods: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch available pods data: {str(e)}"
        )

@router.get("/check_project_id_to_pod_map/{project_id}")
async def check_project_id_to_pod_map(project_id: str):
    try:
        # Authorization check
        check_tenant_authorization()
        
        # Database connection and collection access
        collection = mongo_db_init[USER_PODS_COLLECTION]
        
        # Convert project_id to int with validation
        try:
            project_id_int = int(project_id)
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail="Invalid project_id format. Must be a valid integer"
            )
        
        # Query with proper cursor handling
        cursor = collection.find({"project_id": project_id_int})
        pods_data = []
        
        for doc in cursor:
            # Serialize ObjectId fields to strings
            serialized_doc = {
                key: str(value) if isinstance(value, ObjectId) else value
                for key, value in doc.items()
            }
            pods_data.append(serialized_doc)
        
        # Check if no pods found AFTER processing cursor
        if not pods_data:
            raise HTTPException(
                status_code=404, 
                detail=f"No pods found for project_id: {project_id}"
            )
        
        return {
            "status": "success",
            "data": pods_data,
            "count": len(pods_data),
            "message": f"Successfully retrieved {len(pods_data)} pod record(s)"
        }
        
    except HTTPException:
        raise
    except PyMongoError as e:
        print(f"Database error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Database operation failed"
        )
    except Exception as e:
        print(f"Unexpected error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to fetch pod data"
        )


@router.delete("/delete_project_to_pod_map/{project_id}")
async def delete_pod_by_project(project_id: str):
    try:
        # Authorization check
        check_tenant_authorization()
        # Database connection and collection access
        collection = mongo_db_init[USER_PODS_COLLECTION]
        
        # Convert project_id to int for query
        try:
            project_id_int = int(project_id)
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail="Invalid project_id format. Must be a valid integer"
            )
        
        # Check if pods exist before deletion
        existing_pods = list(collection.find({"project_id": project_id_int}))
        
        if not existing_pods:
            raise HTTPException(
                status_code=404, 
                detail=f"No pods found for project_id: {project_id}"
            )
        try:
            base_url =  get_codegen_url(tenant_id=get_tenant_id(), project_id=project_id, stage=STAGE)
            delete_url = f"{base_url}/delete"
            requests.delete(delete_url, timeout=10, verify=False)
        except Exception as e:
            print(f"Error occured while deleting: {e}")
        # Delete all pods with the given project_id
        delete_result = collection.delete_many({"project_id": project_id_int})
        
        if delete_result.deleted_count == 0:
            raise HTTPException(
                status_code=500,
                detail="Failed to delete pods"
            )
        
        return {
            "status": "success",
            "deleted_count": delete_result.deleted_count,
            "message": f"Successfully deleted {delete_result.deleted_count} pod(s) for project_id: {project_id}"
        }
        
    except HTTPException:
        raise
    except PyMongoError as e:
        print(f"Database error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Database operation failed"
        )
    except Exception as e:
        print(f"Unexpected error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to delete pod data"
        )

@router.delete("/delete_pod_by_pod_id/{pod_id}")
async def delete_pod_by_project(pod_id: str):
    try:
        # Authorization check
        check_tenant_authorization()
        # Database connection and collection access
        collection = mongo_db_init[USER_PODS_COLLECTION]
        
    
        # Check if pods exist before deletion
        existing_pods = list(collection.find({"pod_id": pod_id}))
        
        if not existing_pods:
            raise HTTPException(
                status_code=404, 
                detail=f"No pods found for pod_id: {pod_id}"
            )
        try:
            project_ids = set()  # Use set to avoid duplicates
            for pod in existing_pods:
                if "project_id" in pod and pod["project_id"]:
                    project_ids.add(pod["project_id"])
            for project_id in project_ids:
                base_url =  get_codegen_url(tenant_id=get_tenant_id(), project_id=project_id, stage=STAGE)
                delete_url = f"{base_url}/delete"
                requests.delete(delete_url, timeout=10, verify=False)
        except Exception as e:
            print(f"Error occured while deleting: {e}")

        # Delete all pods with the given project_id
        delete_result = collection.delete_many({"pod_id": pod_id})
        
        if delete_result.deleted_count == 0:
            raise HTTPException(
                status_code=500,
                detail="Failed to delete pods"
            )
        
        return {
            "status": "success",
            "deleted_count": delete_result.deleted_count,
            "message": f"Successfully deleted {delete_result.deleted_count} pod(s) for pod_id: {pod_id}"
        }
        
    except HTTPException:
        raise
    except PyMongoError as e:
        print(f"Database error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Database operation failed"
        )
    except Exception as e:
        print(f"Unexpected error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to delete pod data"
        )
from app.services.session_tracker import get_name_by_user_id,get_organization_name_by_tenant_id


@router.get("/tenants_filter_service_type")
async def get_tenants(
    date_from: Optional[str] = Query(None),
    date_to: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    service_type: Optional[str] = Query(None)
):
    """Get all tenants with session statistics with optional date filtering"""
    try:
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
        
        # Build match stage for filtering
        match_stage = {}
        
        if date_from or date_to:
            match_stage["created_at"] = {}
            if date_from:
                match_stage["created_at"]["$gte"] = datetime.fromisoformat(date_from)
            if date_to:
                match_stage["created_at"]["$lte"] = datetime.fromisoformat(date_to)
        
        if status:
            match_stage["status"] = status
            
        if service_type:
            match_stage["service_type"] = service_type
        
        pipeline = []
        
        # Add match stage if we have filters
        if match_stage:
            pipeline.append({"$match": match_stage})
        
        pipeline.extend([
            {
                "$group": {
                    "_id": "$tenant_id",
                    "total_sessions": {"$sum": 1},
                    "active_sessions": {
                        "$sum": {"$cond": [{"$eq": ["$status", "active"]}, 1, 0]}
                    },
                    "completed_sessions": {
                        "$sum": {"$cond": [{"$eq": ["$status", "completed"]}, 1, 0]}
                    },
                    "failed_sessions": {
                        "$sum": {"$cond": [{"$eq": ["$status", "failed"]}, 1, 0]}
                    },
                    "total_cost": {"$sum": "$total_cost"},
                    "last_activity": {"$max": "$last_updated"}
                }
            },
            {"$sort": {"last_activity": -1}}
        ])
        
        tenants = list(mongo_db.db["session_tracking"].aggregate(pipeline))
        
        return {
            "success": True,
            "tenants": [
                {
                    "id": tenant["_id"],
                    "name": get_organization_name_by_tenant_id(tenant["_id"]),
                    "total_sessions": tenant["total_sessions"],
                    "active_sessions": tenant["active_sessions"],
                    "completed_sessions": tenant["completed_sessions"],
                    "failed_sessions": tenant["failed_sessions"],
                    "total_cost": tenant["total_cost"],
                    "last_activity": tenant["last_activity"].isoformat() if tenant["last_activity"] else None
                }
                for tenant in tenants
            ],
            "filters_applied": {
                "date_from": date_from,
                "date_to": date_to,
                "status": status,
                "service_type": service_type
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    

@router.get("/tenants/{tenant_id}/users")
async def get_users_by_tenant(
    tenant_id: str,
    date_from: Optional[str] = Query(None),
    date_to: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    service_type: Optional[str] = Query(None)
):
    """Get all users for a specific tenant with session statistics and optional date filtering"""
    try:
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
        
        # Build match stage
        match_stage = {"tenant_id": tenant_id}
        
        if date_from or date_to:
            match_stage["created_at"] = {}
            if date_from:
                match_stage["created_at"]["$gte"] = datetime.fromisoformat(date_from)
            if date_to:
                match_stage["created_at"]["$lte"] = datetime.fromisoformat(date_to)
        
        if status:
            match_stage["status"] = status
            
        if service_type:
            match_stage["service_type"] = service_type
        
        pipeline = [
            {"$match": match_stage},
            {
                "$group": {
                    "_id": "$user_id",
                    "total_sessions": {"$sum": 1},
                    "active_sessions": {
                        "$sum": {"$cond": [{"$eq": ["$status", "active"]}, 1, 0]}
                    },
                    "total_cost": {"$sum": "$total_cost"},
                    "last_session": {"$max": "$last_updated"}
                }
            },
            {"$sort": {"last_session": -1}}
        ]
        
        users = list(mongo_db.db["session_tracking"].aggregate(pipeline))
        
        return {
            "success": True,
            "users": [
                {
                    "id": user["_id"],
                    "name": get_name_by_user_id(user["_id"]),
                    "total_sessions": user["total_sessions"],
                    "active_sessions": user["active_sessions"],
                    "total_cost": user["total_cost"],
                    "last_session": user["last_session"].isoformat() if user["last_session"] else None
                }
                for user in users
            ],
            "filters_applied": {
                "date_from": date_from,
                "date_to": date_to,
                "status": status,
                "service_type": service_type
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get("/tenants/{tenant_id}/users/{user_id}/sessions")
async def get_sessions_by_user(
    tenant_id: str, 
    user_id: str,
    status: Optional[str] = Query(None),
    service_type: Optional[str] = Query(None),
    date_from: Optional[str] = Query(None),
    date_to: Optional[str] = Query(None),
    page: int = Query(1, ge=1),
    limit: int = Query(0, ge=0, le=1000)  # Changed: Allow 0 for no limit, increased max to 1000
):
    """Get sessions for a specific user"""
    try:
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
        
        # Build query
        query = {"tenant_id": tenant_id, "user_id": user_id}
        
        if status:
            query["status"] = status
            
        if service_type:
            query["service_type"] = service_type
            
        if date_from or date_to:
            query["created_at"] = {}
            if date_from:
                query["created_at"]["$gte"] = datetime.fromisoformat(date_from)
            if date_to:
                query["created_at"]["$lte"] = datetime.fromisoformat(date_to)
        
        # Get total count
        total = mongo_db.db["session_tracking"].count_documents(query)
        
        # Get sessions with conditional pagination
        sessions_query = mongo_db.db["session_tracking"].find(query).sort("created_at", -1)
        
        # Apply pagination only if limit > 0
        if limit > 0:
            sessions = list(sessions_query.skip((page - 1) * limit).limit(limit))
            total_pages = (total + limit - 1) // limit
        else:
            # No limit - return all records
            sessions = list(sessions_query)
            total_pages = 1
            limit = total  # Set limit to total for response consistency
            page = 1  # Reset page to 1 since we're returning everything
        
        # Format sessions
        formatted_sessions = []
        for session in sessions:
            session["_id"] = str(session["_id"])
            if session.get("session_start"):
                session["session_start"] = session["session_start"].isoformat()
            if session.get("session_end"):
                session["session_end"] = session["session_end"].isoformat()
            if session.get("last_updated"):
                session["last_updated"] = session["last_updated"].isoformat()
            if session.get("created_at"):
                session["created_at"] = session["created_at"].isoformat()
            
            # Format cost history timestamps
            if session.get("cost_history"):
                for cost_entry in session["cost_history"]:
                    if cost_entry.get("timestamp"):
                        cost_entry["timestamp"] = cost_entry["timestamp"].isoformat()
                
            formatted_sessions.append(session)
        
        return {
            "success": True,
            "sessions": formatted_sessions,
            "total": total,
            "page": page,
            "limit": limit,
            "total_pages": total_pages
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/sessions/{task_id}")
async def get_session_details(task_id: str):
    """Get detailed session information including cost history"""
    try:
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
        
        # Get session
        session = mongo_db.db["session_tracking"].find_one({"task_id": task_id})
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Format dates
        if session.get("session_start"):
            session["session_start"] = session["session_start"].isoformat()
        if session.get("session_end"):
            session["session_end"] = session["session_end"].isoformat()
        if session.get("last_updated"):
            session["last_updated"] = session["last_updated"].isoformat()
        if session.get("created_at"):
            session["created_at"] = session["created_at"].isoformat()
        
        session["_id"] = str(session["_id"])
        
        # Format cost history timestamps
        if session.get("cost_history"):
            for cost_entry in session["cost_history"]:
                if cost_entry.get("timestamp"):
                    cost_entry["timestamp"] = cost_entry["timestamp"].isoformat()
        
        return {
            "success": True,
            "session": session
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))



@router.delete("/delete_all_available_pods")
async def delete_all_available_pods():
    try:
        # Authorization check
        check_tenant_authorization()
        # Database connection and collection access
        collection = mongo_db_init[PODS_COLLECTION]
        
        # Check if available pods exist before deletion
        existing_pods = list(collection.find({"status": "available"}))
        
        if not existing_pods:
            raise HTTPException(
                status_code=404, 
                detail="No available pods found"
            )
        try:
            project_ids = set()  # Use set to avoid duplicates
            for pod in existing_pods:
                if "project_id" in pod and pod["project_id"]:
                    project_ids.add(pod["project_id"])
            for project_id in project_ids:
                base_url =  get_codegen_url(tenant_id=get_tenant_id(), project_id=project_id, stage=STAGE)
                delete_url = f"{base_url}/delete"
                requests.delete(delete_url, timeout=10, verify=False)
        except Exception as e:
            print(f"Error occured while deleting: {e}")
        # Delete all available pods
        delete_result = collection.delete_many({"status": "available"})
        
        if delete_result.deleted_count == 0:
            raise HTTPException(
                status_code=500,
                detail="Failed to delete available pods"
            )
        
        return {
            "status": "success",
            "deleted_count": delete_result.deleted_count,
            "message": f"Successfully deleted {delete_result.deleted_count} available pod(s)"
        }
        
    except HTTPException:
        raise
    except PyMongoError as e:
        print(f"Database error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Database operation failed"
        )
    except Exception as e:
        print(f"Unexpected error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to delete available pods"
        )
    
@router.delete("/delete_available_pod/{pod_name}")
async def delete_particular_available_pod(pod_name: str, background_tasks: BackgroundTasks):
    try:
        # Authorization check
        check_tenant_authorization()
        pod = KubernetesAvailablePodsManager('dev')
        namespace = pod._get_namespace()
        
        STAGE = os.environ.get("STAGE",None)
        if 'dev' in STAGE:
            STAGE = 'dev'
        background_tasks.add_task(cleanup_pod_sync, pod_name, STAGE, namespace)
        return {
            "status": "success",
            "message": f"Successfully deleted available pod with pod_id: {pod_name}"
        }
        
    except HTTPException:
        raise
    except PyMongoError as e:
        print(f"Database error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Database operation failed"
        )
    except Exception as e:
        print(f"Unexpected error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to delete available pod"
        )

@router.delete("/delete_multiple_used_pods")
async def delete_multiple_used_pods(request: PodBulkDeleteRequest):
    try:
        # Authorization check
        check_tenant_authorization()
        # Database connection and collection access
        collection = mongo_db_init[USER_PODS_COLLECTION]
        
        pod_ids = request.pod_ids
        
        if not pod_ids:
            raise HTTPException(
                status_code=400,
                detail="Pod IDs list cannot be empty"
            )
        
        # Check if used pods exist before deletion
        existing_pods = list(collection.find({
            "pod_id": {"$in": pod_ids}, 
            "status": {"$ne": "available"}
        }))
        
        if not existing_pods:
            raise HTTPException(
                status_code=404, 
                detail="No used pods found with the provided pod_ids"
            )
        try:
            project_ids = set()  # Use set to avoid duplicates
            for pod in existing_pods:
                if "project_id" in pod and pod["project_id"]:
                    project_ids.add(pod["project_id"])
            for project_id in project_ids:
                base_url =  get_codegen_url(tenant_id=get_tenant_id(), project_id=project_id, stage=STAGE)
                delete_url = f"{base_url}/delete"
                requests.delete(delete_url, timeout=10, verify=False)
        except Exception as e:
            print(f"Error occured while deleting: {e}")
        # Delete multiple used pods
        delete_result = collection.delete_many({
            "pod_id": {"$in": pod_ids}, 
            "status": {"$ne": "available"}
        })
        
        if delete_result.deleted_count == 0:
            raise HTTPException(
                status_code=500,
                detail="Failed to delete used pods"
            )
        
        return {
            "status": "success",
            "deleted_count": delete_result.deleted_count,
            "requested_count": len(pod_ids),
            "message": f"Successfully deleted {delete_result.deleted_count} used pod(s) out of {len(pod_ids)} requested"
        }
        
    except HTTPException:
        raise
    except PyMongoError as e:
        print(f"Database error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Database operation failed"
        )
    except Exception as e:
        print(f"Unexpected error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to delete multiple used pods"
        )

@router.delete("/delete_all_used_pods")
async def delete_all_used_pods():
    try:
        # Authorization check
        check_tenant_authorization()
        # Database connection and collection access
        collection = mongo_db_init[USER_PODS_COLLECTION]
        
        # Check if used pods exist before deletion
        existing_pods = list(collection.find({"status": {"$ne": "available"}}))
        
        if not existing_pods:
            raise HTTPException(
                status_code=404, 
                detail="No used pods found"
            )
        try:
            project_ids = set()  # Use set to avoid duplicates
            for pod in existing_pods:
                if "project_id" in pod and pod["project_id"]:
                    project_ids.add(pod["project_id"])
            for project_id in project_ids:
                base_url =  get_codegen_url(tenant_id=get_tenant_id(), project_id=project_id, stage=STAGE)
                delete_url = f"{base_url}/delete"
                requests.delete(delete_url, timeout=10, verify=False)
        except Exception as e:
            print(f"Error occured while deleting: {e}")
        # Delete all used pods
        delete_result = collection.delete_many({"status": {"$ne": "available"}})
        
        if delete_result.deleted_count == 0:
            raise HTTPException(
                status_code=500,
                detail="Failed to delete used pods"
            )
        
        return {
            "status": "success",
            "deleted_count": delete_result.deleted_count,
            "message": f"Successfully deleted {delete_result.deleted_count} used pod(s)"
        }
        
    except HTTPException:
        raise
    except PyMongoError as e:
        print(f"Database error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Database operation failed"
        )
    except Exception as e:
        print(f"Unexpected error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to delete used pods"
        )
        


@router.get("/stats/overall")
async def get_overall_stats(
    date_from: Optional[str] = Query(None),
    date_to: Optional[str] = Query(None),
    status: Optional[str] = Query(None)
):
    """Get overall dashboard statistics with optional date filtering"""
    try:
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
        
        # Build match stage for filtering
        match_stage = {}
        
        # Safe datetime parsing
        if date_from or date_to:
            match_stage["created_at"] = {}
            if date_from:
                parsed_from = safe_parse_datetime(date_from)
                if parsed_from:
                    match_stage["created_at"]["$gte"] = parsed_from
            if date_to:
                parsed_to = safe_parse_datetime(date_to)
                if parsed_to:
                    match_stage["created_at"]["$lte"] = parsed_to
        
        if status and status.lower() != 'all':
            match_stage["status"] = status
        
        # Main pipeline for overall stats
        pipeline = []
        
        # Add match stage if we have filters
        if match_stage:
            pipeline.append({"$match": match_stage})
        
        pipeline.extend([
            {
                "$group": {
                    "_id": None,
                    "total_sessions": {"$sum": 1},
                    "active_sessions": {
                        "$sum": {"$cond": [{"$eq": ["$status", "active"]}, 1, 0]}
                    },
                    "completed_sessions": {
                        "$sum": {"$cond": [{"$eq": ["$status", "completed"]}, 1, 0]}
                    },
                    "failed_sessions": {
                        "$sum": {"$cond": [{"$eq": ["$status", "failed"]}, 1, 0]}
                    },
                    "total_cost": {"$sum": "$total_cost"},
                    "unique_tenants": {"$addToSet": "$tenant_id"},
                    "unique_users": {"$addToSet": "$user_id"}
                }
            }
        ])
        
        result = list(mongo_db.db["session_tracking"].aggregate(pipeline))
        
        if result:
            stats = result[0]
            return {
                "success": True,
                "stats": {
                    "total_sessions": stats["total_sessions"],
                    "active_sessions": stats["active_sessions"],
                    "completed_sessions": stats["completed_sessions"],
                    "failed_sessions": stats["failed_sessions"],
                    "total_cost": stats["total_cost"],
                    "total_tenants": len(stats["unique_tenants"]),
                    "total_users": len(stats["unique_users"])
                },
                "filters_applied": {
                    "date_from": date_from,
                    "date_to": date_to,
                    "status": status
                }
            }
        else:
            return {
                "success": True,
                "stats": {
                    "total_sessions": 0,
                    "active_sessions": 0,
                    "completed_sessions": 0,
                    "failed_sessions": 0,
                    "total_cost": 0.0,
                    "total_tenants": 0,
                    "total_users": 0
                },
                "filters_applied": {
                    "date_from": date_from,
                    "date_to": date_to,
                    "status": status
                }
            }
        
    except Exception as e:
        print(f"Error in get_overall_stats: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/tenants")
async def get_tenants(
    date_from: Optional[str] = Query(None),
    date_to: Optional[str] = Query(None),
    status: Optional[str] = Query(None)
):
    """Get all tenants with session statistics with optional date filtering"""
    try:
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
        
        # Build match stage for filtering
        match_stage = {}
        
        # Safe datetime parsing
        if date_from or date_to:
            match_stage["created_at"] = {}
            if date_from:
                parsed_from = safe_parse_datetime(date_from)
                if parsed_from:
                    match_stage["created_at"]["$gte"] = parsed_from
            if date_to:
                parsed_to = safe_parse_datetime(date_to)
                if parsed_to:
                    match_stage["created_at"]["$lte"] = parsed_to
        
        if status and status.lower() != 'all':
            match_stage["status"] = status
        
        pipeline = []
        
        # Add match stage if we have filters
        if match_stage:
            pipeline.append({"$match": match_stage})
        
        pipeline.extend([
            {
                "$group": {
                    "_id": "$tenant_id",
                    "total_sessions": {"$sum": 1},
                    "active_sessions": {
                        "$sum": {"$cond": [{"$eq": ["$status", "active"]}, 1, 0]}
                    },
                    "completed_sessions": {
                        "$sum": {"$cond": [{"$eq": ["$status", "completed"]}, 1, 0]}
                    },
                    "failed_sessions": {
                        "$sum": {"$cond": [{"$eq": ["$status", "failed"]}, 1, 0]}
                    },
                    "total_cost": {"$sum": "$total_cost"},
                    "last_activity": {"$max": "$last_updated"}
                }
            },
            {"$sort": {"last_activity": -1}}
        ])
        
        tenants = list(mongo_db.db["session_tracking"].aggregate(pipeline))
        
        return {
            "success": True,
            "tenants": [
                {
                    "id": tenant["_id"],
                    "name": get_organization_name_by_tenant_id(tenant["_id"]),
                    "total_sessions": tenant["total_sessions"],
                    "active_sessions": tenant["active_sessions"],
                    "completed_sessions": tenant["completed_sessions"],
                    "failed_sessions": tenant["failed_sessions"],
                    "total_cost": tenant["total_cost"],
                    "last_activity": tenant["last_activity"].isoformat() if tenant["last_activity"] else None
                }
                for tenant in tenants
            ],
            "filters_applied": {
                "date_from": date_from,
                "date_to": date_to,
                "status": status
            }
        }
        
    except Exception as e:
        print(f"Error in get_tenants: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/stats/chart-data")
async def get_chart_data(
    date_from: Optional[str] = Query(None),
    date_to: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    granularity: Optional[str] = Query("day", regex="^(day|hour)$")
):
    """Get chart data for sessions over time"""
    try:
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
        
        # Build match stage
        match_stage = {}
        
        # Safe datetime parsing
        if date_from or date_to:
            match_stage["created_at"] = {}
            if date_from:
                parsed_from = safe_parse_datetime(date_from)
                if parsed_from:
                    match_stage["created_at"]["$gte"] = parsed_from
            if date_to:
                parsed_to = safe_parse_datetime(date_to)
                if parsed_to:
                    match_stage["created_at"]["$lte"] = parsed_to
        
        if status and status.lower() != 'all':
            match_stage["status"] = status
        
        # Set default date range if not provided (last 7 days)
        if not date_from and not date_to:
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=7)
            match_stage["created_at"] = {
                "$gte": start_date,
                "$lte": end_date
            }
        
        # Choose date format based on granularity
        if granularity == "hour":
            date_format = "%Y-%m-%d %H:00:00"
        else:
            date_format = "%Y-%m-%d"
        
        pipeline = []
        
        if match_stage:
            pipeline.append({"$match": match_stage})
        
        pipeline.extend([
            {
                "$group": {
                    "_id": {
                        "date": {"$dateToString": {"format": date_format, "date": "$created_at"}},
                        "tenant_id": "$tenant_id",
                        "user_id": "$user_id"
                    },
                    "sessions": {"$sum": 1},
                    "total_cost": {"$sum": "$total_cost"},
                    "active_sessions": {"$sum": {"$cond": [{"$eq": ["$status", "active"]}, 1, 0]}},
                    "completed_sessions": {"$sum": {"$cond": [{"$eq": ["$status", "completed"]}, 1, 0]}},
                    "failed_sessions": {"$sum": {"$cond": [{"$eq": ["$status", "failed"]}, 1, 0]}}
                }
            },
            {
                "$group": {
                    "_id": "$_id.date",
                    "unique_users": {"$addToSet": "$_id.user_id"},
                    "unique_tenants": {"$addToSet": "$_id.tenant_id"},
                    "total_sessions": {"$sum": "$sessions"},
                    "total_cost": {"$sum": "$total_cost"},
                    "active_sessions": {"$sum": "$active_sessions"},
                    "completed_sessions": {"$sum": "$completed_sessions"},
                    "failed_sessions": {"$sum": "$failed_sessions"}
                }
            },
            {
                "$project": {
                    "date": "$_id",
                    "unique_users_count": {"$size": "$unique_users"},
                    "unique_tenants_count": {"$size": "$unique_tenants"},
                    "total_sessions": 1,
                    "total_cost": 1,
                    "active_sessions": 1,
                    "completed_sessions": 1,
                    "failed_sessions": 1,
                    "_id": 0
                }
            },
            {"$sort": {"date": 1}}
        ])
        
        result = list(mongo_db.db["session_tracking"].aggregate(pipeline))
        
        return {
            "success": True,
            "chart_data": result,
            "granularity": granularity,
            "filters_applied": {
                "date_from": date_from,
                "date_to": date_to,
                "status": status
            }
        }
        
    except Exception as e:
        print(f"Error in get_chart_data: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


referral_service = ReferralService()

# SUPER ADMIN ENDPOINTS
@router.post("/admin/referral/create-code/{user_id}", response_model=ReferralCodeResponse)
async def create_referral_code_for_user(
    user_id: str,
    force_regenerate: bool = Query(False, description="Force regenerate if code already exists")
):
    """Create or regenerate referral code for a specific user (Super Admin only)"""
    try:
        result = await referral_service.create_referral_code(user_id, force_regenerate)
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        print(f"Error creating referral code: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/referral/validate/{referral_code}", response_model=ValidateReferralResponse)
async def validate_referral_code(referral_code: str):
    """Validate a referral code (Public endpoint for registration form)"""
    try:
        result = await referral_service.validate_referral_code(referral_code)
        return result
    except Exception as e:
        print(f"Error validating referral code: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/admin/referral/stats/{user_id}")
async def get_user_referral_stats(user_id: str):
    """Get referral statistics for a user (Super Admin only)"""
    try:
        stats = await referral_service.get_referral_stats(user_id)
        return {
            "success": True,
            "data": stats
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        print(f"Error getting referral stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/admin/referral/organization-stats/{organization_id}")
async def get_organization_referral_stats(organization_id: str):
    """Get referral statistics for an entire organization (Super Admin only)"""
    try:
        stats = await referral_service.get_organization_referral_stats(organization_id)
        return {
            "success": True,
            "data": stats
        }
    except Exception as e:
        print(f"Error getting organization referral stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/admin/referral/all-referrers")
async def get_all_referrers():
    """Get all users with referral codes across all organizations (Super Admin only)"""
    try:
        # Get all users with referral codes
        referrers_cursor = referral_service.users_collection.db.users.find(
            {"referral_code": {"$exists": True, "$ne": None}},
            {
                "_id": 1,
                "name": 1,
                "email": 1,
                "organization_id": 1,
                "referral_code": 1,
                "referral_stats": 1,
                "status": 1,
                "created_at": 1
            }
        ).sort("organization_id", 1)
        
        referrers = list(referrers_cursor)
        
        # Group by organization
        organizations = {}
        for referrer in referrers:
            org_id = referrer["organization_id"]
            if org_id not in organizations:
                organizations[org_id] = {
                    "organization_id": org_id,
                    "referrers": [],
                    "total_referrals": 0
                }
            
            organizations[org_id]["referrers"].append(referrer)
            organizations[org_id]["total_referrals"] += referrer.get("referral_stats", {}).get("total_referrals", 0)
        
        return {
            "success": True,
            "data": {
                "total_referrers": len(referrers),
                "organizations": list(organizations.values())
            }
        }
        
    except Exception as e:
        print(f"Error getting all referrers: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/referral/user-codes/{user_id}")
async def get_user_referral_code(user_id: str):
    """Get referral code for a specific user (for sharing purposes)"""
    try:
        user = await referral_service.users_collection.get_one(
            filter={"_id": user_id},
            db=referral_service.users_collection.db.users
        )
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        referral_code = user.get("referral_code")
        if not referral_code:
            return {
                "success": False,
                "message": "No referral code found for this user"
            }
        
        stats = ReferralStats(**user.get("referral_stats", {}))
        
        return {
            "success": True,
            "data": {
                "referral_code": referral_code,
                "user_name": user["name"],
                "organization_id": user["organization_id"],
                "stats": stats.model_dump(),
                "share_url": f"{getattr(settings, 'FRONTEND_URL', 'http://localhost:3000')}/signup?ref={referral_code}"
            }
        }
        
    except Exception as e:
        print(f"Error getting referral code: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/referral/update-stats/{user_id}")
async def update_referral_stats_on_verification(
    user_id: str,
    action: str = Query(..., description="Action: verified, activated, deactivated")
):
    """Update referral stats when referred user status changes"""
    try:
        user = await referral_service.users_collection.get_one(
            filter={"_id": user_id},
            db=referral_service.users_collection.db.users
        )
        
        if not user or not user.get("referred_by"):
            return {
                "success": True,
                "message": "No referral to update"
            }
        
        referrer_id = user["referred_by"]["user_id"]
        
        # Update referrer stats based on action
        if action == "verified":
            await referral_service.update_referral_stats(referrer_id, "verified_referrals")
        elif action == "activated":
            await referral_service.update_referral_stats(referrer_id, "active_referrals")
        elif action == "deactivated":
            # Decrement active referrals
            await referral_service.users_collection.update_one_data(
                filter={"_id": referrer_id},
                update={
                    "$inc": {"referral_stats.active_referrals": -1},
                    "$set": {"updated_at": datetime.now()}
                }
            )
        
        return {
            "success": True,
            "message": f"Referral stats updated for action: {action}"
        }
        
    except Exception as e:
        print(f"Error updating referral stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
    
@router.get("/admin/referral/detailed-stats/{user_id}")
async def get_detailed_referral_stats(user_id: str):
    """Get detailed referral statistics with individual usage records"""
    try:
        referral_service = ReferralService()
        stats = await referral_service.get_detailed_referral_stats(user_id)
        return {
            "success": True,
            "data": stats
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        print(f"Error getting detailed referral stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/admin/referral/usage-history/{user_id}")
async def get_referral_usage_history(
    user_id: str,
    limit: int = Query(50, ge=1, le=1000),
    skip: int = Query(0, ge=0)
):
    """Get paginated referral usage history for a user"""
    try:
        referral_service = ReferralService()
        
        referrer = referral_service.users_collection.db[referral_service.users_collection.collection].find_one(
            {"_id": user_id},
            {"referral_stats.referral_history": 1, "name": 1, "email": 1}
        )
        
        if not referrer:
            raise HTTPException(status_code=404, detail="User not found")
        
        referral_history = referrer.get("referral_stats", {}).get("referral_history", [])
        
        # Sort by referred_at descending and paginate
        sorted_history = sorted(
            referral_history,
            key=lambda x: x.get("referred_at", datetime.min),
            reverse=True
        )
        
        paginated_history = sorted_history[skip:skip + limit]
        
        return {
            "success": True,
            "data": {
                "referrer_name": referrer["name"],
                "referrer_email": referrer["email"],
                "total_referrals": len(referral_history),
                "returned_count": len(paginated_history),
                "skip": skip,
                "limit": limit,
                "referral_history": paginated_history
            }
        }
        
    except Exception as e:
        print(f"Error getting referral usage history: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")