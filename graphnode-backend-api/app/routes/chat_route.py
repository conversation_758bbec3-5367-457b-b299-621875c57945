import json
from bson import ObjectId
from fastapi.responses import StreamingResponse , JSONResponse
from fastapi import Head<PERSON>, APIRouter, Depends, Body , Query, Request
from app.connection.establish_db_connection import  get_node_db
from app.conversation.conversation_finder import ConversationFinder
from app.routes import node_route
from typing import  Optional
from dataclasses import dataclass
from app.utils.auth_utils import get_current_user
from app.utils.conversation_utils import ConversationUtils
from pydantic import BaseModel
from app.connection.establish_db_connection import get_mongo_db
from app.conversation.conversation_session_manager import get_or_create_chat_session
from app.models.chat_model import ChatContext
_SHOW_NAME = "conversation"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)

conversation = ConversationUtils()

class NodeData(BaseModel):
    node_data: dict
    





    
 
@router.get("/chat")
async def chat(project_id:int, message: str, discussion_id: int = None, current_user = Depends(get_current_user)):

    user_id = current_user.get('sub')
    session= await get_or_create_chat_session(user_id, project_id, discussion_id)
    if not discussion_id:
        discussion_id = session.get('discussion_id')

    context = ChatContext(
        project_id=project_id,
        user_id=current_user.get('sub'),
        session= session,
        message=message,
        discussion_id=discussion_id,
        agent_name='conversation'
    )

    find_conversation = ConversationFinder(context)
    
    conversation = find_conversation._iterate_chat()
    
    return StreamingResponse(conversation, media_type="text/event-stream")

@router.post('/rename')
async def chat(discussion_id: int, new_title: str):
    db = get_node_db()
    node_type = 'Conversation'
    properties = {
        'Title': new_title
    }
    node = await db.update_node_by_id(discussion_id, properties, node_type)
    if node:
        return {'message': 'updated successfully'}
    else:
        return {'message': 'discussion_id not found'}
    
@router.delete('/chat')
async def chat(discussion_id: int, db = Depends(get_node_db)):
    deleted_node_ids = await db.delete_node_and_associates(discussion_id, ['Conversation', 'HAS_CHILD'])
    if deleted_node_ids:
        return { 'message': "Deleted successfully"}
    else:
        return { 'message': 'Disucssion not found in database.'}
    
@router.delete('/delete_query_discussion')
async def delete_query_discussion(discussion_id: int, db = Depends(get_node_db)):
    deleted_node_ids = await db.delete_code_query_node(discussion_id)
    if deleted_node_ids:
        return { 'message': "Deleted successfully"}
    else:
        return { 'message': 'Discussion not found in database.'}
    

@router.get("/get_chat_history")
async def chat(current_user = Depends(get_current_user), limit: int = Query(default=10, ge=1), project_id: int = None):
    # get the user history based on user_id
    if limit <= 20:
        return await conversation._get_chat_history(current_user.get('sub'), limit, project_id)
    return {"message": "Limit should be less than 20"}


@router.get("/get_query_history")
async def get_query_history(
    current_user=Depends(get_current_user), 
    limit: int = Query(default=10, ge=1), 
    skip: int = Query(default=0, ge=0),
    project_id: int = None
):
    if limit <= 50:
        return await conversation.get_query_history(current_user.get('sub'), limit,skip, project_id)
    return {"message": "Limit should be less than 50"}

@router.get("/{discussion_id}")
async def load_discussion(discussion_id: int, debug: bool = Query(default=False, description="Enable debug mode")):
    
    result = await conversation._load_discussion(discussion_id)
    if result:
        
        if debug:
            return result
        
        # Loop through the remaining array and modify the keys
        final_result = []
        
        for idx, item in enumerate(result):
            
            if item.get("timestamp"):
            
                if item.get("role") == "assistant" or item.get("role") == "system":
                    sender = "AI"
                
                if item.get("role") == "user":
                    sender = "user"
                    
                if item.get('role') != 'function' and item.get('role') != 'tool':
                    modified_item = {
                        'sender': sender,
                        'text': item.get('content'),
                        'id': idx + 1,  # Adding id key
                        'timestamp': item.get('timestamp', "unknown")
                    }
                    if(item.get('file_attachments')):
                        modified_item['file_attachments'] = item.get('file_attachments')
                        
                final_result.append(modified_item)

        return final_result
    
    return {"message": "discussion not found"}

@router.post("/confirmation/{task_id}")
async def confirmation(task_id: str, req: Request, node_data: Optional[NodeData] = Body(None) , current_user = Depends(get_current_user)):
     
    global mongo_handler
    mongo_handler = get_mongo_db(collection_name='confirmation')
    discussion_id = req.query_params.get('discussion_id')
    result = await mongo_handler.get_by_id(ObjectId(task_id), mongo_handler.db)
    print("result: ", result)
    if result:
        object_id = result['_id']
        function_name = result['function_call']['function']
        params = result['function_call']['parameters']
        
        operation = 'created' if function_name == 'create_node' else 'updated'

        if function_name in ['create_node', 'update_node']:
            print(node_data)
            if node_data:
                name = node_data.node_data['Name']
                description = node_data.node_data['Description']
                scope = node_data.node_data['Scope']

                if function_name == 'update_node':
                    node_id = params['node_id']
                    params = {
                        'node_id': node_id,
                        'request': {
                            'node_type': 'Project',
                            'name': name,
                            'description': description,
                            'scope': scope
                        }
                    }
                    
                if function_name == 'create_node':
                    params = {
                        'request': {
                            'node_type': 'Project',
                            'name': name,
                            'description': description,
                        }
                    }
                
            params['current_user'] = current_user

        function_call = getattr(node_route, function_name, None)

        await function_call(**params)
        
        if discussion_id:
            node_type = result['function_call']['type']
            messages = await conversation._load_discussion(int(discussion_id))
            messages.append({"role": "assistant", "content": f"{node_type} {operation} successfully by the system"})
            await get_node_db().update_node_by_id(int(discussion_id), {'Discussion': json.dumps(messages)})
            
        await mongo_handler.delete(object_id, mongo_handler.db)
        return JSONResponse(
            {
                'Message': f"{node_type} {operation} successfully", 
                'TaskId': task_id, 
                'status' : req.query_params['confirm'],
            }, status_code=200)

    else:
        return JSONResponse(
                {
                    'Message' : 'Task not found'
                }, status_code=404)

@router.get("/code_query/{discussion_id}")
async def load_code_query_discussion(
    discussion_id: int, 
    debug: bool = Query(default=False, description="Enable debug mode")
):
    # Get the code query node
    db = get_node_db()
    query = """
    MATCH (d:code_query)
    WHERE ID(d) = $discussion_id
    RETURN d
    """
    result = await db.async_run(query, discussion_id=discussion_id)
    data = result.data()
    
    if not data:
        return {"message": "discussion not found"}
        
    node = data[0]['d']
    discussion_data = node.get('Discussion')
    
    if not discussion_data:
        return {"message": "no discussion data found"}
        
    # Parse the discussion JSON
    messages = json.loads(discussion_data)
    
    if debug:
        return messages
    
    # Format messages similar to regular discussions
    final_result = []
    for idx, item in enumerate(messages):
        if item.get("role") == "assistant" or item.get("role") == "system":
            sender = "AI"
        elif item.get("role") == "user":
            sender = "user"
            
        if item.get('role') != 'function':
            modified_item = {
                'sender': sender,
                'text': item.get('content'),
                'id': idx + 1
            }
            final_result.append(modified_item)

    return final_result

