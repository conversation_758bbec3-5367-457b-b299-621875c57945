from fastapi import APIRout<PERSON>, HTT<PERSON><PERSON>x<PERSON>, Depends, Body
from fastapi.responses import JSONResponse
from typing import Dict, Any, Optional,List
import os
import logging
import subprocess 
import json
import asyncio
from urllib.parse import urlparse
from datetime import datetime
from github import Github, GithubException
import shutil
import boto3
from app.connection.establish_db_connection import get_node_db, NodeDB

from app.routes.deployment_helper.sample_tf import get_workflows,get_outputs_tf,get_main_tf,get_providers_tf,get_variables_tf
from app.routes.repository_route import get_repository,list_branches
from app.routes.deployment_helper.node_helper import get_deployment_node
from app.routes.deployment_helper.directory_finder import find_application_directory
from app.routes.deployment_helper.aws_handler import read_init_tool_content,modify_codecommit_repo_name,push_to_codecommit,clone_codecommit_repo
from app.routes.deployment_helper.get_tf_files import get_backend_terraform_files
from app.routes.deployment_helper.s3_handler import save_terraform_files_to_s3
from app.routes.deployment_helper.status_handler import get_amplify_deployment_status,get_amplify_details,get_ecs_service_status,get_elb_details,get_backend_terraform_files
from app.routes.deployment_helper.terraform_handler import deploy_infrastructure_handler
import time
import random
import string
from fastapi.responses import StreamingResponse
from typing import AsyncGenerator, Union, Callable
from enum import Enum

# Configure logging
logger = logging.getLogger(__name__)
handler = logging.StreamHandler()
handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(handler)
logger.setLevel(logging.INFO)

_SHOW_NAME = "deployment"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)

def get_first_two_words(text):
    words = text.split('-')
    return '-'.join(words[:2])

async def determine_container_type(component):
        """
        Analyze component to determine its type based on properties and description.
        Returns: str - Component type ('frontend', 'backend', 'database', or 'unknown')
        """
        # Extract properties and convert to lowercase for case-insensitive matching
        title = component['properties'].get('Title', '').lower()
        description = component['properties'].get('Description', '').lower()
        technology = component['properties'].get('Technology', '').lower()
        
        # Combine all fields for comprehensive searching
        combined_text = f"{title} {description} {technology}"
        
        # Enhanced keyword sets with more comprehensive terms
        database_keywords = {
            'database', 'db', 'storage', 'mongo', 'postgresql', 'mysql', 'redis',
            'oracle', 'sqlserver', 'cassandra', 'elasticsearch', 'dynamodb',
            'mariadb', 'sql', 'nosql', 'data store'
        }
        
        frontend_keywords = {
            'ui', 'frontend', 'react', 'vue', 'angular', 'web', 'client',
            'browser', 'spa', 'tsx', 'jsx','javascript','html','css'
        }
        
        backend_keywords = {
            'api', 'backend', 'server', 'service', 'nodejs', 'java', 'python',
            'microservice', 'rest', 'graphql', 'grpc', 'flask', 'django', 'express','Node.js','Chart.js',
        }

        # Count matches for each type to handle components that might match multiple types
        matches = {
            'database': sum(1 for keyword in database_keywords if keyword in combined_text),
            'frontend': sum(1 for keyword in frontend_keywords if keyword in combined_text),
            'backend': sum(1 for keyword in backend_keywords if keyword in combined_text)
        }
        
        # Debug logging to help track matches
        print(f"Component: {title}")
        print(f"Matches found: {matches}")
        print("--------------------}")
        
        # If we have matches, return the type with the most matches
        if any(matches.values()):
            print(max(matches.items(), key=lambda x: x[1])[0])
            return max(matches.items(), key=lambda x: x[1])[0]
        
        return "unknown"

def generate_unique_domain(base_name: str ) -> str:
    """
    Generate a valid unique domain name for AWS Amplify.
    
    Args:
        base_name (str): Base name for the domain
        
    Returns:
        str: Valid unique domain name
    """
    # Clean the base name - remove special characters and convert to lowercase
    clean_base = ''.join(c if c.isalnum() or c == '-' else '-' for c in base_name.lower())
    clean_base = clean_base.strip('-')
    
    # Generate timestamp
    timestamp = int(time.time())
    
    # Generate random string
    random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=4))
    
    # Combine with default AWS Amplify domain
    # Format: base-name-timestamp-random.amplifyapp.com
    domain = f"{clean_base}-{timestamp}-{random_suffix}.amplifyapp.com"
    
    return domain


# Status Enums for Consistency
class StepStatus(Enum):
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    ERROR = "error"

def get_step_response(status: str, step: str, message: str) -> Dict[str, Any]:
    return {
        "status": status,
        "step": step,
        "message": message
    }
@router.post("/{project_id}/{container_id}/{branch}/deploy_infrastructure")
async def deploy_infrastructure(
    project_id: int,
    container_id: int,
    branch: str,
    db: NodeDB = Depends(get_node_db)
) -> StreamingResponse:
    async def generate_progress():
        temp_dir = "./deployments"
        
        try:
            # Get container type
            container = await db.get_node_by_id(container_id)
            container_type = await determine_container_type(container)
            
            # Get repo info
            repo_info = await get_repository(project_id, container_id, db)
            repo_url = repo_info["repository"]["cloneUrlHttp"]
            repo_name = repo_url.split('/')[-1]
            
            init_step = {
                "status": "in_progress",
                "step": "initialization",
                "message": f"Starting {container_type} deployment process"
            }
            yield f"data: {json.dumps(init_step)}\n\n"

            # Cleanup and create temp directory
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
            os.makedirs(temp_dir)
            logger.debug(f"Created temporary directory: {temp_dir}")

            # Handle frontend (Amplify) deployment
            if container_type == "frontend":
                # Read .init-run-tool directly from CodeCommit
                try:
                    init_tool_content = await read_init_tool_content(
                        repo_name=repo_name,
                        branch=branch
                    )
                    
                    
                    if not init_tool_content:
                        raise Exception("No working_directory specified in .init-run-tool")
                    
                    msg = {
                        "status": "in_progress",
                        "step": "configuration",
                        "message": f"Found working directory: {init_tool_content}"
                    }
                    yield f"data: {json.dumps(msg)}\n\n"
                    
                except Exception as e:
                    raise Exception(f"Error reading .init-run-tool: {str(e)}")

                 # Get or create branch deployment node
                deployment_config = {
                    "repo_url": repo_url,
                    "app_name": f"{repo_name}-{branch}",
                    "branch": branch,
                    "working_directory": init_tool_content['working_directory'],
                    "deployment_type": "frontend"  # Assuming frontend for Amplify
                }
                
                  # Generate Amplify-specific terraform files
                terraform_files = {
                                "main.tf": """
resource "aws_amplify_app" "{repo_name}" {
name       = "${var.app_name}-${var.branch_name}"
repository = "https://git-codecommit.us-east-1.amazonaws.com/v1/repos/${var.repository_name}"
enable_branch_auto_build = true
iam_service_role_arn = aws_iam_role.amplify_role.arn

build_spec = <<-EOT
    version: 1
    frontend:
    phases:
        preBuild:
        commands:
            - cd {init_tool_content['working_directory']}
            - npm ci
        build:
        commands:
            - npm run build
    artifacts:
        baseDirectory: {init_tool_content['working_directory']}/build
        files:
        - '**/*'
    cache:
        paths:
        - {init_tool_content['working_directory']}/node_modules/**/*
EOT

custom_rule {
    source = "/<*>"
    status = "404"
    target = "/index.html"
                }
                }

resource "aws_amplify_branch" "main" {
app_id      = aws_amplify_app.app.id
branch_name = var.branch_name
stage       = "PRODUCTION"
enable_auto_build = true
}


resource "aws_iam_role" "amplify_role" {
  provider = aws.east1
  name     = "amplify-role-${var.app_name}-${var.branch_name}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "amplify.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy" "amplify_codecommit_policy" {
  name = "amplify-codecommit-policy-${var.app_name}"
  role = aws_iam_role.amplify_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "codecommit:BatchGet*",
          "codecommit:BatchDescribe*",
          "codecommit:List*",
          "codecommit:Get*",
          "codecommit:Describe*",
          "codecommit:GitPull",
          "codecommit:CancelUploadArchive",
          "codecommit:GetBranch",
          "codecommit:GetCommit",
          "codecommit:GetRepository",
          "codecommit:GetUploadArchiveStatus",
          "codecommit:UploadArchive"
        ]
        Resource = [
          "arn:aws:codecommit:us-east-1:${var.account_id}:${var.repository_name}",
          aws_amplify_app.app.arn
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "codecommit:ListRepositories",
          "codecommit:ListBranches",
          "codecommit:GetFile"
        ]
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy" "amplify_cross_account" {
  name = "amplify-cross-account-policy-${var.app_name}"
  role = aws_iam_role.amplify_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "sts:AssumeRole"
        ]
        Resource = "*"
      }
    ]
  })
}

variable "app_name" {
  description = "Base name of the application"
  type        = string
}

variable "branch_name" {
  description = "Name of the branch to deploy"
  type        = string
}

variable "account_id" {
  description = "AWS Account ID"
  type        = string
}

variable "repository_name" {
  description = "Name of the CodeCommit repository"
  type        = string
}


provider "aws" {
region = var.aws_region
}

output "amplify_app_id" {
value = "aws_amplify_app.${var.repository_name}.id"
                }

output "amplify_app_url" {
value = aws_amplify_app.${var.repository_name}.default_domain
}


                
                """


                }

                

                # # Get or create deployment node for this branch
                # branch_deployment = await get_or_create_branch_deployment_node(
                #     project_id=project_id,
                #     container_id=container_id,
                #     branch=branch,
                #     deployment_config=deployment_config,
                #     terraform_files=terraform_files,
                #     db=db
                # )

            # Handle backend (ECS) deployment
            else:
                # Clone repository for backend setup
                temp_clone_dir = f"{temp_dir}_full"
                if os.path.exists(temp_clone_dir):
                    shutil.rmtree(temp_clone_dir)
                os.makedirs(temp_clone_dir)

                clone_step = {
                    "status": "in_progress",
                    "step": "clone",
                    "message": "Cloning repository for backend setup"
                }
                yield f"data: {json.dumps(clone_step)}\n\n"

                clone_success = await clone_codecommit_repo(
                    repo_url=repo_url,
                    local_path=temp_clone_dir,
                    branch=branch
                )

                if not clone_success:
                    raise Exception("Failed to clone repository")

                try:
                    # Get working directory from cloned repo
                    working_directory = await get_working_directory(temp_clone_dir)
                    
                    # Setup backend deployment directory
                    if os.path.exists(temp_dir):
                        shutil.rmtree(temp_dir)
                    
                    # Copy contents for backend deployment
                    shutil.copytree(working_directory, temp_dir, dirs_exist_ok=True)

                    setup_complete = {
                        "status": "completed",
                        "step": "setup",
                        "message": "Backend directory setup completed"
                    }
                    yield f"data: {json.dumps(setup_complete)}\n\n"

                finally:
                    if os.path.exists(temp_clone_dir):
                        shutil.rmtree(temp_clone_dir)

                # Generate backend terraform files
                terraform_files = get_backend_terraform_files(
                    technology="node",
                    repo_name=repo_name.replace("-", "_"),
                    branch=branch
                )

            # # Common deployment steps for both types
            # deployment_config = {
            #     "repo_url": repo_url,
            #     "app_name": f"{repo_name}-{branch}",
            #     "branch": branch,
            #     "working_directory": init_tool_content['working_directory'],
            #     "deployment_type": container_type
            # }

            # # Get or create branch deployment node
            # branch_deployment = await get_or_create_branch_deployment_node(
            #     project_id=project_id,
            #     container_id=container_id,
            #     branch=branch,
            #     deployment_config=deployment_config,
            #     terraform_files=terraform_files,
            #     db=db
            # )

            # Setup infrastructure directory
            infrastructure_path = os.path.join(temp_dir, 'infrastructure')
            os.makedirs(infrastructure_path, exist_ok=True)
           # File Creation
            logger.info("Creating infrastructure files")
            saved_files = {"terraform": []}
            for filename, content in terraform_files.items():
                if content and filename != 'workflow_file':
                    file_path = os.path.join(infrastructure_path, filename)
                    print(content)
                    with open(file_path, 'w') as f:
                        f.write(content)
                    saved_files["terraform"].append(filename)
                    logger.debug(f"Created file: {filename}")
            # Deploy infrastructure
            msg = {
                "status": "in_progress",
                "step": "terraform",
                "message": f"Deploying {container_type} infrastructure"
            }
            yield f"data: {json.dumps(msg)}\n\n"

            terraform_outputs = await deploy_infrastructure_handler(
                project_id=project_id,
                container_id=container_id,
                infrastructure_path=infrastructure_path,
                terraform_files=terraform_files,
                reuse_workspace=True
            )

            # Update deployment node
            updated_properties = {
                **branch_deployment['properties'],
                'terraform_outputs': json.dumps(terraform_outputs),
                'deployment_status': 'infrastructure_deployed',
                'last_deployment': datetime.now().isoformat()
            }
            await db.update_node_by_id(branch_deployment['id'], updated_properties)

            # Final completion
            completion_data = {
                "status": "completed",
                "step": "deployment",
                "message": f"{container_type} infrastructure deployed successfully",
                "data": {
                    "project_id": project_id,
                    "container_id": container_id,
                    "branch": branch,
                    "terraform_outputs": terraform_outputs,
                    "working_directory": working_directory,
                    "deployment_type": container_type
                }
            }
            yield f"data: {json.dumps(completion_data)}\n\n"

        except Exception as e:
            logger.error(f"Deployment failed: {str(e)}")
            error_data = {
                "status": "error",
                "step": "error",
                "message": f"Error in deployment: {str(e)}"
            }
            yield f"data: {json.dumps(error_data)}\n\n"
            
        finally:
            if os.path.exists(temp_dir):
                print(temp_dir)
                # shutil.rmtree(temp_dir)

    return StreamingResponse(
        generate_progress(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )


@router.get("/{project_id}/{container_id}/get_deployment_form")
async def get_deployment_form(
    project_id: int,
    container_id: int,
    branch: str = None,
    db = Depends(get_node_db)
) -> JSONResponse:
    """
    Get deployment form configuration and existing values based on branch.
    
    Args:
        project_id: Project ID
        container_id: Container ID
        branch: Git branch name (optional)
        
    Returns:
        Form configuration and existing values
    """
    try:
        # Get all deployment nodes for container
        deployment_nodes = await db.get_child_nodes(container_id, "Deployment")
        
        # Get repository information
        project_repositories = await get_repository(project_id, container_id, db)
        if "error" in project_repositories:
            raise HTTPException(status_code=404, detail="Repository not found")
            
        repo_info = project_repositories["repository"]
        repo_url = repo_info.get("cloneUrlHttp", "")
        repo_name = repo_info.get("repositoryName", "")
        aws_repo_url = modify_codecommit_repo_name(repo_url)
        
        # Get branch information
        branches_info = await list_branches(project_id, container_id, db)
        branch_options = []
        for branch_info in branches_info.get("branches", []):
            branch_data = {
                "label": f"{branch_info['name']}", 
                "value": branch_info['name']
            }
            branch_options.append(branch_data)

        # If no branch specified, use the first available branch
        if not branch and branch_options:
            branch = branch_options[0]["value"]
            
        if not branch:
            raise HTTPException(status_code=400, detail="No branch available")

        # Look for existing deployment node for this branch
        branch_deployment = None
        for node in deployment_nodes:
            deployment_config = node['properties'].get('deployment_config', '{}')
            if isinstance(deployment_config, str):
                deployment_config = json.loads(deployment_config)
            if deployment_config.get('branch') == branch:
                branch_deployment = node
                break

        # Generate unique domain name
        repo_name_prefix = get_first_two_words(repo_name)
        domain_name = generate_unique_domain(repo_name_prefix)

        # Create default configuration
        default_config = {
            "repo_url": aws_repo_url,
            "app_name": f"{repo_name_prefix}-{branch}",
            "domain_name": domain_name,
            "branch": branch
        }

        # Use existing config if available, otherwise use default
        if branch_deployment:
            existing_config = branch_deployment['properties'].get('deployment_config', '{}')
            if isinstance(existing_config, str):
                try:
                    existing_config = json.loads(existing_config)
                except json.JSONDecodeError:
                    existing_config = {}
            config_to_use = {**default_config, **existing_config}
        else:
            config_to_use = default_config

        # Build form configuration
        form_config = {
            "title": f"Deployment Configuration - {branch}",
            "description": f"Configure your infrastructure deployment settings for branch: {branch}",
            "fields": [
                {
                    "name": "repo_url",
                    "label": "Repository URL",
                    "type": "text",
                    "placeholder": "https://github.com/username/repo.git",
                    "required": True,
                    "value": config_to_use.get("repo_url", aws_repo_url),
                    "validation": "url"
                },
                {
                    "name": "branch",
                    "label": "Branch",
                    "type": "select",
                    "required": True,
                    "options": branch_options,
                    "value": branch,
                    "onChange": {
                        "url": f"/api/deployment/{project_id}/{container_id}/get_deployment_form",
                        "method": "GET",
                        "queryParam": "branch"
                    }
                },
                {
                    "name": "app_name",
                    "label": "Application Name",
                    "type": "text",
                    "placeholder": "my-application",
                    "required": True,
                    "value": config_to_use.get("app_name", f"{repo_name_prefix}-{branch}")
                },
                {
                    "name": "domain_name",
                    "label": "Domain Name",
                    "type": "text",
                    "placeholder": "example.com",
                    "required": False,
                    "value": config_to_use.get("domain_name", domain_name)
                },
                {
                    "name": "aws_region",
                    "label": "AWS Region",
                    "type": "select",
                    "required": True,
                    "value": config_to_use.get("aws_region", "us-east-2"),
                    "options": [
                        {"label": "US West (Oregon)", "value": "us-west-2"},
                        {"label": "US East (N. Virginia)", "value": "us-east-1"},
                        {"label": "EU (Ireland)", "value": "eu-west-1"},
                        {"label": "Asia Pacific (Singapore)", "value": "ap-southeast-1"}
                    ]
                }
            ],
            "submit": {
                "label": "Deploy Infrastructure",
                "endpoint": f"/api/deployment/{project_id}/{container_id}/update_deployment_config",
                "method": "POST"
            }
        }

        return JSONResponse(
            status_code=200,
            content={
                "form_config": form_config,
                "deployment_node_id": branch_deployment['id'] if branch_deployment else None,
                "branch": branch,
                "is_new_deployment": branch_deployment is None
            }
        )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting deployment form configuration: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{project_id}/{container_id}/update_deployment_config")
async def update_deployment_config(
    project_id: int,
    container_id: int,
    form_data: dict = Body(...),
    db: NodeDB = Depends(get_node_db)
) -> JSONResponse:
    """
    Update or create branch-specific deployment configuration.
    """
    try:
        branch = form_data.get('branch')
        if not branch:
            raise HTTPException(status_code=400, detail="Branch is required")

        # Get all deployment nodes for container
        deployment_nodes = await db.get_child_nodes(container_id, "Deployment")
        
        # Look for existing deployment node for this branch
        branch_deployment = None
        for node in deployment_nodes:
            deployment_config = node['properties'].get('deployment_config', '{}')
            if isinstance(deployment_config, str):
                deployment_config = json.loads(deployment_config)
            if deployment_config.get('branch') == branch:
                branch_deployment = node
                break

        timestamp = datetime.now().isoformat()
        
        # Base properties for deployment node
        deployment_properties = {
            "Title": f"Deployment Configuration - {branch}",
            "Description": f"Infrastructure deployment configuration for branch: {branch}",
            "Type": "Deployment",
            "deployment_config": json.dumps(form_data),
            "updated_at": timestamp
        }

        if branch_deployment:
            # Update existing node
            updated_properties = {
                **branch_deployment['properties'],
                **deployment_properties
            }
            
            result = await db.update_node_by_id(
                branch_deployment['id'],
                updated_properties
            )
            
            message = "Deployment configuration updated successfully"
            node_id = branch_deployment['id']
            
        else:
            # Create new deployment node
            deployment_properties.update({
                "created_at": timestamp,
                "deployment_status": "pending",
                "deployment_history": json.dumps([{
                    "timestamp": timestamp,
                    "action": "created",
                    "config": form_data
                }])
            })
            
            result = await db.create_node(
                ["Deployment"],
                deployment_properties,
                container_id
            )
            
            message = "New deployment configuration created successfully"
            node_id = result.get('id')

        if not result:
            raise HTTPException(
                status_code=500,
                detail="Failed to update deployment configuration"
            )
            
        return JSONResponse(
            status_code=200,
            content={
                "message": message,
                "deployment_config": form_data,
                "node_id": node_id,
                "branch": branch
            }
        )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating deployment configuration: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))



# Modified deployment route to use CodeCommit and CodePipeline
@router.post("/{project_id}/{container_id}/{branch}/push_to_new_repo")
async def deploy_to_aws(
    project_id: int,
    container_id: int,
    branch: str,
    repo_url: str,
    db = Depends(get_node_db)
) -> StreamingResponse:
    async def generate_progress():
        working_dir = "./deployments_latest"
        # Create temp directory
        temp_clone_dir = f"{working_dir}_full"
        if os.path.exists(temp_clone_dir):
            shutil.rmtree(temp_clone_dir)
        os.makedirs(temp_clone_dir)
        try:
            # Initialize
            init_step = {
                "status": "in_progress",
                "step": "initialization",
                "message": "Starting AWS deployment process"
            }
            yield f"data: {json.dumps(init_step)}\n\n"
            
            # Get repository info
            repo_info = await get_repository(project_id, container_id, db)
            repo_url = repo_info["repository"]["cloneUrlHttp"]
            
            # Clone repository
            clone_step = {
                "status": "in_progress",
                "step": "clone",
                "message": "Cloning CodeCommit repository"
            }
            yield f"data: {json.dumps(clone_step)}\n\n"
            
            clone_success = await clone_codecommit_repo(
                repo_url=repo_url,
                local_path=temp_clone_dir,
                branch=branch
            )
            
            if not clone_success:
                raise Exception("Failed to clone repository")
            
            # Get working directory from .init-run-tool
            frontend_path = await find_application_directory(temp_clone_dir)
            

            if not os.path.exists(frontend_path):
                raise Exception("Frontend directory not found in repository")

            # if os.path.exists(working_dir):
            #     shutil.rmtree(working_dir)
            # os.makedirs(working_dir)

            shutil.copytree(frontend_path, working_dir, dirs_exist_ok=True)
            shutil.rmtree(temp_clone_dir)

            logger.info("Frontend directory setup completed")
            frontend_complete = {
                "status": "completed",
                "step": "frontend_setup",
                "message": "Frontend directory setup completed"
            }
            yield f"data: {json.dumps(frontend_complete)}\n\n"

            # Workflow Setup
            logger.info("Setting up Codecommit workflows")
            workflow_step = {
                "status": "in_progress",
                "step": "workflow_setup",
                "message": "Setting up Codecommit workflows"
            }
            yield f"data: {json.dumps(workflow_step)}\n\n"
        

            logger.info("GitHub workflows setup completed")
            workflow_complete = {
                "status": "completed",
                "step": "workflow_setup",
                "message": "GitHub workflows setup completed"
            }
            yield f"data: {json.dumps(workflow_complete)}\n\n"

            # Push changes
            push_step = {
                "status": "in_progress",
                "step": "push",
                "message": "Pushing to CodeCommit"
            }
            yield f"data: {json.dumps(push_step)}\n\n"
            
            await push_to_codecommit(
                repo_path=repo_url,
                branch=branch,
                source_directory=working_dir,
                force=True
            )

            push_step = {
                "status": "completed",
                "step": "push",
                "message": "Pushing to CodeCommit"
            }
            yield f"data: {json.dumps(push_step)}\n\n"
            
            
            # Final completion
            completion_data = {
                "status": "completed",
                "step": "completion",
                "message": "AWS deployment completed"}
            #     "data": {
            #         "pipeline_execution": pipeline_execution,
            #         "final_status": status
            #     # }
            # }
            yield f"data: {json.dumps(completion_data)}\n\n"

        except Exception as e:
            logger.error(f"AWS deployment failed: {str(e)}")
            error_data = {
                "status": "error",
                "step": "error",
                "message": f"Error in AWS deployment: {str(e)}"
            }
            yield f"data: {json.dumps(error_data)}\n\n"
            
        finally:
            # Cleanup
            if os.path.exists(working_dir):
                print(working_dir)
                # shutil.rmtree(working_dir)

    return StreamingResponse(
        generate_progress(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )

@router.post("/{project_id}/{container_id}/create_or_update_deployment_node")
async def create_or_update_deployment_node(
    project_id: int,
    container_id: int,
    db: NodeDB = Depends(get_node_db)
) -> JSONResponse:
    """Create or update a deployment node with Terraform configurations."""
    try:
        container = await db.get_node_by_id(container_id)
        container_type = await determine_container_type(container)
        # Get repository and container information
        repo_info = await get_repository(project_id, container_id, db)
        if "error" in repo_info:
            raise HTTPException(status_code=404, detail="Repository not found")

        # Get container information
        container = await db.get_node_by_id(container_id)
        if not container:
            raise HTTPException(status_code=404, detail="Container not found")

        # Check for existing deployment node
        existing_deployment_nodes = await db.get_child_nodes(container_id, "Deployment")
        
        # Get container type and properties
        # container_type = container.get("properties", {}).get("Type", "").lower()
        repo_data = repo_info["repository"]
        repo_url = repo_data.get("cloneUrlHttp", "")
        repo_name = repo_data.get("repositoryName", "")
        technology = repo_data.get("technology", "react")

        # Get branch information
        branches_info = await list_branches(project_id, container_id, db)
        default_branch = branches_info.get("branches", [{}])[0].get("name", "kavia-main")

         # Get all deployment nodes for container
        deployment_nodes = await db.get_child_nodes(container_id, "Deployment")
        
        # Try to find existing node for this branch
        branch_deployment = next(
            (node for node in deployment_nodes 
             if json.loads(node['properties'].get('deployment_config', '{}')).get('branch') == default_branch),
            None
        )
        
        if branch_deployment:
            logger.info(f"Found existing deployment node for branch: {default_branch}")
            return branch_deployment
            
        # Create new deployment node for this branch
        logger.info(f"Creating new deployment node for branch: {default_branch}")

        # Generate configurations based on container type
        if container_type == "frontend":
            terraform_files = get_backend_terraform_files(
                technology=technology,
                repo_name=repo_name.replace("-", "_"),
                branch=default_branch
            )
            deployment_type = "frontend"

            deployment_properties = {
                "Title": f"Deployment Configuration for {repo_name}",
                "Description": "AWS Amplify deployment configuration",
                "deployment_type": deployment_type,
                "main_tf": terraform_files["main_tf"],
                "variables_tf": terraform_files["variables_tf"],
                "providers_tf": terraform_files["providers_tf"],
                "outputs_tf": terraform_files["outputs_tf"],
                "workflow_file": terraform_files["workflow_file"],
                "configuration_state": "configured",
                "Type": "Deployment",
                "updated_at": datetime.now().isoformat(),
                "deployment_config":""
            }
        else:
            terraform_files = get_backend_terraform_files(
                technology=technology,
                repo_name=repo_name.replace("-", "_"),
                branch=default_branch
            )
            deployment_type = "backend"

            deployment_properties = {
                "Title": f"Deployment Configuration for {repo_name}",
                "Description": "AWS ECS deployment configuration",
                "deployment_type": deployment_type,
                "main_tf": terraform_files["main_tf"],
                "variables_tf": terraform_files["variables_tf"],
                "providers_tf": terraform_files["providers_tf"],
                "outputs_tf": terraform_files["outputs_tf"],
                "dockerfile": terraform_files["dockerfile"],
                "docker_compose": terraform_files["docker_compose"],
                "configuration_state": "configured",
                "Type": "Deployment",
                "updated_at": datetime.now().isoformat()
            }

        if existing_deployment_nodes:
            # Update existing deployment node
            existing_node = existing_deployment_nodes[0]
            updated_node = await db.update_node_by_id(
                existing_node["id"],
                deployment_properties
            )
            message = "Deployment node updated successfully"
        else:
            # Create new deployment node
            updated_node = await db.create_node(
                ["Deployment"],
                deployment_properties,
                container_id
            )
            message = "Deployment node created successfully"

        return JSONResponse(
            status_code=200,
            content={
                "message": message,
                "deployment_node": updated_node,
                "deployment_type": deployment_type
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get("/{project_id}/{container_id}/deployment-status")
async def monitor_deployment_status(
    project_id: int,
    container_id: int,
    db: NodeDB = Depends(get_node_db)
) -> StreamingResponse:
    """Monitor deployment status and update deployment node."""
    
    async def generate_status():
        max_retries = 1
        retry_count = 0

        try:
            # Get deployment node
            deployment_nodes = await db.get_child_nodes(container_id, "Deployment")
            if not deployment_nodes:
                raise Exception("Deployment node not found")
                
            deployment_node = deployment_nodes[0]
            deployment_type = deployment_node['properties'].get('deployment_type', 'frontend')
            
            # Get deployment config
            deployment_config = json.loads(deployment_node['properties'].get('deployment_config', '{}'))
            app_name = deployment_config.get('app_name')
            branch = deployment_config.get('branch')

            if not branch:
                raise Exception("Branch name not found in deployment configuration")

            # Handle frontend (Amplify) deployment
            if deployment_type == 'frontend':
                app_id = "d268io8nblatyc"
                if not app_id:
                    raise Exception("No Amplify app ID found in Terraform outputs")
                
                msg = {
                    'status': 'in_progress',
                    'step': 'infrastructure',
                    'message': 'Infrastructure deployed successfully',
                    'app_id': app_id
                }
                yield f"data: {json.dumps(msg)}\n\n"
                
                try:
                    app_details = await get_amplify_details(app_id)
                    deployment_status = await get_amplify_deployment_status(app_id, branch)
                    
                    msg = {
                        'status': 'in_progress',
                        'step': 'deployment',
                        'message': 'Checking deployment status...',
                        'details': app_details,
                        'deployment_status': deployment_status
                    }
                    yield f"data: {json.dumps(msg)}\n\n"
                    
                    # Check if deployed successfully
                    if deployment_status.get('is_deployed'):
                        final_details = await get_amplify_details(app_id)
                        deployment_url = final_details['branch_domains'].get(branch)
                        msg = {
                            'status': 'completed',
                            'step': 'finished',
                            'message': 'Deployment completed successfully',
                            'details': final_details,
                            'url': deployment_url,
                            'deployment_status': deployment_status
                        }
                        yield f"data: {json.dumps(msg)}\n\n"
                        
                        # Update deployment node
                        updated_properties = {
                            **deployment_node['properties'],
                            'deployment_url': deployment_url,
                            'deployment_status': 'deployed',
                            'last_deployed': datetime.now().isoformat(),
                            'deployment_details': json.dumps({
                                **final_details,
                                'deployment_status': deployment_status
                            })
                        }
                        await db.update_node_by_id(deployment_node['id'], updated_properties)
                    elif deployment_status.get('has_active_job'):
                        msg = {
                            'status': 'in_progress',
                            'step': 'deployment',
                            'message': 'Deployment is still in progress',
                            'details': deployment_status
                        }
                        yield f"data: {json.dumps(msg)}\n\n"
                    else:
                        msg = {
                            'status': 'error',
                            'step': 'deployment',
                            'message': f'Deployment failed or not started: {deployment_status.get("status")}',
                            'details': deployment_status
                        }
                        yield f"data: {json.dumps(msg)}\n\n"
                except Exception as e:
                    msg = {
                        'status': 'error',
                        'step': 'deployment',
                        'message': f'Error checking deployment status: {str(e)}'
                    }
                    yield f"data: {json.dumps(msg)}\n\n"
                    
                retry_count += 1

        except Exception as e:
            msg = {
                'status': 'error',
                'step': 'error',
                'message': f"Deployment monitoring failed: {str(e)}"
            }
            yield f"data: {json.dumps(msg)}\n\n"

    return StreamingResponse(
        generate_status(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )

async def get_or_create_branch_deployment_node(
    project_id: int,
    container_id: int,
    branch: str,
    deployment_config: dict,
    terraform_files: dict,
    db: NodeDB
) -> dict:
    """
    Get existing deployment node for a branch or create a new one.
    
    Args:
        project_id: Project ID
        container_id: Container ID
        branch: Branch name
        deployment_config: Deployment configuration
        terraform_files: Terraform configuration files
        db: Database connection
        
    Returns:
        dict: Deployment node
    """
    try:
        # Get all deployment nodes for container
        deployment_nodes = await db.get_child_nodes(container_id, "Deployment")
        
        # Try to find existing node for this branch
        branch_deployment = next(
            (node for node in deployment_nodes 
             if json.loads(node['properties'].get('deployment_config', '{}')).get('branch') == branch),
            None
        )
        
        timestamp = datetime.now().isoformat()
        deployment_id = f"deployment_{branch}_{int(time.time())}"
        
        # Base deployment properties
        deployment_properties = {
            "Title": f"Deployment Configuration - {branch}",
            "Description": f"Infrastructure deployment configuration for branch {branch}",
            "deployment_type": deployment_config.get("deployment_type", "frontend"),
            "deployment_id": deployment_id,
            "deployment_timestamp": timestamp,
            "deployment_config": json.dumps(deployment_config),
            "branch": branch,
            "Status": "pending",
            "Type": "Deployment",
            "created_at": timestamp,
            "updated_at": timestamp
        }

        # Add terraform files
        if terraform_files:
            deployment_properties.update({
                "main_tf": terraform_files.get("main_tf"),
                "variables_tf": terraform_files.get("variables_tf"),
                "providers_tf": terraform_files.get("providers_tf"),
                "outputs_tf": terraform_files.get("outputs_tf"),
                "workflow_file": terraform_files.get("workflow_file")
            })

            # Add Docker files if present for backend
            if "dockerfile" in terraform_files:
                deployment_properties.update({
                    "dockerfile": terraform_files["dockerfile"],
                    "docker_compose": terraform_files["docker_compose"]
                })

        if branch_deployment:
            # Update existing node
            logger.info(f"Updating existing deployment node for branch: {branch}")
            
            # Keep existing deployment history
            existing_history = branch_deployment['properties'].get('deployment_history', '[]')
            existing_history_list = json.loads(existing_history)
            
            # Add current state to history
            history_entry = {
                "timestamp": timestamp,
                "deployment_id": deployment_id,
                "config": deployment_config,
                "status": "pending"
            }
            existing_history_list.append(history_entry)
            
            # Update properties while preserving existing data
            updated_properties = {
                **branch_deployment['properties'],
                **deployment_properties,
                'deployment_history': json.dumps(existing_history_list)
            }
            
            # Update node
            updated_node = await db.update_node_by_id(
                branch_deployment['id'],
                updated_properties
            )
            logger.info(f"Updated deployment node: {updated_node['id']}")
            return updated_node
            
        else:
            # Create new deployment node
            logger.info(f"Creating new deployment node for branch: {branch}")
            
            # Initialize deployment history
            deployment_properties['deployment_history'] = json.dumps([{
                "timestamp": timestamp,
                "deployment_id": deployment_id,
                "config": deployment_config,
                "status": "pending"
            }])
            
            # Create new node
            new_node = await db.create_node(
                ["Deployment"],
                deployment_properties,
                container_id
            )
            logger.info(f"Created new deployment node: {new_node['id']}")
            return new_node

    except Exception as e:
        logger.error(f"Error managing branch deployment node: {str(e)}")
        raise Exception(f"Failed to manage deployment node: {str(e)}")
    
async def get_working_directory(repo_path: str) -> str:
    """
    Get working directory from .init-run-tool file in repository.
    
    Args:
        repo_path: Path to cloned repository
        
    Returns:
        str: Working directory path
    Raises:
        Exception: If working directory cannot be determined
    """
    try:
        init_file_path = os.path.join(repo_path, '.init-run-tool')
        if not os.path.exists(init_file_path):
            raise Exception(".init-run-tool file not found in repository")

        # Read and parse .init-run-tool file
        with open(init_file_path, 'r') as f:
            init_config = json.load(f)
            
        working_dir = init_config.get('working_directory')
        if not working_dir:
            raise Exception("No working_directory specified in .init-run-tool")
            
        # Handle absolute vs relative paths
        if os.path.isabs(working_dir):
            # For absolute paths, extract the relative part after repo name
            repo_name = os.path.basename(repo_path)
            working_dir_parts = working_dir.split(repo_name)
            if len(working_dir_parts) > 1:
                relative_path = working_dir_parts[1].lstrip('/')
                working_dir = os.path.join(repo_path, relative_path)
            else:
                working_dir = os.path.join(repo_path, working_dir.lstrip('/'))
        else:
            working_dir = os.path.join(repo_path, working_dir)

        # Verify directory exists
        if not os.path.exists(working_dir):
            raise Exception(f"Working directory {working_dir} not found in repository")

        logger.info(f"Found working directory: {working_dir}")
        return working_dir

    except json.JSONDecodeError:
        raise Exception("Invalid JSON in .init-run-tool file")
        
    except Exception as e:
        logger.error(f"Error getting working directory: {str(e)}")
        raise