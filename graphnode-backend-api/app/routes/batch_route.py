import asyncio
import hashlib
import json
import random
import string
import boto3
import git
from fastapi import APIRouter, HTTPException, Request, Depends, Query, BackgroundTasks
from fastapi.responses import StreamingResponse, JSONResponse
from pydantic import BaseModel
from typing import Dict, Optional
from pymongo import ASCENDING, DESCENDING, MongoClient
import requests
import time
import uuid
import os
import zipfile
import io
import subprocess
from app.models.user_model import LLMModel
from app.classes.<PERSON>c2<PERSON><PERSON><PERSON> import Ec2<PERSON>andler
from app.connection.establish_db_connection import get_node_db, NodeDB, get_mongo_db, MongoDBHandler, get_mongo_db_v1, TaskRepository
from app.repository.mongodb.client import get_db
from app.core.Settings import settings
from app.core.constants import TASKS_COLLECTION_NAME as tasks_collection_name, TaskStatus, PODS_COLLECTION, USER_PODS_COLLECTION, KUBERNETES_MONITOR_COLLECTION_NAME as DB_NAME
from app.utils.deep_query.setup_workspace_utils import setup_workspace
from app.utils.k8_job import K8<PERSON><PERSON><PERSON><PERSON>ger
from app.utils.node_utils import get_node_type
from app.utils.batch_utils import get_detailed_report
from app.utils.project_utils import get_stage, name_to_slug
from app.utils.auth_utils import get_current_user 
from app.utils.user_utils import get_module_configuration_for_user
from fastapi.responses import StreamingResponse
from app.utils.respository_utils import create_repository_in_workspace
from app.models.code_generation_model import Message
from datetime import datetime
from app.core.git_tools import EnhancedGitTools
from app.utils.code_generation_utils import get_codegeneration_path
from pydantic import BaseModel, Field
from typing import Optional
from app.connection.tenant_middleware import get_tenant_id
from typing import AsyncGenerator
from app.utils.datetime_utils import generate_timestamp
from fastapi import Body
from dotenv import load_dotenv
from app.utils.datetime_utils import generate_timestamp
from app.utils.stream_utils import format_response_for_stream
import shutil
import tempfile
import configparser
from app.utils.logs_CGA_utils import update_config_dir
from app.utils.task_utils import generate_task_id, get_codegen_pod, get_codegen_url
from fastapi.responses import FileResponse
from app.classes.S3Handler import S3Handler
from app.utils.k8.create_project_v2 import change_to_used_for_beta, change_to_used_for_dev, change_to_used_for_qa, create_project_for_dev, create_project_for_qa, create_project_for_beta
from app.utils.project_utils import safe_name
from app.services.session_tracker import get_session_tracker
from app.core.kubernetes_monitor import KubernetesAvailablePodsManager , get_environment_and_namespace, kubernetes_manager

class CreatePRRequest(BaseModel):
    destination_branch: str
    title: Optional[str] = Field(None, description="PR title. If not provided, will use default format")
    description: Optional[str] = Field(None, description="PR description. If not provided, will use default format")
    

_SHOW_NAME = "batch"
MAX_CODE_MAINTENANCE_SESSIONS = 3
class CodeGenerationResponse(BaseModel):
    job_id: str = ''
    task_id: str = ''
    iframe: str = ''
    message: str = ''
    wait: int = 0

router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {'description': 'Not found'}},
)

VSCODE_CLOUD_URL = settings.VSCODE_CLOUD_URL

            
class UserInputRequest(BaseModel):
    user_input: str
    
async def task_execute_maintenance(project_id: int, db: NodeDB = None):
    if not db:
        db = get_node_db()
    
    project = await db.get_node_by_label_id(project_id, "Project")
    project_details = project.get("properties")
    
    if not project_details or get_node_type(project_details.get("Type")) != "Project":
        raise HTTPException(status_code=404, detail="Project not configured")
    
    project_repositories = json.loads(project_details.get("repositories", "{}"))
    
    containers = await db.get_nodes_connected_by_multiple_hops(project_id, "HAS_CHILD", "Container", 5)
    
    work_items = {
        "project_id": project_id,
        "project_name": project_details.get("Title"),
        "containers": []
    }

    for container in containers:
        container_id = str(container.get("id"))
        
        # Handle repository creation/verification
        if container_id not in project_repositories:
            try:
                repository_response = await create_repository_in_workspace(
                    project_id=project_id,
                    container_id=int(container_id),
                    db=db
                )
                
                repository_details = repository_response.get("repository")
                project_repositories[container_id] = repository_details
                
            except Exception as e:
                print(f"Error creating repository for container {container_id}: {str(e)}")
                continue
        
        components = await db.get_child_nodes(int(container_id), "Architecture")
        contanier_details = {
            "name": container.get("properties", {}).get("Title"),
            "description": container.get("properties", {}).get("Description"),
            "repository": project_repositories.get(container_id),
            "components": {}
        }


        for component in components:
            component_id = str(component.get("id"))
            component_info = await db.get_work_items(int(component_id))
            
            if component_info:
                contanier_details["components"][component_id] = {
                    "name": component_info.get("component_name"),
                    "description": component_info.get("description"),
                    "repository_name": component_info.get("repository_name"),
                    "root_folder": component_info.get("root_folder"),
                    "design": component_info.get("design", {}),
                    "algorithms": component_info.get("Algorithm", []),
                    "pseudocode": component_info.get("Pseudocode", [])
                }
        work_items["containers"].append(contanier_details)

    project_details["repositories"] = json.dumps(project_repositories)

    return project_details, work_items

async def task_execute(project_id: int, container_ids: list, db: NodeDB = None, test_case: bool = False, mongo_db: MongoClient = None):
    if not db:
        db = get_node_db()
        
    if not mongo_db:
        mongo_db = get_mongo_db().db
    
    project = await db.get_node_by_label_id(project_id, "Project")
    project_details = project.get("properties")
    

    design_node = project_details.get('Init_project_info',{})
    if design_node:
        design_node = json.loads(design_node)

    if not project_details or get_node_type(project_details.get("Type")) != "Project":
        print("Project not configured")
        raise HTTPException(status_code=404, detail="Project not configured")
    
    # Load project repositories
    project_repositories = json.loads(project_details.get("repositories", "{}"))
    print("container_ids", container_ids)
    print(project_repositories)
    project_details["current_repositories"] = []
    # Check if container repository exists, if not create one
    for container_id in container_ids:
        container_node = await db.get_node_by_id(container_id)
        container_details = container_node.get("properties")
        container_repository = project_repositories.get(str(container_id))
        if not container_repository:
            try:
                if not os.getenv("encrypted_scm_id"):
                    repository_response = await create_repository_in_workspace(
                        project_id=project_id,
                        container_id=container_id,
                        db=db
                    )
                else: 
                    from app.routes.repository_route import create_github_repository, SCMConfiguration, name_to_slug
                    from app.utils.hash import decrypt_string
                    encrypted_scm_id = os.getenv("encrypted_scm_id")
                    scm_id = decrypt_string (encrypted_scm_id)
                    config = mongo_db["scm_configurations"].find_one({"scm_id": scm_id})
                    config['encrypted_scm_id'] = encrypted_scm_id
                    scm_config= SCMConfiguration(**config) if config else None
                    repository_name = name_to_slug(
            f"{project_details.get('Title', project_details.get('Name'))}"
        )
                    repository_name = f'{repository_name}-{container_id}'
                    repository_response = await create_github_repository(repository_name, config=scm_config, is_private=False)
                
                # Update project_repositories
                project_repositories[str(container_id)] = repository_response
        
                repository_response["container_id"] = str(container_id)
                repository_response["container_name"] = str(container_details.get("Title"))
                # Update project_details with latest data
                project_details["repositories"] = json.dumps(project_repositories)
                project_details["current_repositories"].append(repository_response) 
            except Exception as e:
                raise HTTPException(status_code=400, detail=str(e))
        else:
            container_repository["container_id"] = str(container_id)
            container_repository["container_name"] = str(container_details.get("Title"))
            project_details["current_repositories"].append(container_repository) 
            
    await db.update_node_by_id(
            project_id,
            {"repositories": json.dumps(project_repositories)}
        )
        # Get repository details for this container
        # container_repository = project_repositories.get(str(container_id))
        # if container_repository:
        #     print("Container repository found")
        #     project_details["current_repository"] = container_repository

    # input_arguments = os.environ.get("input_arguments")
    # if input_arguments:
    #     test_case = json.loads(input_arguments).get("test_case",False)
    #     if test_case:
    #         work_items = await db.get_work_items_for_testcase(container_id)
    #     else:
    #         work_items = await db.get_work_items_for_container(container_id=container_id)

    if test_case:
        work_items = await db.get_work_items_for_testcase(project_id)
    else:
        work_items = await db.get_work_items_for_container(container_id=container_id)


    # work_items = await db.get_work_items_for_testcase(container_id)

    # Commenting out project details update in work item, as it is now passed via the design node
    # work_items.update(project_details)
    
    work_items['App Name'] = design_node.get('name', None)
    
    techStackList = []
    def is_valid_stack(stack):
        if not stack:
            return False
        first_item = stack[0]
        # Check if first_item is not None and not string 'none' (case-insensitive)
        if first_item is None:
            return False
        if isinstance(first_item, str) and first_item.lower() == "none":
            return False
        return True

    if is_valid_stack( design_node.get('techStack', {}).get("frontend", [])):
        techStackList.append("frontend")
    if is_valid_stack( design_node.get('techStack', {}).get("backend", [])):
        techStackList.append("backend")

    name_stacks = ', '.join(techStackList)

    for key in ['id', 'description', 'estimatedTime', 'complexity', 'name']:
        if key == 'name' :
            design_node[key] = f"{project_details.get('Title', '')} {name_stacks} Container"
        else:
            design_node.pop(key,None)    

    for feature in design_node.get('features', []):
        feature.pop("id", None)
        # feature.pop("isEnabled", None)
        
    work_items.update({"container":json.dumps(design_node, indent=2)})

    for key in ['created_at', 'branch', 'Pseudocode', 'Algorithm', 'Description']:
        work_items.pop(key,'') 

    if project_details.get('work_item_type','') == 'project' :
        work_items['work_item_type'] = 'project' 
        work_items['containers'] = json.loads(project_details.get('containers','[]'))

    return project_details, work_items

def get_pod_url(stage, pod_prefix: str):

    """
    Generate the pod URL based on the tenant ID, project ID, and stage.
    """
    if stage == "dev":
        return f"https://vscode-internal-{pod_prefix}-dev.dev01.cloud.kavia.ai"
    elif stage == "qa":
        return f"https://vscode-internal-{pod_prefix}-qa.qa01.cloud.kavia.ai"
    elif stage == "pre_prod":
        return f"https://vscode-internal-{pod_prefix}-beta.beta01.cloud.kavia.ai"
    else:
        return f"https://vscode-internal-{pod_prefix}-{stage}.{stage}.cloud.kavia.ai"

@router.get("/get_active_task/{project_id}")
async def get_active_task(
    project_id: int,
    container_id=None, 
    architecture_id=None,
    custom_filter=None,
):
    mongo_db = get_mongo_db()
    find_query = {
        "project_id": project_id,
        "status": {"$regex": f"^({'submitted|started|pending|running|paused|in_progress' if custom_filter == None else custom_filter})$", "$options": "i"}
    }
    if container_id:
        find_query["container_id"] = container_id
    if architecture_id:
        find_query["architecture_id"] = architecture_id
    
    active_task = mongo_db.db[tasks_collection_name].find_one(
        find_query,
        {'_id':1,'job_id':1,'status':1,'iframe':1,'ip':1,'architecture_id':1}
    )
    
    previous_task = [i for i in mongo_db.db["tf_tasks"].find(
        {
            "project_id": project_id, 
            "status": {"$regex": "^(complete)$", "$options": "i"}
        }
    ).sort('start_time', -1).limit(1)]
    
    if previous_task:
        previous_task = previous_task[0]
    else:
        previous_task = None

    if active_task:
        try:
            job_id = active_task.get("_id")
            active_task['task_id'] = str(job_id)
            return active_task
        except:
            pass
    return {
        "message": "No active task found",
        "prev_task_id":str(previous_task.get("_id")) if previous_task != None else None
    }  

def cancel_existing_running_tasks(project_id: int, mongo_db: MongoDBHandler):
    # Query to find RUNNING tasks
    query = {
        'project_id': project_id,
        'status': 'RUNNING'
    }
    
    # Update to change status to STOPPED
    update = {
        '$set': {'status': TaskStatus.STOPPED.value}
    }
    
    # Update the matching documents
    result = mongo_db.db[tasks_collection_name].update_many(query, update)

    return result.modified_count

def run_local_instance(project_id,container_ids,job_name,stage,llm_model,
        background_tasks: BackgroundTasks = BackgroundTasks(),
        mongo_db: MongoDBHandler = Depends(get_mongo_db),
        tenant_id:str = "T0000",
        user_id:str = "U0000",
        platform:str = "common", 
        test_case: bool = False,
        encrypted_scm_id: str = None
        ):
        os.environ["BATCH_JOB_TRIGGER"] = "True"
        os.environ["FEATURE_FLAG_GIT_TOOL"] = "True"
        os.environ["FEATURE_FLAG_USE_DOCKER"] = "True"  
        os.environ["BATCH_JOB_STAGE"]= stage
        os.environ["CELERY_BROKER_URL"] = settings.CELERY_BROKER_URL
        os.environ["PLATFORM"] = platform
        os.environ["TEST_CASE"] = str(test_case)
        # print"LOCAL_DEBUG")
        
        input_arguments = json.dumps({
            "project_id": project_id,
            "container_id": container_ids,
            "llm_model": str(llm_model),
            "task_id": job_name,
            "user_id": user_id,
            "tenant_id": tenant_id,
            "platform": platform,
            "test_case": test_case,
            "iframe": "http://localhost:8080/?folder=/tmp/kavia/workspace",
            "encrypted_scm_id": encrypted_scm_id,
        })
        os.environ["input_arguments"] = input_arguments
        os.environ["task_id"] = job_name
        # use screen to run the job with log
        
        try:
            os.makedirs("/tmp/kavia/workspace", exist_ok=True)
            os.makedirs("/tmp/kavia/workspace/logs", exist_ok=True)
        except Exception as e:
            print(f"Error creating directory: {e}")
            pass
        #################################
        
        input_args = json.loads(input_arguments) if isinstance(input_arguments, str) else input_arguments
        project_id_ = int(input_args.get("project_id"))
        task_id_ = input_args.get("task_id")
        update_config_dir("config.ini",tenant_id=get_tenant_id(),project_id=project_id_,task_id=task_id_)
        subprocess.run([
            "screen", "-L", 
            "-Logfile", f"/tmp/kavia/workspace/logs/{job_name}.log",
            "-dmS", job_name, 
            "bash", "-c", 
            f"python app/batch_jobs/jobs.py --input_args '{input_arguments}' --stage {stage} | ts '[%Y-%m-%d %H:%M:%S]'"
        ], env=os.environ)
        # #run the job in background without screen for debugging
        # background_tasks.add_task(subprocess.run, ["python", "app/batch_jobs/jobs.py"], env=os.environ)
        
        mongo_db.db[tasks_collection_name].update_one(
            {"_id": job_name},
            {"$set": {
                "job_id": job_name,
                "project_id": project_id,
                "container_id": container_ids,
                "container_ids": container_ids,
                "llm_model": str(llm_model),
                "start_time": generate_timestamp(),
                "status": "SUBMITTED",
                "user_id": user_id,
                "platform": platform,
                "test_case": test_case,
                "context": {},
                "iframe": "http://localhost:8080/?folder=/tmp/kavia/workspace/code-generation",
                "encrypted_scm_id": encrypted_scm_id
            }},
            upsert=True
        )
        
        return {
            "task_id": job_name,
            "llm_model": llm_model,
            "container_id": container_ids,
            "message": "Job submitted successfully"
        }

def run_local_deep_query(project_id,selected_repos,job_name,stage,llm_model,
        background_tasks: BackgroundTasks = BackgroundTasks(),
        mongo_db: MongoDBHandler = Depends(get_mongo_db),
        tenant_id:str = "T0000",
        user_id:str = "U0000",
        ):
    os.environ["BATCH_JOB_TRIGGER"] = "True"
    os.environ["FEATURE_FLAG_GIT_TOOL"] = "True"
    os.environ["FEATURE_FLAG_USE_DOCKER"] = "True"  
    os.environ["BATCH_JOB_STAGE"]= stage
    os.environ["CELERY_BROKER_URL"] = settings.CELERY_BROKER_URL

    input_arguments = json.dumps({
            "project_id": project_id,
            "llm_model": str(llm_model),
            "task_id": job_name,
            "user_id": user_id,
            "agent_name": "DocumentCreation",
            "tenant_id": tenant_id,
        })
    
    os.environ["input_arguments"] = input_arguments
    os.environ["task_id"] = job_name
    # use screen to run the job with log
    print("Running screen", job_name)
    subprocess.run(["screen", "-L", "-Logfile", f"logs/{job_name}.log", "-dmS", job_name, "python", "app/batch_jobs/jobs.py", "--input_args", input_arguments, "--stage", stage], env=os.environ)

    mongo_db.db[tasks_collection_name].update_one(
            {"_id": job_name},
            {"$set": {
                "job_id": job_name,
                "project_id": project_id,
                "llm_model": str(llm_model),
                "agent_name": "DocumentCreation",
                "start_time": generate_timestamp(),
                "status": "SUBMITTED",
                "user_id": user_id,
                "context": {},
                "repositories": selected_repos,
                "iframe": "http://localhost:8080/?folder=/tmp/kavia/workspace/code-generation"
            }},
            upsert=True
        )
        
    return {
        "task_id": job_name,
        "llm_model": llm_model,
        "message": "Job submitted successfully"
    }


# Need to add health check for the instance (3/3) Minumum 2 instance
async def stream_start_workspace_status(
        project_id: int,
        container_ids: list,
        session_name:str,
        description:str,
        background_tasks: BackgroundTasks = BackgroundTasks(),
        mongo_db: MongoDBHandler = Depends(get_mongo_db),
        node_db: NodeDB = Depends(get_node_db),
        current_user = Depends(get_current_user),
        new_repo_creation: bool = True,
        resume: bool = False,
        resume_task_id: str = "",
        test_case: bool = False,
        encrypted_scm_id = None
        ):
    """
    Generator function to stream workspace status as Server-Sent Events (SSE)
    """
    try:
        db_execution_time = None
        start_time = time.time()
        tenant_id = get_tenant_id()
        job_name = generate_task_id("cg") if not resume else resume_task_id
        stage = get_stage(settings)
        user_id = current_user.get('cognito:username') if isinstance(current_user, dict) else None
        llm_model = LLMModel.bedrock_claude_3_7_sonnet.value
        
        print("TESTCASEGENERATION", container_ids, project_id,)
        project = await node_db.get_node_by_label_id(project_id, "Project")
        container_id=container_ids[0]
        container = await node_db.get_node_by_label_id(container_id, "Container")
        
        if not container:
            yield format_response_for_stream({'error':'Container is not configured','end':True})
            return
            
        container_id = container.get("id")
        platform = container.get("properties", {}).get("platform", "common")

        if not new_repo_creation:
            project_repositories = json.loads(project.get("properties", {}).get("repositories", "{}"))
            container_repository = project_repositories.get(str(container_id))
            if not container_repository:
                yield format_response_for_stream({'error':'Container repository is not configured','end':True})
                return

        
        try:
            # Get session tracker
            tracker = get_session_tracker()
            
            # Initialize session tracking
            session_result = await tracker.initialize_session(
                task_id=job_name,  # This is your task_id
                tenant_id=tenant_id,
                user_id=user_id,
                project_id=project_id,
                container_id=container_ids,
                service_type="code-generation",
                session_data={
                    "session_name": session_name,
                    "description": description,
                    "platform": platform,
                    "llm_model": llm_model,
                }
            )
            
            if session_result["success"]:
                print(f"Session tracking initialized for task_id: {job_name}")
            else:
                print(f"Failed to initialize session tracking: {session_result['error']}")
                    
        except Exception as session_error:
            print(f"Session tracking initialization error: {session_error}") 
            
            
        # For local debug
        if os.environ.get("LOCAL_DEBUG"):
            print("Local debug true")
            response = run_local_instance(project_id,container_ids,job_name,stage,llm_model, background_tasks,mongo_db, tenant_id, user_id=user_id, platform=platform,
                                          test_case=test_case, encrypted_scm_id=encrypted_scm_id)
            response['end'] = True
            yield format_response_for_stream(response)
        else:
            stage_params = 'dev' if stage == 'develop' else stage
            
            db_start_time = time.time()
            unique_id = tenant_id + str(project_id)
            # Assign a pod to this project
            start_assign_time = time.time()


            # Always assing a new pod for each session

            # Use findOneAndDelete for atomic operation
            selected_pod = kubernetes_manager.get_one_available_pod_and_mark_as_used(job_name, tenant_id)
            
            if not selected_pod:
                yield format_response_for_stream({'error': 'No available pods found', 'end': True})
                return
            print("Selected Pod: ", selected_pod)
            pod_prefix = selected_pod.get("project_id", "")
            
            
        
            assign_time = time.time() - start_assign_time
            yield format_response_for_stream({'message': f'Pod {pod_prefix} assigned in {assign_time:.2f} seconds'})
            
            
            # Continue with the same workflow as in the else block
            print("Instance name : ", pod_prefix)
            
            # Set up URLs
            vs_code_complete_url = f"https://vscode-internal-{pod_prefix}-dev.dev-vscode.cloud.kavia.ai/?folder=/home/<USER>/workspace/code-generation/"
            application_preivew_url = f"https://vscode-internal-{pod_prefix}-dev.dev-vscode.cloud.kavia.ai"
            
            
            if stage_params == "dev" or stage_params=="qa":
                pod_url = get_pod_url(stage_params, pod_prefix)
                vs_code_complete_url = f"{pod_url}/?folder=/home/<USER>/workspace/code-generation/"
                application_preivew_url = pod_url
      
            elif stage_params == "pre_prod":
                vs_code_complete_url = f"https://vscode-internal-{pod_prefix}-beta.beta01.cloud.kavia.ai/?folder=/home/<USER>/workspace/code-generation/"
                application_preivew_url = f"https://vscode-internal-{pod_prefix}-beta.beta01.cloud.kavia.ai"
                
            planned_job_id = job_name
            anticipate_message_dict = {
                "message": "install",
                "planned_job_id": planned_job_id,
                "end": False
            }
            
            yield format_response_for_stream(anticipate_message_dict)
            params = f'?project_id={project_id}&task_id={job_name}&stage={stage_params}&llm_model={llm_model}&tenant_id={tenant_id}&platform={platform}&pod_id={pod_prefix}&user_id={user_id}'
            
            ip = '127.0.0.1'
            
            yield format_response_for_stream({'message': 'install|provision|config'})
            
            iframe_url = vs_code_complete_url
            ports = [{f'{port}': vs_code_complete_url} for port in [3000, 5000, 8000]]
            
            # Submit request before start codegeneration
            mongo_db.db[tasks_collection_name].update_one(
                {"_id": job_name},
                {
                    "$set": {
                        "job_id": job_name,
                        "project_id": project_id,
                        "container_id": container_ids,
                        "container_ids": container_ids,
                        "session_name": session_name,
                        "description": description,
                        "status": "SUBMITTED",
                        "ip": ip,
                        "iframe": iframe_url,
                        "ports": ports,
                        "context": {},
                        "llm_model": llm_model,
                        "user_id": user_id,
                        "platform": platform,
                        "params": params,
                        "stage": stage_params,
                        "new_repo_creation": new_repo_creation,
                        "start_time": generate_timestamp(),
                        "application_preivew_url": application_preivew_url,
                        "pod_id": pod_prefix,
                        "pod_name": pod_prefix,
                        "encrypted_scm_id": encrypted_scm_id
                    }
                },
                upsert=True
            )
            
            # Maximum number of retries
            MAX_RETRIES = 15
            # Delay between retries in seconds
            RETRY_DELAY = 2
            
            start_maintenance_url = get_codegen_url(stage_params, pod_prefix=pod_prefix)
            start_maintenance_url = f'{start_maintenance_url}/start'+ params

            if stage_params == "pre_prod":
                stage_params="beta"
                start_maintenance_url = f'http://internal-{pod_prefix}-beta.duploservices-k-beta01.svc.cluster.local:8003/api/code_gen/start' + params
            print("Calling", start_maintenance_url)
            db_execution_time = time.time() - db_start_time
    
            # Print results
            print(f"Execution time: {db_execution_time:.4f} seconds")
                
            connected = False
            attempts = 0
            
            while not connected and attempts < MAX_RETRIES:
                try:
                    resp = requests.get(start_maintenance_url, verify=False, timeout=5)
                    
                    if resp.status_code == 200:
                        connected = True
                        print(f"Connection successful after {attempts+1} attempts")
                    else:
                        attempts += 1
                        print(f"Attempt {attempts}/{MAX_RETRIES}: Connection failed with status code {resp.status_code}")
                        yield format_response_for_stream({
                            'message':'install|provision|config',
                            'debug_error': f'Connection attempt {attempts}/{MAX_RETRIES} failed. Status code: {resp.status_code}. Retrying...'
                        })
                        time.sleep(RETRY_DELAY)
                except requests.RequestException as e:
                    attempts += 1
                    print(f"Attempt {attempts}/{MAX_RETRIES}: Connection error: {str(e)}")
                    yield format_response_for_stream({
                        'message':'install|provision|config',
                        'debug_error': f'Connection attempt {attempts}/{MAX_RETRIES} failed. Error: {str(e)}. Retrying...'
                    })
                    time.sleep(RETRY_DELAY)

            if connected:
                success = True
                
                    
                if success:
                    end_time = time.time()
                    workspace_elapsed_time = end_time - start_time

                    yield format_response_for_stream({
                        'message': 'install|provision|config|final',
                        'k8_job_creation': workspace_elapsed_time,
                        "task_id": job_name,
                        'db_execution_time': db_execution_time,
                        "ip": ip,
                        "iframe": iframe_url,
                        "ports": ports,
                        'end': True
                    })
                    
                        
                else:
                    yield format_response_for_stream({
                        'message': 'Connection failed after 15 attempts. Please try to reconnect with VSCode.',
                        "task_id": job_name,
                        "ip": ip,
                        "debug_error": f"Failed to connect to {iframe_url} after 15 attempts",
                        'end': True
                    })
                     
            else:
                yield format_response_for_stream({
                    'message': f'Failed to connect after {MAX_RETRIES} attempts. Please check the service status.',
                    "task_id": job_name,
                    "ip": ip,
                    "iframe": iframe_url,
                    "ports": ports,
                    'end': True
                })
                
            return
         
        return # Terminate the existing Connection 
    except Exception as e:
        import traceback
        traceback.print_exc()
        print(f"Exception in stream_start_workspace_status: {str(e)}")
        error_data = json.dumps({"error": str(e), "end": True})
        yield f"data: {error_data}\n\n"
        
@router.post("/start_code_generation/{project_id}/")
async def submit_code_generation_job_opt(
        project_id: int,
        container_id: int,
        session_name: str = Body(default="Untitled"),
        description: str = Body(default="Untitled"),
        background_tasks: BackgroundTasks = BackgroundTasks(),
        mongo_db: MongoDBHandler = Depends(get_mongo_db),
        db: NodeDB = Depends(get_node_db),
        current_user = Depends(get_current_user),
        resume: bool = Query(False, description="Resume flag"),
        resume_task_id: str = Query("", description="Resume task id"),
        test_case: bool = Query(False, description="Test case flag")
    ): 
    if test_case: 

        node_query = '''MATCH (n:Project)-[:HAS_CHILD]->(s:SystemContext)-[:HAS_CHILD]->(c:Container) 
        WHERE ID(n) = $node_id 
        AND c.ContainerType = 'internal' 
        AND (c.Repository_Name IS NOT NULL OR c.repository_name IS NOT NULL)
        RETURN ID(c) AS container_id'''

        node_result = await db.async_run(node_query, node_id=project_id)
        node_data = node_result.data()

        container_id = None
        if node_data:
            container_id = node_data[0].get('container_id')

        if not container_id:
            return {"error": "No valid container found for this project"}

    container_ids = []
    container_ids.append(container_id)
    return StreamingResponse(
        stream_start_workspace_status(
            project_id, container_ids, session_name, description, background_tasks, mongo_db, db, current_user,
            resume=resume, resume_task_id=resume_task_id, test_case=test_case
        ),
        media_type="text/event-stream",
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # This is for Nginx specifically
        }
    )
@router.get("/past_maintenance_tasks/{project_id}")
async def get_past_maintenance_tasks(
    project_id: int,
    limit: int = Query(10, ge=1, le=100),
    skip: int = Query(0, ge=0),
    current_user = Depends(get_current_user),
    agent_name = Query('CodeMaintenance', alias="agent"),
    background_tasks: BackgroundTasks = BackgroundTasks(),
):
    """
    Retrieve past code maintenance tasks for a given project_id.
    """
    tenant_id = get_tenant_id()
    task_repository: TaskRepository = await get_mongo_db_v1("task", table_name="code_gen_tasks")
    db = await get_db(f"{settings.MONGO_DB_NAME}_{tenant_id}")
    query = {
        "project_id": int(project_id),
        "agent_name": agent_name
    }

    
    projection = {
        "_id": 1,
        "status": 1,
        "llm_model": 1,
        "agent_name": 1,
        "start_time": 1,
        "session_id": 1,
        "messages":1,
        "description": 1,
        "session_name": 1,
        "start_time":1
    }
    sort = [("start_time", -1)]

    tasks = await task_repository.find_many(query, db, projection, skip=skip, limit=limit, sort=sort)

    
    # Convert ObjectId to string in tasks
    serialized_tasks = []
    for task in tasks:
        task['_id'] = str(task['_id'])  # Convert ObjectId to string
        serialized_tasks.append(task)

    total_count = await task_repository.count_documents(query, db)

    # try:
        # ec2_handler = Ec2Handler()
        # print("Invoking ec2 instance")
        # tenant_id = current_user.get("custom:tenant_id")
        # stage = get_stage() 
        # instance_name =f"{tenant_id}-{project_id}-{stage}"
        # background_tasks.add_task(
        #     ec2_handler.wake_up_instance,
        #     instance_name=instance_name)
    # except Exception as e:
    #     print("Error tracking project usage: ", str(e))
    #     pass
    return {
        "tasks": serialized_tasks,
        "total_count": total_count,
        "limit": limit,
        "skip": skip
    }

@router.get("/active_maintenance_sessions/{project_id}")
async def get_active_maintenance_sessions(
    project_id: int,
):
    return await get_active_code_maintenance_sessions(project_id)

@router.delete("/past_maintenance_tasks/{task_id}")
async def delete_maintenance_task(
    task_id: str,
):
    """
    Delete a specific maintenance task by its ID.
    """
    # return(task_id)
    tenant_id = get_tenant_id()
    task_repository: TaskRepository = await get_mongo_db_v1("task", table_name="code_gen_tasks")
    db = await get_db(f"{settings.MONGO_DB_NAME}_{tenant_id}")
    
    # # Convert string ID to ObjectId for MongoDB query
    # from bson import ObjectId
    # try:
    #     object_id = ObjectId(task_id)
    # except:
    #     raise HTTPException(status_code=400, detail="Invalid task ID format")

    # Query to find and delete the specific task
    query = {
        "_id": task_id
        # "agent_name": "CodeMaintenance"
    }
    print(task_id)
    # Delete the task
    result = await task_repository.delete_one(query, db)
    
    if result==False:
        raise HTTPException(status_code=404, detail="Task not found")

    
    return {"message": "Task deleted successfully"}


async def get_active_maintenance_task(project_id: int):
    """Get the last active code maintenance task for a project"""
    task_repository: TaskRepository = await get_mongo_db_v1("task", table_name=tasks_collection_name)
    tenant_id = get_tenant_id()
    db = await get_db(f"{settings.MONGO_DB_NAME}_{tenant_id}")
    
    # Query for active task
    query = {
        "project_id": project_id,
        "agent_name": "CodeMaintenance",
        "status": {
            "$regex": "^(submitted|started|pending|running|paused|in_progress)$",
            "$options": "i"
        }
    }
    projection = {
        "_id": 1, 
        "job_id": 1, 
        "status": 1, 
        "iframe": 1, 
        "ip": 1
    }
    
    active_task = await task_repository.find_one(query, db, projection)
    
    if active_task:
        try:
            job_id = active_task.get("_id")
            active_task['task_id'] = str(job_id)
            return active_task
        except:
            return {}
            
    
    # Query for previous completed task
    prev_query = {
        "project_id": project_id,
        "agent_name": "CodeMaintenance",
        "status": "COMPLETE"
    }
    previous_task = await task_repository.find_one(
        prev_query, 
        db
    )
    
    return {
        "message": "No active maintenance task found",
        "prev_task_id": str(previous_task.get("_id")) if previous_task else None
    }

async def get_active_code_maintenance_sessions(project_id: int):
    try:
        db = get_mongo_db().db
        query = {
            "project_id": int(project_id),
            "agent_name": "CodeMaintenance",
            "status": {
                "$regex": "^(submitted|started|pending|running|paused|in_progress)$",
                "$options": "i"
            }
        }
        projection = {
            "_id": 1,
            "job_id": 1,
            "status": 1,
            "session_name": 1,
            "start_time": 1,
            "ip": 1
        }
        active_sessions = db[tasks_collection_name].find(query, projection)
        active_sessions = list(active_sessions)
        print("active_sessions",active_sessions)
        print("len",len(active_sessions))
        return active_sessions
    except Exception as e:
        print("Error in get_active_code_maintenance_sessions: ", str(e))
        return []

def generate_codegen_hash():
    # Generate 4 random letters (a-z)
    random_chars = ''.join(random.choices(string.ascii_lowercase, k=4))
    return f"{random_chars}"
            
async def stream_code_maintanence(
    project_id: int,
    selected_repos: dict,
    session_name:str,
    description:str,
    background_tasks: BackgroundTasks = BackgroundTasks(),
    mongo_db: MongoDBHandler = Depends(get_mongo_db),
    db: NodeDB = Depends(get_node_db),
    current_user = Depends(get_current_user)
):
    try:
        tenant_id = get_tenant_id()
        job_name = generate_task_id("cm")
        stage = get_stage(settings)
        user_id = current_user.get('cognito:username')
        llm_model = LLMModel.bedrock_claude_3_7_sonnet.value
        print(selected_repos)
        
        try:
            # Get session tracker
            tracker = get_session_tracker()
            
            # Initialize session tracking
            session_result = await tracker.initialize_session(
                task_id=job_name,  # This is your task_id
                tenant_id=tenant_id,
                user_id=user_id,
                project_id=project_id,
                service_type="code-maintenance",
                session_data={
                    "session_name": session_name,
                    "description": description,
                    "llm_model": llm_model,
                    "selected_repos":selected_repos
                }
            )
            
            if session_result["success"]:
                print(f"Session tracking initialized for task_id: {job_name}")
            else:
                print(f"Failed to initialize session tracking: {session_result['error']}")
                    
        except Exception as session_error:
            print(f"Session tracking initialization error: {session_error}")  

        # For local debug
        if os.environ.get("LOCAL_DEBUG"):
            os.environ["BATCH_JOB_TRIGGER"] = "True"
            os.environ["FEATURE_FLAG_GIT_TOOL"] = "True"
            os.environ["FEATURE_FLAG_USE_DOCKER"] = "True"  
            os.environ["BATCH_JOB_STAGE"] = stage
            os.environ["CELERY_BROKER_URL"] = settings.CELERY_BROKER_URL
            yield format_response_for_stream({'message':'install|provision|config'})

            input_arguments = json.dumps({
                "project_id": project_id,
                "llm_model": str(llm_model),
                "task_id": job_name,
                "tenant_id": tenant_id,
                "agent_name": "CodeMaintenance",
                "user_id": user_id,
                "iframe": f"http://localhost:8080/?folder=/tmp/kavia/workspace/code-generation",
                
            })
            os.environ["input_arguments"] = input_arguments
            os.environ["task_id"] = job_name
            # use screen to run the job with log
            # subprocess.run(["screen", "-L", "-Logfile", f"logs/{job_name}.log", "-dmS", job_name, "python", "app/batch_jobs/jobs.py"], env=os.environ)
            os.makedirs("/tmp/kavia/workspace/logs", exist_ok=True)
            screen_cmd = [
                'screen', '-L', 
                '-Logfile', f"/tmp/kavia/workspace/logs/{job_name}.log",
                '-dmS', job_name, 
                'python3.11', 'app/batch_jobs/jobs.py',
                '--input_args', input_arguments,  # Pass arguments directly
                '--stage', stage
            ]
            
            subprocess.run(
                screen_cmd,
                check=True,
                env=os.environ
            )
            
            mongo_db.db[tasks_collection_name].update_one(
                {"_id": job_name},
                {"$set": {
                    "job_id": job_name,
                    "project_id": project_id,
                    "session_name": session_name,
                    "description": description,
                    "llm_model": str(llm_model),
                    "agent_name": "CodeMaintenance",
                    "start_time": generate_timestamp(),
                    "status": "SUBMITTED",
                    "user_id": user_id,
                    "context": {},
                    "repositories": selected_repos,
                    "iframe": f"http://localhost:8080/?folder=/tmp/kavia/workspace/code-generation",
                }},
                upsert=True
            )
            
            response = {
                "task_id": job_name,
                "llm_model": llm_model,
                "message": "Job submitted successfully",
                "iframe": f"http://localhost:8080/?folder=/tmp/kavia/workspace/code-generation",
                "end": True
            }
            print("STREAMING RESPONSE ", response)
            yield format_response_for_stream(response)
            return
        else:
            
            stage_params = 'dev' if stage == 'develop' else stage

            unique_id = tenant_id + str(project_id)
            # Assign a pod to this project
            start_assign_time = time.time()
            # Always assign a new pod for each session
            # Use findOneAndDelete for atomic operation

            selected_pod = kubernetes_manager.get_one_available_pod_and_mark_as_used(job_name, tenant_id)
            
            if not selected_pod:
                yield format_response_for_stream({'error': 'No available pods found', 'end': True})
                return
            
            pod_name = selected_pod.get("project_id", "")
            
            

            
            pod_prefix = pod_name
            
            
            assign_time = time.time() - start_assign_time
            yield format_response_for_stream({'message': f'Pod {pod_name} assigned in {assign_time:.2f} seconds'})
            
            
            # Continue with the same workflow as in the else block
            print("Instance name : ", pod_name)
            
            
            if stage_params == "dev" or stage_params=="qa":
                pod_url = get_pod_url(stage_params, pod_prefix)
                vs_code_complete_url = f"{pod_url}/?folder=/home/<USER>/workspace/{job_name}"
                application_preivew_url = pod_url
            
            elif stage_params == "pre_prod":
                vs_code_complete_url = f"https://vscode-internal-{pod_prefix}-beta.beta01.cloud.kavia.ai/?folder=/home/<USER>/workspace/{job_name}"
                application_preivew_url = f"https://vscode-internal-{pod_prefix}-beta.beta01.cloud.kavia.ai"
            
            planned_job_id = job_name
            anticipate_message_dict ={
                "message":"install",
                "planned_job_id":planned_job_id,
                "end":False
            }   
            yield format_response_for_stream(anticipate_message_dict)
            # for stage params and variables
            active_sessions = await get_active_code_maintenance_sessions(project_id)
            params = f'?project_id={project_id}&task_id={job_name}&stage={stage_params}&llm_model={llm_model}&tenant_id={tenant_id}&agent_name=CodeMaintenance&platform=common&user_id={user_id}'
            
            if len(active_sessions) >= MAX_CODE_MAINTENANCE_SESSIONS:
                error_message = f"Maximum number of active code maintenance sessions reached. Code maintenance sessions are limited to {MAX_CODE_MAINTENANCE_SESSIONS} at a time. ({len(active_sessions)}/{MAX_CODE_MAINTENANCE_SESSIONS}). Please close some sessions and try again."
                active_sessions = json.dumps(active_sessions)
                yield format_response_for_stream({'error':error_message, 'active_sessions':active_sessions,'end':True})
            else:
                ip = '127.0.0.1'
                yield format_response_for_stream({'message':'install|provision|config'})
                iframe_url = vs_code_complete_url
                ports = [{f'{port}':vs_code_complete_url} for port in [3000,5000,8000]]
                
                mongo_db.db[tasks_collection_name].update_one(
                    {"_id": job_name},
                    {"$set": {
                        "job_id": job_name,
                        "stage": stage_params,
                        "project_id": project_id,
                        "session_name": session_name,
                        "description": description,
                        "llm_model": str(llm_model),
                        "agent_name": "CodeMaintenance",
                        "status": "SUBMITTED",
                        "user_id": user_id,
                        "start_time": generate_timestamp(),
                        "ip": ip,
                        "iframe": iframe_url,
                        "ports": ports,
                        "context": {},
                        "params":params,
                        "repositories": selected_repos,
                        "application_preivew_url": application_preivew_url,
                    }},
                    upsert=True
                )
                
                # Maximum number of retries
                MAX_RETRIES = 15  
                # Delay between retries in seconds
                RETRY_DELAY = 2   
                    
                start_maintenance_url = get_codegen_url(stage_params, pod_prefix=pod_prefix)
                start_maintenance_url = f'{start_maintenance_url}/start'+ params
                    
                if stage_params == "pre_prod":
                    stage_params="beta"
                    start_maintenance_url = f'http://internal-{pod_prefix}-beta.duploservices-k-beta01.svc.cluster.local:8003/api/code_gen/start'+ params
                print("Calling", start_maintenance_url)

                connected = False
                attempts = 0
                while not connected and attempts < MAX_RETRIES:
                    try:
                        resp = requests.get(start_maintenance_url, verify=False, timeout=5)
                        
                        if resp.status_code == 200:
                            connected = True
                            print(f"Connection successful after {attempts+1} attempts")
                        else:
                            attempts += 1
                            print(f"Attempt {attempts}/{MAX_RETRIES}: Connection failed with status code {resp.status_code}")
                            yield format_response_for_stream({
                                'message':'install|provision|config',
                                'error': f'Connection attempt {attempts}/{MAX_RETRIES} failed. Status code: {resp.status_code}. Retrying...'
                            })
                            time.sleep(RETRY_DELAY)
                    except requests.RequestException as e:
                        attempts += 1
                        print(f"Attempt {attempts}/{MAX_RETRIES}: Connection error: {str(e)}")
                        yield format_response_for_stream({
                            'message':'install|provision|config',
                            'error': f'Connection attempt {attempts}/{MAX_RETRIES} failed. Error: {str(e)}. Retrying...'
                        })
                        time.sleep(RETRY_DELAY)

                if connected:
                    retry_count = 0
                    max_retries = 15
                    success = False
                    
                    while retry_count < max_retries and not success:
                        try:
                            resp = requests.get(iframe_url, verify=False, timeout=5)
                            if resp.status_code == 200:
                                success = True
                                print(f"Debug: Connection successful on attempt {retry_count+1}")
                            else:
                                error = f"Debug: Got status code {resp.status_code} on attempt {retry_count+1}"
                                retry_count += 1
                                time.sleep(1)  # Add delay between retries
                                
                                yield format_response_for_stream({
                                    "debug_error": error
                                })
                                
                        except Exception as e:
                            print(f"Debug_error: Connection failed on attempt {retry_count+1} with error: {str(e)}")
                            retry_count += 1
                            time.sleep(1)  # Add delay between retries
                    
                    if success:
                        yield format_response_for_stream({
                            'message': 'install|provision|config|final',
                            "task_id": job_name,
                            "ip": ip,
                            "iframe": iframe_url,
                            "ports": ports,
                            'end': True
                        })
                        
                    else:
                        yield format_response_for_stream({
                            'message': 'Connection failed after 15 attempts. Please try to reconnect with VSCode.',
                            "task_id": job_name,
                            "ip": ip,
                            "debug_error": f"Failed to connect to {iframe_url} after 15 attempts",
                            'end': True
                        })
                else:
                    yield format_response_for_stream({
                        'message': f'Failed to connect after {MAX_RETRIES} attempts. Please check the service status.',
                        "task_id": job_name,
                        "ip": ip,
                        "iframe": iframe_url,
                        "ports": ports,
                        'end': True
                    })
                    
                if stage_params == "dev":
                    change_to_used_for_dev(pod_prefix)
                elif stage_params == "qa":
                    change_to_used_for_qa(pod_prefix)
                elif stage_params == "beta":
                    change_to_used_for_beta(pod_prefix)
                    
                return
       
    except Exception as e:
        print(f"Error in stream_code_maintanence: {str(e)}")
        error_data = json.dumps({"error": str(e), "end": True})
        yield f"data: {error_data}\n\n"
        return

@router.post("/start_code_maintenance/{project_id}/")
async def submit_code_maintanence_job_opt(
        project_id: int,
        selectedrepos: dict = Body(...),
        session_name: str = Body(default="Untitled"),
        description: str = Body(default="Untitled"),
        background_tasks: BackgroundTasks = BackgroundTasks(),
        mongo_db: MongoDBHandler = Depends(get_mongo_db),
        db: NodeDB = Depends(get_node_db),
        current_user = Depends(get_current_user)
):
        print("steaming ...")
        return StreamingResponse(
        stream_code_maintanence(project_id,selectedrepos,session_name,description,background_tasks,mongo_db,db,current_user),
        media_type="text/event-stream",
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # This is for Nginx specifically
        }
    )


async def get_active_deep_query_task(project_id: int):
    """Get the last active code maintenance task for a project"""
    task_repository: TaskRepository = await get_mongo_db_v1("task", table_name=tasks_collection_name)
    tenant_id = get_tenant_id()
    db = await get_db(f"{settings.MONGO_DB_NAME}_{tenant_id}")
    
    # Query for active task
    query = {
        "project_id": project_id,
        "agent_name": "DocumentCreation",
        "status": {
            "$regex": "^(submitted|started|pending|running|paused|in_progress)$",
            "$options": "i"
        }
    }
    projection = {
        "_id": 1, 
        "job_id": 1, 
        "status": 1, 
        "iframe": 1, 
        "ip": 1
    }
    
    active_task = await task_repository.find_one(query, db, projection)
    
    if active_task:
        try:
            job_id = active_task.get("_id")
            active_task['task_id'] = str(job_id)
            return active_task
        except:
            return {}
            
    
    # Query for previous completed task
    prev_query = {
        "project_id": project_id,
        "agent_name": "DocumentCreation",
        "status": "COMPLETE"
    }
    previous_task = await task_repository.find_one(
        prev_query, 
        db
    )
    
    return {
        "message": "No active maintenance task found",
        "prev_task_id": str(previous_task.get("_id")) if previous_task else None
    }
    
async def stream_deep_query(
        project_id: int,
        selected_repos: dict,
        background_tasks: BackgroundTasks = BackgroundTasks(),
        mongo_db: MongoDBHandler = Depends(get_mongo_db),
        node_db: NodeDB = Depends(get_node_db),
        current_user = Depends(get_current_user)
        ):
    """
    Generator function to stream workspace status as Server-Sent Events (SSE)
    """
    try:
        tenant_id = get_tenant_id()
        job_name = f"deep-query-job-{uuid.uuid4().hex[:8]}"
        print(selected_repos)
        
        # Copy the file to tmp folder        
        # temp_workspace = setup_workspace(selected_repos, project_id, tenant_id, job_name)
        
        # print("Temp workspace : ", temp_workspace)
        
        stage = get_stage(settings)
        user_id = current_user.get('cognito:username')
        model_config = await get_module_configuration_for_user(module_name='code_generation', user_id=user_id)
        # if model_config:
        #     llm_model = model_config.get('llm_model', LLMModel.gpt_4o_mini.value )
        # else:
        #     llm_model = LLMModel.gpt_4o_mini.value
        llm_model = LLMModel.bedrock_claude_3_7_sonnet.value
        
        try:
            # Get session tracker
            tracker = get_session_tracker()
            
            # Initialize session tracking
            session_result = await tracker.initialize_session(
                task_id=job_name,  # This is your task_id
                tenant_id=tenant_id,
                user_id=user_id,
                project_id=project_id,
                service_type="deep-query",
                session_data={
                    "llm_model": llm_model,
                }
            )
            
            if session_result["success"]:
                print(f"Session tracking initialized for task_id: {job_name}")
            else:
                print(f"Failed to initialize session tracking: {session_result['error']}")
                    
        except Exception as session_error:
            print(f"Session tracking initialization error: {session_error}")
            
        # For local debug
        if os.environ.get("LOCAL_DEBUG"):
            response = run_local_deep_query(project_id,selected_repos,job_name,stage,llm_model, background_tasks,mongo_db, tenant_id, user_id=user_id)
            response['end'] = True
            yield format_response_for_stream(response)
        else:
            ip = '127.0.0.1'
            # for stage params and variables
            stage_params = 'dev' if stage == 'develop' else stage
            params = f'?project_id={project_id}&task_id={job_name}&stage={stage_params}&llm_model={llm_model}&tenant_id={tenant_id}&agent_name=DocumentCreation&platform=common&user_id={user_id}'
            
            yield format_response_for_stream({'message':'install|provision|config', 'end': False} )
            
            stage_params = 'dev' if stage == 'develop' else stage
            
            db_start_time = time.time()
            unique_id = tenant_id + str(project_id)
            # Assign a pod to this project
            start_assign_time = time.time()
            # Always assign a new pod for each session
            # Use findOneAndDelete for atomic operation
            selected_pod = kubernetes_manager.get_one_available_pod_and_mark_as_used(job_name, tenant_id)
            
            if not selected_pod:
                yield format_response_for_stream({'error': 'No available pods found', 'end': True})
                return
            
            pod_name = selected_pod.get("project_id", "")
            
            
            
            pod_prefix = pod_name
            
            assign_time = time.time() - start_assign_time
            yield format_response_for_stream({'message': f'Pod {pod_name} assigned in {assign_time:.2f} seconds'})
            

            if stage_params == "dev" or stage_params== "qa":
                pod_url = get_pod_url(stage_params,pod_prefix=pod_prefix)
                vs_code_complete_url = f"{pod_url}/?folder=/home/<USER>/workspace/{job_name}"
                application_preivew_url = pod_url
                
            elif stage_params == "pre_prod":
                vs_code_complete_url = f"https://vscode-internal-{pod_prefix}-beta.beta01.cloud.kavia.ai/?folder=/home/<USER>/workspace/{job_name}"
                application_preivew_url = f"https://vscode-internal-{pod_prefix}-beta.beta01.cloud.kavia.ai"

            iframe_url = vs_code_complete_url

            ports = [{f'{port}':vs_code_complete_url} for port in [3000,5000,8000]]
            mongo_db.db[tasks_collection_name].update_one({"_id": job_name},{
                "$set":{
                "job_id": job_name,
                "project_id": project_id,
                "agent_name": "DocumentCreation",
                "status": "SUBMITTED",
                "ip":ip,
                "iframe":iframe_url,
                "ports":ports,
                "context": {},
                "stage": stage_params,
                "llm_model":llm_model,
                "user_id": user_id,
                "start_time": generate_timestamp(),
                "repositories": selected_repos,
                "params": params,
                "application_preivew_url": application_preivew_url,
            }},upsert=True)
                            # Maximum number of retries
            MAX_RETRIES = 15  
            # Delay between retries in seconds
            RETRY_DELAY = 2   
                
            start_query_url = get_codegen_url( stage_params, pod_prefix=pod_prefix)
            start_query_url = f'{start_query_url}/start'+ params
            
            if stage_params == "pre_prod":
                stage_params= 'beta'
                start_query_url = f'http://internal-{pod_prefix}-beta.duploservices-k-beta01.svc.cluster.local:8003/api/code_gen/start'+ params
            print("Calling", start_query_url)

            connected = False
            attempts = 0
            while not connected and attempts < MAX_RETRIES:
                try:
                    resp = requests.get(start_query_url, verify=False, timeout=5)
                    
                    if resp.status_code == 200:
                        connected = True
                        print(f"Connection successful after {attempts+1} attempts")
                    else:
                        attempts += 1
                        print(f"Attempt {attempts}/{MAX_RETRIES}: Connection failed with status code {resp.status_code}")
                        yield format_response_for_stream({
                            'message':'install|provision|config',
                            'error': f'Connection attempt {attempts}/{MAX_RETRIES} failed. Status code: {resp.status_code}. Retrying...',
                            'end': False
                        })
                        time.sleep(RETRY_DELAY)
                except requests.RequestException as e:
                    attempts += 1
                    print(f"Attempt {attempts}/{MAX_RETRIES}: Connection error: {str(e)}")
                    yield format_response_for_stream({
                        'message':'install|provision|config',
                        'error': f'Connection attempt {attempts}/{MAX_RETRIES} failed. Error: {str(e)}. Retrying...',
                        'end': False
                    })
                    time.sleep(RETRY_DELAY)

            if connected:
                retry_count = 0
                max_retries = 15
                success = False
                
                while retry_count < max_retries and not success:
                    try:
                        resp = requests.get(iframe_url, verify=False, timeout=5)
                        if resp.status_code == 200:
                            success = True
                            print(f"Debug: Connection successful on attempt {retry_count+1}")
                        else:
                            error = f"Debug: Got status code {resp.status_code} on attempt {retry_count+1}"
                            retry_count += 1
                            time.sleep(1)  # Add delay between retries
                            
                            yield format_response_for_stream({
                                "debug_error": error
                            })
                            
                    except Exception as e:
                        print(f"Debug_error: Connection failed on attempt {retry_count+1} with error: {str(e)}")
                        retry_count += 1
                        time.sleep(1)  # Add delay between retries
                
                if success:
                    yield format_response_for_stream({
                        'message': 'install|provision|config|final',
                        "task_id": job_name,
                        "ip": ip,
                        "iframe": iframe_url,
                        "ports": ports,
                        'end': True
                    })
                    
                else:
                    yield format_response_for_stream({
                        'message': 'Connection failed after 15 attempts. Please try to reconnect with VSCode.',
                        "task_id": job_name,
                        "ip": ip,
                        "debug_error": f"Failed to connect to {iframe_url} after 15 attempts",
                        'end': True
                    })
            else:
                yield format_response_for_stream({
                    'message': f'Failed to connect after {MAX_RETRIES} attempts. Please check the service status.',
                    "task_id": job_name,
                    "ip": ip,
                    "iframe": iframe_url,
                    "ports": ports,
                    'end': True
                })
            
            if stage_params == "dev":
                change_to_used_for_dev(pod_prefix)
            elif stage_params == "qa":
                change_to_used_for_qa(pod_prefix)
            elif stage_params == "beta":
                change_to_used_for_beta(pod_prefix)
                    
            return
        
                
    except Exception as e:
        print(f"Error in stream_start_workspace_status: {str(e)}")
        error_data = json.dumps({"error": str(e), "end": True})
        yield f"data: {error_data}\n\n"
        
@router.post("/start_deep_query/{project_id}/")
async def submit_deep_query_job_opt(
        project_id: int,
        selected_repos: dict = Body(...),
        background_tasks: BackgroundTasks = BackgroundTasks(),
        mongo_db: MongoDBHandler = Depends(get_mongo_db),
        db: NodeDB = Depends(get_node_db),
        current_user = Depends(get_current_user)
    ): 
    return StreamingResponse(
        stream_deep_query(project_id,selected_repos,background_tasks,mongo_db,db,current_user),
        media_type="text/event-stream",
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # This is for Nginx specifically
        }
    )
    
@router.patch("/update_task/{task_id}")
async def update_task(
    task_id: str,
    update_data: dict = Body(...),
    mongo_db: MongoDBHandler = Depends(get_mongo_db)
):
    """
    Update specific fields of a task by its ID.
    """
    try:
        # Remove any attempt to update _id field as it's immutable
        if '_id' in update_data:
            del update_data['_id']

        result = mongo_db.db[tasks_collection_name].update_one(
            {'_id': task_id},
            {'$set': update_data}
        )

        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="Task not found")

        if result.modified_count == 0:
            return {
                "message": "No changes were made to the task",
                "task_id": task_id
            }

        return {
            "message": "Task updated successfully",
            "task_id": task_id,
            "modified_count": result.modified_count
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error updating task: {str(e)}"
        )

 
@router.post("/retry_code_generation/{task_id}")
async def retry_code_gen(
    task_id: str,
    mongo_db: MongoDBHandler = Depends(get_mongo_db),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    current_user = Depends(get_current_user) 
):
    try:
        tenant_id = get_tenant_id()
        stage = get_stage(settings)
        os.environ["BATCH_JOB_TRIGGER"] = "True"
        os.environ["FEATURE_FLAG_GIT_TOOL"] = "True"
        os.environ["FEATURE_FLAG_USE_DOCKER"] = "True"  
        os.environ["BATCH_JOB_STAGE"] = stage
        user_id = current_user.get("cognito:username")
        task = mongo_db.db[tasks_collection_name].find_one({"_id": task_id})
        model_config = await get_module_configuration_for_user(module_name='code_generation', user_id=user_id)
        print("Model", model_config)
        if model_config:
            llm_model = model_config.get('llm_model', LLMModel.gpt_4o_mini.value )
        else:
            llm_model = LLMModel.gpt_4o_mini.value
            
        llm_model = LLMModel.bedrock_claude_3_7_sonnet.value
        
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        if os.environ.get("LOCAL_DEBUG"):
            input_arguments = json.dumps({
                "task_id": task_id,
                "tenant_id": tenant_id,
                "project_id": task.get("project_id"),
                "retry": True
            })
            mongo_db.db[tasks_collection_name].update_one(
                    {"_id": task_id},
                    {"$set": {
                        "status": "SUBMITTED",
                        "tenant_id": tenant_id,
                        "project_id": task.get("project_id"),
                        "failure_reason": "",
                        "llm_model":llm_model
                    }},
                    upsert=True
                )
            os.environ["input_arguments"] = input_arguments
            os.environ["task_id"] = task_id
            # use screen to run the job with log
            subprocess.run(["screen", "-L", "-Logfile", f"logs/{task_id}.log", "-dmS", task_id, "python", "app/batch_jobs/jobs.py"], env=os.environ)
        else:
            handler = Ec2Handler()
            # Accumulate all responses from get_project
            accumulated_responses = []
            ip_address = None
            
            async for response in handler.get_project(
                project_id=str(task.get("project_id")), 
                stage=get_stage(settings)
            ):
                accumulated_responses.append(response)
                if 'ip' in response:
                    ip_address = response['ip']
                if response.get('end', False):
                    break
            
            if ip_address:
                # Update mongo with the instance information
                iframe_url = f'https://workspace.develop.kavia.ai/?ip={ip_address}&port=8080&folder=/home/<USER>/workspace/'
                ports = [{f'{port}':f'https://workspace.develop.kavia.ai/?ip={ip_address}&port={port}'} for port in [3000,5000,8000]]
                params = f'?project_id={task.get("project_id")}&architecture_id={task.get("architecture_id")}&container_id={task.get("container_id")}&task_id={task_id}&stage={get_stage(settings=settings)}&llm_model={llm_model}&tenant_id={tenant_id}'
                mongo_db.db[tasks_collection_name].update_one(
                    {"_id": task_id},
                    {"$set": {
                        "status": "SUBMITTED",
                        "failure_reason": "",
                        "llm_model":llm_model,
                        "ip": ip_address,
                        "iframe": iframe_url,
                        "ports": ports,
                        "last_retry_time": generate_timestamp(),
                        "retry": True,
                        "tenant_id": tenant_id
                    }},
                    upsert=True
                )
                start_code_generation_url = f'https://8765_{ip_address.replace(".","_")}.workspace.develop.kavia.ai/retry'+ params
                print("Calling", start_code_generation_url)
                resp = requests.get(start_code_generation_url, verify=False)
                
                return {
                    "task_id": task_id,
                    "message": "Retry Initiated",
                    "status": "success",
                    "ip": ip_address,
                    "iframe": iframe_url,
                    "ports": ports,
                    "responses": accumulated_responses
                }
            else:
                raise HTTPException(
                    status_code=500,
                    detail="Failed to get instance IP address"
                )
                
        return {
            "task_id": task_id,
            "message": "Retry Initiated",
            "status": "submitted"
        }

    except TimeoutError:
        raise HTTPException(
            status_code=504, 
            detail="Timeout waiting for instance to be ready"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error during retry: {str(e)}"
        )

@router.post("/retry/{task_id}")
async def retry_task(
    task_id: str,
    mongo_db: MongoDBHandler = Depends(get_mongo_db)
):
    #TODO: Need to clean this endpoint.
    return {
            "message": "Task retried successfully",
            "status": "success"
        }
    
    task = mongo_db.db[tasks_collection_name].find_one({"_id": task_id})
    retry_count = 0
    MAX_RETRIES = 3
    if task: 
        while retry_count < MAX_RETRIES:
            ip = task.get('ip')
            params = task.get('params')  # Get the stored params
            
            if ip and params:
                
                start_url = f'{CODEGEN_URL}/api/code_gen/start'+ params
                print(f"Retrying start URL (attempt {retry_count + 1}): {start_url}")
                response = requests.get(start_url, verify=False, timeout=10)
                if response.status_code == 200:
                    # Mark the old messages that needs_response as false
                    mongo_db.db[tasks_collection_name].update_one(
                        {"_id": task_id},
                        {"$set": {"status": "SUBMITTED",
                                  "failure_reason": "",
                                  "retry": True,
                                  "last_retry_time": generate_timestamp(),
                                  
                                  }}
                    )
                    messages = task.get('messages', [])
                    for message in messages:
                        try:
                            if message.get("status", "") == "needs_response" or message.get("status", "") == "pending":
                                message["status"] = "completed"
                        except:
                            pass
                    
                    mongo_db.db[tasks_collection_name].update_one(
                        {"_id": task_id},
                        {"$set": {"messages": messages}}
                    )
                    return {
                        "message": "Task retried successfully",
                        "status": "success"
                    }
                else:
                    return {
                        "message": "Failed to retry task",
                        "status": "failed"
                    }
            
    return task

@router.get("/task_status/{task_id}")
async def task_status(
    task_id: str,
    mongo_db: MongoDBHandler = Depends(get_mongo_db)
):
    try:
        response = mongo_db.db[tasks_collection_name].find_one({'_id':task_id},
                                                               {'_id':1,'job_id':1,'status':1,'iframe':1,'ip':1,'architecture_id':1, 'session_name':1, 'preview_url':1,'preview_url_status':1 })
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get job status: {str(e)}")

@router.patch("/user_input/{task_id}")
async def update_user_input(task_id: str, user_input_request: UserInputRequest, mongo_db:MongoDBHandler =Depends(get_mongo_db)):
    """Streams updates for a configuration task."""
    
    user_input_dict = user_input_request.model_dump_json()
    user_input_dict = json.loads(user_input_dict)
    user_message = Message(content=user_input_dict.get("user_input"), sender="User")
    task = mongo_db.db[tasks_collection_name].find_one_and_update(
        {
            "_id": task_id
        },
        {
            "$set": {
                "user_input": user_input_dict.get("user_input"),
                "waiting_for_user": False
            },
            "$push": {"messages": user_message.to_dict()}
                   
        }
    )
    return {
        "message": "User input updated successfully"
    }


@router.patch("/control/{task_id}")
async def control_task(
    task_id: str, 
    control: str, 
    project_id: int, 
    commit_hash: str="", 
    mongo_db: MongoDBHandler = Depends(get_mongo_db)
):
    task = mongo_db.db[tasks_collection_name].find_one({"_id": task_id})
    is_maintenance = task.get("agent_name") == "CodeMaintenance"
    is_document_creation = task_id.startswith("deep-query-job")
    
    if control.casefold() == "pause":
        mongo_db.db[tasks_collection_name].update_one(
            {"_id": task_id},
            {"$set": {"status": "PAUSED"}}
        )
        return {"message": "Task paused successfully"}
        
    elif control.casefold() == "resume":
        mongo_db.db[tasks_collection_name].update_one(
            {"_id": task_id},
            {"$set": {"status": "RUNNING"}}
        )
        return {"message": "Task resumed successfully"}
        
    elif control.casefold() == "stop":
            
        # Update query based on task type
        update_query = {
            "project_id": project_id,
            "status": {
                "$regex": "^(submitted|started|pending|running|paused|in_progress)$",
                "$options": "i"
            }
        }
        if is_maintenance:
            update_query["_id"] = task_id
            update_query["agent_name"] = "CodeMaintenance"
        elif is_document_creation:
            update_query["agent_name"] = "DocumentCreation"
        else:
            update_query["container_id"] = task.get("container_id")
            
        mongo_db.db[tasks_collection_name].update_many(
            update_query,
            {"$set": {"status": TaskStatus.STOPPED.value}}
        )
        
        try:
            _ip = task.get('ip')
            
            if _ip:
                # stop_url = f"https://8765_{_ip.replace('.','_')}.workspace.develop.kavia.ai/stop"
                base_url =  get_codegen_url( stage=task.get("stage"),pod_prefix=task.get("pod_prefix"))
                stop_url = f'{base_url}/stop'

                if not is_maintenance and not is_document_creation:
                    stop_url += f"?container_id={task.get('container_id')}"
                else:
                    stop_url += f"?project_id={project_id}&task_id={task_id}&agent_name={task.get('agent_name')}"
                print("Calling", stop_url)
                result = requests.get(stop_url, timeout=10, verify=False)
                response = result.json()
                print("Stop response:", response)
                return response
            else:
                return 
        except:
            return 

    elif control.casefold() == "rollback":
        mongo_db.db[tasks_collection_name].update_one(
            {"_id": task_id},
            {"$set": {
                "status": "ROLLBACK",
                "commit_hash": commit_hash
            }}
        )
        return {"message": "Rollback initiated successfully"}
 
@router.post("/merge/{task_id}")
async def merge(
    task_id: str,
    pr_request: CreatePRRequest,
    db: NodeDB = Depends(get_node_db),
    mongo_db: MongoDBHandler = Depends(get_mongo_db)
):
    """Create a pull request for merging code changes"""
    try:
        # Get task details
        task = mongo_db.db[tasks_collection_name].find_one({'_id': task_id})
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        # Get repository details from project
        project_id = task.get('project_id')
        project = await db.get_node_by_id(project_id)
        project_details = project.get("properties", {})
        
        try:
            project_repositories = json.loads(project_details.get("repositories", "{}"))
        except:
            project_repositories = {}
            
        container_id = task.get('container_id')
        repository_metadata = project_repositories.get(str(container_id))

        if not repository_metadata:
            raise HTTPException(status_code=404, detail="Repository not found")

        # Get work item details
        work_item_details = task.get('work_item_details')
        if isinstance(work_item_details, str):
            work_item_details = json.loads(work_item_details)
        elif not work_item_details:
            work_item_details = {}

        component_name = work_item_details.get('component_name', '')
        if not component_name:
            raise HTTPException(status_code=400, detail="Component name not found in work item details")
            
        source_branch = f"feature/{name_to_slug(component_name)}"

        # Initialize enhanced GitTools
        git_tool = EnhancedGitTools(
            callback_functions=None,
            base_path=get_codegeneration_path(),
            logger=None,
            access_token=settings.GITHUB_ACCESS_TOKEN,
            aws_credentials={
                'region': settings.AWS_REGION,
                'access_key_id': settings.AWS_ACCESS_KEY_ID,
                'secret_access_key': settings.AWS_SECRET_ACCESS_KEY
            }
        )

        # Create PR title and description
        default_title = f"Merge {source_branch} into {pr_request.destination_branch}"
        default_description = (
            f"Automated PR created for task {task_id}\n\n"
            f"Component: {component_name}\n"
            f"Source Branch: {source_branch}\n"
            f"Target Branch: {pr_request.destination_branch}\n"
            f"Created At: {generate_timestamp()}"
        )

        pr_result = git_tool.create_pull_request(
            source_branch=source_branch,
            target_branch=pr_request.destination_branch,
            title=pr_request.title or default_title,
            description=pr_request.description or default_description,
            repository_path=get_codegeneration_path(),
            repository_metadata=repository_metadata
        )
        
        if isinstance(pr_result, str) and "Error" in pr_result:
            raise Exception(pr_result)
        
        # Update task with PR details
        pr_details = {
            "pr_number": pr_result.get("number"),
            "pr_url": pr_result.get("url"),
            "title": pr_request.title or default_title,
            "description": pr_request.description or default_description,
            "source_branch": source_branch,
            "target_branch": pr_request.destination_branch,
            "status": "created",
            "created_at": generate_timestamp()
        }

        mongo_db.db[tasks_collection_name].update_one(
            {"_id": task_id},
            {
                "$set": {
                    "pr_details": pr_details
                }
            }
        )
        
        return {
            "message": "Pull request created successfully",
            "pr_details": pr_details
        }

    except Exception as e:
        error_message = f"Failed to create PR: {str(e)}"
        print(f"Error details: {error_message}")
        
        # Update task with error details
        mongo_db.db[tasks_collection_name].update_one(
            {"_id": task_id},
            {
                "$set": {
                    "pr_error": {
                        "error": error_message,
                        "timestamp": generate_timestamp()
                    }
                }
            }
        )
        raise HTTPException(status_code=500, detail=error_message)

     
@router.get("/past_code_generation_tasks/{project_id}")
async def get_past_code_generation_tasks(
    project_id: int, 
    container_id: Optional[int] = Query(None),
    limit: int = Query(10, ge=1, le=100),
    skip: int = Query(0, ge=0),
    mongo_db: MongoDBHandler = Depends(get_mongo_db)
):
    """
    Retrieve past code generation tasks for a given project_id.
    Optionally filter by architecture_id.
    """
    query = {
        "project_id": project_id
    }
    
    if container_id is not None:
        query["container_id"] = container_id
    
    tasks = list(mongo_db.db[tasks_collection_name].find(
        query,
        {
            "_id": 1,
            "job_id": 1,
            "status": 1,
            "project_id":1,
            "start_time":1,
            "container_id": 1,
            "session_id":1,
            "description":1,
            "messages":1,
            
        }
    ).sort("start_time", -1).skip(skip).limit(limit))

    total_count = mongo_db.db[tasks_collection_name].count_documents(query)

    return {
        "tasks": tasks,
        "total_count": total_count,
        "limit": limit,
        "skip": skip
    }
       


@router.get("/callback/{callback_type}/{task_id}")
async def get_callback_output(
    callback_type: str,
    task_id: str,
    mongo_db: MongoDBHandler = Depends(get_mongo_db)
):
    print("Callback:", callback_type)
    if callback_type == "function_calls":
        return StreamingResponse(
            function_calls_stream(task_id, mongo_db),
            media_type="text/event-stream",
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'X-Accel-Buffering': 'no'
            }
        )
    elif callback_type == "browser":
        return StreamingResponse(browser_stream(task_id, mongo_db), media_type="text/event-stream" , headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # This is for Nginx specifically
        })
    elif callback_type == "document":
        return StreamingResponse(get_documents(task_id, mongo_db), media_type="text/event-stream" , headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # This is for Nginx specifically
        })
    elif callback_type == "messages":
        return StreamingResponse(messages_stream(task_id, mongo_db), media_type="text/event-stream" , headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # This is for Nginx specifically
        })
    elif callback_type == "terminal":
        return StreamingResponse(terminal_stream(task_id, mongo_db), media_type="text/event-stream" , headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # This is for Nginx specifically
        })
    elif callback_type == "past_steps":
        return StreamingResponse(past_steps_stream(task_id, mongo_db), media_type="text/event-stream" , headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # This is for Nginx specifically
        })

        
    elif callback_type == "status":
        return StreamingResponse(status_stream(task_id, mongo_db), media_type="text/event-stream" , headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # This is for Nginx specifically
        })
        
    elif callback_type == "task_plan":
        return StreamingResponse(task_plan_stream(task_id, mongo_db), media_type="text/event-stream" , headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # This is for Nginx specifically
        })
    elif callback_type == "code_server":
        task = mongo_db.db[tasks_collection_name].find_one({
            '_id': task_id
        },
        {
            'status': 1,
            'iframe': 1,
        })
        
        return {
            "iframe": task.get('iframe', "")
        }
        
    elif callback_type == "llm_cost":
        return StreamingResponse(llm_cost_stream(task_id, mongo_db), media_type="text/event-stream" , headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # This is for Nginx specifically
        })
    
    elif callback_type == "steps":
        return StreamingResponse(steps_stream(task_id, mongo_db), media_type="text/event-stream" , headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # This is for Nginx specifically
        })
    
async def get_documents(task_id: str, mongo_db: MongoDBHandler):
    # With heartbeat
    continue_stream = True
    previous_output = []
    while continue_stream:
        tasks = mongo_db.db[tasks_collection_name].find_one({
            '_id': task_id
        })
        if not tasks:
            yield f"data: {json.dumps({'error': 'Task not found!!'})}\n\n"
            return

        base_path = f"/tmp/kavia/workspace/{task_id}/docs"

        documents = {}
        total_count = 0
        
        for root, dirs, files in os.walk(base_path):
            
            # Get relative path from base_path
            rel_path = os.path.relpath(root, base_path)
            # Use '.' for base directory
            rel_path = base_path if rel_path == '.' else os.path.join(base_path, rel_path)

            # Filter .md and .mmd files
            md_files = [f for f in files if f.endswith(('.md', '.mmd'))]

            if md_files:
                documents[rel_path] = md_files
                total_count += len(md_files)

        print(documents)
        yield f"data: {json.dumps({'documents': documents})}"
        
        if tasks.get('status','').casefold() in [TaskStatus.FAILED.casefold(), TaskStatus.CANCELLED.lower(), TaskStatus.COMPLETE.casefold(), TaskStatus.PAUSED.casefold()]:
            continue_stream = False
            yield f"data: {json.dumps({'documents': {}, 'base_path': base_path, 'total_count': total_count})}\n\n"            
            return
        await asyncio.sleep(3)
def get_docs_folder( task_id):
    if(task_id.startswith("deep-query")):
        return f"deep_query_docs/{task_id}"
    elif(task_id.startswith("cg")):
        return f"code_maintenance_docs/{task_id}"
    else:
        return f"code_generation_docs/{task_id}"

@router.get("/list-document-from-s3")
async def get_document_from_s3(task_id:str):
    try:
        tenant_id = get_tenant_id()
        docs_folder = get_docs_folder(task_id)
        s3_handler = S3Handler(tenant_id, folder_name= docs_folder)
        documents = s3_handler.list_all_filenames(docs_folder)
        if documents:
            return documents
        else:
            return []
    except Exception as e :
        raise HTTPException(status_code=500, detail=f"Failed to retrieve documents: {str(e)}")
@router.get('/retrive-document-content-from-s3')
async def get_document_content_from_s3(task_id :str,title:str):
    try:
        tenant_id = get_tenant_id()
        docs_folder = get_docs_folder(task_id)
        s3_handler = S3Handler(tenant_id, folder_name= docs_folder)
        content = s3_handler.get_file(title)
        if content:
            content = content.decode('utf-8')
            return content
        else:
            return []
    except Exception as e :
        raise HTTPException(status_code=500, detail=f"Failed to retrieve file content from s3: {str(e)}")
    


@router.get("/get_deep_query_documents/{task_id}")
async def get_deep_query_docs(task_id: str):
    base_path = f"/tmp/kavia/workspace/{task_id}/docs"

    documents = {}
    total_count = 0

    for root, dirs, files in os.walk(base_path):
        # Get relative path from base_path
        rel_path = os.path.relpath(root, base_path)
        # Use '.' for base directory
        rel_path = base_path if rel_path == '.' else os.path.join(base_path, rel_path)

        # Filter .md and .mmd files
        md_files = [f for f in files if f.endswith(('.md', '.mmd'))]

        if md_files:
            documents[rel_path] = md_files
            total_count += len(md_files)

    return {'documents': documents, "base_path": base_path, "total_count": total_count}


async def messages_stream(task_id: str, mongo_db: MongoDBHandler):
    # With heartbeat
    continue_stream = True
    previous_output = []
    
    while continue_stream:
        continue_stream = False
        tasks = mongo_db.db[tasks_collection_name].find_one({
            '_id': task_id
        })
        if not tasks:
            yield f"data: {json.dumps({'error': 'Task not found!!'})}\n\n"
            return
        messages = tasks.get('messages', [])
        waiting_for_user = tasks.get('waiting_for_user', False)
        
        messages_dict = {
            "messages": messages,
            "waiting_for_user": waiting_for_user
        }
        
        if previous_output != messages_dict:
            previous_output = messages_dict
            yield f"data: {json.dumps({'messages': messages_dict['messages'], 'waiting_for_user': messages_dict['waiting_for_user']})}\n\n"
        else:
            yield f"data: {json.dumps({'message': 'Waiting for updates...'})}\n\n"
        
        if tasks.get('status','').casefold() in [TaskStatus.FAILED.casefold(), TaskStatus.CANCELLED.lower(), TaskStatus.COMPLETE.casefold(), TaskStatus.PAUSED.casefold()]:
            continue_stream = False
            yield f"data: {json.dumps({'messages': messages_dict['messages'], 'waiting_for_user': messages_dict['waiting_for_user']})}\n\n"
            return


async def browser_stream(task_id: str, mongo_db: MongoDBHandler):
    # With heartbeat
    continue_stream = True
    previous_output = []
    while continue_stream:
        continue_stream = False
        tasks = mongo_db.db[tasks_collection_name].find_one({
            '_id': task_id
        },{
            'status': 1,
            'task_status': 1,
            'browser_output': 1,
        })
        if not tasks:
            yield f"data: {json.dumps({'error': 'Task not found!!'})}\n\n"
            return
        browser_output = tasks.get('browser_output', [])
        browser_output = browser_output[-1] if browser_output else ""
        if previous_output != browser_output:
            previous_output = browser_output
            yield f"data: {json.dumps({'browser_output': browser_output})}\n\n"
        else:
            yield f"data: {json.dumps({'message': 'Waiting for updates...'})}\n\n"
        
        if tasks.get('status','').casefold() in [TaskStatus.FAILED.casefold(), TaskStatus.CANCELLED.lower(), TaskStatus.COMPLETE.casefold(), TaskStatus.PAUSED.casefold()]:
            continue_stream = False
            yield f"data: {json.dumps({'browser_output': browser_output})}\n\n"
            return
  

async def function_calls_stream(task_id: str, mongo_db: MongoDBHandler):
    MAX_FUNCTION_CALLS = 25
    continue_stream = True
    previous_output = []

    while continue_stream:
        continue_stream = False
        # Updated MongoDB query to fetch only the last 25 function calls
        tasks = mongo_db.db[tasks_collection_name].find_one(
            {'_id': task_id},
            {
                'status': 1,
                'function_calls': 1,
                
            }
        )

        if not tasks:
            yield f"data: {json.dumps({'error': 'Task not found!!'})}\n\n"
            return

        function_calls = tasks.get('function_calls', [])
        if len(function_calls) > MAX_FUNCTION_CALLS:
            function_calls = function_calls[-MAX_FUNCTION_CALLS:]

        if previous_output != function_calls:
            previous_output = function_calls
            yield f"data: {json.dumps({'function_calls': function_calls})}\n\n"
        else:
            yield f"data: {json.dumps({'message': 'Waiting for updates...'})}\n\n"
        
        if tasks.get('status', '').casefold() in [TaskStatus.FAILED.casefold(), TaskStatus.CANCELLED.casefold(), TaskStatus.COMPLETE.casefold(), TaskStatus.PAUSED.casefold()]:
            continue_stream = False
            yield f"data: {json.dumps({'function_calls': function_calls})}\n\n"
            return

        
async def terminal_stream(task_id: str, mongo_db: MongoDBHandler):
    MAX_TERMINAL_OUTPUT = 50
    continue_stream = True
    previous_output = []

    while continue_stream:
        continue_stream = False
        tasks = mongo_db.db[tasks_collection_name].find_one(
            {'_id': task_id},
            {
                'status': 1,
                'terminal_output': 1
            }
        )

        if not tasks:
            yield f"data: {json.dumps({'error': 'Task not found!!'})}\n\n"
            return

        terminal_output = tasks.get('terminal_output', [])
        if len(terminal_output) > MAX_TERMINAL_OUTPUT:
            terminal_output = terminal_output[-MAX_TERMINAL_OUTPUT:]
        
        if previous_output != terminal_output:
            previous_output = terminal_output
            yield f"data: {json.dumps({'terminal_output': terminal_output})}\n\n"
        else:
            yield f"data: {json.dumps({'message': 'Waiting for updates...'})}\n\n"
        
        if tasks.get('status', '').casefold() in [TaskStatus.FAILED.casefold(), TaskStatus.CANCELLED.casefold(), TaskStatus.COMPLETE.casefold(), TaskStatus.PAUSED.casefold()]:
            continue_stream = False
            yield f"data: {json.dumps({'terminal_output': terminal_output})}\n\n"
            return


async def past_steps_stream(task_id: str, mongo_db: MongoDBHandler):
    continue_stream = True
    previous_output = []

    while continue_stream:
        continue_stream = True
        tasks = mongo_db.db[tasks_collection_name].find_one({'_id': task_id},{
            "status": 1,
            "task_status": 1,
            "past_steps": 1
        })

        if not tasks:
            yield f"data: {json.dumps({'error': 'Task not found!!'})}\n\n"
            return
        
        past_steps_output = tasks.get('past_steps', [])
        if previous_output != past_steps_output:
            previous_output = past_steps_output
            yield f"data: {json.dumps({'past_steps': past_steps_output})}\n\n"
        else:
            yield f"data: {json.dumps({'message': 'Waiting for updates...'})}\n\n"
        
        if tasks.get('status','').casefold() in [TaskStatus.FAILED.casefold(), TaskStatus.CANCELLED.lower(), TaskStatus.COMPLETE.casefold(), TaskStatus.PAUSED.casefold()]:
            continue_stream = False
            yield f"data: {json.dumps({'past_steps': past_steps_output})}\n\n"
            return

async def steps_stream(task_id:str, mongo_db: MongoDBHandler):
    continue_stream = True
    previous_output = []

    while continue_stream:
        continue_stream = False
        tasks = mongo_db.db[tasks_collection_name].find_one({'_id': task_id},{
            "status": 1,
            "total_tasks": 1,
            "past_steps": 1
        })

        if not tasks:
            yield f"data: {json.dumps({'error': 'Task not found!!'})}\n\n"
            return
        
        total_tasks = tasks.get('total_tasks', [])
        past_steps = tasks.get('past_steps', [])
        total_tasks = json.loads(total_tasks) if isinstance(total_tasks, str) else total_tasks

        steps = []
        for step in total_tasks:
            steps.append({
                "title": step.get("step", ""),
                "description": step.get("details", ""),
                "status": step.get("status", "to-do")
            })
        for step in past_steps:
            steps.append({
                "title": step.get("title", ""),
                "description": step.get("action", ""),
                "status": "completed"
            })
        
        if previous_output != steps:
            previous_output = steps
            yield f"data: {json.dumps({'steps': steps})}\n\n"
        else:
            yield f"data: {json.dumps({'message': 'Waiting for updates...'})}\n\n"
        
        if tasks.get('status','').casefold() in [TaskStatus.FAILED.casefold(), TaskStatus.CANCELLED.lower(), TaskStatus.COMPLETE.casefold(), TaskStatus.PAUSED.casefold()]:
            continue_stream = False
            yield f"data: {json.dumps({'steps': steps})}\n\n"
            return
        


async def status_stream(task_id:str, mongo_db: MongoDBHandler):
    continue_stream = True
    previous_output = []
    MAX_LOOP = 100
    count = 0
    while continue_stream:
        count = count + 1
        if count>= MAX_LOOP:
            continue_stream = False
        tasks = mongo_db.db[tasks_collection_name].find_one(
            {'_id': task_id},{
                "status": 1,
                "task_status": 1,
                "_status": 1,
                "description": 1,
                "past_statuses": 1,
                "session_id":1,
                "llm_model":1,
                "failure_reason":1,
                "branch_name":1,
                "ip": 1,
                "params": 1  # Get stored params
            }
        )
        
        if not tasks:
            yield f"data: {json.dumps({'error': 'Task not found!!'})}\n\n"
            return

        
        status_output = {
            "past_statuses": tasks.get('past_statuses', []),
            "status": tasks.get('status', ""),
            "task_status": tasks.get('task_status', ""),
            "description": tasks.get('description', ""),
            "_status": tasks.get('_status', ""),
            "session_id":tasks.get('session_id',""),
            "llm_model":tasks.get('llm_model', ""),
            "failure_reason":tasks.get('failure_reason', ""),
            "branch_name":tasks.get('branch_name', "")
        }
        
        if previous_output != status_output:
            previous_output = status_output
            yield f"data: {json.dumps({'status_output': status_output})}\n\n"
        else:
            yield f"data: {json.dumps({'message': 'Waiting for updates...'})}\n\n"
        
        if tasks.get('status','').casefold() in [TaskStatus.FAILED.casefold(), TaskStatus.CANCELLED.lower(), TaskStatus.COMPLETE.casefold()]:
            continue_stream = False
            yield f"data: {json.dumps({'status_output': status_output})}\n\n"
            return
        await asyncio.sleep(3)
        
async def task_plan_stream(task_id: str, mongo_db: MongoDBHandler):
    continue_stream = True
    previous_output = []

    while continue_stream:
        continue_stream = False
        tasks = mongo_db.db[tasks_collection_name].find_one(
            {'_id': task_id},
            {
                'status': 1,
                'total_tasks': 1,
            }
        )

        if not tasks:
            yield f"data: {json.dumps({'error': 'Task not found!!'})}\n\n"
            return

        task_plan = tasks.get('total_tasks', [])
        if previous_output != task_plan:
            previous_output = task_plan
            yield f"data: {json.dumps({'task_plan': task_plan})}\n\n"
        else:
            yield f"data: {json.dumps({'message': 'Waiting for updates...'})}\n\n"
        
        if tasks.get('status', '').casefold() in [TaskStatus.FAILED.casefold(), TaskStatus.CANCELLED.casefold(), TaskStatus.COMPLETE.casefold(), TaskStatus.PAUSED.casefold()]:
            continue_stream = False
            yield f"data: {json.dumps({'task_plan': task_plan})}\n\n"
            return


async def llm_cost_stream(task_id: str, mongo_db: MongoDBHandler):
    continue_stream = True
    previous_output = {}

    while continue_stream:
        continue_stream = False
        tasks = mongo_db.db[tasks_collection_name].find_one(
            {'_id': task_id},
            {
                'status': 1,
                'agents_cost': 1,
                'total_cost': 1,
            }
        )

        if not tasks:
            yield f"data: {json.dumps({'error': 'Task not found!!'})}\n\n"
            return

        llm_cost = {
            'agents_cost': tasks.get('agents_cost', {}),
            'total_cost': tasks.get('total_cost', 0)
        }
        
        if previous_output != llm_cost:
            previous_output = llm_cost
            yield f"data: {json.dumps({'llm_cost': llm_cost})}\n\n"
        else:
            yield f"data: {json.dumps({'message': 'Waiting for updates...'})}\n\n"
        
        if tasks.get('status', '').casefold() in [TaskStatus.FAILED.casefold(), TaskStatus.CANCELLED.casefold(), TaskStatus.COMPLETE.casefold(), TaskStatus.PAUSED.casefold()]:
            continue_stream = False
            yield f"data: {json.dumps({'llm_cost': llm_cost})}\n\n"
            return


@router.get("/callback_state/{callback_type}/{task_id}")
async def get_callback_state(callback_type: str, task_id: str, db: NodeDB = Depends(get_node_db)):
    """Non-streaming version of callbacks that returns current state"""
    
    # First verify task exists
    mongo_db = get_mongo_db()
    task = mongo_db.db[tasks_collection_name].find_one({'_id': task_id})
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    if callback_type == "function_calls":
        # Get last 25 function calls
        function_calls = task.get('function_calls', [])[-25:]
        return {"function_calls": function_calls}

    elif callback_type == "browser":
        browser_output = task.get('browser_output', [])
        browser_output = browser_output[-1] if browser_output else ""
        return {"browser_output": browser_output}

    elif callback_type == "messages":
        messages = task.get('messages', [])
        messages_to_send = []
        cached_user_details = {}
        
        # Create a dictionary to track messages by ID
        message_map = {}
        
        # Status priority map (lower number = higher priority)
        status_priority = {
            "completed": 1,
            "resolved": 2,
            "pending": 3,
            "streaming": 4,
            "needs_response": 5,
            "failed": 6
        }
        
        for message in messages:
            # Handle both regular messages and file updates
            message_id = message.get("id", "") or message.get("message_id", "")
            
            if not message_id:
                continue
                
            # For file updates, just add them directly to the messages_to_send list
            if message.get("msg_type") == "file_update":
                messages_to_send.append(message)
                continue
                
            # If we've seen this ID before, merge the messages
            if message_id in message_map:
                existing_msg = message_map[message_id]
                
                # Compare status priorities
                existing_status = existing_msg.get("status", "").lower()
                new_status = message.get("status", "").lower()
                
                if (status_priority.get(new_status, 999) < 
                    status_priority.get(existing_status, 999)):
                    existing_msg["status"] = message.get("status")
                
                # Compare requires_resolution flags
                existing_requires = existing_msg.get("requires_resolution", True)
                new_requires = message.get("requires_resolution", True)
                if not existing_requires or not new_requires:
                    existing_msg["requires_resolution"] = False
                
                # Update resolution_id if present
                if message.get("resolution_id"):
                    existing_msg["resolution_id"] = message.get("resolution_id")
                    
                # Merge metadata if present
                if message.get("metadata"):
                    if not existing_msg.get("metadata"):
                        existing_msg["metadata"] = {}
                    existing_msg["metadata"].update(message.get("metadata", {}))
                    
                continue
                
            # Process user details
            metadata = message.get("metadata", {})
            user_id = ""
            if metadata:
                user_id = metadata.get("user_id", "")
            
            if user_id:
                if user_id not in cached_user_details:
                    user_details = await db.get_user_by_id(user_id)
                    node_properties = user_details.get("properties", {})
                    cached_user_details[user_id] = {
                        "name": node_properties.get("Name", ""),
                        "email": node_properties.get("Email", ""),
                        "designation": node_properties.get("Designation", ""),
                        "department": node_properties.get("Department", "")
                    }
                message["user_details"] = cached_user_details[user_id]
            
            # Store message in map
            message_map[message_id] = message

        # Add regular messages to the messages_to_send list
        messages_to_send.extend(list(message_map.values()))
        
        # Sort all messages by timestamp if available
        messages_to_send.sort(
            key=lambda x: x.get("timestamp", "") or x.get("created_at", ""),
            reverse=False
        )
        
        waiting_for_user = task.get('waiting_for_user', False)
        return {
            "messages": messages_to_send,
            "waiting_for_user": waiting_for_user
        }

    elif callback_type == "terminal":
        # Get last 50 terminal outputs
        terminal_output = task.get('terminal_output', [])[-50:]
        return {"terminal_output": terminal_output}

    elif callback_type == "past_steps":
        past_steps = task.get('past_steps', [])
        return {"past_steps": past_steps}
    
    elif callback_type == "steps":
        steps = []
        total_tasks = task.get('total_tasks', [])
        past_steps = task.get('past_steps', [])
        
        # Process total_tasks if it's a string (JSON)
        if isinstance(total_tasks, str):
            try:
                total_tasks = json.loads(total_tasks)
            except:
                total_tasks = []
        
        # Create a combined array of steps with title, description and status
        for step in total_tasks:
            steps.append({
                "title": step.get("step", ""),
                "description": step.get("details", ""),
                "status": step.get("status", "to-do")
            })
            
        # Add completed steps from past_steps
        for step in past_steps:
            steps.append({
                "title": step.get("title", ""),
                "description": step.get("action", ""),
                "status": "completed"
            })
            
        return {"steps": steps}

    elif callback_type == "status":
        return {
            "status_output": {
                "past_statuses": task.get('past_statuses', []),
                "status": task.get('status', ""),
                "task_status": task.get('task_status', ""),
                "description": task.get('description', ""),
                "_status": task.get('_status', ""),
                "session_id": task.get('session_id', ""),
                "branch_name": task.get('branch_name',"")
            }
        }

    elif callback_type == "task_plan":
        task_plan = task.get('total_tasks', [])
        return {"task_plan": task_plan}

    elif callback_type == "llm_cost":
        return {
            "llm_cost": {
                "agents_cost": task.get('agents_cost', {}),
                "total_cost": task.get('total_cost', 0)
            }
        }
        
    elif callback_type == "code_server":
        return {
            "iframe": task.get('iframe', "")
        }
    
    elif callback_type == "document":
        return get_documents(task_id, mongo_db)

    else:
        raise HTTPException(status_code=400, detail=f"Unknown callback type: {callback_type}")
    
    
@router.get("/check_live_status/")
async def check_live_status( project_id: str ,ip_address : str,architecture_id: int, current_user=Depends(get_current_user), 
    db: NodeDB = Depends(get_node_db),) -> StreamingResponse:
    mongo_db : MongoDBHandler = get_mongo_db()
    
    return True

@router.post("/check-live-status-code-maintenance/")
async def check_live_status_code_maintenance(project_id: str, ip_address: str, selectedrepos: dict = Body(...),
    session_name:str = Body(...),
    description:str = Body(...),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    mongo_db: MongoDBHandler = Depends(get_mongo_db),
    db: NodeDB = Depends(get_node_db),
    current_user = Depends(get_current_user)) -> StreamingResponse:
    
    return True

    if is_live(ip_address):
            
            async def streamResponse():
                yield format_response_for_stream({"status": "live"})
            return StreamingResponse(
            streamResponse(),
            media_type="application/json",)
    else:
        return StreamingResponse(
        stream_code_maintanence(project_id,selectedrepos,session_name,description,background_tasks,mongo_db,db,current_user),
        media_type="application/json",)
    
@router.post("/download-repository/{task_id}")
async def download_repository(
    task_id: str, 
    project_id: str, 
    repo_name: str,
    branch: str = Query("main", description="Branch to download"),
    mongo_db: MongoDBHandler = Depends(get_mongo_db),
    db: NodeDB = Depends(get_node_db)
):
    """
    Download repository by pulling from git source, compressing in memory, and streaming the response.
    """
    import git
    from io import BytesIO
    
    try:
        tenant_id = get_tenant_id()
        
        # Get task information
        task = mongo_db.db[tasks_collection_name].find_one({"_id": task_id})
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        # Get project_details from task
        project_details = task.get('project_details', {})
        
        # Handle case where project_details is a JSON string
        if isinstance(project_details, str):
            try:
                project_details = json.loads(project_details)
            except json.JSONDecodeError as e:
                print(f"Error parsing project_details JSON: {e}")
                raise HTTPException(status_code=400, detail="Invalid project_details format")
        
        print(f"Debug: project_details type: {type(project_details)}")
        print(f"Debug: repositories type: {type(project_details.get('repositories', {}))}")
        
        # Find the repository metadata
        repository_metadata = None
        container_id = task.get('container_id')
        
        # Look for repository in project_details.repositories first
        repositories = project_details.get('repositories', {})
        
        # Handle case where repositories might be a string
        if isinstance(repositories, str):
            try:
                repositories = json.loads(repositories)
            except json.JSONDecodeError:
                repositories = {}
        
        if container_id and str(container_id) in repositories:
            repo_data = repositories[str(container_id)]
            # Handle case where repo_data might be a string
            if isinstance(repo_data, str):
                try:
                    repository_metadata = json.loads(repo_data)
                except json.JSONDecodeError:
                    repository_metadata = None
            else:
                repository_metadata = repo_data
        
        # If not found by container_id, search by repo_name in repositories
        if not repository_metadata and repositories:
            for key, repo_data in repositories.items():
                # Skip if repo_data is not a dict and can't be parsed
                if isinstance(repo_data, str):
                    try:
                        repo_data = json.loads(repo_data)
                    except json.JSONDecodeError:
                        continue
                
                if isinstance(repo_data, dict) and repo_data.get('repositoryName') == repo_name:
                    repository_metadata = repo_data
                    break
        
        # If not found in repositories, check current_repository
        if not repository_metadata:
            current_repo = project_details.get('current_repository', {})
            
            # Handle case where current_repository might be a string
            if isinstance(current_repo, str):
                try:
                    current_repo = json.loads(current_repo)
                except json.JSONDecodeError:
                    current_repo = {}
            
            if current_repo and isinstance(current_repo, dict):
                if current_repo.get('repositoryName') == repo_name or not repo_name:
                    repository_metadata = current_repo
        
        if not repository_metadata:
            raise HTTPException(status_code=404, detail=f"Repository '{repo_name}' not found in project")
        
        # Extract repository details using the correct field names
        clone_url = repository_metadata.get('cloneUrlHttp') or repository_metadata.get('cloneUrlSsh')
        # Use the branch parameter, fallback to repository default, then to 'main'
        target_branch = branch or repository_metadata.get('default_branch', 'main')
        
        if not clone_url:
            raise HTTPException(status_code=400, detail="Repository clone URL not found")
        
        # Get organization from repository metadata
        organization = repository_metadata.get('organization', '')
        
        # Fetch access token from scm_configurations collection
        access_token = None
        if organization:
            scm_config = mongo_db.db["scm_configurations"].find_one({
                "tenant_id": tenant_id,
                "credentials.organization": organization
            })
            if scm_config:
                access_token = scm_config.get('credentials', {}).get('access_token')
        
        # Fallback to settings if no specific token found
        if not access_token:
            access_token = settings.GITHUB_ACCESS_TOKEN
        
        if not access_token:
            raise HTTPException(status_code=400, detail="GitHub access token not found")
        
        # Create temporary directory for cloning
        temp_clone_dir = tempfile.mkdtemp()
        
        try:
            # Clone repository to temporary directory
            print(f"Cloning repository from {clone_url}")
            
            # Prepare authenticated clone URL for HTTPS
            if clone_url.startswith('https://'):
                # Insert token into HTTPS URL
                auth_clone_url = clone_url.replace('https://', f'https://{access_token}@')
            else:
                # Use original URL for SSH (assumes SSH key is configured)
                auth_clone_url = clone_url
            
            # Clone the repository
            repo = git.Repo.clone_from(
                auth_clone_url,
                os.path.join(temp_clone_dir, repo_name),
                branch=target_branch,
                env={
                    'GIT_TERMINAL_PROMPT': '0',
                    'GIT_ASKPASS': 'echo'
                }
            )
            
            repo_path = os.path.join(temp_clone_dir, repo_name)
            
            # Create zip file in memory
            zip_buffer = BytesIO()
            
            with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                # Walk through all files in the repository
                for root, dirs, files in os.walk(repo_path):
                    # Skip .git directory
                    if '.git' in dirs:
                        dirs.remove('.git')
                    
                    for file in files:
                        file_path = os.path.join(root, file)
                        # Get relative path from repo root
                        arcname = os.path.relpath(file_path, repo_path)
                        
                        try:
                            zip_file.write(file_path, arcname)
                        except Exception as e:
                            print(f"Warning: Could not add file {file_path} to zip: {str(e)}")
                            continue
            
            zip_buffer.seek(0)
            
            # Create a streaming response
            def generate_zip():
                yield zip_buffer.getvalue()
            
            # Clean up temporary directory
            shutil.rmtree(temp_clone_dir, ignore_errors=True)
            
            return StreamingResponse(
                io.BytesIO(zip_buffer.getvalue()),
                media_type="application/zip",
                headers={"Content-Disposition": f"attachment; filename={repo_name}.zip"}
            )
            
        except git.exc.GitCommandError as e:
            shutil.rmtree(temp_clone_dir, ignore_errors=True)
            raise HTTPException(status_code=500, detail=f"Git operation failed: {str(e)}")
        except Exception as e:
            shutil.rmtree(temp_clone_dir, ignore_errors=True)
            raise HTTPException(status_code=500, detail=f"Error processing repository: {str(e)}")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")


@router.get("/health")
async def health_check(url: str = Query(..., description="URL to check health status")):
    """Health check endpoint to verify if a URL returns a 200 status code."""
    try:
        response = requests.get(url, timeout=10,verify=False)
        return {
            "status": "healthy" if response.status_code == 200 else "unhealthy",
            "url": url,
            "status_code": response.status_code,
            "healthy":True if response.status_code == 200 else False
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "url": url,
            "error": str(e)
        }
    
@router.get("/past_code_tasks/{project_id}")
async def get_past_code_tasks(
   project_id: int,
    limit: int = Query(10, ge=1, le=100),
    skip: int = Query(0, ge=0),
    agent_name: Optional[str] = Query(None, description="Filter by agent name"),
    current_user = Depends(get_current_user),
    background_tasks: BackgroundTasks = BackgroundTasks(),
):
    """
    Retrieve past code maintenance tasks for a given project_id.
    Includes duration calculation between start_time and last message timestamp.
    """
    from datetime import datetime

    def calculate_duration(start_time_str, end_time_str):
        try:
            start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
            end_time = datetime.fromisoformat(end_time_str.replace('Z', '+00:00'))
            
            # Calculate difference in seconds
            diff_seconds = (end_time - start_time).total_seconds()
            
            # Convert to various units
            minutes = int(diff_seconds // 60)
            hours = int(minutes // 60)
            days = int(hours // 24)
            weeks = int(days // 7)
            years = int(days // 365.25)
            
            # Calculate remaining values
            remaining_weeks = int((days % 365.25) // 7)
            remaining_days = int(days % 7)
            remaining_hours = int(hours % 24)
            remaining_minutes = int(minutes % 60)
            remaining_seconds = int(diff_seconds % 60)
            
            # Return appropriate format based on duration
            if years >= 1:
                return f"{years}y {remaining_weeks}w"
            elif weeks >= 1:
                return f"{weeks}w {remaining_days}d"
            elif days >= 1:
                return f"{days}d {remaining_hours}h"
            elif hours >= 1:
                return f"{hours}h {remaining_minutes}m"
            elif minutes >= 1:
                return f"{minutes}m {remaining_seconds}s"
            else:
                return "<1m"
                
        except Exception as e:
            print(f"Error calculating duration: {str(e)}")
            return "0"

    tenant_id = get_tenant_id()
    task_repository: TaskRepository = await get_mongo_db_v1("task", table_name="code_gen_tasks")
    db = await get_db(f"{settings.MONGO_DB_NAME}_{tenant_id}")
    query = {
        "project_id": int(project_id)
    }
    if agent_name:
        if agent_name.lower() == "codegeneration":
            query["agent_name"] = {"$exists": False}
        elif agent_name.lower() == "codemaintenance":
            query["agent_name"] = "CodeMaintenance"
    else:
        query = {
        "$and": [
            {"project_id": int(project_id)},
            {"$or": [
                {"agent_name": "CodeMaintenance"},
                {"agent_name": {"$exists": False}}
            ]}
        ]
    }   
    projection = {
        "_id": 1,
        "status": 1,
        "llm_model": 1,
        "agent_name": 1,
        "start_time": 1,
        "session_id": 1,
        "description": 1,
        "session_name": 1,
        "messages": 1,
        "container_id": 1,
    }
    sort = [("start_time", -1)]

    tasks = await task_repository.find_many(query, db, projection, skip=skip, limit=limit, sort=sort)

    # Convert ObjectId to string in tasks and calculate duration
    serialized_tasks = []
    for task in tasks:
        task['_id'] = str(task['_id'])
        
        # Get start time
        start_time = task.get('start_time')
        
        # Get last message timestamp
        messages = task.get('messages', [])
        end_time = start_time  # Default to start_time if no messages
        
        if messages:
            last_message = messages[-1]
            if isinstance(last_message, dict):
                end_time = last_message.get('timestamp', start_time)
        
        # Calculate and add duration
        if start_time and end_time:
            task['duration'] = calculate_duration(start_time, end_time)
        else:
            task['duration'] = "<1m"
        
        # Ensure container_id is included in the serialized task
        if 'container_id' not in task:
            task['container_id'] = None
            
        serialized_tasks.append(task)

    total_count = await task_repository.count_documents(query, db)

    return {
        "tasks": serialized_tasks,
        "total_count": total_count,
        "limit": limit,
        "skip": skip
    }

@router.post("/merge/{task_id}")
async def merge_repos(
    task_id: str,
    mongo_db: MongoDBHandler = Depends(get_mongo_db)
):
    """
    Merge all repositories for a given task_id by calling the code_gen service.
    """
    try:
        # Get task details to determine project_id and stage
        task = mongo_db.db[tasks_collection_name].find_one({"_id": task_id})
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        project_id = task.get("project_id")
        stage = task.get("stage", "dev")
        tenant_id = get_tenant_id()
        
        if not project_id:
            raise HTTPException(status_code=400, detail="Project ID not found in task")
        
        # Get the code_gen service URL
        codegen_base_url = get_codegen_url(tenant_id, project_id, stage)
        merge_url = f'{codegen_base_url}/merge?task_id={task_id}'
        
        print(f"Calling merge endpoint: {merge_url}")
        
        # Make request to the code_gen service
        try:
            response = requests.post(merge_url, verify=False, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                return result
            else:
                error_detail = f"Code generation service returned status {response.status_code}"
                try:
                    error_response = response.json()
                    if "detail" in error_response:
                        error_detail = error_response["detail"]
                except:
                    pass
                
                raise HTTPException(
                    status_code=response.status_code,
                    detail=error_detail
                )
                
        except requests.RequestException as e:
            raise HTTPException(
                status_code=503,
                detail=f"Failed to connect to code generation service: {str(e)}"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in merge operation for task_id {task_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error processing merge operation: {str(e)}"
        )

