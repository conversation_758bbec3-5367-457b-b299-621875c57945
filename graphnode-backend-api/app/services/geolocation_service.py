# app/services/geolocation_service.py
import httpx
import asyncio
from typing import Optional, Dict
from app.telemetry.logger_config import get_logger
import os

logger = get_logger(__name__)

class GeolocationService:
    def __init__(self):
        # You can use multiple services for fallback
        self.services = [
            {
                "name": "ipapi",
                "url": "http://ip-api.com/json/{ip}?fields=status,message,country,regionName,city,lat,lon,timezone,isp,query",
                "free": True
            },
            {
                "name": "ipinfo",
                "url": "https://ipinfo.io/{ip}/json",
                "token": os.getenv("IPINFO_TOKEN"),  # Optional API token
                "free": True
            }
        ]
        self.cache = {}  # Simple in-memory cache
        self.cache_ttl = 3600  # 1 hour cache
    
    async def get_location(self, ip_address: str) -> Optional[Dict]:
        """Get location information for an IP address"""
        
        # Skip private/local IPs
        if self._is_private_ip(ip_address):
            return self._get_default_location()
        
        # Check cache first
        if ip_address in self.cache:
            cached_data = self.cache[ip_address]
            if cached_data.get('timestamp', 0) + self.cache_ttl > asyncio.get_event_loop().time():
                return cached_data['data']
        
        # Try each service
        for service in self.services:
            try:
                location_data = await self._fetch_from_service(service, ip_address)
                if location_data:
                    # Cache the result
                    self.cache[ip_address] = {
                        'data': location_data,
                        'timestamp': asyncio.get_event_loop().time()
                    }
                    return location_data
            except Exception as e:
                logger.warning(f"Failed to get location from {service['name']}: {str(e)}")
                continue
        
        # Return default if all services fail
        return self._get_default_location()
    
    async def _fetch_from_service(self, service: Dict, ip_address: str) -> Optional[Dict]:
        """Fetch location data from a specific service"""
        url = service["url"].format(ip=ip_address)
        
        headers = {}
        if service.get("token"):
            headers["Authorization"] = f"Bearer {service['token']}"
        
        async with httpx.AsyncClient(timeout=5.0) as client:
            response = await client.get(url, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                return self._normalize_response(service["name"], data)
        
        return None
    
    def _normalize_response(self, service_name: str, data: Dict) -> Dict:
        """Normalize response from different services to a common format"""
        
        if service_name == "ipapi":
            if data.get("status") == "success":
                return {
                    "country": data.get("country"),
                    "region": data.get("regionName"),
                    "city": data.get("city"),
                    "latitude": data.get("lat"),
                    "longitude": data.get("lon"),
                    "timezone_name": data.get("timezone"),
                    "isp": data.get("isp"),
                    "ip": data.get("query")
                }
        
        elif service_name == "ipinfo":
            loc = data.get("loc", "").split(",")
            return {
                "country": data.get("country"),
                "region": data.get("region"),
                "city": data.get("city"),
                "latitude": float(loc[0]) if len(loc) > 0 and loc[0] else None,
                "longitude": float(loc[1]) if len(loc) > 1 and loc[1] else None,
                "timezone_name": data.get("timezone"),
                "isp": data.get("org"),
                "ip": data.get("ip")
            }
        
        return {}
    
    def _is_private_ip(self, ip: str) -> bool:
        """Check if IP is private/local"""
        private_ranges = [
            "127.", "10.", "172.16.", "172.17.", "172.18.", "172.19.",
            "172.20.", "172.21.", "172.22.", "172.23.", "172.24.", "172.25.",
            "172.26.", "172.27.", "172.28.", "172.29.", "172.30.", "172.31.",
            "192.168.", "localhost", "0.0.0.0"
        ]
        return any(ip.startswith(prefix) for prefix in private_ranges)
    
    def _get_default_location(self) -> Dict:
        """Return default location for private/unknown IPs"""
        return {
            "country": "Unknown",
            "region": "Unknown",
            "city": "Unknown",
            "latitude": None,
            "longitude": None,
            "timezone_name": None,
            "isp": "Unknown",
            "ip": "private"
        }

# Global service instance
geolocation_service = GeolocationService()