from datetime import datetime
from typing import Dict, Any, Optional
import json
from bson import ObjectId
from app.connection.tenant_middleware import KAVIA_ROOT_DB_NAME
from app.connection.establish_db_connection import get_mongo_db
import asyncio
def get_organization_name_by_tenant_id(tenant_id):
   mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
   
   try:
       result = mongo_db.db["organizations"].find_one(
           {"_id": tenant_id},
           {"name": 1, "_id": 0}
       )
       
       return result["name"] if result and "name" in result else "unknown"
   
   except Exception:
       return "unknown"
   
def get_name_by_user_id(user_id):
   mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
   
   try:
       result = mongo_db.db["users"].find_one(
           {"_id": user_id},
           {"name": 1, "_id": 0}
       )
       
       return result["name"] if result and "name" in result else "unknown"
   
   except Exception:
       return "unknown"

class SessionTrackerService:
    def __init__(self):
        self.mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
        # Single collection for all session tracking data
        self.sessions_collection = "session_tracking"
        print(f"[DEBUG] SessionTrackerService initialized with db: {KAVIA_ROOT_DB_NAME}")
    
    async def initialize_session(self, 
                               task_id: str, 
                               tenant_id: str, 
                               user_id: str, 
                               project_id: str,
                               service_type: str,
                               container_id: str = None,
                               session_data: dict = None) -> dict:
        """
        Initialize a new session when start-project or workspace is triggered
        """
        try:
            print(f"[DEBUG] Initializing session for task_id: {task_id}")
            
            # Check if session already exists
            existing_session = self.mongo_db.db[self.sessions_collection].find_one({"task_id": task_id})
            if existing_session:
                print(f"[DEBUG] Session already exists for task_id: {task_id}")
                return {"success": True, "session": existing_session, "already_exists": True}
            
            session_doc = {
                "task_id": task_id,
                "tenant_id": tenant_id,
                "organization_name": get_organization_name_by_tenant_id(tenant_id),
                "user_id": user_id,
                "User_name": get_name_by_user_id(user_id),
                "project_id": str(project_id),
                "container_id": str(container_id) if container_id else None,
                "service_type": service_type,
                "session_start": datetime.utcnow(),
                "session_end": None,
                "status": "active",
                "total_cost": 0.0,  # Start with zero
                "agent_costs": {}, # Start all agents with zero

                "cost_history": [],  # Array to store cost updates over time
                "last_updated": datetime.utcnow(),
                "session_data": session_data or {},
                "created_at": datetime.utcnow()
            }
            
            print(f"[DEBUG] Inserting session document with zero costs")
            
            # Insert session document
            result = self.mongo_db.db[self.sessions_collection].insert_one(session_doc)
            session_doc["_id"] = str(result.inserted_id)
            
            print(f"[SUCCESS] Session initialized: {task_id} for tenant: {tenant_id}, user: {user_id}")
            print(f"[DEBUG] Inserted document ID: {result.inserted_id}")
            
            return {"success": True, "session": session_doc}
            
        except Exception as e:
            print(f"[ERROR] Error initializing session: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "error": str(e)}
    
    async def update_session_cost(self, 
                                task_id: str, 
                                agent_costs: dict, 
                                total_cost: float) -> dict:
        """
        Update session cost when cost_update_callback is triggered
        Logic:
        - If session doesn't exist, create it first with zero costs
        - Always REPLACE the total_cost and agent_costs (don't add/append)
        - Add current cost update to cost_history for tracking
        """
        try:
            print(f"[DEBUG] Updating session cost for task_id: {task_id}")
            print(f"[DEBUG] Agent costs: {agent_costs}")
            print(f"[DEBUG] Total cost: {total_cost}")
            
            # Check if session exists
            existing_session = self.mongo_db.db[self.sessions_collection].find_one({"task_id": task_id})
            
            if not existing_session:
                print(f"[DEBUG] No session found for task_id: {task_id}. Creating new session...")
                
                # Create session with zero costs first
                session_doc = {
                    "task_id": task_id,
                    "tenant_id": "unknown",  # We'll update this when we have the info
                    "user_id": "unknown",    # We'll update this when we have the info
                    "project_id": "unknown", # We'll update this when we have the info
                    "container_id": None,
                    "service_type": "unknown",
                    "session_start": datetime.utcnow(),
                    "session_end": None,
                    "status": "active",
                    "total_cost": 0.0,  # Start with zero
                    "agent_costs": {  # Start all agents with zero
                        'InitialSetupAgent': 0,
                        'PlanningAgent': 0,
                        'CodeWritingAgent': 0,
                        'TestCodeWritingAgent': 0,
                        'TestExecutionAgent': 0,
                        'VisualVerificationAgent': 0,
                        'ContainerBuilderAgent': 0,
                        'BugFixingAndVerificationAgent': 0,
                        'QuestionAnsweringAgent': 0,
                        'DocumentationAgent': 0,
                        'GeneralistAgent': 0,
                        'HelpAgent': 0,
                        'DesignExtractionAgent': 0,
                        'Summarizer': 0,
                        'Knowledge': 0
                    },
                    "cost_history": [],
                    "last_updated": datetime.utcnow(),
                    "session_data": {"created_from": "cost_callback"},
                    "created_at": datetime.utcnow()
                }
                
                # Insert the new session
                result = self.mongo_db.db[self.sessions_collection].insert_one(session_doc)
                print(f"[DEBUG] Created new session with ID: {result.inserted_id}")
            
            print(f"[DEBUG] Now updating costs - REPLACING current values")
            
            # Create cost history entry with timestamp
            cost_history_entry = {
                "timestamp": datetime.utcnow(),
                "agent_costs": agent_costs,
                "total_cost": total_cost
            }
            
            print(f"[DEBUG] Creating cost history entry: {cost_history_entry}")
            
            # REPLACE the total_cost and agent_costs (don't add to them)
            # Also add this update to the cost_history array
            update_result = self.mongo_db.db[self.sessions_collection].update_one(
                {"task_id": task_id},
                {
                    "$set": {
                        "total_cost": total_cost,  # REPLACE with new total
                        "agent_costs": agent_costs,  # REPLACE with new agent costs
                        "last_updated": datetime.utcnow()
                    },
                    "$push": {
                        "cost_history": cost_history_entry  # ADD to history for tracking
                    }
                }
            )
            
            print(f"[DEBUG] Update result - matched: {update_result.matched_count}, modified: {update_result.modified_count}")
            
            if update_result.modified_count > 0:
                print(f"[SUCCESS] Session cost REPLACED for task_id: {task_id}")
                print(f"[SUCCESS] New total_cost: {total_cost}")
                print(f"[SUCCESS] New agent_costs: {agent_costs}")
                return {"success": True, "updated": True}
            else:
                print(f"[ERROR] Update operation did not modify any documents for task_id: {task_id}")
                return {"success": False, "error": "Update operation failed"}
                
        except Exception as e:
            print(f"[ERROR] Error updating session cost: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "error": str(e)}
    
    async def update_session_metadata(self, task_id: str, tenant_id: str = None, user_id: str = None, project_id: str = None) -> dict:
        """
        Update session metadata when we have more information
        """
        try:
            update_data = {"last_updated": datetime.utcnow()}
            
            if tenant_id:
                update_data["tenant_id"] = tenant_id
            if user_id:
                update_data["user_id"] = user_id
            if project_id:
                update_data["project_id"] = str(project_id)
            
            result = self.mongo_db.db[self.sessions_collection].update_one(
                {"task_id": task_id},
                {"$set": update_data}
            )
            
            if result.modified_count > 0:
                print(f"[SUCCESS] Session metadata updated for task_id: {task_id}")
                return {"success": True}
            else:
                return {"success": False, "error": "Session not found or no changes made"}
                
        except Exception as e:
            print(f"[ERROR] Error updating session metadata: {e}")
            return {"success": False, "error": str(e)}
    
    async def end_session(self, task_id: str, status: str = "completed") -> dict:
        """
        End a session and update status
        """
        try:
            update_result = self.mongo_db.db[self.sessions_collection].update_one(
                {"task_id": task_id},
                {
                    "$set": {
                        "session_end": datetime.utcnow(),
                        "status": status,
                        "last_updated": datetime.utcnow()
                    }
                }
            )
            
            if update_result.modified_count > 0:
                print(f"Session ended for task_id: {task_id} with status: {status}")
                return {"success": True}
            else:
                return {"success": False, "error": "Session not found"}
                
        except Exception as e:
            print(f"Error ending session: {e}")
            return {"success": False, "error": str(e)}

# Global instance
session_tracker = None

def get_session_tracker():
    global session_tracker
    if session_tracker is None:
        session_tracker = SessionTrackerService()
    return session_tracker