# app/services/supabase_management_service.py
import httpx
import os
from typing import Dict, List, Optional
from datetime import datetime
from app.services.supabase_database_service import SupabaseDatabaseService

supabase_data_service = SupabaseDatabaseService()

class SupabaseManagementService:
    def __init__(self):
        self.base_url = "https://api.supabase.com/v1"
    
    def get_headers(self, access_token: str) -> Dict[str, str]:
        """Get headers with user's access token"""
        return {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
    
    async def create_project(
        self,
        user_id:str,
        project_id:str,
        name: str, 
        organization_id: str,
        db_password: str,
        region: str = "us-east-1"
    ) -> Dict:
        """Create a new Supabase project"""
        async with httpx.AsyncClient() as client:
            try:
                tokens = await supabase_data_service.get_user_supabase_tokens(user_id,project_id)
                access_token = tokens['access_token']
                response = await client.post(
                    f"{self.base_url}/projects",
                    headers=self.get_headers(access_token),
                    json={
                        "name": name,
                        "organization_id": organization_id,
                        "db_pass": db_password,
                        "region": region
                    },
                    timeout=30.0
                )
                
                if response.status_code == 201:
                    return {"success": True, "data": response.json()}
                else:
                    return {
                        "success": False, 
                        "error": f"Failed to create project: {response.text}",
                        "status_code": response.status_code
                    }
                    
            except httpx.TimeoutException:
                return {"success": False, "error": "Request timeout"}
            except Exception as e:
                return {"success": False, "error": str(e)}
    
    async def list_projects(self,  user_id,project_id,organization_id: Optional[str] = None) -> Dict:
        """List all projects or projects for a specific organization"""
        async with httpx.AsyncClient() as client:
            try:
                tokens = await supabase_data_service.get_user_supabase_tokens(user_id,project_id)
                access_token = tokens['access_token']
                url = f"{self.base_url}/projects"
                params = {}
                if organization_id:
                    params["organization_id"] = organization_id
                
                response = await client.get(
                    url,
                    headers=self.get_headers(access_token),
                    params=params,
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    projects = response.json()
                    return {
                        "success": True, 
                        "data": projects,
                        "count": len(projects)
                    }
                else:
                    return {
                        "success": False,
                        "error": f"Failed to fetch projects: {response.text}",
                        "status_code": response.status_code
                    }
                    
            except httpx.TimeoutException:
                return {"success": False, "error": "Request timeout"}
            except Exception as e:
                return {"success": False, "error": str(e)}
    
    async def get_project_details(self, user_id,project_id,project_id_supabase: str) -> Dict:
        """Get detailed project information including API keys"""
        async with httpx.AsyncClient() as client:
            try:
                tokens = await supabase_data_service.get_user_supabase_tokens(user_id,project_id)
                access_token = tokens['access_token']
                response = await client.get(
                    f"{self.base_url}/projects/{project_id_supabase}",
                    headers=self.get_headers(access_token),
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    return {"success": True, "data": response.json()}
                else:
                    return {
                        "success": False,
                        "error": f"Failed to fetch project details: {response.text}",
                        "status_code": response.status_code
                    }
                    
            except Exception as e:
                return {"success": False, "error": str(e)}

    async def get_project_api_keys(self, user_id,project_id,supabase_project_id: str) -> Dict:
        """Get project API keys (anon and service_role)"""
        async with httpx.AsyncClient() as client:
            try:
                tokens = await supabase_data_service.get_user_supabase_tokens(user_id,project_id)
                access_token = tokens['access_token']
                response = await client.get(
                    f"{self.base_url}/projects/{supabase_project_id}/api-keys",
                    headers=self.get_headers(access_token),
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    return {"success": True, "data": response.json()}
                else:
                    return {
                        "success": False,
                        "error": f"Failed to fetch API keys: {response.text}",
                        "status_code": response.status_code
                    }
                    
            except Exception as e:
                return {"success": False, "error": str(e)}
    
    async def get_organizations(self,user_id,project_id) -> Dict:
        """Get user organizations"""
        async with httpx.AsyncClient() as client:
            try:
                tokens = await supabase_data_service.get_user_supabase_tokens(user_id,project_id)
                access_token = tokens['access_token']
                response = await client.get(
                    f"{self.base_url}/organizations",
                    headers=self.get_headers(access_token),
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    return {"success": True, "data": response.json()}
                else:
                    return {
                        "success": False,
                        "error": f"Failed to fetch organizations: {response.text}",
                        "status_code": response.status_code
                    }
                    
            except Exception as e:
                return {"success": False, "error": str(e)}

    async def get_project_config(self,user_id,project_id, supabase_project_id: str) -> Dict:
        """Get project configuration including settings"""
        async with httpx.AsyncClient() as client:
            try:
                tokens = await supabase_data_service.get_user_supabase_tokens(user_id,project_id)
                access_token = tokens['access_token']
                response = await client.get(
                    f"{self.base_url}/projects/{supabase_project_id}/config",
                    headers=self.get_headers(access_token),
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    return {"success": True, "data": response.json()}
                else:
                    return {
                        "success": False,
                        "error": f"Failed to fetch project config: {response.text}",
                        "status_code": response.status_code
                    }
                    
            except Exception as e:
                return {"success": False, "error": str(e)}
    async def get_supabase_project_details(
        self,
        project_id: str,
        project_id_supabase: str,
        user_id: str,
    ):
        """
        Get detailed information for a specific Supabase project
        """
        try:
            
            # Get project details
            details_result = await self.get_project_details(user_id,project_id,project_id_supabase)
            
            project_data = details_result["data"]
            
            # Get API keys
            keys_result = await self.get_project_api_keys(user_id,project_id,project_id_supabase)
            anon_key = None
            service_role_key = None
            
            if keys_result.get("success"):
                keys_data = keys_result["data"]
                for key in keys_data:
                    if key.get("name") == "anon":
                        anon_key = key.get("api_key")
                    elif key.get("name") == "service_role":
                        service_role_key = key.get("api_key")
            
            # Check connection status
            try:
                tokens = await supabase_data_service.get_user_supabase_tokens(user_id, project_id)
                is_connected = bool(tokens)
                connected_at = tokens.get("connected_at") if tokens else None
            except:
                is_connected = False
                connected_at = None
            
            return {
                "success": True,
                "message": "Project details retrieved successfully",
                "data": {
                    "id": project_id_supabase,
                    "name": project_data.get("name"),
                    "organization_id": project_data.get("organization_id"),
                    "region": project_data.get("region"),
                    "status": project_data.get("status"),
                    "created_at": project_data.get("created_at"),
                    "database_url": project_data.get("database", {}).get("host"),
                    "api_url": f"https://{project_id_supabase}.supabase.co",
                    "dashboard_url": f"https://supabase.com/dashboard/project/{project_id_supabase}",
                    "anon_key": anon_key,
                    "service_role_key": service_role_key,
                    "is_connected": is_connected,
                    "connected_at": connected_at,
                    "database": project_data.get("database", {}),
                    "settings": project_data.get("settings", {})
                }
            }
            

        except Exception as e:
            print(f"[ERROR] Error getting project details: {str(e)}")
            