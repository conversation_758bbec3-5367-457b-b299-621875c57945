import configparser
import os
import threading


class Config<PERSON><PERSON>ler:
    """
    A singleton class for handling configuration settings.

    This class reads configuration from both a default INI file and a custom INI file,
    allowing for default values to be overridden by custom settings.
    """

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(<PERSON>fi<PERSON><PERSON><PERSON><PERSON>, cls).__new__(cls)
            cls._instance.config = configparser.ConfigParser()
            cls._instance.load_config()
            cls._instance._runtime_config = {}
            cls._instance._lock = threading.RLock()
        return cls._instance

    def set_runtime_config(self, section, option, value):
        """
        Set a configuration value at runtime without persisting it to a config file.
        Thread-safe implementation.

        Args:
            section (str): The configuration section
            option (str): The configuration option
            value: The value to set

        Returns:
            None
        """
        with self._lock:
            if section not in self._runtime_config:
                self._runtime_config[section] = {}

            # Set the value in runtime config
            self._runtime_config[section][option] = value

    def get_runtime_config(self, section, option, default=None):
        """
        Get a configuration value from runtime config or fall back to regular config.
        Thread-safe implementation.

        Args:
            section (str): The configuration section
            option (str): The configuration option
            default: Default value to return if option is not found

        Returns:
            The configuration value or default if not found
        """
        with self._lock:
            if section in self._runtime_config and option in self._runtime_config[section]:
                return self._runtime_config[section][option]

            return default

    def clear_runtime_config(self, section=None, option=None):
        """
        Clear runtime configuration values.
        Thread-safe implementation.

        Args:
            section (str, optional): The section to clear. If None, clears all runtime config.
            option (str, optional): The option to clear. If None, clears entire section.

        Returns:
            None
        """
        with self._lock:
            if section is None:
                self._runtime_config = {}
            elif section in self._runtime_config:
                if option is None:
                    del self._runtime_config[section]
                elif option in self._runtime_config[section]:
                    del self._runtime_config[section][option]

    def load_config(self):
        """
        Load the configuration files.

        First, it loads the default configuration file (config.default.ini).
        Then, if it exists, it loads the custom configuration file (config.ini),
        which overrides any values from the default configuration.
        """
        default_path = os.path.join('config.default.ini')
        custom_path = os.path.join('config.ini')

        # Load the default configuration
        self.config.read(default_path)

        # If custom config exists, load it to override default values
        if os.path.exists(custom_path):
            self.config.read(custom_path)
        else:
            print(f"No custom configuration found. Using default settings.")

    def __getattr__(self, name):
        """
        Delegate method calls to the underlying ConfigParser object.
        This allows calling ConfigParser methods directly on the ConfigHandler instance.
        """
        return getattr(self.config, name)


# Global instance
config = ConfigHandler()
