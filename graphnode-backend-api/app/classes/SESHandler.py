import boto3
import smtplib
from email.mime.text import MIME<PERSON>ext
from email.mime.multipart import MIME<PERSON><PERSON><PERSON>art
from typing import Dict, List, Optional
from app.core.Settings import settings

class EmailTemplates:
    ACTIVATION_TEMPLATE = "emails/template/account_activation.html"
    DEACTIVATION_TEMPLATE = "emails/template/account_deactivation.html"

class SESHandler:
    def __init__(self):
        """Initialize the SES Handler with AWS credentials"""
        self.ses_client = boto3.client('ses',
            region_name=settings.AWS_REGION,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        
        # SMTP configuration
        self.smtp_server = "email-smtp.us-east-1.amazonaws.com"
        self.smtp_port = 587
        self.smtp_username = settings.SES_SMTP_USERNAME
        self.smtp_password = settings.SES_SMTP_PASSWORD
        self.default_sender = f"noreply@{settings.SES_EMAIL_DOMAIN}"
    
    def get_verified_identities(self) -> Dict:
        """Get list of verified email addresses and domains"""
        try:
            response = self.ses_client.list_identities(
                IdentityType='EmailAddress',
                MaxItems=100
            )
            return response
        except Exception as e:
            raise Exception(f"Error fetching verified identities: {str(e)}")
    
    def verify_email_identity(self, email: str) -> Dict:
        """Send verification email to a new identity"""
        try:
            response = self.ses_client.verify_email_identity(
                EmailAddress=email
            )
            return {"message": f"Verification email sent to {email}", "status": "success"}
        except Exception as e:
            raise Exception(f"Error verifying email identity: {str(e)}")
    
    def delete_identity(self, identity: str) -> Dict:
        """Delete a verified identity"""
        try:
            self.ses_client.delete_identity(
                Identity=identity
            )
            return {"message": f"Identity {identity} deleted successfully", "status": "success"}
        except Exception as e:
            raise Exception(f"Error deleting identity: {str(e)}")
    
    def send_email_api(self, 
                   to_addresses: List[str], 
                   subject: str, 
                   body_text: str, 
                   body_html: Optional[str] = None,
                   sender: Optional[str] = None,
                   reply_to: Optional[List[str]] = None) -> Dict:
        """Send email using AWS SES API"""
        if not sender:
            sender = self.default_sender
            
        try:
            email_message = {
                'Subject': {'Data': subject},
                'Body': {
                    'Text': {'Data': body_text}
                }
            }
            
            if body_html:
                email_message['Body']['Html'] = {'Data': body_html}
                
            response = self.ses_client.send_email(
                Source=sender,
                Destination={
                    'ToAddresses': to_addresses,
                },
                Message=email_message,
                ReplyToAddresses=reply_to if reply_to else []
            )
            
            return {
                "message": "Email sent successfully",
                "message_id": response['MessageId'],
                "status": "success"
            }
            
        except Exception as e:
            raise Exception(f"Error sending email via API: {str(e)}")
    
    def send_email_smtp(self, 
                     to_address: str, 
                     subject: str, 
                     body_text: str,
                     body_html: Optional[str] = None,
                     sender_email: Optional[str] = None,
                     sender_name: Optional[str] = None) -> Dict:
        """Send email using SMTP connection
        
        Args:
            to_address: Email address of the recipient
            subject: Email subject
            body_text: Plain text content of the email
            body_html: HTML content of the email (optional)
            sender_email: Email address of the sender (optional)
            sender_name: Name of the sender (optional)
            
        Returns:
            Dict: Status of the email sending operation
        """
        if not sender_email:
            sender_email = self.default_sender
            
        from_address = f"{sender_name} <{sender_email}>" if sender_name else sender_email
        
        try:
            # Create message
            message = MIMEMultipart('alternative')
            message["From"] = from_address
            message["To"] = to_address
            message["Subject"] = subject
            message["X-Priority"] = "3"
            message["List-Unsubscribe"] = f"<mailto:{sender_email}?subject=unsubscribe>"
            message["X-Mailer"] = "Kavia AI Mailer"
            
            # Attach parts - the email client will try to render the last part first
            # Attach plain text part
            part1 = MIMEText(body_text, 'plain')
            message.attach(part1)
            
            # Attach HTML part if provided
            if body_html:
                part2 = MIMEText(body_html, 'html')
                message.attach(part2)
            
            # Create SMTP connection
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()  # Secure the connection
            
            # Login to server
            server.login(self.smtp_username, self.smtp_password)
            
            # Send email
            server.sendmail(sender_email, to_address, message.as_string())
            server.quit()
            
            return {
                "message": f"Email sent successfully to {to_address}",
                "status": "success"
            }
            
        except Exception as e:
            raise Exception(f"Error sending email via SMTP: {str(e)}")
    
    def get_sending_statistics(self) -> Dict:
        """Get email sending statistics from SES"""
        try:
            response = self.ses_client.get_send_statistics()
            return response
        except Exception as e:
            raise Exception(f"Error fetching sending statistics: {str(e)}")

ses_handler = SESHandler()