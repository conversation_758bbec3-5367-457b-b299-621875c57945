import boto3
from botocore.exceptions import ClientError
import logging
from app.utils.file_utils.upload_utils import get_tenant_bucket
from typing import Union, Dict
import json
import datetime
import time
import uuid
from app.core.Settings import settings

def get_staged_profile_picture_bucket(stage: str) -> str:
    """
    Get the profile picture bucket name based on the deployment stage
    
    Args:
        stage (str): Deployment stage (e.g., dev, staging, prod)
        
    Returns:
        str: Profile picture bucket name for the stage
    """
    if stage =="develop" or stage == "dev":
        return "kavia-profile-pictures-dev"
    if stage == "pre_prod":
        return "kavia-profile-pictures-preprod"
    
    return f"kavia-profile-pictures-{stage}"
class S3Handler:
    """
    Handles S3 operations with modern security practices and bucket policy-based access control.
    
    This class implements S3 operations following AWS best practices:
    - Uses bucket policies instead of ACLs for access control
    - Enforces BucketOwnerEnforced ownership control
    - Requires HTTPS for all operations
    - Implements proper error handling and configuration verification
    - Supports both private and public buckets with appropriate security settings
    
    The handler manages two types of buckets:
    1. Private tenant-specific bucket for design files
    2. Public bucket for profile pictures with policy-based access control
    """

    def __init__(self, tenant_id, region_name='us-east-1', folder_name = "design", stage='dev'):
        """
        Initialize S3 handler with tenant ID, region and stage
        
        Args:
            tenant_id (str): Tenant identifier
            region_name (str): AWS region name (default: 'us-east-1')
            stage (str): Deployment stage (default: 'dev') - Used for bucket naming and configuration.
                        Determines the environment-specific bucket names (e.g., dev, staging, prod)
        
        Raises:
            ValueError: If tenant_id is empty or None
            ClientError: If AWS credentials are invalid or missing
            RuntimeError: If bucket creation or configuration fails
            
        Note:
            This implementation uses modern S3 security practices:
            - No ACLs (Access Control Lists) are used
            - Access is controlled via bucket policies
            - BucketOwnerEnforced ownership control is enforced
            - HTTPS is required for all operations
            - Comprehensive error handling and configuration verification
        """
        if not tenant_id:
            raise ValueError("tenant_id cannot be empty or None")
            
        self.tenant_id = tenant_id
        self.region_name = region_name
        self.stage = stage.lower()  # Normalize stage name
        self.bucket_name = get_tenant_bucket(tenant_id)  # Get bucket from tenant_id
        self.cognito_manager =  None
        # Stage-specific bucket for public profile pictures
        # Format: kavia-profile-pictures-{stage} (e.g., kavia-profile-pictures-dev)
        # This bucket is configured for public read access and optimized for image hosting
        self.profile_pictures_bucket = get_staged_profile_picture_bucket(settings.STAGE)
        self.s3_client = boto3.client('s3', region_name=region_name)
        self.logger = logging.getLogger(__name__)
        self.folder_name = folder_name
        
        # Ensure both buckets exist and are properly configured
        if not self._ensure_bucket_exists():
            raise RuntimeError(f"Failed to create/configure main bucket {self.bucket_name}")
            
        self.logger.info(f"Successfully initialized S3Handler with buckets: {self.bucket_name} and {self.profile_pictures_bucket}")

    
    def set_cognito_manager(self, cognito_manager):
        self.cognito_manager = cognito_manager
    def _get_full_path(self, filename):
        """
        Get full S3 path including design folder
        
        Args:
            filename (str): Name of the file
            
        Returns:
            str: Full path including folder
        """
        return f"{self.folder_name}/{filename}"
    def _get_full_path_CGA(self, filename):
        """
        Get full S3 path including design folder
        
        Args:
            filename (str): Name of the file
            
        Returns:
            str: Full path including folder
        """
        return f"design_CGA/{filename}"

    def _ensure_bucket_exists(self):
        """
        Check if bucket exists and create it if it doesn't
        
        Returns:
            bool: True if bucket exists or was created successfully, False otherwise
        """
        try:
            self.s3_client.head_bucket(Bucket=self.bucket_name)
            return True
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code')
            
            if error_code == '404' or error_code == '403':
                # Bucket doesn't exist or we can't verify ownership
                return self._create_bucket()
            
            self.logger.error(f"Error checking bucket {self.bucket_name}: {str(e)}")
            return False

    def _create_bucket(self):
        """
        Create an S3 bucket and configure it with proper settings
        
        Returns:
            bool: True if bucket was created and configured successfully, False otherwise
        """
        try:
            if self.region_name == 'us-east-1':
                # Special case for us-east-1 which doesn't accept LocationConstraint
                self.s3_client.create_bucket(
                    Bucket=self.bucket_name
                )
            else:
                self.s3_client.create_bucket(
                    Bucket=self.bucket_name,
                    CreateBucketConfiguration={
                        'LocationConstraint': self.region_name
                    }
                )
            
            # Configure bucket with secure settings
            self.s3_client.put_public_access_block(
                Bucket=self.bucket_name,
                PublicAccessBlockConfiguration={
                    'BlockPublicAcls': True,  # Block ACLs for security
                    'IgnorePublicAcls': True,  # Ignore any existing ACLs
                    'BlockPublicPolicy': False,  # Allow bucket policy for controlled access
                    'RestrictPublicBuckets': False  # Allow bucket policy for controlled access
                }
            )
            
            # Set BucketOwnerEnforced ownership control
            self.s3_client.put_bucket_ownership_controls(
                Bucket=self.bucket_name,
                OwnershipControls={
                    'Rules': [{'ObjectOwnership': 'BucketOwnerEnforced'}]
                }
            )
            
            # Configure bucket policy for controlled access
            bucket_policy = {
                "Version": "2012-10-17",
                "Statement": [
                    {
                        "Sid": "DenyNonSecureTransport",
                        "Effect": "Deny",
                        "Principal": "*",
                        "Action": "s3:*",
                        "Resource": [
                            f"arn:aws:s3:::{self.bucket_name}",
                            f"arn:aws:s3:::{self.bucket_name}/*"
                        ],
                        "Condition": {
                            "Bool": {
                                "aws:SecureTransport": "false"
                            }
                        }
                    },
                    {
                        "Sid": "DenyACLs",
                        "Effect": "Deny",
                        "Principal": "*",
                        "Action": "s3:PutObjectAcl",
                        "Resource": [f"arn:aws:s3:::{self.bucket_name}/*"]
                    }
                ]
            }
            
            # Apply bucket policy
            self.s3_client.put_bucket_policy(
                Bucket=self.bucket_name,
                Policy=json.dumps(bucket_policy)
            )
            
            # Verify the configuration
            if not self._verify_bucket_configuration(self.bucket_name):
                raise RuntimeError("Failed to verify bucket configuration")
            
            self.logger.info(f"Successfully created and configured bucket {self.bucket_name}")
            return True
            
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code')
            error_msg = e.response.get('Error', {}).get('Message', str(e))
            self.logger.error(f"Error creating/configuring bucket {self.bucket_name}: {error_code} - {error_msg}")
            return False
        except RuntimeError as e:
            self.logger.error(str(e))
            return False

    def add_file(self, filename, data):
        """
        Upload a file to S3 bucket with proper error handling and configuration
        
        Args:
            filename (str): Name of the file to be created in S3
            data (bytes|str|file-like object): Content to be uploaded
            
        Returns:
            bool: True if successful, False otherwise
            
        Raises:
            ValueError: If filename is empty/None or data is empty
            ClientError: For AWS-related errors during upload
            RuntimeError: If bucket configuration is invalid
        """
        if not filename:
            raise ValueError("filename cannot be empty or None")
        if not data:
            raise ValueError("data cannot be empty")
            
        try:
            # Ensure bucket exists and is properly configured
            if not self._ensure_bucket_exists():
                raise RuntimeError(f"Failed to ensure bucket {self.bucket_name} exists and is properly configured")
                
            # Verify bucket configuration before upload
            if not self._verify_bucket_configuration(self.bucket_name):
                self.logger.error(f"Bucket {self.bucket_name} configuration verification failed")
                return False

            try:
                # Upload object with proper metadata
                self.s3_client.put_object(
                    Bucket=self.bucket_name,
                    Key=self._get_full_path(filename),
                    Body=data,
                    Metadata={
                        'tenant-id': self.tenant_id,
                        'upload-date': datetime.datetime.utcnow().isoformat()
                    }
                )
                
                self.logger.info(f"Successfully uploaded file {filename}")
                return True
                
            except ClientError as e:
                error_code = e.response.get('Error', {}).get('Code')
                error_msg = e.response.get('Error', {}).get('Message', str(e))
                
                if error_code == 'AccessControlListNotSupported':
                    self.logger.error("AccessControlListNotSupported - Bucket is using Object Ownership settings that require bucket policy for access control")
                elif error_code == 'NoSuchBucket':
                    self.logger.error(f"Bucket {self.bucket_name} does not exist")
                elif error_code == 'InvalidRequest':
                    self.logger.error(f"Invalid request while uploading file: {error_msg}")
                else:
                    self.logger.error(f"Error uploading file {filename}: {error_code} - {error_msg}")
                    
                return False
            
        except Exception as e:
            self.logger.error(f"Unexpected error uploading file {filename}: {str(e)}")
            return False

    def delete_file(self, filename):
        """
        Delete a file from S3 bucket
        
        Args:
            filename (str): Name of the file to be deleted
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.s3_client.delete_object(
                Bucket=self.bucket_name,
                Key=self._get_full_path(filename)  # Use full path with design folder
            )
            return True
        except ClientError as e:
            self.logger.error(f"Error deleting file {filename}: {str(e)}")
            return False

    def update_file(self, filename, data):
        """
        Update an existing file in S3 bucket
        
        Args:
            filename (str): Name of the file to be updated
            data (bytes|str|file-like object): New content
            
        Returns:
            bool: True if successful, False otherwise
        """
        # In S3, put_object will overwrite if file exists
        return self.add_file(filename, data)

    def get_file(self, filename):
        """
        Retrieve a file from S3 bucket
        
        Args:
            filename (str): Name of the file to retrieve
            
        Returns:
            bytes: File content if successful, None otherwise
        """
        try:
            response = self.s3_client.get_object(
                Bucket=self.bucket_name,
                Key=self._get_full_path(filename)  # Use full path with design folder
            )
            return response['Body'].read()
        except ClientError as e:
            if e.response['Error']['Code'] == 'NoSuchKey':
                self.logger.warning(f"File {filename} not found")
            else:
                self.logger.error(f"Error retrieving file {filename}: {str(e)}")
            return None
        
    def list_all_filenames(self, root_path):
        """
        Retrieve upto 1000 file names from S3 bucket by providing path
        
        Args:
            root_path (str): Path from where to retrieve files
            
        Returns:
            file_names(list): List with all file names, None otherwise
        """
        try:
            response = self.s3_client.list_objects_v2(
                Bucket=self.bucket_name,
                Prefix=root_path
            )

            file_names = []

            if response.get('Contents', []):
                for content in response['Contents']:
                    if 'Key' in content:
                        if root_path.endswith('/'):
                            file_names.append(content['Key'].split(root_path)[1])
                        else:
                            if len(content['Key'].split(f"{root_path}/")) > 1:
                                file_names.append(content['Key'].split(f"{root_path}/")[1])

            return file_names
        except ClientError as e:
            return None


    def is_file(self, filename):
        """
        Check if a file exists in S3 bucket
        
        Args:
            filename (str): Name of the file to check
            
        Returns:
            bool: True if file exists, False otherwise
        """
        try:
            self.s3_client.head_object(
                Bucket=self.bucket_name,
                Key=self._get_full_path(filename)  # Use full path with design folder
            )
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return False
            self.logger.error(f"Error checking file {filename}: {str(e)}")
            return False

    async def update_file_async(self, file_key: str, content: Union[str, bytes, Dict], content_type: str = None,is_CGA: bool = False) -> bool:
        """
        Asynchronously update a file in S3
        
        Args:
            file_key (str): The key/path of the file in S3
            content (Union[str, bytes, Dict]): The content to upload
            content_type (str, optional): The MIME type of the content
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get the full path including the design folder
            full_path = self._get_full_path(file_key) if not is_CGA else self._get_full_path_CGA(file_key)
            
            # Convert content to appropriate format
            if isinstance(content, dict):
                content = json.dumps(content)
            if isinstance(content, str):
                content = content.encode('utf-8')
            
            # Ensure bucket exists
            if not self._ensure_bucket_exists():
                print(f"Failed to ensure bucket {self.bucket_name} exists")
                return False
                
            # Direct S3 upload without run_in_executor
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=full_path,
                Body=content,
                ContentType=content_type or 'application/json'
            )
            
            print(f"Successfully updated file {full_path} in bucket {self.bucket_name}")
            return True
            
        except Exception as e:
            print(f"Error updating file {file_key} in S3: {str(e)}")
            return False

    def _get_profile_picture_path(self, user_id: str) -> str:
        """
        Get full S3 path for profile picture
        
        Args:
            user_id (str): User identifier
            
        Returns:
            str: Path for the profile picture in the dedicated bucket
        """
        return f"user-{user_id}"


    def upload_profile_picture(self, user_id: str, image_data: bytes, content_type: str = 'image/jpeg') -> str:
        if not user_id or not image_data:
            raise ValueError("user_id and image_data cannot be empty or None")
            
        try:
            key = self._get_profile_picture_path(user_id)
            new_key = f"{key}-{uuid.uuid4()}"
            
            try:
                # Upload new version
                self.s3_client.put_object(
                    Bucket=self.profile_pictures_bucket,
                    Key=new_key,
                    Body=image_data,
                    ContentType=content_type,
                    Metadata={
                        'user-id': user_id,
                        'tenant-id': self.tenant_id,
                        'timestamp': str(int(time.time()))
                    },
                    CacheControl='public, max-age=31536000',  # Cache for 1 year
                    ContentDisposition=f'inline; filename=\"profile-{user_id}\"'
                )
                
                # Get old picture key from Cognito and delete in background
                try:
                    user = self.cognito_manager.get_user(
                        user_id
                    )
                    old_picture_key = next(
                        (attr['Value'].split('/')[-1] for attr in user['UserAttributes'] 
                        if attr['Name'] == 'picture'),
                        None
                    )
                    
                    if old_picture_key:      
                        self._delete_old_picture(
                            bucket=self.profile_pictures_bucket,
                            key=old_picture_key)
                        
                        
                except Exception as e:
                    self.logger.warning(f"Failed to schedule old picture deletion: {str(e)}")
                
                self.logger.info(f"Successfully uploaded profile picture for user {user_id}")
                return new_key
                
            except ClientError as e:
                error_code = e.response.get('Error', {}).get('Code')
                error_msg = e.response.get('Error', {}).get('Message', str(e))
                self.logger.error(f"Error uploading profile picture: {error_code} - {error_msg}")
                return ""
                
        except Exception as e:
            self.logger.error(f"Unexpected error uploading profile picture: {str(e)}")
            return ""

    def _delete_old_picture(self, bucket: str, key: str):
        """Delete a single old profile picture."""
        try:
            self.s3_client.delete_object(
                Bucket=bucket,
                Key=key
            )
            return True
        except Exception as e:
            self.logger.error(f"Failed to delete old picture {key}: {str(e)}")
            return False
            
    def get_profile_picture(self, user_id: str) -> Union[bytes, None]:
        """
        Retrieve a user's profile picture from the dedicated S3 bucket
        
        Args:
            user_id (str): User identifier
            
        Returns:
            Union[bytes, None]: Image content if successful, None otherwise
            
        Raises:
            ValueError: If user_id is empty or None
            ClientError: For AWS-related errors
        """
        if not user_id:
            raise ValueError("user_id cannot be empty or None")
        try:
            response = self.s3_client.get_object(
                Bucket=self.profile_pictures_bucket,
                Key=self._get_profile_picture_path(user_id)
            )
            return response['Body'].read()
        except ClientError as e:
            if e.response['Error']['Code'] == 'NoSuchKey':
                self.logger.warning(f"Profile picture not found for user {user_id}")
            else:
                self.logger.error(f"Error retrieving profile picture for user {user_id}: {str(e)}")
            return None

    # PUBLIC_INTERFACE
    def update_profile_picture(self, user_id: str, image_data: bytes, content_type: str = 'image/jpeg') -> bool:
        """
        Update a user's profile picture in S3
        
        Args:
            user_id (str): User identifier
            image_data (bytes): New image content in bytes
            content_type (str): Image MIME type (default: 'image/jpeg')
            
        Returns:
            bool: True if successful, False otherwise
        """
        return self.upload_profile_picture(user_id, image_data, content_type)

    def has_profile_picture(self, user_id: str) -> bool:
        """
        Check if a user has a profile picture in the dedicated S3 bucket
        
        Args:
            user_id (str): User identifier
            
        Returns:
            bool: True if profile picture exists, False otherwise
        """
        try:
            self.s3_client.head_object(
                Bucket=self.profile_pictures_bucket,
                Key=self._get_profile_picture_path(user_id)
            )
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return False
            self.logger.error(f"Error checking profile picture for user {user_id}: {str(e)}")
            return False

    # PUBLIC_INTERFACE
    def get_profile_picture_url(self, user_id: str) -> str:
        """
        Get the public URL for a user's profile picture
        
        Args:
            user_id (str): User identifier
            
        Returns:
            str: Public URL of the profile picture
        """
        # Format: https://{bucket}.s3.{region}.amazonaws.com/{key}
        return f"https://{self.profile_pictures_bucket}.s3.{self.region_name}.amazonaws.com/{self._get_profile_picture_path(user_id)}"

    def _get_bucket_url(self, bucket_name: str) -> str:
        """
        Get the base URL for an S3 bucket
        
        Args:
            bucket_name (str): Name of the S3 bucket
            
        Returns:
            str: Base URL for the bucket
        """
        return f"https://{bucket_name}.s3.{self.region_name}.amazonaws.com"
    def _verify_bucket_configuration(self, bucket_name: str) -> bool:
        """
        Verify that a bucket has the correct configuration settings.
        Checks for:
        - Bucket policy existence and validity
        - Ownership controls (BucketOwnerEnforced)
        - Public access block settings
        - CORS configuration (for profile pictures bucket)
        - Encryption settings
        - Versioning status
        - Object Lock configuration
        - Lifecycle rules
        - Logging configuration
        
        Args:
            bucket_name (str): Name of the bucket to verify
            
        Returns:
            bool: True if configuration is correct, False otherwise
            
        Raises:
            RuntimeError: If any configuration verification fails
            ClientError: For AWS-related errors during verification
        """
        try:
            # Verify bucket policy with detailed checks
            try:
                policy = self.s3_client.get_bucket_policy(Bucket=bucket_name)
                if not policy or 'Policy' not in policy:
                    raise RuntimeError("Bucket policy verification failed")
                    
                policy_json = json.loads(policy['Policy'])
                required_policies = {
                    'DenyNonSecureTransport': False,
                    'DenyACLs': False
                }
                
                for statement in policy_json.get('Statement', []):
                    sid = statement.get('Sid', '')
                    if sid in required_policies:
                        required_policies[sid] = True
                
                missing_policies = [policy for policy, exists in required_policies.items() if not exists]
                if missing_policies:
                    raise RuntimeError(f"Missing required policies: {', '.join(missing_policies)}")
            except ClientError as e:
                if e.response['Error']['Code'] == 'NoSuchBucketPolicy':
                    raise RuntimeError("No bucket policy found")
                raise
            
            # Verify ownership controls
            ownership = self.s3_client.get_bucket_ownership_controls(Bucket=bucket_name)
            if not ownership or 'Rules' not in ownership:
                raise RuntimeError("Ownership controls verification failed")
                
            rules = ownership.get('Rules', [])
            if not any(rule.get('ObjectOwnership') == 'BucketOwnerEnforced' for rule in rules):
                raise RuntimeError("BucketOwnerEnforced ownership not configured")
            
            # Verify public access block settings
            public_access = self.s3_client.get_public_access_block(Bucket=bucket_name)
            if not public_access or 'PublicAccessBlockConfiguration' not in public_access:
                raise RuntimeError("Public access block configuration verification failed")
                
            config = public_access['PublicAccessBlockConfiguration']
            if not (config.get('BlockPublicAcls') and config.get('IgnorePublicAcls')):
                raise RuntimeError("ACL blocking not properly configured")
            
            # Verify encryption settings with stricter requirements
            try:
                encryption = self.s3_client.get_bucket_encryption(Bucket=bucket_name)
                if not encryption or 'ServerSideEncryptionConfiguration' not in encryption:
                    self._configure_bucket_encryption(bucket_name)
                else:
                    # Verify encryption algorithm (AES256 or KMS)
                    rules = encryption['ServerSideEncryptionConfiguration']['Rules']
                    if not any(rule.get('ApplyServerSideEncryptionByDefault', {}).get('SSEAlgorithm') 
                              in ['AES256', 'aws:kms'] for rule in rules):
                        self._configure_bucket_encryption(bucket_name)
            except ClientError as e:
                if e.response['Error']['Code'] == 'ServerSideEncryptionConfigurationNotFoundError':
                    self._configure_bucket_encryption(bucket_name)
                else:
                    raise
            
            # Verify versioning status
            versioning = self.s3_client.get_bucket_versioning(Bucket=bucket_name)
            if versioning.get('Status') != 'Enabled':
                self.logger.warning(f"Versioning not enabled for bucket {bucket_name}")
            
            # Verify Object Lock configuration
            try:
                object_lock = self.s3_client.get_object_lock_configuration(Bucket=bucket_name)
                if not object_lock or 'ObjectLockConfiguration' not in object_lock:
                    self.logger.info(f"Object Lock not configured for bucket {bucket_name}")
            except ClientError as e:
                if e.response['Error']['Code'] not in ['ObjectLockConfigurationNotFoundError', 'MethodNotAllowed']:
                    raise

            # Verify logging configuration
            try:
                logging_config = self.s3_client.get_bucket_logging(Bucket=bucket_name)
                if not logging_config or 'LoggingEnabled' not in logging_config:
                    self.logger.info(f"Logging not enabled for bucket {bucket_name}")
            except ClientError as e:
                if e.response['Error']['Code'] != 'NoSuchBucketPolicy':
                    raise

            # For profile pictures bucket, verify CORS configuration with detailed checks
            if bucket_name == self.profile_pictures_bucket:
                try:
                    cors = self.s3_client.get_bucket_cors(Bucket=bucket_name)
                    if not cors or 'CORSRules' not in cors:
                        raise RuntimeError("CORS configuration verification failed")
                        
                    # Verify CORS rules in detail
                    rules = cors['CORSRules']
                    required_headers = ['Authorization', 'Content-Type']
                    required_methods = ['GET', 'PUT']
                    required_origins = [f'https://*.kavia.ai']
                    
                    for rule in rules:
                        if not all(header in rule.get('AllowedHeaders', []) for header in required_headers):
                            raise RuntimeError("Missing required CORS headers")
                        if not all(method in rule.get('AllowedMethods', []) for method in required_methods):
                            raise RuntimeError("Missing required CORS methods")
                        if not any(origin in rule.get('AllowedOrigins', []) for origin in required_origins):
                            raise RuntimeError("Missing required CORS origins")
                except ClientError as e:
                    if e.response['Error']['Code'] == 'NoSuchCORSConfiguration':
                        raise RuntimeError("CORS configuration missing for profile pictures bucket")
                    raise
            
            self.logger.info(f"All configurations verified successfully for bucket {bucket_name}")
            self.logger.info("- Bucket policy verified with HTTPS enforcement and ACL blocking")
            self.logger.info("- BucketOwnerEnforced ownership confirmed")
            self.logger.info("- Public access block settings verified")
            self.logger.info("- ACL blocking confirmed")
            self.logger.info("- Server-side encryption configured")
            self.logger.info("- Object Lock status verified")
            self.logger.info("- Logging configuration checked")
            if bucket_name == self.profile_pictures_bucket:
                self.logger.info("- CORS configuration verified with detailed rules")
            
            return True
            
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code')
            error_msg = e.response.get('Error', {}).get('Message', str(e))
            self.logger.error(f"Failed to verify bucket configuration: {error_code} - {error_msg}")
            if error_code == 'AccessDenied':
                self.logger.error("Insufficient permissions to verify bucket configuration")
            elif error_code == 'NoSuchBucket':
                self.logger.error(f"Bucket {bucket_name} does not exist")
            elif error_code == 'InvalidRequest':
                self.logger.error(f"Invalid request while verifying configuration: {error_msg}")
            return False
        except RuntimeError as e:
            self.logger.error(f"Configuration verification failed: {str(e)}")
            self.logger.error("Please check bucket configuration and permissions")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error during configuration verification: {str(e)}")
            return False
    def _configure_bucket_encryption(self, bucket_name: str) -> bool:
        """
        Configure default encryption for a bucket using AES256
        
        Args:
            bucket_name (str): Name of the bucket to configure
            
        Returns:
            bool: True if successful, False otherwise
            
        Raises:
            ClientError: For AWS-related errors during encryption configuration
        """
        try:
            self.s3_client.put_bucket_encryption(
                Bucket=bucket_name,
                ServerSideEncryptionConfiguration={
                    'Rules': [
                        {
                            'ApplyServerSideEncryptionByDefault': {
                                'SSEAlgorithm': 'AES256'
                            },
                            'BucketKeyEnabled': True
                        }
                    ]
                }
            )
            self.logger.info(f"Successfully configured encryption for bucket {bucket_name}")
            return True
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code')
            error_msg = e.response.get('Error', {}).get('Message', str(e))
            self.logger.error(f"Failed to configure bucket encryption: {error_code} - {error_msg}")
            if error_code == 'AccessDenied':
                self.logger.error("Insufficient permissions to configure bucket encryption")
            elif error_code == 'InvalidRequest':
                self.logger.error(f"Invalid encryption configuration request: {error_msg}")
            return False

    def copy_files(self, source_path: str, destination_path: str, 
               destination_bucket: str = None, source_bucket: str = None,
               preserve_metadata: bool = True, recursive: bool = False) -> Union[bool, Dict[str, bool]]:
        """
        Universal copy method that handles both single files and folders,
        to same bucket or different bucket
        
        Args:
            source_path (str): Source file/folder path
            destination_path (str): Destination file/folder path
            destination_bucket (str, optional): Target bucket name. If None, uses current bucket
            source_bucket (str, optional): Source bucket name. If None, uses current bucket
            preserve_metadata (bool): Whether to preserve original metadata
            recursive (bool): If True, copy entire folder recursively. If False, copy single file
            
        Returns:
            Union[bool, Dict[str, bool]]: 
                - For single file: True if successful, False otherwise
                - For recursive copy: Dictionary mapping file keys to copy success status
                
        Examples:
            # Copy single file within same bucket to different folder
            handler.copy_files(
                "extracted-docs-T0004/project-6947/file.json",
                "backup/extracted-docs-T0004/project-6947/file.json"
            )
            
            # Copy single file to different bucket
            handler.copy_files(
                "extracted-docs-T0004/project-6947/file.json",
                "archived/project-6947/file.json",
                destination_bucket="kavia-archive-bucket"
            )
            
            # Copy entire folder within same bucket
            handler.copy_files(
                "extracted-docs-T0004/project-6947/",
                "backup/extracted-docs-T0004/project-6947/",
                recursive=True
            )
            
            # Copy from different source bucket to different destination bucket
            handler.copy_files(
                "extracted-docs-T0004/project-6947/file.json",
                "archived/project-6947/file.json",
                destination_bucket="kavia-archive-bucket",
                source_bucket="kavia-attachments-dev-t0005"
            )
        """
        if not source_path or not destination_path:
            raise ValueError("source_path and destination_path cannot be empty")

        # Use current bucket if no destination bucket specified
        target_bucket = destination_bucket or self.bucket_name
        source_bucket_name = source_bucket or self.bucket_name
        is_cross_bucket = destination_bucket is not None

        target_bucket = get_tenant_bucket(target_bucket)
        print('target_bucket--->', target_bucket)
        print('source_path-------->', source_path)
        try:
            if recursive:
                return self._copy_folder(
                    source_path, destination_path, target_bucket, source_bucket_name,
                    preserve_metadata, is_cross_bucket
                )
            else:
                return self._copy_single_file(
                    source_path, destination_path, target_bucket, source_bucket_name,
                    preserve_metadata, is_cross_bucket
                )
                
        except Exception as e:
            print('error', e)
            self.logger.error(f"Unexpected error in copy_files: {str(e)}")
            return False if not recursive else {}
        
    def _copy_single_file(self, source_key: str, destination_key: str, 
                     target_bucket: str, source_bucket_name: str, preserve_metadata: bool, 
                     is_cross_bucket: bool) -> bool:
        """
        Internal method to copy a single file
        
        Args:
            source_key (str): Source file key/path
            destination_key (str): Destination file key/path
            target_bucket (str): Target bucket name
            source_bucket_name (str): Source bucket name
            preserve_metadata (bool): Whether to preserve original metadata
            is_cross_bucket (bool): Whether copying to different bucket
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            copy_source = {
                'Bucket': source_bucket_name,
                'Key': source_key
            }
            
            extra_args = {}
            if preserve_metadata:
                # Get source object metadata
                try:
                    response = self.s3_client.head_object(
                        Bucket=source_bucket_name,
                        Key=source_key
                    )
                    extra_args['Metadata'] = response.get('Metadata', {})
                    extra_args['MetadataDirective'] = 'REPLACE'
                except ClientError as e:
                    self.logger.warning(f"Could not retrieve metadata for {source_key}: {str(e)}")
            
            self.s3_client.copy_object(
                CopySource=copy_source,
                Bucket=target_bucket,
                Key=destination_key,
                **extra_args
            )
            
            operation_type = "cross-bucket" if is_cross_bucket else "within-bucket"
            self.logger.info(f"Successfully copied {source_bucket_name}/{source_key} to {target_bucket}/{destination_key} ({operation_type})")
            return True
            
        except ClientError as e:
            print('error:--', e)
            error_code = e.response.get('Error', {}).get('Code')
            error_msg = e.response.get('Error', {}).get('Message', str(e))
            
            if error_code == 'NoSuchBucket':
                self.logger.error(f"Bucket {target_bucket} does not exist")
            elif error_code == 'NoSuchKey':
                self.logger.error(f"Source file {source_key} does not exist in bucket {source_bucket_name}")
            elif error_code == 'AccessDenied':
                self.logger.error(f"Access denied when copying from {source_bucket_name} to {target_bucket}")
            else:
                self.logger.error(f"Error copying file: {error_code} - {error_msg}")
            return False
        except Exception as e:
            print('error::', e)
            self.logger.error(f"Unexpected error copying single file: {str(e)}")
            return False    
        
    def _copy_folder(self, source_prefix: str, destination_prefix: str, 
                target_bucket: str, source_bucket_name: str, preserve_metadata: bool, 
                is_cross_bucket: bool) -> Dict[str, bool]:
        """
        Internal method to copy all files in a folder recursively
        
        Args:
            source_prefix (str): Source folder prefix
            destination_prefix (str): Destination folder prefix
            target_bucket (str): Target bucket name
            source_bucket_name (str): Source bucket name
            preserve_metadata (bool): Whether to preserve original metadata
            is_cross_bucket (bool): Whether copying to different bucket
            
        Returns:
            Dict[str, bool]: Dictionary mapping file keys to copy success status
        """
        print('---source_prefix>>>>>', source_prefix)
        print('target_bucket--->', target_bucket)

        # Ensure prefixes end with /
        if not source_prefix.endswith('/'):
            source_prefix += '/'
        if not destination_prefix.endswith('/'):
            destination_prefix += '/'
            
        results = {}
        print('source_bucket_name--->', source_bucket_name)
        print('source_prefix--->', source_prefix)
        try:
            # List all objects with the source prefix
            paginator = self.s3_client.get_paginator('list_objects_v2')
            pages = paginator.paginate(Bucket=source_bucket_name, Prefix=source_prefix)
            
            print('pages--->>', pages)

            total_files = 0
            for page in pages:
                print('page--->', page)
                if 'Contents' not in page:
                    continue
                    
                for obj in page['Contents']:
                    source_key = obj['Key']
                    
                    # Skip if it's just the folder itself (ends with /)
                    if source_key.endswith('/'):
                        continue
                        
                    # Replace source prefix with destination prefix
                    destination_key = source_key.replace(source_prefix, destination_prefix, 1)
                    
                    success = self._copy_single_file(
                        source_key, destination_key, target_bucket, source_bucket_name,
                        preserve_metadata, is_cross_bucket
                    )
                    results[source_key] = success
                    total_files += 1
                    
            operation_type = "cross-bucket" if is_cross_bucket else "within-bucket"
            success_count = sum(1 for success in results.values() if success)
            
            self.logger.info(f"Copied {success_count}/{total_files} files from {source_bucket_name}/{source_prefix} "
                            f"to {target_bucket}/{destination_prefix} ({operation_type})")
            
            print('target_bucket-', target_bucket)
            print('destination_prefix-', destination_prefix)
            if total_files == 0:
                self.logger.warning(f"No files found with prefix {source_prefix}")

            results['target_bucket'] = target_bucket  
            results['destination_prefix'] = destination_prefix  

            return results
            
        except ClientError as e:
            print('error:', e)
            error_code = e.response.get('Error', {}).get('Code')
            error_msg = e.response.get('Error', {}).get('Message', str(e))
            self.logger.error(f"Error copying folder: {error_code} - {error_msg}")
            return results
        except Exception as e:
            print('error:-', e)
            self.logger.error(f"Unexpected error copying folder: {str(e)}")
            return results    