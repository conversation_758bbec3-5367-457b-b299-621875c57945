from typing import Dict, Any, Callable, List
import subprocess
from pathlib import Path
from app.models.code_generation_model import Message
from app.core.constants import TASKS_COLLECTION_NAME
from app.core.git_tools import EnhancedGitTools
from app.connection.tenant_middleware import get_tenant_id
from app.core.git_status_monitor import GitStatusMonitor
from app.core.git_llm import GitLLM
from app.utils.code_generation_utils import get_logs_path
from app.utils.datetime_utils import generate_timestamp
from app.utils.task_utils import CheckPoints, CheckPoint
from app.core.Settings import settings
import re


def get_sensitive_fields_from_settings():
    """
    Dynamically extract all field names from Settings class that likely contain sensitive data
    """
    sensitive_keywords = [
        'password', 'secret', 'key', 'token', 'uri', 'connection', 
        'access', 'credentials', 'auth', 'salt', 'smtp'
    ]
    
    sensitive_fields = []
    
    # Get all field names from Settings class
    for field_name in settings.model_fields.keys():
        field_name_lower = field_name.lower()
        
        # Check if field name contains any sensitive keywords
        if any(keyword in field_name_lower for keyword in sensitive_keywords):
            sensitive_fields.append(field_name)
    
    return sensitive_fields


def check_diff_for_sensitive_data(repository_path):
    """
    Check git diff for sensitive data from Settings fields before committing
    Returns tuple: (has_sensitive_data, sensitive_files, patterns_found)
    """
    try:
        # Get git diff output for staged changes
        diff_result = subprocess.run(
            ["git", "diff", "--cached"],
            cwd=repository_path,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if diff_result.returncode != 0:
            print(f"Error getting git diff: {diff_result.stderr}")
            return False, [], []
        
        diff_content = diff_result.stdout
        if not diff_content.strip():
            return False, [], []  # No staged changes
        
        # Get sensitive fields from Settings
        sensitive_fields = get_sensitive_fields_from_settings()
        print(f"🔍 Checking for these sensitive fields: {sensitive_fields}")
        
        sensitive_files = set()
        patterns_found = []
        
        # Create regex patterns for each sensitive field
        sensitive_patterns = []
        for field in sensitive_fields:
            # Various formats where sensitive data might appear
            patterns = [
                rf'{field}\s*=\s*["\']?([^"\'\s\n]+)["\']?',  # FIELD=value or FIELD="value"
                rf'["\']?{field}["\']?\s*:\s*["\']([^"\']+)["\']',  # "FIELD": "value" (JSON/dict)
                rf'{field}\s*[=:]\s*["\']([^"\']+)["\']',  # FIELD: "value"
                rf'env\(["\']?{field}["\']?\)',  # env("FIELD") or env('FIELD')
                rf'getenv\(["\']?{field}["\']?\)',  # getenv("FIELD")
                rf'environ\.get\(["\']?{field}["\']?\)',  # environ.get("FIELD")
            ]
            sensitive_patterns.extend(patterns)
        
        # Check each line in the diff
        current_file = None
        for line in diff_content.split('\n'):
            # Track current file being processed
            if line.startswith('+++'):
                current_file = line.split('+++')[-1].strip()
                if current_file.startswith('b/'):
                    current_file = current_file[2:]
            
            # Only check added lines (starting with +)
            if line.startswith('+') and not line.startswith('+++'):
                line_content = line[1:]  # Remove the + prefix
                
                # Check against all sensitive patterns
                for pattern in sensitive_patterns:
                    try:
                        match = re.search(pattern, line_content, re.IGNORECASE)
                        if match:
                            if current_file:
                                sensitive_files.add(current_file)
                            patterns_found.append({
                                'file': current_file,
                                'pattern': pattern,
                                'line': line_content.strip()[:100],  # Truncate for safety
                                'field_detected': True
                            })
                    except re.error as e:
                        print(f"Regex error with pattern {pattern}: {e}")
                        continue
        
        has_sensitive = len(sensitive_files) > 0
        return has_sensitive, list(sensitive_files), patterns_found
        
    except subprocess.TimeoutExpired:
        print("Git diff check timed out")
        return False, [], []
    except Exception as e:
        print(f"Error checking diff for sensitive data: {e}")
        return False, [], []


def add_to_gitignore(repository_path, files_to_ignore):
    """
    Add sensitive files to .gitignore
    """
    try:
        gitignore_path = Path(repository_path) / '.gitignore'
        
        # Read existing .gitignore content
        existing_content = ""
        if gitignore_path.exists():
            with open(gitignore_path, 'r', encoding='utf-8') as f:
                existing_content = f.read()
        
        # Prepare new entries
        new_entries = []
        for file_path in files_to_ignore:
            if file_path not in existing_content:
                new_entries.append(file_path)
        
        if new_entries:
            # Add separator comment if not empty
            if existing_content and not existing_content.endswith('\n'):
                existing_content += '\n'
            
            # Add comment about auto-added entries
            existing_content += '\n# Auto-added by security check to prevent sensitive data exposure\n'
            
            # Add each file
            for entry in new_entries:
                existing_content += f"{entry}\n"
            
            # Write updated .gitignore
            with open(gitignore_path, 'w', encoding='utf-8') as f:
                f.write(existing_content)
            
            print(f"✅ Added {len(new_entries)} files to .gitignore: {new_entries}")
            return True
        
        return False
        
    except Exception as e:
        print(f"Error updating .gitignore: {e}")
        return False


def unstage_sensitive_files(repository_path, sensitive_files):
    """
    Unstage files that contain sensitive data
    """
    try:
        for file_path in sensitive_files:
            result = subprocess.run(
                ["git", "reset", "HEAD", file_path],
                cwd=repository_path,
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode != 0:
                print(f"Warning: Could not unstage {file_path}: {result.stderr}")
        
        print(f"🔄 Unstaged {len(sensitive_files)} sensitive files")
        return True
    except Exception as e:
        print(f"Error unstaging sensitive files: {e}")
        return False


class GitController:
    def __init__(self, task_id, git_tool:EnhancedGitTools, directories: List[str], ws_client, db, repository_metadata:List[Dict[str, Any]]=None):
        self.task_id = task_id
        self.git_tool = git_tool
        self.base_paths = directories
        self.current_repo_index = 0
        self.ws_client = ws_client
        self.db = db
        self.repository_metadata = repository_metadata
        self._command_map = self._initialize_command_map()
        self.status_monitor: GitStatusMonitor = None
        self.initialize_git_tool()
        # Reduced limits to 50
        self.MAX_MESSAGE_HISTORY = 50
        self.MAX_COMMIT_LOGS = 50
        self.auto_commit = True
        self.auto_push = False
        self.llm = GitLLM(task_id, logs_path=get_logs_path(task_id))
                
    def initialize_git_tool(self):
        self.git_tool.repository_metadata = self.current_repository_metadata
        self.git_tool.base_path = self.current_base_path
    
    def set_auto_push(self, auto_push: bool):
        self.auto_push = True
        
    def set_status_monitor(self, status_monitor: GitStatusMonitor):
        self.status_monitor = status_monitor
        self.status_monitor.set_current_base_path(self.current_base_path)
    
    @property
    def current_base_path(self):
        return self.base_paths[self.current_repo_index]

    @property
    def current_repository_metadata(self):
        return self.repository_metadata[self.current_repo_index] if self.repository_metadata else None

    def controller_status(self, _):
        status_data ={
            "auto-commit": self.auto_commit,
            "auto-push": self.auto_push,
            "current-repository": self.current_repository_metadata
        }
        self.ws_client.send_message("git_controller_status", status_data)

    def toggle_auto_commit(self, _):
        self.auto_commit = not (self.auto_commit)
        return self.controller_status(_)
        
    def toggle_auto_push(self, _):
        self.auto_push = not (self.auto_push)
        return self.controller_status(_)
            
    def switch_repository(self, command_data: Dict[str, Any]) -> str:
        """Switch to a different repository by index"""
        index = command_data.get('index')

        if index is None:
            return "Index is required to switch repositories."
        
        if 0 <= index < len(self.base_paths):
            self.current_repo_index = index
            repo_name = self.current_repository_metadata.get('repositoryName', f'Repository {index}') if self.current_repository_metadata else f'Repository {index}'
            message = f"Switched to repository: {repo_name}"
            self._send_message(message)
            
            # Send repository update to client
            self.ws_client.send_message("repository_update", {
                "current_repository_index": self.current_repo_index,
                "repository_name": repo_name,
                "repository_id": self.current_repository_metadata.get('repositoryId'),
                "total_repositories": len(self.base_paths),
                "current_branch": self.git_tool.execute_git_command(f"branch --show-current", repository_path=self.current_base_path)
            })
            self.git_tool = EnhancedGitTools(
                callback_functions=None,
                base_path=self.current_base_path,
                logger=None,
                repository_metadata=self.current_repository_metadata,
                tenant_id=get_tenant_id()
            )
            if self.status_monitor:
                self.status_monitor.set_git_tool(self.git_tool)
            
            # Refresh git status after switch
            self._handle_status({})
            return message
        else:
            return f"Invalid repository index. Must be between 0 and {len(self.base_paths) - 1}"
        

    def switch_to_checkpoints(self, checkpoints: CheckPoints):
        for checkpoint in checkpoints.check_points:
            _git_tool = EnhancedGitTools(
                callback_functions=None,
                base_path=checkpoint.path,
                logger=None,
                repository_metadata=self.repository_metadata[self.current_repo_index],
                tenant_id=get_tenant_id()
            )
            try:
                _git_tool.git_stash(repository_path=checkpoint.path, untracked=True)
            except Exception as e:  
                print(f"Error stashing changes: {e}")
            _git_tool.git_checkout(checkpoint.hash, repository_path=checkpoint.path)
            

    def handle_switch_to_message_checkpoints(self, input_data: Dict[str, Any] = None):
        message_id = input_data.get("message_id")
        print(f"Attempting to switch to checkpoints for message ID: {message_id}")
        try:
            messages = self.db[TASKS_COLLECTION_NAME].find_one({"_id": self.task_id}, {"messages": 1})
            if not messages:
                print(f"No messages found for task ID: {self.task_id}")
                return
                
            found = False
            for message in messages.get("messages", []):
                if message.get("id") == message_id:
                    found = True
                    checkpoints = message.get("check_points", [])
                    print(f"Found message with ID {message_id}, checkpoints: {checkpoints}")
                    if checkpoints:
                        print(f"Switching to {len(checkpoints)} checkpoints")
                        self.switch_to_checkpoints(CheckPoints(check_points=checkpoints))
                    else:
                        print("No checkpoints found in the message")
                    break
                    
            if not found:
                print(f"Message with ID {message_id} not found in task")
            if self.ws_client:
                self.ws_client.send_message("switch_to_checkpoints_status", {
                    "status": "completed"
                })
        except Exception as e:
            if self.ws_client:
                self.ws_client.send_message("switch_to_checkpoints_status", {
                    "status": "error"
                })
            print(f"Error switching to message checkpoints: {e}")
            
    def list_repositories(self, _):
        """List all available repositories"""
        print('Debug: Print all class variables')
        print("base_paths", self.base_paths)
        self.base_paths = []
        for data in self.repository_metadata:
            self.base_paths.append(data['current_path'])
            
        print("base_paths after", self.base_paths)
        print("r_m, ",self.repository_metadata)
        print("cri", self.current_repo_index)
        
        try:
            repo_list = []
            for i, path in enumerate(self.base_paths):
                repo_metadata = self.repository_metadata[i] if i < len(self.repository_metadata) else None
                repo_name = repo_metadata.get('repositoryName', f'Repository {i}') if repo_metadata else f'Repository {i}'
                repo_list.append({
                    'index': i,
                    'name': repo_name,
                    'path': path,
                    'service': repo_metadata.get('service'),
                    'organization': repo_metadata.get('organization'),
                    'current_branch': self.git_tool.execute_git_command(f"branch --show-current", repository_path=path),
                    'id': repo_metadata.get('repositoryId'),
                    'is_current': i == self.current_repo_index
                })
            
            # Send repository list to client
            self.ws_client.send_message("repository_list", {
                "repositories": repo_list
            })
            
            # Format message for display
            message = "**Available Repositories:**\n\n"
            for repo in repo_list:
                current_marker = " (current)" if repo['is_current'] else ""
                message += f"- [{repo['index']}] {repo['name']}{current_marker}\n"
            
            self._send_message(message)
            return repo_list
        except Exception as e:
            error_message = f"Error listing repositories: {str(e)}"
            self._send_message(error_message)
            return error_message

    def _initialize_command_map(self) -> Dict[str, Callable]:
        """Initialize mapping of command names to their handler methods"""
        return {
            'commit_code': self._handle_commit,
            'pull_code': self._handle_pull,
            'push_code': self._handle_push,
            'list_branches': self._handle_branch_list,
            'git_status': self._handle_status,
            'git_log': self._handle_log,
            'create_branch': self._handle_create_branch,
            'switch_branch': self._handle_switch_branch,
            'git_revert_to': self._handle_revert,
            'execute_git_command': self._handle_custom_command,
            'create_pr': self._handle_create_pr,
            'merge_to_kavia_main': self.handle_merge_to_kavia_main_all_repos,
            'switch_to_checkpoints': self.handle_switch_to_message_checkpoints,
            'list_repositories': self.list_repositories,
            'switch_repository': self.switch_repository,
            'controller_status': self.controller_status,
            'auto_commit_toggle': self.toggle_auto_commit,
            'auto_push_toggle': self.toggle_auto_push,
            'commit_changes': self._handle_commit_changes
        }


    def _handle_create_pr(self, input_data: Dict[str, Any]):
        """Handle PR creation"""
        if not self.current_repository_metadata:
            self._send_message("No repository metadata available. Cannot create PR.")
            return

        source_branch = input_data.get('source_branch')
        target_branch = input_data.get('target_branch')
        title = input_data.get('title')
        description = input_data.get('description', '')

        if not all([source_branch, target_branch, title]):
            self._send_message("Missing required fields for PR creation. Need source_branch, target_branch, and title.")
            return

        try:
            result = self.git_tool.create_pull_request(
                source_branch=source_branch,
                target_branch=target_branch,
                title=title,
                description=description,
                repository_path=self.current_base_path,
                repository_metadata=self.current_repository_metadata
            )
            
            if isinstance(result, dict):
                pr_message = (
                    f"**Pull Request Created Successfully**\n\n"
                    f"- PR Number: {result.get('number')}\n"
                    f"- Status: {result.get('status')}\n"
                    f"- URL: {result.get('url')}\n"
                )
                self._send_message(pr_message)
            else:
                self._send_message(result)  # Send error message
        except Exception as e:
            error_message = f"Error creating pull request: {str(e)}"
            self._send_message(error_message)

    def _handle_merge_to_kavia_main(self, input_data: Dict[str, Any]):
        """Handle merging current branch to kavia-main"""
        try:
            print(f"Starting merge to kavia-main with input: {input_data}")
            # Get current branch name
            current_branch = self.git_tool.execute_git_command(
                "branch --show-current", 
                repository_path=self.current_base_path
            ).strip()
            
            print(f"Current branch detected: {current_branch}")
            
            if not current_branch:
                self._send_message("Error: Could not determine current branch")
                return
                
            if current_branch == "kavia-main":
                self._send_message("Already on kavia-main branch. No merge needed.")
                return
            
            self._send_message(f"Starting merge process from '{current_branch}' to 'kavia-main'...")
            
            # Step 1: Commit all changes to current branch
            print(f"Step 1: Preparing to commit changes to '{current_branch}'")
            self._send_message(f"Step 1: Committing all changes to '{current_branch}'...")
            commit_message = input_data.get("commit_message", f"Auto-commit before merging to kavia-main from {current_branch}")
            
            add_result = self.git_tool.git_add_all(repository_path=self.current_base_path)
            print(f"Git add result: {add_result}")
            self._send_message(add_result)
            
            commit_result = self.git_tool.git_commit(commit_message, repository_path=self.current_base_path)
            print(f"Git commit result: {commit_result}")
            self._send_message(commit_result)
            
            # Step 2: Switch to kavia-main branch
            print("Step 2: Switching to kavia-main branch")
            self._send_message("Step 2: Switching to 'kavia-main' branch...")
            switch_result = self.git_tool.switch_branch(
                branch_name="kavia-main",
                repository_path=self.current_base_path,
                create=False
            )
            print(f"Switch branch result: {switch_result}")
            self._send_message(switch_result)
            
            # Check if switch was successful
            new_branch = self.git_tool.execute_git_command(
                "branch --show-current", 
                repository_path=self.current_base_path
            ).strip()
            
            print(f"Current branch after switch: {new_branch}")
            
            if new_branch != "kavia-main":
                self._send_message(f"Error: Failed to switch to kavia-main. Currently on: {new_branch}")
                return
            
            # Step 3: Merge the original branch into kavia-main
            print(f"Step 3: Merging '{current_branch}' into 'kavia-main'")
            self._send_message(f"Step 3: Merging '{current_branch}' into 'kavia-main'...")
            merge_result = self.git_tool.execute_git_command(
                f"merge {current_branch}",
                repository_path=self.current_base_path
            )
            print(f"Merge result: {merge_result}")
            self._send_message(merge_result)
            
            # Step 4: Push the merged changes to remote
            print("Step 4: Pushing merged changes to remote kavia-main")
            self._send_message("Step 4: Pushing merged changes to remote repository...")
            push_result = self.git_tool.git_push(repository_path=self.current_base_path)
            print(f"Push result: {push_result}")
            self._send_message(push_result)
            
            # Final status check
            print("Merge to kavia-main completed successfully with push")
            self._send_message("Merge to kavia-main completed successfully! Changes have been pushed to remote repository.")
            self._handle_status({})  # Show current status
            
            # Send completion status
            # if self.ws_client:
            #     self.ws_client.send_message("merge_to_kavia_main_completed", {
            #         "status": "completed",
            #         "message": "Merge to kavia-main operation completed successfully",
            #         "original_branch": current_branch,
            #         "target_branch": "kavia-main",
            #         "timestamp": generate_timestamp()
            #     })
            
        except Exception as e:
            print(f"Error during merge to kavia-main: {str(e)}")
            error_message = f"Error during merge to kavia-main: {str(e)}"
            self._send_message(error_message)




    def handle_merge_to_kavia_main_all_repos(self, input_data: Dict[str, Any]):
        """
        Handle merging to kavia-main across all repositories
        
        Args:
            input_data: Dict containing:
                - commit_message: Optional base commit message
                - skip_repos: Optional list of repository indices to skip
                - stop_on_error: Optional bool to stop on first error (default: False)
        """
        commit_message_base = input_data.get("commit_message", "Auto-commit before merging to kavia-main")
        skip_repos = set(input_data.get("skip_repos", []))
        stop_on_error = input_data.get("stop_on_error", True)
        
        original_repo_index = self.current_repo_index
        results = []
        successful_merges = 0
        failed_merges = 0
        
        current_branch = self.git_tool.execute_git_command(
                "branch --show-current", 
                repository_path=self.current_base_path
            ).strip()
        
        try:
            self._send_message(f"**Starting merge to kavia-main across {len(self.base_paths)} repositories**\n")
            
            for repo_index in range(len(self.base_paths)):
                if repo_index in skip_repos:
                    self._send_message(f"Skipping repository {repo_index} as requested")
                    continue
                    
                repo_metadata = self.repository_metadata[repo_index] if repo_index < len(self.repository_metadata) else None
                repo_name = repo_metadata.get('repositoryName', f'Repository {repo_index}') if repo_metadata else f'Repository {repo_index}'
                
                try:
                    # Switch to the repository
                    self._send_message(f"\n--- Processing {repo_name} (Index: {repo_index}) ---")
                    self.current_repo_index = repo_index
                    self.initialize_git_tool()
                    
                    if self.status_monitor:
                        self.status_monitor.set_current_base_path(self.current_base_path)
                        self.status_monitor.set_git_tool(self.git_tool)
                    
                    # Create repository-specific commit message
                    repo_commit_message = f"{commit_message_base} - {repo_name}"
                    repo_input_data = {**input_data, "commit_message": repo_commit_message}
                    
                    # Execute the merge for this repository
                    self._handle_merge_to_kavia_main(repo_input_data)
                    
                    results.append({
                        "repo_index": repo_index,
                        "repo_name": repo_name,
                        "status": "success"
                    })
                    successful_merges += 1
                    self._send_message(f"✅ Successfully merged {repo_name} to kavia-main")
                    
                except Exception as e:
                    error_msg = f"❌ Failed to merge {repo_name}: {str(e)}"
                    self._send_message(error_msg)
                    
                    results.append({
                        "repo_index": repo_index,
                        "repo_name": repo_name,
                        "status": "error",
                        "error": str(e)
                    })
                    failed_merges += 1
                    
                    if stop_on_error:
                        self._send_message(f"Stopping merge process due to error in {repo_name}")
                        break
        
        finally:
            # Always return to original repository
            if original_repo_index != self.current_repo_index:
                self.current_repo_index = original_repo_index
                self.initialize_git_tool()
                
                if self.status_monitor:
                    self.status_monitor.set_current_base_path(self.current_base_path)
                    self.status_monitor.set_git_tool(self.git_tool)
        
        # Send summary
        summary_message = (
            f"\n**Multi-Repository Merge Summary:**\n"
            f"- Total repositories: {len(self.base_paths)}\n"
            f"- Successful merges: {successful_merges}\n"
            f"- Failed merges: {failed_merges}\n"
            f"- Skipped repositories: {len(skip_repos)}\n"
        )
        self._send_message(summary_message)
        
        # Send detailed results via WebSocket
        if self.ws_client:
            self.ws_client.send_message("merge_to_kavia_main_completed", {
                "status": "completed",
                "summary": {
                    "total_repos": len(self.base_paths),
                    "successful": successful_merges,
                    "failed": failed_merges,
                    "skipped": len(skip_repos)
                },
                "status": "completed",
                "message": "Merge to kavia-main operation completed successfully",
                "original_branch": current_branch,
                "target_branch": "kavia-main",
                "timestamp": generate_timestamp()
            })
        
        return results
        
    def handle_command(self, command: str, input_data: Dict[str, Any] = None) -> bool:
        """Dynamically handle git commands"""
        print(f"GitController.handle_command called with: {command} and input: {input_data}")
        print(f"Available commands in command map: {list(self._command_map.keys())}")
        handler = self._command_map.get(command)
        if handler:
            try:
                print(f"Found handler for command: {command}, executing...")
                handler(input_data or {})
                return True
            except Exception as e:
                print(f"Error handling git command {command}: {e}")
                return False
        else:
            print(f"No handler found for command: {command}")
        return False

    def _send_message(self, content: str):
        """Helper method to send messages through websocket and update DB"""
        # Truncate content if it exceeds 2500 characters
        if len(content) > 2500:
            content = content[:2497] + "..."
            
        content = self.llm.remove_sensitive_patterns(content)
        message_obj = Message(
            content=content, 
            sender="Git", 
            timestamp=generate_timestamp()
        )
        
        # Set additional attributes after initialization
        message_obj.parent_id = self.current_repository_metadata.get('repositoryId')
        message_obj.metadata = {
            "repositoryId": self.current_repository_metadata.get('repositoryId'),
            "repositoryName": self.current_repository_metadata.get('repositoryName'),
            "repositoryPath": self.current_base_path
        }
        
        if self.ws_client is not None:
            self.ws_client.send_message("git_command_output", message_obj.to_dict())

        if self.db is not None:
            # Update with limit using $slice
            self.db[TASKS_COLLECTION_NAME].update_one(
                {"_id": self.task_id},
                {
                    "$push": {
                        f"git_commands_outputs.{self.current_repository_metadata.get('repositoryId')}": {
                            "$each": [message_obj.to_dict()],
                            "$slice": -self.MAX_MESSAGE_HISTORY  # Keep only the latest messages
                        }
                    }
                }
            )

    def _handle_commit(self, input_data: Dict[str, Any]):
        commit_message = input_data.get("commit_message", "Automated commit")
        add_result = self.git_tool.git_add_all(repository_path=self.current_base_path)
        self._send_message(add_result)
        commit_result = self.git_tool.git_commit(commit_message, repository_path=self.current_base_path)
        self._send_message(commit_result)

    def _handle_pull(self, _):
        result = self.git_tool.git_pull(repository_path=self.current_base_path)
        self._send_message(result)

    def _handle_push(self, _):
        result = self.git_tool.git_push(repository_path=self.current_base_path)
        self._send_message(result)

    def _handle_branch_list(self, input_data: Dict[str, Any]):
        send_message = input_data.get('send_message', True)  # Default to True for backward compatibility
        raw_branches = self.git_tool.git_branch(repository_path=self.current_base_path, all_branches=True)
        branches = raw_branches.split('\n')
        
        # Always send git update
        self.ws_client.send_message("git_update", {
            "type": "branches",
            "branches": branches
        })
        
        # Only send formatted message if requested
        if send_message:
            formatted_output = self._format_branch_list(raw_branches)
            self._send_message(formatted_output)

    def _handle_status(self, _):
        result = self.git_tool.git_status(repository_path=self.current_base_path)
        self._send_message(result)

    def _handle_log(self, input_data: Dict[str, Any]):
        n = input_data.get('n', self.MAX_COMMIT_LOGS)  # Default to MAX_COMMIT_LOGS if not specified
        n = min(n, self.MAX_COMMIT_LOGS)  # Ensure n doesn't exceed maximum limit
        branch = input_data.get('branch')
        logs = self.git_tool.git_log(repository_path=self.current_base_path, n=n, branch=branch)
        
        if isinstance(logs, list):
            formatted_logs = [
                {
                    'hash': log['hash'],
                    'author': log['author'],
                    'date': log['date'],
                    'message': log['message']
                }
                for log in logs[:self.MAX_COMMIT_LOGS]  # Additional safety check
            ]
            self.ws_client.send_message("git_update", {
                "type": "commit_history",
                "commits": formatted_logs
            })
        else:
            self._send_message(logs)

    def _handle_create_branch(self, input_data: Dict[str, Any]):
        branch_name = input_data.get('branch_name')
        start_point = input_data.get('start_point')
        if branch_name:
            result = self.git_tool.create_branch_(
                branch_name=branch_name,
                repository_path=self.current_base_path,
                start_point=start_point,
                checkout=True
            )
            self._send_message(result)

    def _handle_switch_branch(self, input_data: Dict[str, Any]):
        branch_name = input_data.get('branch_name')
        create = input_data.get('create', False)
        if branch_name:
            result = self.git_tool.switch_branch(
                branch_name=branch_name,
                repository_path=self.current_base_path,
                create=create
            )
            self._send_message(result)

        self.ws_client.send_message("rollback_update", {
            "status":"completed"
        })

    def _handle_revert(self, input_data: Dict[str, Any]):
        commit_hash = input_data.get('commit_hash')
        result = self.git_tool.git_revert(
            commit_hash=commit_hash, 
            repository_path=self.current_base_path
        )
        self._send_message(result)
        self._handle_log({})  # Refresh git logs after revert

    def _handle_custom_command(self, input_data: Dict[str, Any]):
        command = input_data.get('command', '')
        if command.startswith('git '):
            command = command[4:]
        result = self.git_tool.execute_git_command(
            command=command,
            repository_path=self.current_base_path
        )
        self._send_message(result)

    def _handle_commit_changes(self, input_data: Dict[str, Any]):
        """Handle commit changes command"""
        chat_controller = input_data.get('chat_controller')
        result = self.commit_changes(chat_controller=chat_controller)
        if result:
            self._send_message(f"✅ Commit changes completed. Created {len(result.check_points)} checkpoints.")
        else:
            self._send_message("❌ Commit changes failed.")

    def _format_branch_list(self, raw_branches: str) -> str:
        """Format branch list into markdown"""
        branches = raw_branches.split('\n')
        formatted_output = "**Current Branches:**\n\n"
        
        for branch in branches:
            branch = branch.strip()
            if not branch:
                continue
                
            if branch.startswith('*'):
                branch = branch.replace('*', '').strip()
                formatted_output += f"- **{branch}** (current)\n"
            else:
                formatted_output += f"- {branch}\n"

        return formatted_output

    def commit_changes(self, chat_controller=None):
        """
        Thread-safe commit changes implementation with proper branch management
        Adapted from TaskReporter._commit_changes_thread
        """
        try:
            print(f"Starting commit changes in GitController for task {self.task_id}")
            
            checkpoints = CheckPoints(check_points=[])
            all_repository_metadata = self.repository_metadata
            
            for repository_metadata in all_repository_metadata:
                current_path = repository_metadata.get("current_path")
                if not current_path:
                    print(f"Skipping repository - no current_path: {repository_metadata}")
                    continue
                    
                print(f"Processing repository: {current_path}")
                
                # Initialize git tool for this repository
                git_tool = EnhancedGitTools(
                    callback_functions=None,
                    base_path=current_path,
                    logger=None,
                    repository_metadata=repository_metadata,
                    tenant_id=get_tenant_id()
                )
                
                #git config core.fileMode false
                try:
                    git_tool.execute_git_command(
                        "config core.fileMode false", 
                        repository_path=current_path
                    )
                except Exception as e:
                    print(f"Error configuring fileMode: {e}")
                    
                
                try:
                    # 1. Ensure we're on the correct branch: cga-{task_id}
                    target_branch = f"cga-{self.task_id}"
                    print(f"Target branch: {target_branch}")
                    
                    # Get current branch
                    try:
                        current_branch = git_tool.execute_git_command(
                            "branch --show-current", 
                            repository_path=current_path
                        ).strip()
                        print(f"Current branch: {current_branch}")
                    except Exception as e:
                        print(f"Error getting current branch: {e}")
                        current_branch = "unknown"
                    
                    # Switch to target branch (create if it doesn't exist)
                    if current_branch != target_branch:
                        print(f"Switching from {current_branch} to {target_branch}")
                        try:
                            # Try to switch to existing branch first
                            switch_result = git_tool.switch_branch(
                                branch_name=target_branch,
                                repository_path=current_path,
                                create=False
                            )
                            print(f"Switch result: {switch_result}")
                        except Exception as e:
                            print(f"Branch doesn't exist, creating: {e}")
                            # Create and switch to new branch
                            try:
                                create_result = git_tool.switch_branch(
                                    branch_name=target_branch,
                                    repository_path=current_path,
                                    create=True
                                )
                                print(f"Create branch result: {create_result}")
                            except Exception as create_e:
                                print(f"Error creating branch: {create_e}")
                                # Continue with current branch if branch creation fails
                    
                    
                    #ensure .file_activity_report.json is excluded from commits
                    try:
                        self._ensure_gitignore_exclusions(current_path)
                        print("✅ File exclusions configured")
                    except Exception as e:
                        print(f"Error configuring file exclusions: {e}")
                    
                    
                    
                    # 2. Check git status to see if there are changes
                    try:
                        status = git_tool.git_status(repository_path=current_path)
                        print(f"Git status: {status}")
                        
                        if "nothing to commit" in status.lower() or "working tree clean" in status.lower():
                            print("No changes to commit, skipping repository")
                            continue
                    except Exception as e:
                        print(f"Error checking git status: {e}")
                        # Continue anyway and try to add/commit
                    
                    # 3. Configure git identity if needed
                    try:
                        config_name = subprocess.run(
                            ["git", "config", "user.name"],
                            cwd=current_path,
                            capture_output=True,
                            text=True
                        )
                        config_email = subprocess.run(
                            ["git", "config", "user.email"],
                            cwd=current_path,
                            capture_output=True,
                            text=True
                        )
                        
                        if not config_name.stdout.strip() or not config_email.stdout.strip():
                            print("Setting git identity for commits")
                            subprocess.run(
                                ["git", "config", "user.name", "Kavia Bot"],
                                cwd=current_path, 
                                check=True
                            )
                            subprocess.run(
                                ["git", "config", "user.email", "<EMAIL>"],
                                cwd=current_path, 
                                check=True
                            )
                    except Exception as e:
                        print(f"Error configuring git identity: {e}")
                    
                    # 4. Check for sensitive data before committing
                    try:
                        has_sensitive, sensitive_files, patterns = check_diff_for_sensitive_data(current_path)
                        if has_sensitive:
                            print(f"⚠️ Sensitive data detected in files: {sensitive_files}")
                            print(f"Patterns found: {len(patterns)}")
                            
                            # Add sensitive files to .gitignore
                            if sensitive_files:
                                add_to_gitignore(current_path, sensitive_files)
                                unstage_sensitive_files(current_path, sensitive_files)
                                print("Sensitive files added to .gitignore and unstaged")
                    except Exception as e:
                        print(f"Error checking for sensitive data: {e}")
                        # Continue with commit anyway
                    
                    # 5. Add all files using direct git command (equivalent to git add .)
                    print("Adding all files to git staging area...")
                    try:
                        # Use direct git command to ensure all files are added
                        add_result = subprocess.run(
                            ["git", "add", "."],
                            cwd=current_path,
                            capture_output=True,
                            text=True,
                            timeout=60
                        )
                        
                        if add_result.returncode != 0:
                            print(f"Git add error: {add_result.stderr}")
                            # Try with git_tool as fallback
                            try:
                                git_tool_add_result = git_tool.git_add_all(repository_path=current_path)
                                print(f"Git tool add result: {git_tool_add_result}")
                            except Exception as e:
                                print(f"Git tool add also failed: {e}")
                        else:
                            print(f"Git add successful: {add_result.stdout}")
                            
                    except subprocess.TimeoutExpired:
                        print("Git add timed out, trying with git_tool...")
                        try:
                            git_tool_add_result = git_tool.git_add_all(repository_path=current_path)
                            print(f"Git tool add result: {git_tool_add_result}")
                        except Exception as e:
                            print(f"Git tool add failed: {e}")
                    except Exception as e:
                        print(f"Error adding files: {e}")
                        # Try git_tool as fallback
                        try:
                            git_tool_add_result = git_tool.git_add_all(repository_path=current_path)
                            print(f"Git tool add result: {git_tool_add_result}")
                        except Exception as git_e:
                            print(f"Git tool add also failed: {git_e}")
                    
                    # 6. Commit changes
                    commit_message = f"CheckPoint - {self.task_id}"
                    print(f"Committing with message: {commit_message}")
                    
                    try:
                        commit_data = git_tool.git_commit(
                            message=commit_message,
                            repository_path=current_path
                        )
                        print(f"Commit result: {commit_data}")
                        
                        # Check if commit was actually successful
                        if "nothing to commit" not in str(commit_data).lower():
                            print("✅ Commit successful")
                        else:
                            print("ℹ️ No changes to commit")
                            
                    except Exception as e:
                        print(f"Error committing changes: {e}")
                        continue  # Skip to next repository
                    
                    # 7. Push changes
                    try:
                        push_data = git_tool.git_push(repository_path=current_path)
                        print(f"Push result: {push_data}")
                    except Exception as e:
                        print(f"Error pushing changes: {e}")
                        # Continue anyway - push failure shouldn't block checkpoint creation
                    
                    # 8. Get git logs for checkpoint
                    try:
                        git_logs = git_tool.git_log(repository_path=current_path)
                        print(f"Git logs retrieved: {len(git_logs) if isinstance(git_logs, list) else 'error'}")
                        
                        if isinstance(git_logs, list) and len(git_logs) > 0:
                            latest_log = git_logs[0]
                            checkpoint = CheckPoint(
                                hash=latest_log.get("hash"),
                                message=latest_log.get("message"),
                                date=latest_log.get("date"),
                                path=current_path,
                                author=latest_log.get("author")
                            )
                            checkpoints.check_points.append(checkpoint)
                            print(f"✅ Checkpoint created: {checkpoint.hash[:8]}")
                        else:
                            print("⚠️ No git logs available for checkpoint")
                            
                    except Exception as e:
                        print(f"Error getting git logs: {e}")
                        # Create minimal checkpoint anyway
                        checkpoint = CheckPoint(
                            hash="unknown",
                            message=commit_message,
                            date=generate_timestamp(),
                            path=current_path,
                            author="Kavia Bot"
                        )
                        checkpoints.check_points.append(checkpoint)
                        
                except Exception as repo_e:
                    print(f"Error processing repository {current_path}: {repo_e}")
                    import traceback
                    traceback.print_exc()
                    continue  # Continue with next repository
            
            # 9. Update message with checkpoints (if chat_controller and required data available)
            if chat_controller and self.db:
                try:
                    checkpoint_data = checkpoints.model_dump(mode="json")
                    # This requires an update_message method - we'll need to adapt this part
                    # For now, we'll update the database directly
                    task_doc = self.db[TASKS_COLLECTION_NAME].find_one({"_id": self.task_id})
                    if task_doc:
                        # Get messages array from document
                        messages = task_doc.get("messages", [])
                        
                        # Find the last message with msg_type "llm"
                        message_updated = False
                        for i in range(len(messages) - 1, -1, -1):  # Iterate in reverse order
                            message = messages[i]
                            if message.get("msg_type") == "llm":
                                # Update message with checkpoints
                                for key, value in checkpoint_data.items():
                                    messages[i][key] = value
                                message_updated = True
                                print(f"✅ Found last LLM message with ID: {message.get('id')}")
                                break
                        
                        if message_updated:
                            # Update the document with modified messages array
                            self.db[TASKS_COLLECTION_NAME].update_one(
                                {"_id": self.task_id},
                                {"$set": {"messages": messages}}
                            )
                            print(f"✅ Checkpoints updated in database: {len(checkpoints.check_points)} checkpoints")
                        else:
                            print(f"⚠️ No LLM message found to update with checkpoints")
                except Exception as e:
                    print(f"❌ Error updating checkpoints in database: {e}")
                    import traceback
                    traceback.print_exc()
            else:
                print("⚠️ Cannot update checkpoints - missing chat_controller or db")
                
            print(f"🏁 Commit changes completed for task {self.task_id}")
            return checkpoints
                
        except Exception as e:
            print(f"❌ Error in commit changes: {e}")
            import traceback
            traceback.print_exc()
            return None
        
    def _ensure_gitignore_exclusions(self, repository_path):
        """
        Ensure .file_activity_report.json is excluded from commits
        """
        import os
        
        gitignore_path = os.path.join(repository_path, '.gitignore')
        exclusion_pattern = '.file_activity_report.json'
        
        # Read existing .gitignore content
        existing_content = ""
        if os.path.exists(gitignore_path):
            with open(gitignore_path, 'r', encoding='utf-8') as f:
                existing_content = f.read()
        
        # Add exclusion pattern if not already present
        if exclusion_pattern not in existing_content:
            with open(gitignore_path, 'a', encoding='utf-8') as f:
                if existing_content and not existing_content.endswith('\n'):
                    f.write('\n')
                f.write(f'{exclusion_pattern}\n')
            print(f"Added {exclusion_pattern} to .gitignore")
        
        # Unstage the file if it's already tracked
        try:
            subprocess.run(
                ["git", "rm", "--cached", exclusion_pattern],
                cwd=repository_path,
                capture_output=True,
                text=True,
                check=False  # Don't raise exception if file not tracked
            )
            print(f"Unstaged {exclusion_pattern} from git")
        except Exception as e:
            print(f"Note: Could not unstage {exclusion_pattern}: {e}")