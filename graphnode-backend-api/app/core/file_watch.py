import os
import difflib
from typing import Optional, Dict
from enum import Enum


class FileStatus(Enum):
    MODIFIED = "modified"
    DELETED = "deleted"
    NOT_CREATED = "not_created"
    CREATED = "created"


class FileWatch:
    def __init__(self, base_dir: str, diffs_dir: str):
        """
        Initialize FileWatch with base directory and diffs directory.
        
        Args:
            base_dir (str): Base directory for relative paths
            diffs_dir (str): Absolute path to directory for storing diffs
        """
        self.base_dir = os.path.abspath(base_dir)
        self.diffs_dir = os.path.abspath(diffs_dir)
        os.makedirs(self.diffs_dir, exist_ok=True)

    def get_file_path(self, file_path: str) -> str:
        """Simple path resolution"""
        if os.path.isabs(file_path):
            return file_path
        return os.path.join(self.base_dir, file_path)

    def get_diff_path(self, file_path: str) -> str:
        """Get diff file path"""
        filename = os.path.basename(file_path)
        name, ext = os.path.splitext(filename)
        return os.path.join(self.diffs_dir, f"{name}_prev{ext}")

    def write_file(self, file_path: str) -> Optional[Dict]:
        """Write file changes and maintain diff."""
        try:
            # Get absolute paths
            abs_path = self.get_file_path(file_path)
            filename = os.path.basename(abs_path)
            diff_path = self.get_diff_path(abs_path)

            # Check if file exists
            if not os.path.exists(abs_path):
                return {
                    "filepath": file_path,
                    "filename": filename,
                    "extension": os.path.splitext(filename)[1],
                    "previous_code": "",
                    "current_code": "",
                    "diff": "",
                    "status": FileStatus.DELETED.value,
                    "diff_path": diff_path
                }

            # Read current content
            with open(abs_path, 'r', encoding='utf-8') as f:
                current_code = f.read()

            # Read previous version if exists
            previous_code = ""
            if os.path.exists(diff_path):
                with open(diff_path, 'r', encoding='utf-8') as f:
                    previous_code = f.read()

            # Determine status
            if not os.path.exists(diff_path):
                status = FileStatus.CREATED
            elif previous_code != current_code:
                status = FileStatus.MODIFIED
            else:
                status = FileStatus.NOT_CREATED

            # Generate diff
            diff = difflib.unified_diff(
                previous_code.splitlines(),
                current_code.splitlines(),
                lineterm=''
            )
            diff_text = "\n".join(diff)

            # Save current version for future diff
            with open(diff_path, 'w', encoding='utf-8') as f:
                f.write(current_code)

            return {
                "filepath": file_path,
                "filename": filename,
                "extension": os.path.splitext(filename)[1],
                "previous_code": previous_code,
                "current_code": current_code,
                "diff": diff_text,
                "status": status.value,
                "diff_path": diff_path
            }

        except Exception as e:
            print(f"Error processing file {file_path}: {e}")
            return None

    def read_file_diff(self, file_path: str, content: str = None) -> Optional[Dict]:
        """Read the diff data for a file."""
        try:
            if not file_path.startswith(self.base_dir): 
                file_path = os.path.join(self.base_dir,file_path)
            # Get absolute paths
            abs_path = self.get_file_path(file_path)
            filename = os.path.basename(abs_path)
            diff_path = self.get_diff_path(abs_path)

            # Check if file exists
            if not os.path.exists(abs_path) and content is None:
                
                return {
                    "filepath": file_path[len(self.base_dir):].lstrip(os.sep),
                    "filename": filename,
                    "extension": os.path.splitext(filename)[1],
                    "previous_code": "",
                    "current_code": "",
                    "diff": "",
                    "status": FileStatus.DELETED.value,
                    "diff_path": diff_path
                }

            # Read current content
            current_code = content

            # Read previous version if exists
            previous_code = ""
            if os.path.exists(diff_path):
                with open(diff_path, 'r', encoding='utf-8') as f:
                    previous_code = f.read()

            # Determine status
            if not os.path.exists(diff_path):
                status = FileStatus.CREATED
            elif previous_code != current_code:
                status = FileStatus.MODIFIED
            else:
                status = FileStatus.NOT_CREATED

            # Generate diff
            diff = difflib.unified_diff(
                previous_code.splitlines(),
                current_code.splitlines(),
                lineterm=''
            )
            diff_text = "\n".join(diff)

            return {
                "filepath": file_path[len(self.base_dir):].lstrip(os.sep),
                "filename": filename,
                "extension": os.path.splitext(filename)[1],
                "previous_code": previous_code,
                "current_code": current_code,
                "diff": diff_text,
                "status": status.value,
                "diff_path": diff_path
            }

        except Exception as e:
            print(f"Error reading diff for {file_path}: {e}")
            return None
        
    def get_diff_content(self, content: str) -> Optional[Dict]:
        """
        Parse content with merge conflict markers and return diff information.
        
        Args:
            content (str): Content with merge conflict markers in format:
                        filepath\n<<<<<<< SEARCH\nold content\n=======\nnew content\n>>>>>>> REPLACE
            
        Returns:
            Optional[Dict]: Dictionary containing diff information
        """
        try:
            # Split content by filepath and actual content
            parts = content.split('\n', 1)
            filepath = parts[0].strip()
            content = parts[1] if len(parts) > 1 else ""

            # Find the sections marked by conflict markers
            search_start = content.find('<<<<<<< SEARCH\n')
            search_end = content.find('=======\n')
            replace_start = search_end + 8  # length of '=======\n'
            replace_end = content.find('>>>>>>> REPLACE')

            if search_start == -1 or search_end == -1 or replace_end == -1:
                return {
                    "filepath": filepath[len(self.base_dir):].lstrip(os.sep),
                    "filename": os.path.basename(filepath),
                    "extension": os.path.splitext(filepath)[1],
                    "previous_code": content,
                    "current_code": content,
                    "diff": "",
                    "status": FileStatus.NOT_CREATED.value
                }

            # Extract the before and after content
            previous_code = content[search_start + 15:search_end]  # 15 is length of '<<<<<<< SEARCH\n'
            current_code = content[replace_start:replace_end]

            # Generate diff
            diff = difflib.unified_diff(
                previous_code.splitlines(),
                current_code.splitlines(),
                lineterm=''
            )
            diff_text = "\n".join(diff)

            return {
                "filepath": filepath[len(self.base_dir):].lstrip(os.sep),
                "filename": os.path.basename(filepath),
                "extension": os.path.splitext(filepath)[1],
                "previous_code": previous_code,
                "current_code": current_code,
                "diff": diff_text,
                "status": FileStatus.MODIFIED.value
            }

        except Exception as e:
            print(f"Error parsing diff content: {e}")
            return None
