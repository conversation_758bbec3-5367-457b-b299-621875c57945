import os
import json
import yaml
import subprocess
import tempfile
import time
import uuid
import boto3
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path
import docker
from app.models.code_generation_model import Message
from app.core.constants import TASKS_COLLECTION_NAME, DEPLOYMENT_COLLECTION_NAME
from app.utils.datetime_utils import generate_timestamp
from app.core.websocket.client import WebSocketClient
from app.connection.establish_db_connection import get_mongo_db
from app.connection.tenant_middleware import KAVIA_ROOT_DB_NAME
from app.core.Settings import settings
from pydantic import BaseModel
from code_generation_core_agent.agents.task_execution_agent import (
    TaskExecutionAgent,
)
import threading
import functools

def run_in_daemon_thread(func):
    """Decorator to run function in a daemon thread"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        thread = threading.Thread(
            target=func,
            args=args,
            kwargs=kwargs,
            daemon=True,
            name=f"{func.__name__}_daemon"
        )
        thread.start()
        return thread
    return wrapper

class ContainerDeploymentModel(BaseModel):
    _id: str
    deployment_id: str
    project_id: str
    task_id: str
    service_name: str
    branch_name: str
    image_uri: str
    service_url: str
    framework: str
    workspace_path: str
    run_command: str
    build_command:str
    ecr_repository: str
    task_definition_arn: str
    cluster_name: str
    service_arn: str
    target_group_arn: str
    load_balancer_arn: str
    status: str
    message: str
    created_at: datetime
    updated_at: datetime
    type: str
    port: str

class ECSDeploymentController:
    
    # Shared cluster name for all deployments
    SHARED_CLUSTER_NAME = "kavia-shared-cluster"
    
    def __init__(self, task_id: str, ws_client: WebSocketClient, db,agent):
        self.task_id = task_id
        self.ws_client = ws_client
        self.db = db
        self.kaviarootdb = get_mongo_db(KAVIA_ROOT_DB_NAME).db
        self.docker_client = docker.from_env()
        self._command_map = self._initialize_command_map()
        self.agent:TaskExecutionAgent = agent
        self.session_dir = self.agent.base_path
        
        # AWS clients
        self.ecr_client = boto3.client('ecr',
            region_name=settings.AWS_DEPLOYMENT_REGION,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
        )
        
        self.ecs_client = boto3.client('ecs',
            region_name=settings.AWS_DEPLOYMENT_REGION,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
        )
    
    def _initialize_command_map(self) -> Dict[str, Any]:
        """Initialize mapping of command names to their handler methods"""
        return {
            'start_container_deployment': self._handle_start_container_deploy,
            'deployment_container_status': self._handle_container_deployment_status
            
        }
    
    def _send_container_message(self, content: str):
        """Helper method to send messages through websocket and update DB"""
        if len(content) > 2100:
            content = content[:2000] + "..."
            
        message_obj = Message(
            content=content, 
            sender="Deployment", 
            timestamp=generate_timestamp()
        )
        if self.ws_client is None:
            print("No socket Intializing Socket")
            # self.ws_client = WebSocketClient(self.task_id, uri=settings.WEBSOCKET_URI)
            # self.ws_client.connect()
        if self.ws_client is not None:
            try:
                self.ws_client.send_message("container_deployment_status", message_obj.to_dict())
                print(f"✅ Sent container message: {content[:100]}...")
            except Exception as e:
                print(f"❌ Failed to send WebSocket message: {e}")
        
        print(f" message_obj: {message_obj.to_dict()}")
        if self.db:
            self.db[TASKS_COLLECTION_NAME].update_one(
                {"_id": self.task_id},
                {"$push": {"messages": message_obj.to_dict()}}
            )
    
    def _get_root_directory(self):
        """Get the root workspace directory for the current task"""
        if os.getenv("LOCAL_DEBUG"):
            if self.task_id.startswith("code-generation") or self.task_id.startswith("cg"): 
                return "/tmp/kavia/workspace/code-generation"
            return f"/tmp/kavia/workspace/{self.task_id}"
        if self.task_id.startswith("code-generation") or self.task_id.startswith("cg"): 
            return "/home/<USER>/workspace/code-generation"
        return f"/home/<USER>/workspace/{self.task_id}"
    
    def _ensure_shared_cluster_exists(self):
        """Ensure the shared ECS cluster exists, create if it doesn't"""
        try:
            # Check if cluster exists
            response = self.ecs_client.describe_clusters(clusters=[self.SHARED_CLUSTER_NAME])
            
            if not response['clusters'] or response['clusters'][0]['status'] != 'ACTIVE':
                self._send_container_message(f"Creating shared ECS cluster: {self.SHARED_CLUSTER_NAME}")
                
                self.ecs_client.create_cluster(
                    clusterName=self.SHARED_CLUSTER_NAME,
                    settings=[{
                        'name': 'containerInsights',
                        'value': 'enabled'
                    }],
                    tags=[{
                        'key': 'Name',
                        'value': self.SHARED_CLUSTER_NAME
                    }]
                )
                
                self._send_container_message(f"Shared cluster created: {self.SHARED_CLUSTER_NAME}")
            else:
                self._send_container_message(f"Using existing shared cluster: {self.SHARED_CLUSTER_NAME}")
                
        except Exception as e:
            self._send_container_message(f"Error ensuring shared cluster: {str(e)}")
            raise
    
    @run_in_daemon_thread
    def _handle_start_container_deploy(self, data: Dict[str, Any]):
        """Handle start container deployment request"""
        self._send_container_message(f"Starting container deployment process...{data}")
        
        # Ensure we have a unique ID for backend deployments that won't conflict with frontend
        if not data.get("id"):
            data["id"] = f"backend-{str(uuid.uuid4())[:8]}"
        else:
            # Add backend prefix if it doesn't already have it
            if not data["id"].startswith("backend-"):
                data["id"] = f"backend-{data['id']}"
        
        ports = data.get("port", "3001")
        framework = data.get("framework","django")
        build_command = data.get("build_command","python3 -m venv venv && source venv/bin/activate && pip install -r requirements.txt && python manage.py makemigrations && python manage.py migrate")
        run_command = data.get("run_command","source venv/bin/activate && python manage.py migrate && python manage.py runserver 0.0.0.0:<port>")
        branch_name = data.get("branch_name", "kavia-main")

        if not branch_name or len(branch_name.strip()) == 0:
            branch_name = "kavia-main"
            data["branch_name"] = branch_name
        
        self._send_container_message("Reading manifest.yaml for deployment configuration...")
        
        try:
            # Step 1: Update status to "processing" in MongoDB
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": data.get("id")},
                {"$set": {
                    "status": "processing",
                    "message": f"Container deployment started at {current_time}",
                    "updated_at": datetime.now()
                }},
                upsert=True
            )
            
            # Step 2: Ensure shared cluster exists
            self._ensure_shared_cluster_exists()
            
            # Step 3: Read and parse manifest.yaml
            manifest_data = self._read_manifest()
            if not manifest_data:
                raise Exception("Failed to read or parse manifest.yaml")
            
            # Step 4: Identify backend container and extract details
            backend_config = self._identify_backend_container(manifest_data,ports,framework,build_command,run_command)
            if not backend_config:
                raise Exception("No backend container configuration found in manifest")
            
            # Step 5: Store deployment details
            task_details = self.db[TASKS_COLLECTION_NAME].find_one({"_id": self.task_id})
            project_id = str(task_details.get("project_id", ""))
            
            deployment_model = ContainerDeploymentModel(
                _id=str(data.get("id")),
                deployment_id=str(data.get("id")),
                project_id=project_id,
                task_id=str(self.task_id),
                service_name=backend_config['service_name'],
                branch_name=branch_name,
                image_uri="",  # Will be populated after ECR push
                service_url="",  # Will be populated after ALB setup
                framework=framework,
                workspace_path=backend_config['workspace_path'],
                run_command=run_command,
                build_command=build_command,
                ecr_repository="",
                task_definition_arn="",
                cluster_name=self.SHARED_CLUSTER_NAME,  # Use shared cluster
                service_arn="",
                target_group_arn="",
                load_balancer_arn="",
                status="processing",  # Initial status is "processing"
                message=f"Container deployment started at {current_time}",
                created_at=datetime.now(),
                updated_at=datetime.now(),
                type='backend',
                port=str(ports)
            )
            
            # Store in database
            self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": data.get("id")},
                {"$set": deployment_model.model_dump()},
                upsert=True
            )
            
            # Step 6: Start the deployment process
            self._start_deployment_process(data, backend_config)
            
        except Exception as e:
            error_msg = f" Deployment initialization failed: {str(e)}"
            self._send_container_message(error_msg)
            
            # Update status to "failed" in MongoDB on error
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": data.get("id")},
                {"$set": {
                    "status": "failed",
                    "message": f"{error_msg} at {current_time}",
                    "updated_at": datetime.now()
                }},
                upsert=True
            )
    
    def _read_manifest(self) -> Optional[Dict]:
        """Read and parse manifest.yaml from the workspace"""
        try:
            root_dir = self._get_root_directory()
            manifest_path = os.path.join(root_dir, ".project_manifest.yaml")
            
            if not os.path.exists(manifest_path):
                self._send_container_message(f" manifest.yaml not found in {root_dir}")
                return None
            
            self._send_container_message(f" Reading manifest from: {manifest_path}")
            
            with open(manifest_path, 'r') as f:
                manifest_data = yaml.safe_load(f)
            
            self._send_container_message(" Manifest.yaml loaded successfully")
            return manifest_data
            
        except Exception as e:
            self._send_container_message(f"Error reading manifest.yaml: {str(e)}")
            return None
    
    def _identify_backend_container(self, manifest_data,ports,framework,build_command,run_command) -> Optional[Dict]:
        """Extract backend container configuration from manifest"""
        try:
            containers = manifest_data.get('containers', [])
            
            backend_service = None
            service_name = None
            
            # Search by container_type first (most reliable)
            for container in containers:
                if container.get('container_type') == 'backend':
                    backend_service = container
                    service_name = container.get('container_name')
                    break
            
            if not backend_service:
                self._send_container_message(" No backend container found in manifest")
                return None
            
            # Extract configuration
            backend_config = {
                'service_name': service_name,
                'framework': framework,
                'workspace': backend_service.get('workspace', '.'),
                'workspace_path': self.session_dir,
                'dockerfile_path': 'Dockerfile',  
                'run_command': run_command,
                'build_command':build_command,
                'environment': backend_service.get('environment', {}),
                'ports': [str(ports)],
                'volumes': backend_service.get('volumes', [])
            }
            
            self._send_container_message(f"Identified backend container: {service_name}")
            self._send_container_message(f" Framework: {backend_config['framework']}")
            self._send_container_message(f"Workspace: {backend_config['workspace_path']}")
            self._send_container_message(f"Run command: {backend_config['run_command']}")
            
            return backend_config
            
        except Exception as e:
            self._send_container_message(f"Error identifying backend container: {str(e)}")
            return None
    

    
    @run_in_daemon_thread
    def _start_deployment_process(self, data: Dict[str, Any], backend_config: Dict):
        """Start the complete deployment process with shared cluster"""
        deployment_id = data.get("id")
        
        # Ensure deployment_id is a string and has backend prefix
        if not deployment_id:
            deployment_id = f"backend-{str(uuid.uuid4())[:8]}"
            data["id"] = deployment_id
        elif not str(deployment_id).startswith("backend-"):
            deployment_id = f"backend-{deployment_id}"
            data["id"] = deployment_id
        
        deployment_id = str(deployment_id)  # Ensure it's a string
        
        try:
            # Step 1: Update status to "building" in MongoDB
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": deployment_id},
                {"$set": {
                    "status": "building",
                    "message": f"Building Docker container at {current_time}",
                    "updated_at": datetime.now()
                }}
            )
            
            # Step 1: Build Docker image
            self._send_container_message("🔨 Step 1: Building Docker container...")
            image_tag = self._build_docker_image(deployment_id, backend_config)
            
            # Step 2: Push to ECR
            self._send_container_message("Step 2: Pushing container to ECR...")
            image_uri = self._push_to_ecr(deployment_id, image_tag, backend_config)
            
            # Step 3: Update status to "deploying" in MongoDB
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": deployment_id},
                {"$set": {
                    "status": "deploying",
                    "message": f"Deploying to ECS at {current_time}",
                    "image_uri": image_uri,
                    "updated_at": datetime.now()
                }}
            )
            
            # Step 3: Create task definition with built image
            self._send_container_message("Step 3: Creating ECS task definition...")
            task_definition_arn = self._create_task_definition(deployment_id, image_uri, backend_config)
            
            # Step 4: Generate Terraform configuration for ALB and service
            self._send_container_message(" Step 4: Generating infrastructure configuration...")
            terraform_config = self._generate_terraform_config(deployment_id, task_definition_arn, backend_config)
            
            # Step 5: Apply Terraform
            self._send_container_message(" Step 5: Deploying infrastructure...")
            service_url = self._apply_terraform(deployment_id, terraform_config)
            
            # Step 6: Update deployment status to "success"
            
            self.ws_client.send_message("container_deployment_output", {"message": f"Deployment completed! Service available at: {service_url}"})
            print(f" Deployment completed! Service available at: {service_url}")
            
            # Update database with success status
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": deployment_id},
                {"$set": {
                    "status": "success",
                    "message": f"Deployment completed successfully at {current_time}",
                    "service_url": service_url,
                    "image_uri": image_uri,
                    "task_definition_arn": task_definition_arn,
                    "updated_at": datetime.now()
                }}
            )
            
        except Exception as e:
            error_msg = f"Deployment failed: {str(e)}"
            self._send_container_message(error_msg)
            message = f"Container Deployment error: {error_msg}"
            # Send failure status
            self._send_container_message(message)
                
            # Update database with failed status
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": deployment_id},
                {"$set": {
                    "status": "failed",
                    "message": f"{error_msg} at {current_time}",
                    "updated_at": datetime.now()
                }}
            )
    
    def _build_docker_image(self, deployment_id: str, backend_config: Dict) -> str:
        """Build Docker image from the workspace"""
        try:
            # Ensure deployment_id has backend prefix
            if not str(deployment_id).startswith("backend-"):
                deployment_id = f"backend-{deployment_id}"
                
            # Update status to "building" in MongoDB
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": deployment_id},
                {"$set": {
                    "status": "building",
                    "message": f"Building Docker image at {current_time}",
                    "updated_at": datetime.now()
                }}
            )
            
            root_dir = self._get_root_directory()
            workspace_path = os.path.join(root_dir, backend_config['workspace_path'])
            dockerfile_path = os.path.join(workspace_path, backend_config.get('dockerfile_path', 'Dockerfile'))
            
            # Copy .dockerignore to workspace directory if it doesn't exist
            root_dockerignore = os.path.join(root_dir, '.dockerignore')
            workspace_dockerignore = os.path.join(workspace_path, '.dockerignore')
            
            if os.path.exists(root_dockerignore) and not os.path.exists(workspace_dockerignore):
                import shutil
                shutil.copy2(root_dockerignore, workspace_dockerignore)
                self._send_container_message(" Copied .dockerignore to workspace directory")
            
            # Also ensure workspace-specific .dockerignore includes additional patterns
            workspace_dockerignore_content = """# Standard exclusions
nohup.out
*.log
*.tmp
*.temp
.DS_Store
Thumbs.db

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/
.ENV/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Temporary files
*.tmp
*.temp
*.bak
*.backup
"""
            
            # Write workspace-specific .dockerignore
            with open(workspace_dockerignore, 'w') as f:
                f.write(workspace_dockerignore_content)
            
            self._send_container_message(" Created comprehensive .dockerignore for workspace")
            
            # Clean up problematic files that might interfere with Docker build
            nohup_file = os.path.join(workspace_path, 'nohup.out')
            if os.path.exists(nohup_file):
                try:
                    os.remove(nohup_file)
                    self._send_container_message(" Removed nohup.out file from workspace")
                except Exception as e:
                    self._send_container_message(f" Warning: Could not remove nohup.out: {str(e)}")
            
            # Check if Dockerfile exists
            if not os.path.exists(dockerfile_path):
                self._send_container_message(" No Dockerfile found, generating one...")
                self._generate_dockerfile(workspace_path, backend_config)
            
            image_tag = f"kavia-{deployment_id}:latest"
            
            self._send_container_message(f"Building image: {image_tag}")
            self._send_container_message(f" Build context: {workspace_path}")
            
            # Try building with workspace as context first, fallback to root context if needed
            try:
                # Build the image with workspace as context
                image, build_logs = self.docker_client.images.build(
                    path=workspace_path,
                    tag=image_tag,
                    dockerfile=backend_config.get('dockerfile_path', 'Dockerfile'),
                    rm=True,
                    forcerm=True
                )
            except Exception as workspace_build_error:
                if "nohup.out" in str(workspace_build_error) or "Can not read file in context" in str(workspace_build_error):
                    self._send_container_message(" Workspace context build failed, trying with root context...")
                    
                    # Fallback: Use root directory as context and modify Dockerfile path
                    try:
                        # Modify Dockerfile to use workspace path
                        dockerfile_content = self._generate_dockerfile_with_root_context(workspace_path, backend_config)
                        temp_dockerfile = os.path.join(root_dir, f'Dockerfile.{deployment_id}')
                        
                        with open(temp_dockerfile, 'w') as f:
                            f.write(dockerfile_content)
                        
                        # Build with root context
                        image, build_logs = self.docker_client.images.build(
                            path=root_dir,
                            tag=image_tag,
                            dockerfile=f'Dockerfile.{deployment_id}',
                            rm=True,
                            forcerm=True
                        )
                        
                        # Clean up temporary Dockerfile
                        try:
                            os.remove(temp_dockerfile)
                        except:
                            pass
                            
                    except Exception as root_build_error:
                        self._send_container_message(f" Root context build also failed: {str(root_build_error)}")
                        raise workspace_build_error  # Re-raise original error
                else:
                    raise workspace_build_error
            
            # Log build progress
            for log in build_logs:
                if log and isinstance(log, dict) and 'stream' in log and log['stream']:
                    stream_content = log['stream']
                    if isinstance(stream_content, str):
                        self._send_container_message(f" {stream_content.strip()}")
            
            self._send_container_message(f" Image built successfully: {image_tag}")
            return image_tag
            
        except Exception as e:
            self._send_container_message(f"Docker build failed: {str(e)}")
            
            # Update status to "failed" in MongoDB on error
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": deployment_id},
                {"$set": {
                    "status": "failed",
                    "message": f"Docker build failed: {str(e)} at {current_time}",
                    "updated_at": datetime.now()
                }}
            )
            raise
    
    
    def _generate_dockerfile(self, workspace_path: str, backend_config: Dict):
        """Generate Dockerfile based on detected framework with consistent structure"""
        framework = backend_config['framework']
        run_command = backend_config['run_command']
        workspace = backend_config['workspace']
        service_name = backend_config['service_name']
        ports = backend_config['ports']
        
        # Extract first port from ports list and ensure it's an integer
        if isinstance(ports, list) and ports:
            port_str = str(ports[0])
        else:
            port_str = str(ports)
            
        # Remove any port mapping if present (e.g., "8080:80" -> "80")
        if ":" in port_str:
            port_str = port_str.split(":")[-1]
            
        # Ensure port is a valid integer
        try:
            port = int(port_str)
        except (ValueError, TypeError):
            port = 3000  # Default port if conversion fails
        
        # Parse and format run_command for Docker
        def format_run_command(cmd: str, framework: str) -> str:
            """Convert run command to proper Docker CMD format"""
            if not cmd:
                default_commands = {
                    'express': '["npm", "start"]',
                    'fastapi': f'["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "{port}"]',
                    'django': '["python", "manage.py", "runserver", "0.0.0.0:8000"]',
                    'flask': '["flask", "run", "--host=0.0.0.0", "--port=8000"]'
                }
                return default_commands.get(framework, default_commands['fastapi'])
            
            clean_cmd = cmd.strip()
            
            # For Express/Node.js - keep all commands as-is, just format for Docker
            if framework == 'express':
                # Don't remove anything, just format for Docker
                pass
            else:
                # For Python frameworks - remove virtual environment activation patterns
                venv_patterns = [
                    "source venv/bin/activate &&",
                    "source .venv/bin/activate &&", 
                    ". venv/bin/activate &&",
                    ". .venv/bin/activate &&",
                    "venv/bin/activate &&",
                    ".venv/bin/activate &&"
                ]
                
                for pattern in venv_patterns:
                    if pattern in clean_cmd:
                        clean_cmd = clean_cmd.split(pattern, 1)[1].strip()
                        break
            
            # Check if command contains shell operators OR environment variables
            has_shell_operators = '&&' in clean_cmd or ';' in clean_cmd or '|' in clean_cmd
            has_env_vars = '=' in clean_cmd and any(part for part in clean_cmd.split() if '=' in part and not part.startswith('-'))
            
            if has_shell_operators or has_env_vars:
                # Use shell form with explicit shell invocation
                # Escape quotes in the command
                escaped_cmd = clean_cmd.replace('"', '\\"')
                port_str = str(port)
                escaped_cmd = escaped_cmd.replace('<port>', port_str)
                return f'["sh", "-c", "{escaped_cmd}"]'
            
            # Handle different command structures (existing logic for simple commands)
            if clean_cmd.startswith('"') and clean_cmd.endswith('"'):
                # Already quoted, remove outer quotes and re-format
                clean_cmd = clean_cmd[1:-1]
            
            # Split command and create proper JSON array format
            parts = []
            current_part = ""
            in_quotes = False
            
            for char in clean_cmd:
                if char == '"' and (not current_part or current_part[-1] != '\\'):
                    in_quotes = not in_quotes
                elif char == ' ' and not in_quotes:
                    if current_part:
                        parts.append(current_part)
                        current_part = ""
                else:
                    current_part += char
            
            if current_part:
                parts.append(current_part)
            
            # Replace <port> placeholder with actual port
            port_str = str(port)  # Ensure port is integer
            parts = [part.replace('<port>', port_str) for part in parts if part]
            
            # Format as JSON array string for Dockerfile
            formatted_parts = [f'"{part}"' for part in parts if part]
            return f'[{", ".join(formatted_parts)}]'
        
        # Common workdir pattern for all frameworks
        workdir = f"{workspace}/{service_name}"
        formatted_cmd = format_run_command(run_command, framework)
        
        dockerfile_templates = {
            'fastapi': f"""FROM --platform=linux/amd64 python:3.12-slim
    WORKDIR /app
    COPY {workdir}/requirements.txt .
    RUN pip install --no-cache-dir --upgrade -r requirements.txt
    COPY {workdir}/ .
    EXPOSE {port}
    CMD {formatted_cmd}""",
            
            'django': f"""FROM --platform=linux/amd64 python:3.12-slim
    WORKDIR /app
    COPY {workdir}/requirements.txt .
    RUN pip install --no-cache-dir -r requirements.txt
    COPY {workdir}/ .
    EXPOSE {port}
    CMD {formatted_cmd}""",
            
            'flask': f"""FROM --platform=linux/amd64 python:3.12-slim
    WORKDIR /app
    COPY {workdir}/requirements.txt .
    RUN pip install --no-cache-dir -r requirements.txt
    COPY {workdir}/ .
    EXPOSE {port}
    ENV FLASK_HOST=0.0.0.0
    ENV FLASK_PORT={port}
    CMD {formatted_cmd}""",
            
            'express': f"""FROM node:18-alpine
    WORKDIR /app
    COPY {workdir}/package*.json ./
    RUN npm ci --only=production
    COPY {workdir}/ .
    EXPOSE {port}
    CMD {formatted_cmd}"""
        }
        
        dockerfile_content = dockerfile_templates.get(framework, dockerfile_templates['fastapi'])
        
        # Ensure dockerfile directory exists
        os.makedirs(workspace_path, exist_ok=True)
        dockerfile_path = os.path.join(workspace_path, 'Dockerfile')
        
        try:
            with open(dockerfile_path, 'w', encoding='utf-8') as f:
                f.write(dockerfile_content)
            self._send_container_message(f"✅ Generated Dockerfile for {framework} application at {dockerfile_path}")
        except IOError as e:
            self._send_container_message(f"❌ Failed to generate Dockerfile: {e}")
    
    def _generate_dockerfile_with_root_context(self, workspace_path: str, backend_config: Dict) -> str:
        """Generate Dockerfile that uses the root directory as context."""
        framework = backend_config['framework']
        run_command = backend_config['run_command']
        workspace = backend_config['workspace']
        service_name = backend_config['service_name']
        ports = backend_config['ports']
        
        # Extract first port from ports list and ensure it's an integer
        if isinstance(ports, list) and ports:
            port_str = str(ports[0])
        else:
            port_str = str(ports)
            
        # Remove any port mapping if present (e.g., "8080:80" -> "80")
        if ":" in port_str:
            port_str = port_str.split(":")[-1]
            
        # Ensure port is a valid integer
        try:
            port = int(port_str)
        except (ValueError, TypeError):
            port = 3000  # Default port if conversion fails
        
        # Parse and format run_command for Docker
        def format_run_command(cmd: str, framework: str) -> str:
            """Convert run command to proper Docker CMD format"""
            if not cmd:
                default_commands = {
                    'express': '["npm", "start"]',
                    'fastapi': f'["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "{port}"]',
                    'django': '["python", "manage.py", "runserver", "0.0.0.0:8000"]',
                    'flask': '["flask", "run", "--host=0.0.0.0", "--port=8000"]'
                }
                return default_commands.get(framework, default_commands['fastapi'])
            
            clean_cmd = cmd.strip()
            
            # For Express/Node.js - keep all commands as-is, just format for Docker
            if framework == 'express':
                # Don't remove anything, just format for Docker
                pass
            else:
                # For Python frameworks - remove virtual environment activation patterns
                venv_patterns = [
                    "source venv/bin/activate &&",
                    "source .venv/bin/activate &&", 
                    ". venv/bin/activate &&",
                    ". .venv/bin/activate &&",
                    "venv/bin/activate &&",
                    ".venv/bin/activate &&"
                ]
                
                for pattern in venv_patterns:
                    if pattern in clean_cmd:
                        clean_cmd = clean_cmd.split(pattern, 1)[1].strip()
                        break
            
            # Check if command contains shell operators OR environment variables
            has_shell_operators = '&&' in clean_cmd or ';' in clean_cmd or '|' in clean_cmd
            has_env_vars = '=' in clean_cmd and any(part for part in clean_cmd.split() if '=' in part and not part.startswith('-'))
            
            if has_shell_operators or has_env_vars:
                # Use shell form with explicit shell invocation
                # Escape quotes in the command
                escaped_cmd = clean_cmd.replace('"', '\\"')
                port_str = str(port)
                escaped_cmd = escaped_cmd.replace('<port>', port_str)
                return f'["sh", "-c", "{escaped_cmd}"]'
            
            # Handle different command structures (existing logic for simple commands)
            if clean_cmd.startswith('"') and clean_cmd.endswith('"'):
                # Already quoted, remove outer quotes and re-format
                clean_cmd = clean_cmd[1:-1]
            
            # Split command and create proper JSON array format
            parts = []
            current_part = ""
            in_quotes = False
            
            for char in clean_cmd:
                if char == '"' and (not current_part or current_part[-1] != '\\'):
                    in_quotes = not in_quotes
                elif char == ' ' and not in_quotes:
                    if current_part:
                        parts.append(current_part)
                        current_part = ""
                else:
                    current_part += char
            
            if current_part:
                parts.append(current_part)
            
            # Replace <port> placeholder with actual port
            port_str = str(port)  # Ensure port is integer
            parts = [part.replace('<port>', port_str) for part in parts if part]
            
            # Format as JSON array string for Dockerfile
            formatted_parts = [f'"{part}"' for part in parts if part]
            return f'[{", ".join(formatted_parts)}]'
        
        # Common workdir pattern for all frameworks
        workdir = f"{workspace}/{service_name}"
        formatted_cmd = format_run_command(run_command, framework)
        
        dockerfile_templates = {
            'fastapi': f"""FROM --platform=linux/amd64 python:3.12-slim
    WORKDIR /app
    COPY {workdir}/requirements.txt .
    RUN pip install --no-cache-dir --upgrade -r requirements.txt
    COPY {workdir}/ .
    EXPOSE {port}
    CMD {formatted_cmd}""",
            
            'django': f"""FROM --platform=linux/amd64 python:3.12-slim
    WORKDIR /app
    COPY {workdir}/requirements.txt .
    RUN pip install --no-cache-dir -r requirements.txt
    COPY {workdir}/ .
    EXPOSE {port}
    CMD {formatted_cmd}""",
            
            'flask': f"""FROM --platform=linux/amd64 python:3.12-slim
    WORKDIR /app
    COPY {workdir}/requirements.txt .
    RUN pip install --no-cache-dir -r requirements.txt
    COPY {workdir}/ .
    EXPOSE {port}
    ENV FLASK_HOST=0.0.0.0
    ENV FLASK_PORT={port}
    CMD {formatted_cmd}""",
            
            'express': f"""FROM node:18-alpine
    WORKDIR /app
    COPY {workdir}/package*.json ./
    RUN npm ci --only=production
    COPY {workdir}/ .
    EXPOSE {port}
    CMD {formatted_cmd}"""
        }
        
        dockerfile_content = dockerfile_templates.get(framework, dockerfile_templates['fastapi'])
        
        return dockerfile_content
    
    def _push_to_ecr(self, deployment_id: str, image_tag: str, backend_config: Dict) -> str:
        """Push Docker image to ECR"""
        try:
            # Ensure deployment_id has backend prefix
            if not str(deployment_id).startswith("backend-"):
                deployment_id = f"backend-{deployment_id}"
                
            # Create ECR repository name without the "backend-" prefix for AWS compatibility
            repository_name = f"kavia-{deployment_id.replace('backend-', '')}"
            
            # Create ECR repository
            try:
                self.ecr_client.create_repository(repositoryName=repository_name)
                self._send_container_message(f"Created ECR repository: {repository_name}")
            except self.ecr_client.exceptions.RepositoryAlreadyExistsException:
                self._send_container_message(f"ECR repository already exists: {repository_name}")
            
            # Get ECR login token
            token_response = self.ecr_client.get_authorization_token()
            token = token_response['authorizationData'][0]['authorizationToken']
            registry_url = token_response['authorizationData'][0]['proxyEndpoint']
            
            # Docker login to ECR
            import base64
            username, password = base64.b64decode(token).decode().split(':')
            
            self.docker_client.login(
                username=username,
                password=password,
                registry=registry_url
            )
            
            # Tag image for ECR
            account_id = boto3.client('sts').get_caller_identity()['Account']
            ecr_image_uri = f"{account_id}.dkr.ecr.{settings.AWS_DEPLOYMENT_REGION}.amazonaws.com/{repository_name}:latest"
            
            image = self.docker_client.images.get(image_tag)
            image.tag(ecr_image_uri)
            
            self._send_container_message(f" Pushing image to ECR: {ecr_image_uri}")
            
            # Push to ECR
            push_logs = self.docker_client.images.push(ecr_image_uri, stream=True, decode=True)
            
            for log in push_logs:
                if 'status' in log and 'progressDetail' not in log:
                    self._send_container_message(f" {log['status']}")
            
            self._send_container_message(f" Image pushed successfully: {ecr_image_uri}")
            
            # Update database with image URI
            self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": deployment_id},
                {"$set": {
                    "image_uri": ecr_image_uri,
                    "ecr_repository": repository_name,
                    "updated_at": datetime.now()
                }}
            )
            
            return ecr_image_uri
            
        except Exception as e:
            self._send_container_message(f" ECR push failed: {str(e)}")
            
            # Update status to "failed" in MongoDB on error
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": deployment_id},
                {"$set": {
                    "status": "failed",
                    "message": f"ECR push failed: {str(e)} at {current_time}",
                    "updated_at": datetime.now()
                }}
            )
            raise
    
    def _create_task_definition(self, deployment_id: str, image_uri: str, backend_config: Dict) -> str:
        """Create ECS task definition with the built image"""
        try:
            # Ensure deployment_id has backend prefix
            if not str(deployment_id).startswith("backend-"):
                deployment_id = f"backend-{deployment_id}"
                
            # Use a shorter ID without the prefix for AWS resource names
            short_deployment_id = deployment_id.replace("backend-", "")
            family_name = f"kavia-task-{short_deployment_id}"
            service_name = backend_config['service_name']
            
            # Extract container port and ensure it's an integer
            port_str = ""
            ports = backend_config.get('ports')
            if isinstance(ports, list) and ports:
                port_str = str(ports[0])
            else:
                port_str = str(ports) if ports else "3000"
                
            # Remove any port mapping if present (e.g., "8080:80" -> "80")
            if ":" in port_str:
                port_str = port_str.split(":")[-1]
                
            # Ensure port is a valid integer
            try:
                container_port = int(port_str)
            except (ValueError, TypeError):
                container_port = 3000  # Default port if conversion fails
            
            # Create CloudWatch log group
            log_group_name = f"/ecs/kavia-{short_deployment_id}"
            try:
                import boto3
                logs_client = boto3.client('logs', 
                    region_name=settings.AWS_DEPLOYMENT_REGION,
                    aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
                )
                
                # Create log group without retentionInDays
                logs_client.create_log_group(logGroupName=log_group_name)
                self._send_container_message(f"✅ Created CloudWatch log group: {log_group_name}")
                
                # Set retention policy separately
                try:
                    logs_client.put_retention_policy(
                        logGroupName=log_group_name,
                        retentionInDays=7
                    )
                    self._send_container_message(f"✅ Set log retention to 7 days")
                except Exception as retention_error:
                    self._send_container_message(f"⚠️ Could not set log retention: {str(retention_error)}")
                    
            except logs_client.exceptions.ResourceAlreadyExistsException:
                self._send_container_message(f"ℹ️ Log group already exists: {log_group_name}")
            except Exception as log_error:
                self._send_container_message(f"⚠️ Log group creation error: {str(log_error)}")
            
            # Task definition
            task_definition = {
                'family': family_name,
                'networkMode': 'awsvpc',
                'requiresCompatibilities': ['FARGATE'],
                'cpu': '256',
                'memory': '512',
                'executionRoleArn': self._get_or_create_execution_role(),
                'taskRoleArn': self._get_or_create_task_role(),
                'containerDefinitions': [
                    {
                        'name': service_name,
                        'image': image_uri,
                        'portMappings': [
                            {
                                'containerPort': container_port,
                                'protocol': 'tcp'
                            }
                        ],
                        'logConfiguration': {
                            'logDriver': 'awslogs',
                            'options': {
                                'awslogs-group': log_group_name,
                                'awslogs-region': settings.AWS_DEPLOYMENT_REGION,
                                'awslogs-stream-prefix': 'ecs'
                            }
                        },
                        'essential': True
                    }
                ]
            }
            
            response = self.ecs_client.register_task_definition(**task_definition)
            task_definition_arn = response['taskDefinition']['taskDefinitionArn']
            
            self._send_container_message(f"✅ Task definition created: {family_name}")
            
            # Update database with task definition ARN
            self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": deployment_id},
                {"$set": {
                    "task_definition_arn": task_definition_arn,
                    "updated_at": datetime.now()
                }}
            )
            
            return task_definition_arn
        
        except Exception as e:
            self._send_container_message(f"❌ Task definition creation failed: {str(e)}")
            
            # Update status to "failed" in MongoDB on error
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": deployment_id},
                {"$set": {
                    "status": "failed",
                    "message": f"Task definition creation failed: {str(e)} at {current_time}",
                    "updated_at": datetime.now()
                }}
            )
            raise
    
    def _get_or_create_execution_role(self) -> str:
        """Get or create ECS execution role"""
        role_name = "kavia-ecs-execution-role"
        
        try:
            iam = boto3.client('iam')
            try:
                response = iam.get_role(RoleName=role_name)
                return response['Role']['Arn']
            except iam.exceptions.NoSuchEntityException:
                # Create the role
                assume_role_policy = {
                    "Version": "2012-10-17",
                    "Statement": [
                        {
                            "Effect": "Allow",
                            "Principal": {"Service": "ecs-tasks.amazonaws.com"},
                            "Action": "sts:AssumeRole"
                        }
                    ]
                }
                
                response = iam.create_role(
                    RoleName=role_name,
                    AssumeRolePolicyDocument=json.dumps(assume_role_policy)
                )
                
                # Attach execution policy
                iam.attach_role_policy(
                    RoleName=role_name,
                    PolicyArn='arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy'
                )
                
                return response['Role']['Arn']
                
        except Exception as e:
            self._send_container_message(f"Error with execution role: {str(e)}")
            raise
    
    def _get_or_create_task_role(self) -> str:
        """Get or create ECS task role"""
        role_name = "kavia-ecs-task-role"
        
        try:
            iam = boto3.client('iam')
            try:
                response = iam.get_role(RoleName=role_name)
                return response['Role']['Arn']
            except iam.exceptions.NoSuchEntityException:
                # Create the role
                assume_role_policy = {
                    "Version": "2012-10-17",
                    "Statement": [
                        {
                            "Effect": "Allow",
                            "Principal": {"Service": "ecs-tasks.amazonaws.com"},
                            "Action": "sts:AssumeRole"
                        }
                    ]
                }
                
                response = iam.create_role(
                    RoleName=role_name,
                    AssumeRolePolicyDocument=json.dumps(assume_role_policy)
                )
                
                return response['Role']['Arn']
                
        except Exception as e:
            self._send_container_message(f"Error with task role: {str(e)}")
            raise
    
    def _generate_terraform_config(self, deployment_id: str, task_definition_arn: str, backend_config: Dict) -> str:
        """Generate Terraform configuration for ALB and ECS service using shared cluster"""
        
        # Ensure deployment_id has backend prefix
        if not str(deployment_id).startswith("backend-"):
            deployment_id = f"backend-{deployment_id}"
            
        # Use a shorter ID without the prefix for AWS resource names
        short_id = deployment_id.replace("backend-", "")
        if len(short_id) > 8:
            short_id = short_id[:8]
            
        service_name = backend_config['service_name']
        
        # Extract container port and ensure it's an integer
        port_str = ""
        ports = backend_config.get('ports')
        if isinstance(ports, list) and ports:
            port_str = str(ports[0])
        else:
            port_str = str(ports) if ports else "3000"
            
        # Remove any port mapping if present (e.g., "8080:80" -> "80")
        if ":" in port_str:
            port_str = port_str.split(":")[-1]
            
        # Ensure port is a valid integer
        try:
            container_port = int(port_str)
        except (ValueError, TypeError):
            container_port = 3000  # Default port if conversion fails
        
        terraform_config = f"""
    terraform {{
    required_providers {{
        aws = {{
        source  = "hashicorp/aws"
        version = "~> 5.0"
        }}
    }}
    }}

    provider "aws" {{
    region = "{settings.AWS_DEPLOYMENT_REGION}"
    }}

    # Variables
    variable "deployment_id" {{
    default = "{deployment_id}"
    }}

    variable "short_id" {{
    default = "{short_id}"
    }}

    variable "service_name" {{
    default = "{service_name}"
    }}

    variable "task_definition_arn" {{
    default = "{task_definition_arn}"
    }}

    variable "container_port" {{
    default = {container_port}
    }}

    variable "cluster_name" {{
    default = "{self.SHARED_CLUSTER_NAME}"
    }}

    # Data sources
    data "aws_availability_zones" "available" {{
    state = "available"
    }}

    data "aws_vpc" "default" {{
    default = true
    }}

    data "aws_subnets" "default" {{
    filter {{
        name   = "vpc-id"
        values = [data.aws_vpc.default.id]
    }}
    }}

    # Security Groups
    resource "aws_security_group" "alb" {{
    name_prefix = "kavia-alb-${{var.short_id}}-"
    vpc_id      = data.aws_vpc.default.id
    
    ingress {{
        from_port   = 80
        to_port     = 80
        protocol    = "tcp"
        cidr_blocks = ["0.0.0.0/0"]
    }}
    
    ingress {{
        from_port   = 443
        to_port     = 443
        protocol    = "tcp"
        cidr_blocks = ["0.0.0.0/0"]
    }}
    
    egress {{
        from_port   = 0
        to_port     = 0
        protocol    = "-1"
        cidr_blocks = ["0.0.0.0/0"]
    }}
    
    tags = {{
        Name = "kavia-alb-sg-${{var.short_id}}"
    }}
    }}

    resource "aws_security_group" "ecs" {{
    name_prefix = "kavia-ecs-${{var.short_id}}-"
    vpc_id      = data.aws_vpc.default.id
    
    ingress {{
        from_port       = var.container_port
        to_port         = var.container_port
        protocol        = "tcp"
        security_groups = [aws_security_group.alb.id]
    }}
    
    egress {{
        from_port   = 0
        to_port     = 0
        protocol    = "-1"
        cidr_blocks = ["0.0.0.0/0"]
    }}
    
    tags = {{
        Name = "kavia-ecs-sg-${{var.short_id}}"
    }}
    }}

    # Application Load Balancer
    resource "aws_lb" "main" {{
    name               = "kavia-alb-${{var.short_id}}"
    internal           = false
    load_balancer_type = "application"
    security_groups    = [aws_security_group.alb.id]
    subnets            = data.aws_subnets.default.ids
    
    enable_deletion_protection = false
    
    tags = {{
        Name = "kavia-alb-${{var.short_id}}"
    }}
    }}

    # Target Group
    resource "aws_lb_target_group" "main" {{
    name        = "kavia-tg-${{var.short_id}}"
    port        = var.container_port
    protocol    = "HTTP"
    vpc_id      = data.aws_vpc.default.id
    target_type = "ip"
    
    health_check {{
        enabled             = true
        healthy_threshold   = 2
        unhealthy_threshold = 2
        timeout             = 5
        interval            = 30
        path                = "/"
        matcher             = "200"
    }}
    
    tags = {{
        Name = "kavia-tg-${{var.short_id}}"
    }}
    }}

    # ALB Listener
    resource "aws_lb_listener" "main" {{
    load_balancer_arn = aws_lb.main.arn
    port              = "80"
    protocol          = "HTTP"
    
    default_action {{
        type             = "forward"
        target_group_arn = aws_lb_target_group.main.arn
    }}
    }}

    # ECS Service (using existing shared cluster)
    resource "aws_ecs_service" "main" {{
    name            = "kavia-svc-${{var.short_id}}"
    cluster         = var.cluster_name
    task_definition = var.task_definition_arn
    desired_count   = 1
    launch_type     = "FARGATE"
    
    network_configuration {{
        security_groups  = [aws_security_group.ecs.id]
        subnets          = data.aws_subnets.default.ids
        assign_public_ip = true
    }}
    
    load_balancer {{
        target_group_arn = aws_lb_target_group.main.arn
        container_name   = var.service_name
        container_port   = var.container_port
    }}
    
    depends_on = [aws_lb_listener.main]
    
    tags = {{
        Name = "kavia-svc-${{var.short_id}}"
    }}
    }}

    # Outputs
    output "service_url" {{
    value = "http://${{aws_lb.main.dns_name}}"
    }}

    output "service_name" {{
    value = aws_ecs_service.main.name
    }}

    output "target_group_arn" {{
    value = aws_lb_target_group.main.arn
    }}

    output "load_balancer_arn" {{
    value = aws_lb.main.arn
    }}
    """
        
        return terraform_config
    
    def _apply_terraform(self, deployment_id: str, terraform_config: str) -> str:
        """Apply Terraform configuration and return service URL"""
        try:
            # Ensure deployment_id has backend prefix
            if not str(deployment_id).startswith("backend-"):
                deployment_id = f"backend-{deployment_id}"
                
            # Update status to "deploying" in MongoDB
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": deployment_id},
                {"$set": {
                    "status": "deploying",
                    "message": f"Deploying infrastructure with Terraform at {current_time}",
                    "updated_at": datetime.now()
                }}
            )
            
            # Create temporary directory for Terraform files
            with tempfile.TemporaryDirectory() as temp_dir:
                tf_file = os.path.join(temp_dir, "main.tf")
                
                # Write Terraform configuration
                with open(tf_file, 'w') as f:
                    f.write(terraform_config)
                
                self._send_container_message("Initializing Terraform...")
                
                # Initialize Terraform
                result = subprocess.run([
                    'terraform', 'init'
                ], cwd=temp_dir, capture_output=True, text=True)
                
                if result.returncode != 0:
                    raise Exception(f"Terraform init failed: {result.stderr}")
                
                self._send_container_message("Planning Terraform deployment...")
                
                # Plan Terraform
                result = subprocess.run([
                    'terraform', 'plan', '-out=tfplan'
                ], cwd=temp_dir, capture_output=True, text=True)
                
                if result.returncode != 0:
                    raise Exception(f"Terraform plan failed: {result.stderr}")
                
                self._send_container_message(" Applying Terraform configuration...")
                
                # Apply Terraform
                result = subprocess.run([
                    'terraform', 'apply', '-auto-approve', 'tfplan'
                ], cwd=temp_dir, capture_output=True, text=True)
                
                if result.returncode != 0:
                    raise Exception(f"Terraform apply failed: {result.stderr}")
                
                self._send_container_message("Getting deployment outputs...")
                
                # Get outputs
                result = subprocess.run([
                    'terraform', 'output', '-json'
                ], cwd=temp_dir, capture_output=True, text=True)
                
                if result.returncode != 0:
                    raise Exception(f"Failed to get Terraform outputs: {result.stderr}")
                
                outputs = json.loads(result.stdout)
                service_url = outputs['service_url']['value']
                
                # Update database with Terraform outputs and success status
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                    {"_id": deployment_id},
                    {"$set": {
                        "status": "success",  # Set to success when deployment is complete
                        "message": f"Deployment completed successfully at {current_time}",
                        "service_arn": outputs.get('service_name', {}).get('value', ''),
                        "target_group_arn": outputs.get('target_group_arn', {}).get('value', ''),
                        "load_balancer_arn": outputs.get('load_balancer_arn', {}).get('value', ''),
                        "service_url": service_url,
                        "updated_at": datetime.now()
                    }}
                )
                
                self._send_container_message(f"Infrastructure deployed successfully!")
                
                self.ws_client.send_message("container_deployment_output", {"message": service_url})
                print(f"Service URL: {service_url}")
                return service_url
                
        except Exception as e:
            self._send_container_message(f" Terraform deployment failed: {str(e)}")
            
            # Update status to "failed" in MongoDB on error
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": deployment_id},
                {"$set": {
                    "status": "failed",
                    "message": f"Terraform deployment failed: {str(e)} at {current_time}",
                    "updated_at": datetime.now()
                }}
            )
            raise
    
    @run_in_daemon_thread
    def _handle_container_deployment_status(self, data: Dict[str, Any]):
        """Handle container deployment status updates"""
        deployment_id = data.get("id")
        
        # Ensure deployment_id has backend prefix
        if deployment_id and not str(deployment_id).startswith("backend-"):
            deployment_id = f"backend-{deployment_id}"
            data["id"] = deployment_id
        
        status = data.get("status", "").lower()
        
        # Validate the status is one of the allowed values
        valid_statuses = ["processing", "building", "deploying", "success", "failed"]
        if status not in valid_statuses:
            self._send_container_message(f"Invalid status: {status}. Must be one of {valid_statuses}")
            return
        
        self._send_container_message(f" Deployment status update: {status}")
        
        if status == "success":
            self._send_container_message(" Container deployment completed successfully!")
        elif status == "failed":
            self._send_container_message(f"Container deployment failed: {data.get('message', '')}")
        
        # Update database with the new status
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.kaviarootdb[DEPLOYMENT_COLLECTION_NAME].update_one(
            {"_id": deployment_id},
            {"$set": {
                "status": status,
                "message": f"{data.get('message', '')} at {current_time}",
                "updated_at": datetime.now()
            }}
        )
    
    @run_in_daemon_thread
    def _handle_list_dir(self, input_data: Dict[str, Any]):
        """Handle listing directory contents"""
        try:
            base_dir = input_data.get("base_dir", "")
            
            if base_dir == "root_folder":
                dir_path = self._get_root_directory()
            else:
                if os.path.isabs(base_dir):
                    dir_path = base_dir
                else:
                    dir_path = os.path.join(self._get_root_directory(), base_dir)
            
            if not os.path.exists(dir_path):
                self._send_container_message(f"Directory not found: {dir_path}")
                self.ws_client.send_message("dir_list", {"error": f"Directory not found: {dir_path}"})
                return
            
            dir_contents = []
            for item in os.listdir(dir_path):
                item_path = os.path.join(dir_path, item)
                item_type = "folder" if os.path.isdir(item_path) else "file"
                dir_contents.append({"name": item, "type": item_type})
            
            self.ws_client.send_message("dir_list", {
                "path": dir_path,
                "contents": dir_contents
            })
            
            message = f"**Directory Listing for {dir_path}:**\n\n"
            for item in dir_contents:
                icon = "📁" if item["type"] == "folder" else "📄"
                message += f"{icon} {item['name']}\n"
            
            self._send_container_message(message)
            
        except Exception as e:
            error_message = f"Error listing directory: {str(e)}"
            self._send_container_message(error_message)
            self.ws_client.send_message("dir_list", {"error": error_message})
    
    def handle_command(self, command: str, input_data: Dict[str, Any] = {}) -> bool:
        """Dynamically handle container deployment commands - handlers run in daemon threads"""
       
        print(f"Handling container deployment command: {command} with input: {input_data}")
        handler = self._command_map.get(command)
        if handler:
            try:
                # Handler is decorated with @run_in_daemon_thread, so it returns immediately
                # and runs in background. Any exceptions are handled within the daemon thread.
                handler(input_data)
                return True
            except Exception as e:
                error_msg = f"Error handling container deployment command {command}: {str(e)}"
                self._send_container_message(error_msg)
                return False
        return False