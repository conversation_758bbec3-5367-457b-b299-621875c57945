from code_generation_core_agent.agents.tools.git_tool import GitTools as BaseGitTools
from code_generation_core_agent.agents.tools.schemas.decorators import register_function
from git import Repo, GitCommandError
import os
import requests
import boto3
from github import Github
import gitlab
from app.routes.scm_route import scm_manager
from app.utils.hash import decrypt_string
import re
import datetime

def clean_branch_name(branch_name):
    """Clean the branch name by removing special characters and whitespace"""
    return re.sub(r'[* ]', '', branch_name)

class EnhancedGitTools(BaseGitTools):
    def __init__(self, callback_functions, base_path, logger, llm=None, executor=None, repository_metadata=None, tenant_id=None):
        super().__init__(callback_functions, base_path, logger, llm, executor)
        self.repository_metadata = repository_metadata
        self.tenant_id = tenant_id
        self.scm_config = None
        
        # Only setup SCM if repository_metadata doesn't have direct access_token
        if repository_metadata and tenant_id and not repository_metadata.get('access_token'):
            self._setup_scm_configuration()

    def _setup_scm_configuration(self):
        """Setup SCM configuration"""
        try:
            encrypted_scm_id = self.repository_metadata.get('encrypted_scm_id')
            if encrypted_scm_id:
                decrypted_scm_id = decrypt_string(encrypted_scm_id)
                self.scm_config = scm_manager.get_configuration(self.tenant_id, scm_id=decrypted_scm_id)
        except Exception as e:
            self.logger.error(f"Error setting up SCM configuration: {str(e)}")

    def _get_repo_instance(self, repository_path):
        """Wrapper for repository operations"""
        repo = Repo(os.path.join(self.base_path, repository_path))
        
        # First check if we have direct access token in repository metadata
        if self.repository_metadata and self.repository_metadata.get('access_token'):
            service = self.repository_metadata.get('service', '').lower()
            clone_url = self.repository_metadata.get('cloneUrlHttp')
            if clone_url and service in ['github', 'gitlab']:
                auth_url = clone_url.replace('https://', f'https://oauth2:{self.repository_metadata["access_token"]}@')
                repo.git.remote('set-url', 'origin', auth_url)
            return repo
            
        # Fall back to SCM config if no direct token
        if self.scm_config and self.repository_metadata:
            if (hasattr(self.scm_config.credentials, 'token_expires_at') and 
                self.scm_config.credentials.token_expires_at and 
                (self.scm_config.credentials.token_expires_at <= datetime.datetime.utcnow()) and
                self.scm_config.credentials.refresh_token):
                try:
                    encrypted_scm_id = self.repository_metadata.get('encrypted_scm_id')
                    if encrypted_scm_id:
                        decrypted_scm_id = decrypt_string(encrypted_scm_id)
                        self.scm_config = scm_manager.refresh_token(decrypted_scm_id)
                except Exception as e:
                    self.logger.error(f"Error refreshing token: {str(e)}")
            
            service = self.repository_metadata.get('service', '').lower()
            clone_url = self.repository_metadata.get('cloneUrlHttp')
            if clone_url and service in ['github', 'gitlab']:
                auth_url = clone_url.replace('https://', f'https://oauth2:{self.scm_config.credentials.access_token}@')
                repo.git.remote('set-url', 'origin', auth_url)
                
        return repo

    def __getattribute__(self, name):
        """Intercept git operations for authentication"""
        attr = super().__getattribute__(name)
        
        base_git_ops = [
            'git_clone', 'git_pull', 'git_checkout', 'git_status',
            'git_add', 'git_add_all', 'git_commit', 'git_push',
            'git_revert', 'git_branch', 'git_merge_squash', 
            'git_cherry_pick', 'read_file_from_repo'
        ]
        
        if callable(attr) and name in base_git_ops:
            def wrapper(*args, **kwargs):
                if 'repository_path' in kwargs:
                    # Get just the path from the repository
                    original_path = kwargs['repository_path']
                    repo = self._get_repo_instance(original_path)
                    # Keep the original path in kwargs
                    kwargs['repository_path'] = original_path
                return attr(*args, **kwargs)
            return wrapper
            
        return attr

    def create_github_pr(self, owner, repo_name, source_branch, target_branch, title, description):
        """Create a GitHub Pull Request"""
        access_token = self.repository_metadata.get('access_token') or (self.scm_config and self.scm_config.credentials.access_token)
        if not access_token:
            raise Exception("No access token available")

        # Clean and remove any remotes/origin/ prefix from branch names
        source_branch = clean_branch_name(source_branch.replace('remotes/origin/', ''))
        target_branch = clean_branch_name(target_branch.replace('remotes/origin/', ''))
        
        api_url = f"https://api.github.com/repos/{owner}/{repo_name}/pulls"
        headers = {
            "Authorization": f"token {access_token}",
            "Accept": "application/vnd.github.v3+json"
        }
        data = {
            "title": title,
            "body": description,
            "head": source_branch,
            "base": target_branch
        }

        response = requests.post(api_url, json=data, headers=headers)
        if response.status_code == 201:
            pr_data = response.json()
            return {
                "number": pr_data["number"],
                "url": pr_data["html_url"],
                "status": "created"
            }
        raise Exception(f"Failed to create GitHub PR: {response.json().get('message', '')}")

    def create_gitlab_pr(self, project_id, source_branch, target_branch, title, description):
        """Create a GitLab Merge Request"""
        access_token = self.repository_metadata.get('access_token') or (self.scm_config and self.scm_config.credentials.access_token)
        if not access_token:
            raise Exception("No access token available")

        api_url = self.repository_metadata.get('api_url') or (self.scm_config and self.scm_config.api_url) or 'https://gitlab.com'
        gl = gitlab.Gitlab(api_url, oauth_token=access_token)
        gl.auth()
        
        # Clean and remove any remotes/origin/ prefix from branch names
        source_branch = clean_branch_name(source_branch.replace('remotes/origin/', ''))
        target_branch = clean_branch_name(target_branch.replace('remotes/origin/', ''))
        
        project = gl.projects.get(project_id)
        mr = project.mergerequests.create({
            'source_branch': source_branch,
            'target_branch': target_branch,
            'title': title,
            'description': description
        })
        
        return {
            "number": mr.iid,
            "url": mr.web_url,
            "status": "created"
        }

    @register_function(
        description="Create a pull request from source branch to target branch",
        parameters={
            "type": "object",
            "properties": {
                "source_branch": {"type": "string", "description": "Source branch name"},
                "target_branch": {"type": "string", "description": "Target branch name"},
                "title": {"type": "string", "description": "PR title"},
                "repository_path": {"type": "string", "description": "Repository path"},
                "repository_metadata": {"type": "object", "description": "Repository metadata"},
                "description": {"type": "string", "description": "PR description"}
            },
            "required": ["source_branch", "target_branch", "title", "repository_path", "repository_metadata"]
        }
    )
    def create_pull_request(self, source_branch, target_branch, title, repository_path, repository_metadata, description=""):
        try:
            service = repository_metadata.get('service', '').lower()
            repo_name = repository_metadata.get('repositoryName')
            source_branch = clean_branch_name(source_branch)
            target_branch = clean_branch_name(target_branch)

            if service == 'github':
                org = repository_metadata.get('organization')
                return self.create_github_pr(org, repo_name, source_branch, target_branch, title, description)
            elif service == 'gitlab':
                return self.create_gitlab_pr(
                    repository_metadata.get('repositoryId'),
                    source_branch,
                    target_branch,
                    title,
                    description
                )
            else:
                raise Exception(f"Unsupported git service: {service}")
        except Exception as e:
            return f"Error creating pull request: {str(e)}"

    def merge_github_pr(self, owner, repo_name, pr_number, merge_type):
        """Merge a GitHub Pull Request"""
        if not self.scm_config:
            raise Exception("SCM configuration not available")

        api_url = f"https://api.github.com/repos/{owner}/{repo_name}/pulls/{pr_number}/merge"
        headers = {
            "Authorization": f"token {self.scm_config.credentials.access_token}",
            "Accept": "application/vnd.github.v3+json"
        }
        data = {"merge_method": merge_type}
        
        response = requests.put(api_url, json=data, headers=headers)
        if response.status_code == 200:
            return {"message": "Successfully merged PR", "status": "completed"}
        raise Exception(f"Failed to merge GitHub PR: {response.json().get('message', '')}")

    def merge_gitlab_pr(self, project_id, mr_iid, merge_type):
        """Merge a GitLab Merge Request"""
        if not self.scm_config:
            raise Exception("SCM configuration not available")

        gl = gitlab.Gitlab(
            self.scm_config.api_url or 'https://gitlab.com',
            oauth_token=self.scm_config.credentials.access_token
        )
        gl.auth()
        
        project = gl.projects.get(project_id)
        mr = project.mergerequests.get(mr_iid)
        
        if merge_type == "squash":
            mr.merge(squash=True)
        else:
            mr.merge()
            
        return {
            "message": f"Successfully merged MR #{mr_iid}",
            "status": "completed"
        }
    
    def git_stash(self, repository_path=None, message=None, untracked=False):
        """Stash changes in the working directory
        
        Args:
            repository_path (str, optional): Path to the repository. Defaults to None.
            message (str, optional): Stash message. Defaults to None.
            
        Returns:
            str: Result of the stash operation
        """
        try:
            repo = self._get_repo_instance(repository_path or "")
            cmd = ["stash"]
            if untracked:
                cmd.extend(["--include-untracked"])
            if message:
                cmd.extend(["save", message])
            result = repo.git.execute(["git"] + cmd)
            return result or "Changes stashed successfully"
        except GitCommandError as e:
            return f"Error stashing changes: {str(e)}"

    def _perform_merge_operation(self, service, repo_name, source_branch, target_branch, merge_type):
        """Perform the actual merge operation based on service type"""
        access_token = self.repository_metadata.get('access_token') or (self.scm_config and self.scm_config.credentials.access_token)
        if not access_token:
            raise Exception("No access token available")

        if service == 'github':
            gh = Github(access_token)
            org = self.repository_metadata.get('organization')
            if org:
                repo = gh.get_organization(org).get_repo(repo_name)
            else:
                repo = gh.get_user().get_repo(repo_name)

            if merge_type == "squash":
                repo.merge(target_branch, source_branch, f"Squash merge {source_branch} into {target_branch}", merge_method="squash")
            elif merge_type == "rebase":
                repo.merge(target_branch, source_branch, f"Rebase merge {source_branch} into {target_branch}", merge_method="rebase")
            else:
                repo.merge(target_branch, source_branch, f"Merge {source_branch} into {target_branch}")

        elif service == 'gitlab':
            api_url = self.repository_metadata.get('api_url') or (self.scm_config and self.scm_config.api_url) or 'https://gitlab.com'
            gl = gitlab.Gitlab(api_url, oauth_token=access_token)
            gl.auth()
            
            project = gl.projects.get(self.repository_metadata['repositoryId'])
            project.merge_requests.create({
                'source_branch': source_branch,
                'target_branch': target_branch,
                'title': f"Merge {source_branch} into {target_branch}",
                'squash': merge_type == "squash"
            })

    @register_function(
        description="Perform direct merge operations",
        parameters={
            "type": "object",
            "properties": {
                "source_branch": {"type": "string"},
                "target_branch": {"type": "string"},
                "repository_path": {"type": "string"},
                "repository_metadata": {"type": "object"},
                "merge_type": {"type": "string"}
            },
            "required": ["source_branch", "target_branch", "repository_path", "repository_metadata", "merge_type"]
        }
    )
    def perform_merge(self, source_branch, target_branch, repository_path, repository_metadata, merge_type="merge"):
        try:
            service = repository_metadata.get('service', '').lower()
            repo_name = repository_metadata.get('repositoryName')
            
            repo = self._get_repo_instance(repository_path)
            repo.git.fetch('--all')
            
            self._perform_merge_operation(service, repo_name, source_branch, target_branch, merge_type)
            
            return {
                "message": f"Successfully merged {source_branch} into {target_branch} using {merge_type}",
                "status": "completed"
            }
        except Exception as e:
            return f"Error performing merge: {str(e)}"

    @register_function(
        description="View git commit history/logs",
        parameters={
            "type": "object",
            "properties": {
                "repository_path": {"type": "string"},
                "n": {"type": "integer", "optional": True},
                "branch": {"type": "string", "optional": True}
            },
            "required": ["repository_path"]
        }
    )
    def git_log(self, repository_path, n=None, branch=None):
        try:
            repo = self._get_repo_instance(repository_path)
            log_command = ['--pretty=format:%H|%an|%ad|%s', '--date=iso']
            
            if n:
                log_command.extend([f'-n {n}'])
            if branch:
                log_command.extend([branch])
                
            logs = repo.git.log(log_command).split('\n')
            formatted_logs = []
            
            for log in logs:
                commit_hash, author, date, message = log.split('|')
                formatted_logs.append({
                    'hash': commit_hash,
                    'author': author,
                    'date': date,
                    'message': message
                })
                
            return formatted_logs
        except GitCommandError as e:
            return f"Error getting git logs: {str(e)}"

    @register_function(
        description="Create a new branch",
        parameters={
            "type": "object",
            "properties": {
                "branch_name": {"type": "string"},
                "repository_path": {"type": "string"},
                "start_point": {"type": "string", "optional": True},
                "checkout": {"type": "boolean", "optional": True}
            },
            "required": ["branch_name", "repository_path"]
        }
    )
    def create_branch_(self, branch_name, repository_path, start_point=None, checkout=True):
        try:
            repo = self._get_repo_instance(repository_path)
            branch_name = clean_branch_name(branch_name)
            start_point = clean_branch_name(start_point)
            if start_point:
                new_branch = repo.create_head(branch_name, start_point)
            else:
                new_branch = repo.create_head(branch_name)
                
            if checkout:
                new_branch.checkout()
                return f"Created and checked out new branch: {branch_name}"
            
            return f"Created new branch: {branch_name}"
        except GitCommandError as e:
            return f"Error creating branch: {str(e)}"

    @register_function(
        description="Switch to a different branch",
        parameters={
            "type": "object",
            "properties": {
                "branch_name": {"type": "string"},
                "repository_path": {"type": "string"},
                "create": {"type": "boolean", "optional": True}
            },
            "required": ["branch_name", "repository_path"]
        }
    )
    def switch_branch(self, branch_name, repository_path, create=False):
        try:
            repo = self._get_repo_instance(repository_path)
            
            if create:
                repo.git.checkout('-b', branch_name)
                return f"Created and switched to new branch: {branch_name}"
            else:
                repo.git.checkout(branch_name)
                return f"Switched to branch: {branch_name}"
        except GitCommandError as e:
            return f"Error switching branch: {str(e)}"
        
    def git_push(self, repository_path):
        try:
            repo = self._get_repo_instance(repository_path)
            try:
                # Get current branch name
                current_branch = repo.active_branch.name
                try:
                    # Try normal push first
                    repo.remotes.origin.push()
                except GitCommandError as e:
                    # Check if the error is due to no upstream branch
                    if "no upstream branch" in str(e):
                        # Set upstream and push
                        repo.git.push('--set-upstream', 'origin', current_branch)
                    else:
                        raise e
                return "Successfully pushed changes to remote."
            except GitCommandError as e:
                return f"Failed to push changes: {str(e)}"
        except Exception as e:
            return f"Error in git push operation: {str(e)}"

    def execute_git_command(self, command, repository_path):
        """Execute arbitrary git command"""
        try:
            repo = self._get_repo_instance(repository_path)
            command_args = command.strip().split()
            result = repo.git.execute(['git'] + command_args)
            return result
        except GitCommandError as e:
            return f"Git command error: {str(e)}"
        except Exception as e:
            return f"Error executing git command: {str(e)}"

    def diagnose_repository_path(self, repository_path=None):
        """Debug helper to diagnose repository path issues"""
        debug_info = {
            "base_path": self.base_path,
            "repository_path": repository_path,
            "base_path_exists": os.path.exists(self.base_path) if self.base_path else False,
            "base_path_has_git": False,
            "possible_paths": [],
            "valid_git_repos": []
        }
        
        if self.base_path and os.path.exists(self.base_path):
            debug_info["base_path_has_git"] = os.path.exists(os.path.join(self.base_path, '.git'))
        
        # Collect all possible paths
        possible_paths = []
        
        if repository_path:
            possible_paths.append(os.path.join(self.base_path, repository_path))
            
        possible_paths.append(self.base_path)
        
        if repository_path and os.path.isabs(repository_path):
            possible_paths.append(repository_path)
        
        debug_info["possible_paths"] = possible_paths
        
        # Check which paths have valid git repositories
        for path in possible_paths:
            if os.path.exists(path) and os.path.exists(os.path.join(path, '.git')):
                try:
                    repo = Repo(path)
                    debug_info["valid_git_repos"].append({
                        "path": path,
                        "current_branch": repo.active_branch.name if repo.active_branch else "detached",
                        "is_bare": repo.bare,
                        "remote_urls": [remote.url for remote in repo.remotes]
                    })
                except Exception as e:
                    debug_info["valid_git_repos"].append({
                        "path": path,
                        "error": str(e)
                    })
        
        return debug_info