from datetime import datetime
from typing import Dict, Any, Callable, Optional, List
import os
import json
import subprocess
from app.models.code_generation_model import Message
from app.core.constants import TASKS_COLLECTION_NAME, DEPLOYMENT_COLLECTION_NAME
from app.utils.datetime_utils import generate_timestamp
from app.utils.code_generation_utils import get_codegeneration_path
from app.core.websocket.client import WebSocketClient
import zipfile
import tempfile
import os
import boto3
import json
from app.core.Settings import settings
import time
import uuid
from pydantic import BaseModel
import requests
import yaml

class DeploymentModel(BaseModel):
    _id: str
    deployment_id: str
    project_id: str
    task_id: str
    app_id: str
    branch_name: str
    app_url: str
    artifact_path: str
    custom_domain:str
    job_id: str
    status: str
    message: str
    command: str
    root_path: str
    build_path: str
    created_at: datetime
    updated_at: datetime

class DeploymentController:
    
    WILDCARD_CERTIFICATE_ARN = "arn:aws:acm:us-east-1:058264095463:certificate/487bc3c3-fdb0-40e0-93ef-f14dfa921b9f"
    def __init__(self, task_id, ws_client, db):
        self.task_id = task_id
        self.ws_client = ws_client
        self.db = db
        self._command_map = self._initialize_command_map()
        
    def _initialize_command_map(self) -> Dict[str, Callable]:
        """Initialize mapping of command names to their handler methods"""
        return {
            'list_dir': self._handle_list_dir,
            'start_deploy': self._handle_start_deploy,
            'deployment_status': self._handle_deployment_status,
            'manifest': self._handle_manifest
        }
    
    def _handle_start_deploy(self, data: Dict[str, Any]):
        """Handle start deployment request"""
        print(f"Handling start deployment command with input: {data}")
        if not data.get("id", ""):
            data["id"] = str(uuid.uuid4())[:8]
        
        # Ensure we have a valid branch name
        branch_name = data.get("branch_name", "")
        if not branch_name or len(branch_name.strip()) == 0:
            branch_name = "kavia-main"  # Default to 'main' if branch name is empty
            data["branch_name"] = branch_name
            self._send_message(f"Using default branch name '{branch_name}' as none was provided")
        
        self._send_message("Deployment started")
        self.ws_client.send_message("deploy", data)
        
        task_details = self.db[TASKS_COLLECTION_NAME].find_one({"_id": self.task_id})
        
        # Convert project_id to string if it's not already
        project_id = task_details.get("project_id", "")
        if not isinstance(project_id, str):
            project_id = str(project_id)
        
        deployment_model = DeploymentModel(
            _id=str(data.get("id", "")),
            deployment_id=str(data.get("id", "")),
            project_id=str(project_id),  # Use the converted string project_id
            task_id=str(self.task_id),
            app_id=str(task_details.get("app_id", "")),
            branch_name=str(branch_name),  # Use validated branch name
            app_url=str(data.get("app_url", "")),
            custom_domain="",
            artifact_path=str(data.get("artifact_path", "")),
            command=str(data.get("command", "")),
            root_path=str(data.get("root_path", "")),
            build_path="",  # Initialize with empty string
            job_id="",  # Initialize with empty string
            status="processing",
            message="Deployment started",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        # Log what we're storing in the database
        self._send_message(f"Storing deployment details with branch_name={branch_name}")
        
        # Insert document in database
        self.db[DEPLOYMENT_COLLECTION_NAME].update_one(
            {"_id": data.get("id", "")},
            {"$set": deployment_model.model_dump()},
            upsert=True
        )

    def _handle_deployment_status(self, data: Dict[str, Any]):
        """Handle deployment status request"""
        print(f"Handling deployment status command with input: {data}")
        # Placeholder for actual deployment status logic
        self._send_message("Deployment status updated")
        self.ws_client.send_message("deployment_status", data)
        
        status = data.get("status", "").lower()
        # Build success
        if status == "success":
            self._send_message("Deployment completed successfully")
            data["status"] = "deploying"
            self.ws_client.send_message("deployment_status", data)
            self.handle_deploy_artifact(data.get("build_path", ""), self.ws_client, data)
        else:
            self.db[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": data.get("id", "")},
                {"$set": {
                    "status": status, 
                    "message": data.get("message", ""),
                    "build_path": data.get("build_path", ""),
                    "updated_at": datetime.now()
                }}
            )
    
    def handle_command(self, command: str, input_data: Dict[str, Any] = None) -> bool:
        """Dynamically handle deployment commands"""
        print(f"Handling deployment command: {command} with input: {input_data}")
        handler = self._command_map.get(command)
        if handler:
            try:
                handler(input_data or {})
                return True
            except Exception as e:
                print(f"Error handling deployment command {command}: {e}")
                error_msg = f"Error handling deployment command {command}: {str(e)}"
                self._send_message(error_msg)
                return False
        return False

    def _send_message(self, content: str):
        """Helper method to send messages through websocket and update DB"""
        # Truncate content if it exceeds 2500 characters
        if len(content) > 2500:
            content = content[:2497] + "..."
            
        message_obj = Message(
            content=content, 
            sender="Deployment", 
            timestamp=generate_timestamp()
        )
        print(message_obj)
        if self.ws_client is not None:
            self.ws_client.send_message("deployment_output", message_obj.to_dict())

        if self.db is not None:
            # Update in database
            self.db[TASKS_COLLECTION_NAME].update_one(
                {"_id": self.task_id},
                {
                    "$push": {
                        "messages": message_obj.to_dict()
                    }
                }
            )
    
    def _get_root_directory(self):
        """Get the root workspace directory for the current task"""
        print(f"[DeploymentController] Getting root directory for task_id={self.task_id}")
        if os.getenv("LOCAL_DEBUG"):
            if self.task_id.startswith("code-generation") or self.task_id.startswith("cg"): 
                return "/tmp/kavia/workspace/code-generation"
            return f"/tmp/kavia/workspace/{self.task_id}"
        if self.task_id.startswith("code-generation") or self.task_id.startswith("cg"): 
            return "/home/<USER>/workspace/code-generation"
        return f"/home/<USER>/workspace/{self.task_id}"
    
    def _handle_list_dir(self, input_data: Dict[str, Any]):
        """Handle listing directory contents"""
        try:
            base_dir = input_data.get("base_dir", "")
            
            # If base_dir is "root_folder", use the root directory
            if base_dir == "root_folder":
                dir_path = self._get_root_directory()
            else:
                # Otherwise, join with the root directory if it's not an absolute path
                if os.path.isabs(base_dir):
                    dir_path = base_dir
                else:
                    dir_path = os.path.join(self._get_root_directory(), base_dir)
            
            # Check if directory exists
            if not os.path.exists(dir_path):
                self._send_message(f"Directory not found: {dir_path}")
                self.ws_client.send_message("dir_list", {"error": f"Directory not found: {dir_path}"})
                return
            
            # List files and directories
            dir_contents = []
            for item in os.listdir(dir_path):
                item_path = os.path.join(dir_path, item)
                item_type = "folder" if os.path.isdir(item_path) else "file"
                dir_contents.append({"name": item, "type": item_type})
            
            # Send directory listing to client
            self.ws_client.send_message("dir_list", {
                "path": dir_path,
                "contents": dir_contents
            })
            
            # Format message for display
            message = f"**Directory Listing for {dir_path}:**\n\n"
            for item in dir_contents:
                icon = "📁" if item["type"] == "folder" else "📄"
                message += f"{icon} {item['name']}\n"
            
            self._send_message(message)
        except Exception as e:
            error_message = f"Error listing directory: {str(e)}"
            self._send_message(error_message)
            self.ws_client.send_message("dir_list", {"error": error_message}) 
    
    def _handle_manifest(self, input_data: Dict[str, Any]):
        """Handle manifest reading and parsing"""
        try:
            # Get the root directory
            root_dir = self._get_root_directory()
            manifest_path = os.path.join(root_dir, '.project_manifest.yaml')
            
            self._send_message(f"Reading manifest file from: {manifest_path}")
            
            # Check if manifest file exists
            if not os.path.exists(manifest_path):
                error_msg = f"Manifest file not found: {manifest_path}"
                self._send_message(error_msg)
                self.ws_client.send_message("manifest", {"error": error_msg})
                return
            
            # Read and parse the YAML manifest file
            with open(manifest_path, 'r', encoding='utf-8') as file:
                manifest_data = yaml.safe_load(file)
            
            if not manifest_data or 'containers' not in manifest_data:
                error_msg = "Invalid manifest format: 'containers' section not found"
                self._send_message(error_msg)
                self.ws_client.send_message("manifest", {"error": error_msg})
                return
            
            # Parse containers and format response
            response = {}
            containers = manifest_data.get('containers', [])
            
            for container in containers:
                container_name = container.get('container_name', '')
                container_type = container.get('container_type', '')
                workspace = container.get('workspace', '')
                
                if not container_name or not container_type:
                    self._send_message(f"Skipping container with missing name or type: {container}")
                    continue
                
                base_path = f'{workspace}/{container_name}'
                
                container_info = {
                    "base_path": base_path,
                    "container_name": container_name,
                    "framework": container.get('framework', ''),
                    "type": container_type,
                    "ports": container.get('ports', ''),
                    "buildCommand": container.get('buildCommand', ''),
                    "startCommand": container.get('startCommand', ''),
                    "lintCommand": container.get('lintCommand', ''),
                    "container_details": container.get('container_details', {})
                }
                
                # Add to response under the container type
                if container_type in response:
                    # If multiple containers of same type, convert to list or handle as needed
                    if not isinstance(response[container_type], list):
                        response[container_type] = [response[container_type]]
                    response[container_type].append(container_info)
                else:
                    response[container_type] = container_info
            
            # Send successful response
            self._send_message(f"Successfully parsed manifest with {len(containers)} containers")
            
            # Add project info directly to response
            response["project_name"] = manifest_data.get('overview', {}).get('project_name', '')
            response["description"] = manifest_data.get('overview', {}).get('description', '')
            
            self.ws_client.send_message("manifest", response)
            
        except yaml.YAMLError as e:
            error_msg = f"Error parsing YAML manifest: {str(e)}"
            self._send_message(error_msg)
            self.ws_client.send_message("manifest", {"error": error_msg})
        except Exception as e:
            error_msg = f"Error reading manifest: {str(e)}"
            self._send_message(error_msg)
            self.ws_client.send_message("manifest", {"error": error_msg})
    
    
    def setup_instant_custom_domain(self, amplify_client, app_id: str, 
                               project_id: str, branch_name: str) -> dict:
        """Set up custom domain using wildcard certificate - INSTANT!"""
        try:
            # Generate clean subdomain
            project_clean = str(project_id).lower().replace(" ", "-").replace(".", "-").replace("_", "-")
            branch_clean = branch_name.lower().replace("/", "-").replace("_", "-")
            subdomain_prefix = f"{project_clean}-{branch_clean}"
            custom_url = f"https://{subdomain_prefix}.kavia.app"
            
            self._send_message(f"⚡ Setting up INSTANT custom domain: {custom_url}")
            
            # Check existing domain associations
            try:
                existing_domains = amplify_client.list_domain_associations(appId=app_id)
                domain_associations = existing_domains.get('domainAssociations', [])
                
                # Look for existing kavia.app domain
                kavia_domain = None
                for domain_assoc in domain_associations:
                    if domain_assoc.get('domainName') == 'kavia.app':
                        kavia_domain = domain_assoc
                        break
                
                if kavia_domain:
                    # Check if our subdomain already exists
                    existing_subdomains = [
                        sub.get('prefix', '') 
                        for sub in kavia_domain.get('subDomains', [])
                    ]
                    
                    if subdomain_prefix in existing_subdomains:
                        self._send_message(f"✅ Subdomain {subdomain_prefix} already configured")
                    else:
                        self._send_message(f"📝 Adding new subdomain {subdomain_prefix} to existing domain")
                       
                else:
                    # Create new domain association
                    self._send_message("🔧 Creating domain association with wildcard certificate...")
                    response = amplify_client.create_domain_association(
                        appId=app_id,
                        domainName="kavia.app",
                        subDomainSettings=[{
                            'prefix': subdomain_prefix,
                            'branchName': branch_name
                        }],
                        enableAutoSubDomain=False
                    )
                    
                    self._send_message(f"✅ Domain association created for {subdomain_prefix}.kavia.app")
            
            except Exception as domain_error:
                error_msg = str(domain_error)
                if "DomainAlreadyAssociatedException" in error_msg:
                    self._send_message("ℹ️ Domain already associated with another app")
                    # This is often fine - the subdomain may still work
                elif "LimitExceededException" in error_msg:
                    self._send_message("⚠️ Domain association limit reached")
                    return {'status': 'error', 'message': 'Domain limit exceeded'}
                else:
                    self._send_message(f"⚠️ Domain association warning: {error_msg}")
            
            self._send_message(f"🎉 INSTANT custom domain ready: {custom_url}")
            self._send_message("⚡ No SSL wait time needed - using wildcard certificate!")
            
            return {
                'status': 'instant_success',
                'url': custom_url,
                'setup_time': '< 10 seconds',
                'subdomain_prefix': subdomain_prefix
            }
            
        except Exception as e:
            self._send_message(f"❌ Error setting up custom domain: {str(e)}")
            return {'status': 'error', 'message': str(e)}
    def setup_custom_domain_background(self, amplify_client, amplify_app_id: str, 
                                     deployment_details: dict, data: dict, job_id: str, 
                                     branch_name: str, amplify_url: str):
        """Set up custom domain in background after Amplify URL is already sent"""
        try:
            self._send_message("🔧 Setting up custom domain in background...")
            
            USE_CUSTOM_DOMAIN = True
            project_id = deployment_details.get("project_id", "") or data.get("project_id", "")
            
            if project_id and USE_CUSTOM_DOMAIN:
                # Verify wildcard certificate first
                if self.verify_wildcard_certificate():
                    self._send_message("🚀 Setting up custom domain with wildcard certificate...")
                    
                    domain_result = self.setup_instant_custom_domain(
                        amplify_client, amplify_app_id, project_id, branch_name
                    )
                    
                    if domain_result['status'] == 'instant_success':
                        custom_domain_url = domain_result['url']
                        self._send_message(f"✅ Custom domain ready: {custom_domain_url}")
                        self._send_message("⚡ Domain will be active within 2-3 minutes (DNS propagation)")
                        self._send_message(f"🔗 You now have both URLs available:")
                        self._send_message(f"   • Amplify URL (immediate): {amplify_url}")
                        self._send_message(f"   • Custom domain (2-3 min): {custom_domain_url}")
                        
                        # Send updated status with custom domain as additional info
                        self.ws_client.send_message("deployment_status", {
                            "id": data.get("id", ""),
                            "status": "custom_domain_ready",
                            "job_id": job_id,
                            "message": f"Custom domain configured. Both URLs available.",
                            "app_url": custom_domain_url,  # Keep Amplify URL as primary
                            "amplify_url": amplify_url,
                            "app_id": amplify_app_id,
                            "branch": branch_name,
                            "setup_time": "< 10 seconds (wildcard certificate)"
                        })
                        
                        # Update database with custom domain info and set app_url to custom domain
                        self.db[DEPLOYMENT_COLLECTION_NAME].update_one(
                            {"_id": data.get("id", "")},
                            {"$set": {
                                "app_url": custom_domain_url,  # Update app_url to custom domain
                                "custom_domain": custom_domain_url,
                                "updated_at": datetime.now()
                            }}
                        )
                        
                    else:
                        self._send_message(f"⚠️ Custom domain setup failed, continuing with Amplify URL: {amplify_url}")
                else:
                    # Certificate not ready, use Amplify URL
                    self._send_message(f"⚠️ Wildcard certificate not ready, continuing with Amplify URL: {amplify_url}")
            else:
                self._send_message("ℹ️ Custom domain not configured (no project ID), using Amplify URL only")
                
        except Exception as e:
            self._send_message(f"⚠️ Custom domain setup error: {str(e)}")
            self._send_message(f"✅ App remains available at Amplify URL: {amplify_url}")

    def verify_wildcard_certificate(self) -> bool:
        """Verify that the wildcard certificate is valid and ready"""
        try:
            acm_client = boto3.client('acm',
                region_name=settings.AWS_DEPLOYMENT_REGION,
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
            )
            
            response = acm_client.describe_certificate(
                CertificateArn=self.WILDCARD_CERTIFICATE_ARN
            )
            
            status = response['Certificate']['Status']
            if status != 'ISSUED':
                self._send_message(f"⚠️ Wildcard certificate status: {status}")
                return False
                
            return True
            
        except Exception as e:
            self._send_message(f"Could not verify wildcard certificate: {str(e)}")
            return False
    
    def check_domain_availability(self, url: str, max_timeout: int = 120) -> bool:
        """Check if a domain is available by making HTTP requests
        
        Args:
            url: The URL to check availability for
            max_timeout: Maximum time in seconds to wait for availability
            
        Returns:
            bool: True if domain is available, False otherwise
        """
        self._send_message(f"🔍 Checking availability of {url}")
        start_time = time.time()
        available = False
        
        # Try up to max_timeout seconds
        while time.time() - start_time < max_timeout:
            try:
                # Set a short timeout for each individual request
                response = requests.get(url, timeout=2, allow_redirects=True)
                
                if response.status_code < 500:  # Consider any non-server error as available
                    self._send_message(f"✅ Domain {url} is available! (Status: {response.status_code})")
                    available = True
                    break
                else:
                    self._send_message(f"⏳ Domain still initializing... (Status: {response.status_code})")
            except requests.RequestException as e:
                self._send_message(f"⏳ Domain not yet available: {str(e)}")
            
            # Wait before trying again
            time.sleep(2)
        
        elapsed = time.time() - start_time
        if not available:
            self._send_message(f"⚠️ Domain not available after {elapsed:.1f} seconds. It may take longer to initialize.")
        else:
            self._send_message(f"✅ Domain became available after {elapsed:.1f} seconds")
        
        return available
    
    def handle_deploy_artifact(self, build_path: str, ws_client: WebSocketClient, data: Dict[str, Any]):
        """Handle deploying an artifact
        
        Zips all files in the build_path directory, uploads to S3 bucket,
        and triggers a deployment using AWS Amplify
        
        Args:
            build_path: Path to the directory containing build artifacts
            ws_client: WebSocket client for sending status updates
        """
        
        
        self.ws_client = ws_client
        
        try:
            # Create a friendly deployment name based on timestamp
            timestamp = int(time.time())
            if data.get("id", ""):
                deployment_id = str(data.get("id", ""))
            else:
                deployment_id = str(uuid.uuid4())[:8]
            artifact_name = f"deployment-{timestamp}-{deployment_id}.zip"
            
            self._send_message(f"Starting deployment process for pre-built artifacts in {build_path}")
            
            # Validate build path exists
            if not os.path.exists(build_path):
                raise FileNotFoundError(f"Build path does not exist: {build_path}")
            
            # Check if build directory contains index.html (crucial for web apps)
            if not os.path.exists(os.path.join(build_path, 'index.html')):
                self._send_message("⚠️ Warning: No index.html found in build directory. This may cause issues with deployment.")
            
            # Create a temporary zip file
            with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as temp_file:
                temp_path = temp_file.name
                
            # Zip all files in the build_path
            self._send_message("Compressing build artifacts...")
            with zipfile.ZipFile(temp_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                file_count = 0
                for root, _, files in os.walk(build_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        # Get the relative path inside the build_path
                        rel_path = os.path.relpath(file_path, build_path)
                        zipf.write(file_path, rel_path)
                        file_count += 1
                
                self._send_message(f"Added {file_count} files to deployment package")
            
            # Upload to S3
            self._send_message(f"Uploading deployment artifact to S3...")
            s3_client = boto3.client('s3', 
                region_name=settings.AWS_DEPLOYMENT_REGION,
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
            )
            
            # Get the bucket name from settings or use default
            bucket_name = settings.S3_BUILD_ARTIFACTS_BUCKET
            
            with open(temp_path, 'rb') as file_data:
                s3_client.upload_fileobj(
                    file_data, 
                    bucket_name, 
                    f"deployments/{artifact_name}"
                )
            
            s3_url = f"s3://{bucket_name}/deployments/{artifact_name}"
            self._send_message(f"Artifact uploaded to {s3_url}")
            
            # Create Amplify client
            amplify_client = boto3.client('amplify',
                region_name=settings.AWS_DEPLOYMENT_REGION,
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
            )
            
            # Start deployment with Amplify - this assumes the app is already set up in Amplify
            # Get app ID from environment or pass it as parameter
            deployment_details = self.db[DEPLOYMENT_COLLECTION_NAME].find_one({"_id": data.get("id", "")})
            if not deployment_details:
                self._send_message("Warning: No deployment details found in database")
                deployment_details = {}
                
            # First check if app_id exists in the incoming data
            amplify_app_id = data.get("app_id", "")
            
            # If not in incoming data, try to get from deployment details
            if not amplify_app_id:
                amplify_app_id = deployment_details.get("app_id", "")
                
            # Log the app ID for debugging
            self._send_message(f"Retrieved app_id from data/DB: '{amplify_app_id}'")
            
            # Strip any whitespace and validate app ID is not empty
            if amplify_app_id:
                amplify_app_id = amplify_app_id.strip()
                
            # Verify the app ID by checking if it exists in Amplify
            app_exists = False
            if amplify_app_id:
                try:
                    # Try to get the app to verify it exists
                    amplify_client.get_app(appId=amplify_app_id)
                    app_exists = True
                    self._send_message(f"Verified existing Amplify app with ID: {amplify_app_id}")
                except Exception as e:
                    self._send_message(f"App ID validation failed: {str(e)}")
                    self._send_message("Will create a new app instead.")
                    amplify_app_id = ""  # Reset to empty to trigger new app creation
            
            # Get branch name from data or DB
            branch_name = data.get("branch_name", "")
            if not branch_name:
                branch_name = deployment_details.get("branch_name", "")
                
            # Ensure branch name is valid
            if not branch_name or len(branch_name.strip()) == 0:
                branch_name = "kavia-main"  # Default to 'main' if branch name is empty
                self._send_message(f"Using default branch name '{branch_name}' as none was provided")
                
            # Log all the values for debugging
            self._send_message(f"Final values: app_id='{amplify_app_id}', branch_name='{branch_name}', app_exists={app_exists}")
            
            # Create a new Amplify app if no valid app ID is provided or app doesn't exist
            if not amplify_app_id or not app_exists:
                self._send_message("Creating new Amplify app for manual deployment...")
                app_name = f"app-{deployment_id}"
                
                # Improved custom rules with more specific patterns for SPA routing
                custom_rules = [
                    # Serve index.html for routes without file extensions (SPA routes)
                    {
                        "source": "</^[^.]+$|\\.(?!(css|gif|ico|jpg|js|png|txt|svg|woff|woff2|ttf|map|json)$)([^.]+$)/>",
                        "target": "/index.html",
                        "status": "200"
                    },
                    # Serve static files directly
                    {
                        "source": "/<*>",
                        "target": "/<*>",
                        "status": "200"
                    }
                ]
                
                # Create the new Amplify app with improved settings
                app_response = amplify_client.create_app(
                    name=app_name,
                    description=f"Manual deployment app {deployment_id}",
                    platform="WEB",
                    customRules=custom_rules,
                    enableBranchAutoBuild=False,  # Manual deployments only
                    buildSpec=json.dumps({
                        "version": 1,
                        "frontend": {
                            "phases": {
                                "build": {
                                    "commands": [
                                        "echo 'Using pre-built artifacts'"
                                    ]
                                }
                            },
                            "artifacts": {
                                "baseDirectory": "/",
                                "files": ["**/*"]
                            }
                        }
                    })
                )
                # app
                amplify_app_id = app_response['app']['appId']
                self._send_message(f"Created new Amplify app with ID: {amplify_app_id}")
                
                # Ensure we have a valid branch name
                if not branch_name or len(branch_name.strip()) == 0:
                    branch_name = "kavia-main"  # Default to 'main' if branch name is empty
                    self._send_message(f"Using default branch name '{branch_name}' as none was provided")
                
                # Create a new branch for the app
                branch_response = amplify_client.create_branch(
                    appId=amplify_app_id,
                    branchName=branch_name,
                    stage="PRODUCTION",  # Mark as production branch
                    enableAutoBuild=False  # Manual deployments only
                )
                
                self._send_message(f"Created branch '{branch_name}' for app ID: {amplify_app_id}")
            
            # Ensure we have a valid branch name before starting deployment
            if not branch_name or len(branch_name.strip()) == 0:
                branch_name = "kavia-main"  # Default to 'main' if branch name is empty
                self._send_message(f"Using default branch name '{branch_name}' as none was provided")
            
            # Generate a pre-signed URL for the S3 object (more reliable for deployments)
            s3_parts = s3_url.replace("s3://", "").split("/", 1)
            bucket = s3_parts[0]
            key = s3_parts[1]
            
            # Generate a pre-signed URL that's valid for 1 hour
            presigned_url = s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': bucket, 'Key': key},
                ExpiresIn=3600
            )
            
            self._send_message(f"Generated temporary access URL for deployment artifact")
            
            # Start deployment job using pre-signed URL for reliability
            try:
                self._send_message(f"Initiating manual deployment for app ID: {amplify_app_id}, branch: {branch_name}")
                response = amplify_client.start_deployment(
                    appId=amplify_app_id,
                    branchName=branch_name,
                    sourceUrl=presigned_url
                )
                
                job_id = response.get('jobId', 'Unknown')
                self._send_message(f"Amplify manual deployment started. Job ID: {job_id}")
            except Exception as e:
                error_details = str(e)
                self._send_message(f"Error starting deployment: {error_details}")
                raise
            
            # Wait longer for deployment initialization before checking status
            self._send_message("Waiting for deployment to initialize...")
            
            # Poll for app details with increased timeout and more detailed information
            app_url = ""
            max_attempts = 5
            for attempt in range(1, max_attempts + 1):
                try:
                    # Wait between attempts with exponential backoff
                    wait_time = 5 * attempt
                    self._send_message(f"Waiting {wait_time} seconds for deployment initialization (attempt {attempt}/{max_attempts})...")
                    time.sleep(wait_time)
                    
                    # Get app details
                    app_details = amplify_client.get_app(appId=amplify_app_id)
                    app_info = app_details['app']
                    
                    # Log detailed app information for debugging
                    self._send_message(f"App name: {app_info.get('name')}")
                    self._send_message(f"App ID: {app_info.get('appId')}")
                    self._send_message(f"Default domain: {app_info.get('defaultDomain', 'Not available yet')}")
                    
                    domain = app_info.get('defaultDomain', '')
                    if domain:
                        # First, send the Amplify URL immediately so users can access the app
                        amplify_url = f"https://{branch_name}.{domain}"
                        production_url = f"https://{domain}"
                        
                        self._send_message(f"✅ Application deployed to Amplify: {amplify_url}")
                        self._send_message(f"Default Amplify URL: {production_url}")
                        
                        # ALWAYS send Amplify URL first - this is the immediate access URL
                        self.ws_client.send_message("deployment_status", {
                            "id": data.get("id", ""),
                            "status": "success",
                            "job_id": job_id,
                            "message": f"✅ Deployment successful! App is live at Amplify URL.",
                            "app_url": amplify_url,  # Always Amplify URL first
                            "amplify_url": amplify_url,
                            "app_id": amplify_app_id,
                            "branch": branch_name,
                            "deployment_type": "amplify_ready"
                        })
                        
                        # Check if Amplify URL is available
                        # amplify_available = self.check_domain_availability(amplify_url, max_timeout=30)
                        self._send_message(f"🎉 Your application is live at: {amplify_url}")
                        
                        # Set app_url to Amplify URL for database update
                        app_url = amplify_url
                        
                        # Now try to set up custom domain in background (separate process)
                        self.setup_custom_domain_background(amplify_client, amplify_app_id, deployment_details, data, job_id, branch_name, amplify_url)
                        
                        break
                    else:
                        self._send_message("Domain not yet available, will retry...")
                    
                except Exception as e:
                    self._send_message(f"Could not retrieve app URL (attempt {attempt}/{max_attempts}): {str(e)}")
                    if attempt == max_attempts:
                        self._send_message("❗ Failed to get app URL after maximum attempts. Check AWS Console directly.")
                        self._send_message("Your app may still be deploying correctly despite this error.")
            
            # Check job status to verify deployment is proceeding
            if job_id != 'Unknown':
                try:
                    job_details = amplify_client.get_job(appId=amplify_app_id, branchName=branch_name, jobId=job_id)
                    job_status = job_details.get('job', {}).get('status', 'UNKNOWN')
                    job_summary = job_details.get('job', {}).get('summary', {})
                    
                    self._send_message(f"Deployment job status: {job_status}")
                    if job_summary:
                        self._send_message(f"Job summary: {json.dumps(job_summary, default=str)}")
                except Exception as e:
                    self._send_message(f"Could not retrieve job status: {str(e)}")
            
          
            # Clean up the temporary file
            os.unlink(temp_path)
            
            # Return success with detailed information


            # Success message already sent above with Amplify URL
            # No need to send duplicate success message
                        

            self.db[DEPLOYMENT_COLLECTION_NAME].update_one(
                {"_id": data.get("id", "")},
                {"$set": {
                    "status": "success",
                    "message": f"Pre-built artifacts deployed with job ID: {job_id}",
                    "app_url": app_url,
                    "app_id": amplify_app_id,
                    "branch": branch_name,
                    "job_id": job_id,
                    "updated_at": datetime.now()
                }}
            )
            
            return {
                "status": "success",
                "type": "deployment",
                "message": f"Pre-built artifacts deployed with job ID: {job_id}",
                "job_id": job_id,
                "artifact_path": s3_url,
                "app_url": app_url,
                "app_id": amplify_app_id,
                "branch": branch_name,
                "note": "Deployment in progress. URL may not be immediately accessible."
            }
            
        except Exception as e:
            error_msg = f"Error during deployment: {str(e)}"
            self._send_message(error_msg)
            
            # Send error status via WebSocket
            self.ws_client.send_message("deployment_status", {
                "status": "failed",
                "message": error_msg
            })
            
            # Return failure
            return {
                "status": "failed",
                "message": error_msg
            }
    