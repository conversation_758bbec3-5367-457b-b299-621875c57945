
from typing import Dict, Any
from app.core.code_generation import TaskExecutionAgent
from app.core.websocket.client import WebSocketClient
from app.utils.datetime_utils import generate_timestamp
from app.core.constants import TASKS_COLLECTION_NAME
from code_generation_core_agent.agents.preview.preview_manager import PreviewManager
from app.utils.project_utils import change_host
from app.models.preview_state_model import PreviewResponse
from app.core.Settings import settings

import os
import threading
import functools

def run_in_daemon_thread(func):
    """Decorator to run function in a daemon thread"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        thread = threading.Thread(
            target=func,
            args=args,
            kwargs=kwargs,
            daemon=True,
            name=f"{func.__name__}_daemon"
        )
        thread.start()
        return thread
    return wrapper

def process_url(url) -> str:
    try:
        if os.environ.get("host"):
            print("Host is set in env", os.environ.get("host"))
            if "localhost" in url and not os.environ.get("LOCAL_DEBUG"):
                url = change_host(url, os.environ.get("host"), upgrade_to_https=True)

        if ":3000" in url and settings.STAGE not in ["dev", "develop", "qa","pre_prod"]:
            url = url.replace(":3000", ":3001")
        return url
    except Exception:
        return url

class PreviewController:
    def __init__(self, agent: TaskExecutionAgent, task_id: str, ws_client: WebSocketClient, db):
        self.agent = agent
        self.task_id = task_id
        self.ws_client = ws_client
        self.db = db
        self.preview_manager: PreviewManager = self.agent.preview_manager
        self._command_map = {
            'get_preview_url': self._handle_get_preview_url,
            'restart_preview': self._handle_restart_preview,
            'get_all_containers': self._handle_get_all_containers,
            'run_all_containers': self._handle_run_all_containers,
            'get_all_status': self._handle_get_all_status,
            'restart_container': self._handle_restart_container
        }

    def handle_command(self, command: str, input_data: Dict[str, Any] = None) -> bool:
        """Handle preview commands"""
        handler = self._command_map.get(command)
        if handler:
            try:
                handler(input_data or {})
                return True
            except Exception as e:
                print(f"Error handling preview command {command}: {e}")
                return False
        return False

    @run_in_daemon_thread
    def _handle_get_all_containers(self, input_data: Dict[str, Any]):
        """Get all containers for the current task - runs in daemon thread"""
        try:
            if not self.preview_manager:
                self.ws_client.send_message("containers", {
                    "status": "error", 
                    "message": "Preview manager not initialized",
                    "timestamp": generate_timestamp()
                })
                return
                
            containers = self.preview_manager.containers
            if not containers:
                self.ws_client.send_message("containers", {
                    "status": "error", 
                    "message": "No containers found",
                    "timestamp": generate_timestamp()
                })
                return
            
            print(f"Found {len(containers)} containers for task {self.task_id}")
            print(f"Containers: {containers}")
            container_list = []
            for container in containers.values():
                container_dict = container.to_dict()
                container_dict['name'] = container.container_name
                container_dict['container_type'] = container.container_type
                container_dict['framework'] = container.framework
                container_list.append(container_dict)
            
            print("Containers", container_list)
            self.ws_client.send_message("containers", {
                "status": "success",
                "containers": container_list,
                "timestamp": generate_timestamp()
            })

        except Exception as e:
            print(f"Error getting containers: {e}")
            self.ws_client.send_message("containers", {
                "status": "error", 
                "message": str(e),
                "timestamp": generate_timestamp()
            })

    def _handle_run_all_containers(self, input_data: Dict[str, Any]):
        """Initiate run all containers - already uses daemon thread internally"""
        try:
            if not self.preview_manager:
                self.ws_client.send_message("run_all_containers_response", {
                    "status": "error",
                    "message": "Preview manager not initialized",
                    "timestamp": generate_timestamp()
                })
                return
                
            # Start run in daemon thread
            self._run_all_containers_thread(input_data)
            
            # Send immediate response
            self.ws_client.send_message("run_all_containers_response", {
                "status": "starting",
                "message": "Running all containers initiated",
                "timestamp": generate_timestamp()
            })
            
        except Exception as e:
            print(f"Error running all containers: {e}")
            self.ws_client.send_message("run_all_containers_response", {
                "status": "error",
                "message": str(e),
                "timestamp": generate_timestamp()
            })
        
    @run_in_daemon_thread
    def _handle_get_all_status(self, input_data: Dict[str, Any]):
        """Get status of all containers - runs in daemon thread"""
        try:
            if not self.preview_manager:
                self.ws_client.send_message("container_statuses", {
                    "status": "error", 
                    "message": "Preview manager not initialized",
                    "timestamp": generate_timestamp()
                })
                return
                
            statuses = self.preview_manager.get_all_statuses()
            if not statuses:
                self.ws_client.send_message("container_statuses", {
                    "status": "error", 
                    "message": "No container status found",
                    "timestamp": generate_timestamp()
                })
                return
            
            print(f"Found {len(statuses)} container statuses for task {self.task_id}")
                
            self.ws_client.send_message("container_statuses", {
                "status": "success",
                "container_statuses": statuses,
                "timestamp": generate_timestamp()
            })
        except Exception as e:
            print(f"Error getting container statuses: {e}")
            self.ws_client.send_message("container_statuses", {
                "status": "error", 
                "message": str(e),
                "timestamp": generate_timestamp()
            })
        
    @run_in_daemon_thread
    def _handle_get_preview_url(self, input_data: Dict[str, Any]):
        """Get the current preview URL - runs in daemon thread"""
        try:
            if not self.preview_manager:
                self.ws_client.send_message("preview_error", {"message": "Preview manager not initialized"})
                return
                
            url = self.preview_manager.get_preview_url()
            status = self.preview_manager.get_preview_status()
            
            url = process_url(url=url)
            
            response_data = {
                "url": url,
                "status": status.get('status', 'not_started'),
                "timestamp": status.get('timestamp'),
                "available": url is not None
            }
            
            preview_status = PreviewResponse(
                status=response_data["status"],
                url=url,
                metadata=status
            )
            
            if self.ws_client:
                self.ws_client.send_message("preview_status", response_data)
                
            if self.db:
                self.db[TASKS_COLLECTION_NAME].update_one(
                    {"_id": self.task_id},
                    {"$set": {
                        "preview_url": url,
                        "preview_status": preview_status.model_dump()
                    }}
                )
                
        except Exception as e:
            print(f"Error getting preview URL: {e}")
            self.ws_client.send_message("preview_error", {
                "message": f"Error getting preview URL: {str(e)}"
            })

    def _handle_restart_preview(self, input_data: Dict[str, Any]):
        """Initiate restart preview - already uses daemon thread internally"""
        if not self.preview_manager:
            self.ws_client.send_message("preview_error", {"message": "Preview manager not initialized"})
            return
            
        # Start restart in daemon thread
        self._restart_preview_thread(input_data)
        
        # Send immediate response
        self.ws_client.send_message("preview_restart_response", {
            "status": "starting",
            "message": "Preview restart initiated",
            "timestamp": generate_timestamp()
        })

    @run_in_daemon_thread
    def _restart_preview_thread(self, input_data: Dict[str, Any]):
        """Restart preview in background thread"""
        try:
            print(f"Starting preview restart in thread {threading.current_thread().name}")
            
            # Run the application restart
            result = self.preview_manager.run_application()
            
            if result:
                status_message = result.get('message', 'Preview restarted')
                
                self.ws_client.send_message("preview_restart_response", {
                    "status": "completed", 
                    "result": result,
                    "message": status_message,
                    "timestamp": generate_timestamp()
                })
                print("Preview restart completed successfully")
            else:
                self.ws_client.send_message("preview_error", {
                    "message": "Failed to restart preview"
                })
                print("Preview restart failed - no result")
                
        except Exception as e:
            print(f"Error in preview restart thread: {e}")
            self.ws_client.send_message("preview_error", {
                "message": f"Error restarting preview: {str(e)}"
            })
        finally:
            print(f"Preview restart thread {threading.current_thread().name} completed")

    @run_in_daemon_thread
    def _handle_restart_container(self, input_data: Dict[str, Any]):
        """Restart a container - runs in daemon thread"""
        try:
            if not self.preview_manager:
                self.ws_client.send_message("container_status", {
                    "status": "error", 
                    "message": "Preview manager not initialized",
                    "timestamp": generate_timestamp()
                })
                return
                
            container_name = input_data.get("container_name")
            status = self.preview_manager.run_container(container_name, restart=True)
            self.ws_client.send_message("container_status", {
                "status": "success",
                "container_status": status,
                "timestamp": generate_timestamp()
            })
            containers = self.preview_manager.containers
            if not containers:
                self.ws_client.send_message("containers", {
                    "status": "error", 
                    "message": "No containers found",
                    "timestamp": generate_timestamp()
                })
                return
            
            print(f"Found {len(containers)} containers for task {self.task_id}")
            print(f"Containers", containers)
            container_list = []
            for container in containers.values():
                container_dict = container.to_dict()
                container_dict['name'] = container.container_name
                container_dict['container_type'] = container.container_type
                container_dict['framework'] = container.framework
                container_list.append(container_dict)
            
            print("Containers", container_list)
            
            self.ws_client.send_message("containers", {
                "status": "success",
                "containers": container_list,
                "timestamp": generate_timestamp()
            })
            
        except Exception as e:
            print(f"Error restarting container: {e}")
            self.ws_client.send_message("container_status", {
                "status": "error", 
                "message": str(e),
                "timestamp": generate_timestamp()
            })
        
    @run_in_daemon_thread
    def _run_all_containers_thread(self, input_data: Dict[str, Any]):
        """Run all containers in background thread"""
        try:
            print(f"Starting run all containers in thread {threading.current_thread().name}")
            
            # Run the application restart
            result = self.preview_manager.run_all_containers()
            
            if result:
                status_message = "All containers started successfully"
                
                self.ws_client.send_message("run_all_containers_response", {
                    "status": "completed", 
                    "result": result,
                    "message": status_message,
                    "timestamp": generate_timestamp()
                })
                print("All containers started successfully")
            else:
                self.ws_client.send_message("preview_error", {
                    "message": "Failed to start all containers"
                })
                print("Failed to start all containers - no result")
                
        except Exception as e:
            print(f"Error in run all containers thread: {e}")
            self.ws_client.send_message("preview_error", {
                "message": f"Error running all containers: {str(e)}"
            })
        finally:
            print(f"Run all containers thread {threading.current_thread().name} completed")

    @run_in_daemon_thread
    def get_current_status(self) -> Dict[str, Any]:
        """Get comprehensive preview status for external queries - runs in daemon thread"""
        try:
            if not self.preview_manager:
                return {"status": "error", "message": "Preview manager not initialized"}
                
            status = self.preview_manager.get_preview_status()
            url = self.preview_manager.get_preview_url()
            url = process_url(url=url)
            
            result = {
                "status": status.get('status', 'unknown'),
                "url": url,
                "available": url is not None,
                "timestamp": status.get('timestamp'),
                "build_in_progress": getattr(self.preview_manager, 'build_in_progress', False),
                "framework": getattr(self.preview_manager, 'framework', 'unknown'),
                "platform": getattr(self.preview_manager, 'platform', 'unknown')
            }
            
            # Since this is async, we might want to send the result via websocket
            self.ws_client.send_message("preview_current_status", result)
            return result
            
        except Exception as e:
            print(f"Error getting current preview status: {e}")
            error_result = {"status": "error", "message": str(e)}
            self.ws_client.send_message("preview_current_status", error_result)
            return error_result