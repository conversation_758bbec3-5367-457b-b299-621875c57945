from app.core.function_schema_generator import get_function_schema
from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
from jinja2 import Environment, FileSystemLoader, TemplateNotFound
import json
from datetime import datetime
import numpy as np
from llm_wrapper.core.llm_interface import LLMInterface
from app.utils.logs_utils import get_path
from app.connection.establish_db_connection import get_vector_db
import json


class ComponentTestCaseGeneration(Discussion):
    def __init__(self, discussion_type, node_id=None, discussion_id=None, title=None, description=None):
        super().__init__(discussion_type, node_id, discussion_id, title, description, node_type="Architecture")
        self.template_name = "component_testcase_generation.prompt"
        self.function_schema_type = "TestCaseGenerationConfiguration"
        self.component_id = node_id  # Store the original Component ID
        self.vector_db = get_vector_db()  # Assuming this function returns an instance of VectorDbHandler

    async def async_initialize(self):
        await super().async_initialize()
        self.test_case_root = await self.get_or_create_test_case_root()
        self.test_case_root_id = self.test_case_root['id']  # Store TestCaseRoot id separately
        # Keep original node_id and node_type from initialization
        

    async def retrieve_info(self):
        """Retrieve information about the component and its context"""
        await super().retrieve_info()
        
        # Get component details
        component = await self.db.get_node_by_id(self.component_id)
        if not component:
            self.update_logger.error(f"Component {self.component_id} not found")
            return self.retrieved_info
            
        # Get component's parent container
        container_id = await self.db.get_parent_node(self.component_id)
        if container_id:
            container = await self.db.get_node_by_id(container_id)
            self.retrieved_info['container'] = container
            
        # Get all containers in the project for context
        project_id = await self.db.get_project_id(self.component_id)
        containers = await self.db.get_child_nodes(project_id, "Container")
        self.retrieved_info['containers'] = containers
        
        # Get system context
        system_context_id = await self.get_system_context_id(project_id)
        if system_context_id:
            system_context = await self.db.get_node_by_id(system_context_id)
            self.retrieved_info['system_context'] = system_context
            
        # Get external systems from system context
        external_systems = []
        if system_context:
            external_systems = await self.db.get_child_nodes(system_context_id, "ExternalSystem")
        self.retrieved_info['external_systems'] = external_systems
        
        # Get sibling components (components in the same container)
        sibling_components = []
        if container_id:
            sibling_components = await self.db.get_child_nodes(container_id, "Component")
            # Remove current component from siblings
            sibling_components = [c for c in sibling_components if c['id'] != self.component_id]
        self.retrieved_info['sibling_components'] = sibling_components
        
        # Get component's interfaces
        interfaces = await self.db.get_child_nodes(self.component_id, "Interface")
        self.retrieved_info['interfaces'] = interfaces
        
        # Get component's relationships
        relationships = await self.db.get_relationships_involving_node(self.component_id, relationship_type="INTERFACES_WITH")
        self.retrieved_info['relationships'] = relationships
        
        # Get component's properties
        self.retrieved_info['component'] = {
            'id': component['id'],
            'title': component['properties'].get('Title', ''),
            'description': component['properties'].get('Description', ''),
            'type': component['properties'].get('ComponentType', ''),
            'technology': component['properties'].get('Technology', ''),
            'interfaces': [i['properties'].get('Title', '') for i in interfaces],
            'relationships': [r['properties'] for r in relationships]
        }

        # Get user stories related to this component
        user_stories = await self.get_related_user_stories()
        self.retrieved_info['user_stories'] = user_stories
        
        return self.retrieved_info

    async def get_system_context_id(self, project_id):
        """Get the system context ID for the project"""
        system_context = await self.db.get_child_nodes(project_id, "SystemContext")
        if system_context:
            return system_context[0]['id']
        return None
            
    async def get_component_interfaces(self, component_id):
        """Get all interfaces for a component, including container-level interfaces"""
        # Get component's container
        container_id = await self.db.get_parent_node(component_id)
        if not container_id:
            return {'interface_nodes': [], 'interface_relationships': []}
            
        # Get Interface nodes at component level
        component_interface_nodes = await self.db.get_child_nodes(component_id, "Interface")
        
        # Get interface relationships at component level
        component_interface_relationships = await self.db.get_relationships_involving_node(
            component_id, 
            relationship_type="INTERFACES_WITH"
        )
        
        # Get container's interface relationships
        container_interface_relationships = await self.db.get_relationships_involving_node(
            container_id,
            relationship_type="INTERFACES_WITH"
        )
        
        interfaces = []
        
        # Process component-level interfaces
        for interface in component_interface_relationships:
            interface_info = {
                'source_id': interface['source'],
                'target_id': interface['target'],
                'properties': interface['properties'],
                'level': 'component'
            }
            
            # Get source node details if this component is target
            if interface['target'] == component_id:
                interface_info['source_node'] = await self.db.get_node_by_id(interface['source'])
                
            # Get target node details if this component is source    
            if interface['source'] == component_id:
                interface_info['target_node'] = await self.db.get_node_by_id(interface['target'])
                
            # Get interface node details if specified
            interface_node_id = interface['properties'].get('interface_node_id')
            if interface_node_id:
                interface_info['interface_node'] = await self.db.get_node_by_id(interface_node_id)
                
            interfaces.append(interface_info)
            
        # Process container-level interfaces
        for interface in container_interface_relationships:
            interface_info = {
                'source_id': interface['source'],
                'target_id': interface['target'],
                'properties': interface['properties'],
                'level': 'container'
            }
            
            # Get source container details if this container is target
            if interface['target'] == container_id:
                interface_info['source_container'] = await self.db.get_node_by_id(interface['source'])
                
            # Get target container details if this container is source    
            if interface['source'] == container_id:
                interface_info['target_container'] = await self.db.get_node_by_id(interface['target'])
                
            # Get interface node details if specified
            interface_node_id = interface['properties'].get('interface_node_id')
            if interface_node_id:
                interface_info['interface_node'] = await self.db.get_node_by_id(interface_node_id)
                
            interfaces.append(interface_info)
            
        return {
            'interface_nodes': component_interface_nodes,
            'interface_relationships': interfaces,
            'container_id': container_id
        }

    async def prefilter_and_rank_test_cases(self):
        prefiltered_cases = []  # Initialize prefiltered_cases at the beginning
        try:
            # Get common tags from TestCaseRoot
            common_tags = self.test_case_root['properties'].get('CommonTags', [])
            
            # If there are no common tags, this is the first iteration
            if not common_tags:
                self.update_logger.info("No common tags found. This appears to be the first iteration of test case generation.")
                return []

            # Get all test cases
            all_test_cases = await self.db.get_child_nodes(self.test_case_root_id, None)
            
            # If there are no test cases, return an empty list
            if not all_test_cases:
                self.update_logger.info("No existing test cases found.")
                return []

            # Extract key information from component, project, and parent
            component_info = f"{self.retrieved_info['component']['properties']['Title']} {self.retrieved_info['component']['properties']['Description']} {self.retrieved_info['component']['properties'].get('Functionality', '')}"
            project_info = f"{self.retrieved_info['project']['properties']['Title']} {self.retrieved_info['project']['properties']['Description']}"
            parent_info = f"{self.retrieved_info['parent_node']['properties']['Title']} {self.retrieved_info['parent_node']['properties']['Description']}"
            
            # Combine all information
            context_info = f"{component_info} {project_info} {parent_info}"
            
            # Use LLM to select relevant tags from common tags
            relevant_tags = await self.select_relevant_tags(context_info, common_tags)
            
            # Prefilter test cases based on tags
            prefiltered_cases = [tc for tc in all_test_cases if any(tag in tc['properties'].get('Tags', []) for tag in relevant_tags)]
            
            # If no test cases match the relevant tags, consider all test cases
            if not prefiltered_cases:
                self.update_logger.info("No test cases match the relevant tags. Considering all test cases.")
                prefiltered_cases = all_test_cases

            # Use VectorDbHandler to find similar nodes
            project_id = self.retrieved_info['project']['id']
            similar_node_ids = await self.vector_db.find_similar_nodes(
                self.component_id,
                context_info,
                project_id,
                max_results=10
            )

            # Fetch the similar nodes from the database
            similar_nodes = await self.db.get_nodes_by_ids(similar_node_ids)

            # Sort the prefiltered cases based on their presence in similar nodes
            ranked_cases = sorted(
                prefiltered_cases,
                key=lambda x: similar_node_ids.index(x['id']) if x['id'] in similar_node_ids else len(similar_node_ids)
            )

            return ranked_cases[:10]  # Return top 10 ranked cases

        except Exception as e:
            self.update_logger.error(f"Error in prefilter_and_rank_test_cases: {str(e)}")
            return prefiltered_cases[:10]  # Return top 10 prefiltered cases as fallback

    async def select_relevant_tags(self, test_case_info, common_tags):
        prompt = f"""
        Based on the following component information, select the most relevant tags from the given list of common tags. 
        Choose 2-5 tags that best describe test cases that would be appropriate for this component.

        Component Information:
        {test_case_info}

        Common Tags:
        {', '.join(common_tags)}

        Output the selected tags as a comma-separated list.
        """
        self.llm = LLMInterface(get_path(), 'component_testcase_generation', self.current_user, self.node_id, f"Discussion_{self.discussion_type}")
        messages = [{'role': 'user', 'content': prompt}]
        
        response = await self.llm.llm_interaction_wrapper(
            messages=messages,
            user_prompt=None,
            system_prompt=None,
            response_format={'type': 'text'},
            model=self.model_name,
            stream=False
        )
        
        tags = [tag.strip() for tag in response.split(',') if tag.strip() in common_tags]
        return tags
    
    def get_modifications_from_llm_output(self):
        # Use the standard test case types with additional properties
        self.modifications['child_node_types'] = [
            'FunctionalTestCase', 'NonFunctionalTestCase', 'StressTest', 
            'StabilityTest', 'InteroperabilityTest'
        ]
        return self.modifications

    async def merge_captured_items(self):
        """Merge captured items into the database"""
        # Store original node_id and node_type
        original_node_id = self.node_id
        original_node_type = self.node_type
        
        # Temporarily set node_id and node_type to TestCaseRoot for test case creation
        self.node_id = self.test_case_root_id
        self.node_type = "TestCaseRoot"
        
        # Call parent's merge_captured_items to create test cases under TestCaseRoot
        await super().merge_captured_items()
        
        # Restore original node_id and node_type
        self.node_id = original_node_id
        self.node_type = original_node_type
        
        # Use the nodes created by the parent class
        created_nodes = self.modifications.get('created_nodes', [])
        self.update_logger.info(f"Processing {len(created_nodes)} test cases created by parent class")
        
        # Process newly created test cases to establish relationships
        for test_case_node in created_nodes:
            # Get the test case properties
            test_case_props = test_case_node.get('properties', {})
            
            # Create appropriate relationships based on test level
            if test_case_props.get('TestLevel') == 'Unit':
                await self.db.create_relationship(
                    test_case_node['id'],
                    self.component_id,
                    "UNIT_TEST_FOR"
                )
            elif test_case_props.get('TestLevel') == 'System':
                # For system tests, create relationships with all involved components
                await self.db.create_relationship(
                    test_case_node['id'],
                    self.node_id,
                    "SYSTEM_TEST_INVOLVES"
                )
            else:  # Component level test
                await self.db.create_relationship(
                    test_case_node['id'],
                    self.component_id,
                    "COMPONENT_TEST_FOR"
                )

        # Update the Component node to indicate test cases have been configured
        # await self.db.update_node_by_id(self.component_id, {'testcases_state': 'configured'}, "Component")
        
        self.update_logger.info(f"Test case generation completed for Component {self.component_id}")

    async def get_or_create_test_case_root(self):
        project_id = await self.db.get_project_id(self.node_id)
        test_case_root = await self.db.get_child_nodes(project_id, "TestCaseRoot")
        
        if not test_case_root:
            properties = {
                "Title": "Test Cases",
                "Description": "Root node for all test cases in the project",
                "Type": "TestCaseRoot",
                "configuration_state": "not_configured"
            }
            test_case_root = await self.db.create_node(["TestCaseRoot"], properties, project_id)
        else:
            test_case_root = test_case_root[0]
        
        return test_case_root

    async def get_related_user_stories(self):
        """Get user stories that are related to this component"""
        try:
            # Get all requirement roots in the project
            project_id = await self.db.get_project_id(self.component_id)
            requirement_roots = await self.db.get_child_nodes(project_id, "RequirementRoot")
            if not requirement_roots:
                return []

            related_stories = []
            component_title = self.retrieved_info['component']['title'].lower()
            component_desc = self.retrieved_info['component']['description'].lower()

            for root in requirement_roots:
                # Get epics
                epics = await self.db.get_child_nodes(root['id'], "Epic")
                for epic in epics:
                    # Get user stories
                    stories = await self.db.get_child_nodes(epic['id'], "UserStory")
                    for story in stories:
                        # Check if component is mentioned in the story
                        story_title = story['properties'].get('Title', '').lower()
                        story_desc = story['properties'].get('Description', '').lower()
                        acceptance_criteria = story['properties'].get('AcceptanceCriteria', '').lower()
                        
                        if (component_title in story_title or 
                            component_title in story_desc or 
                            component_title in acceptance_criteria or 
                            component_desc in story_desc):
                            
                            # Get story details
                            story_info = {
                                'id': story['id'],
                                'title': story['properties'].get('Title', ''),
                                'description': story['properties'].get('Description', ''),
                                'acceptance_criteria': story['properties'].get('AcceptanceCriteria', ''),
                                'priority': story['properties'].get('Priority', 'Medium'),
                                'epic': {
                                    'id': epic['id'],
                                    'title': epic['properties'].get('Title', '')
                                }
                            }
                            
                            # Get story's relationships with other components
                            story_relationships = await self.db.get_relationships_involving_node(story['id'], relationship_type="IMPLEMENTS")
                            story_info['component_relationships'] = [
                                r for r in story_relationships 
                                if r['type'] in ['IMPLEMENTS', 'VERIFIES']
                            ]
                            
                            related_stories.append(story_info)

            return related_stories
        except Exception as e:
            self.update_logger.error(f"Error getting related user stories: {str(e)}")
            return []

# Register the new discussion type
DiscussionFactory.register('component_testcase_generation', ComponentTestCaseGeneration, "Architecture") 