from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
import json
import logging
import inspect
from app.discussions.tools.discussion_tool import DiscussionTools
from app.models.user_model import LLMModel
from app.utils.auth_utils import get_current_user
import os
from app.utils.prodefn.docs_tool import DocsTools
from app.utils.prodefn.docs_session_manager import get_or_create_session
import re
from app.utils.file_utils.upload_utils import upload_and_process,get_tenant_bucket
from app.connection.tenant_middleware import get_tenant_id
from app.utils.node_version_manager import NodeVersionManager
from datetime import datetime
from app.telemetry.logger_config import get_logger, set_task_id


logger = logging.getLogger(__name__)

base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
documentation_mapping_path = os.path.join(base_dir, 'discussions', 'types', 'documentation_mapping.json')

class DocumentationDiscussion(Discussion):
    def __init__(self, discussion_type, node_id=None, discussion_id=None, title=None, description=None):
        super().__init__(discussion_type, node_id, discussion_id, title, description, node_type="Documentation")
        self.template_name = "documentation_configuration.prompt"
        self.function_schema_type = "DocumentationConfiguration"
        self.version_manager = NodeVersionManager()
        self.background_info = None
        self.update_logger_agent = get_logger(__name__)

        # Load documentation mapping
        with open(documentation_mapping_path, 'r') as f:
            self.documentation_mapping = json.load(f)

    async def retrieve_info(self):
        """Retrieve context information for documentation nodes"""
        await super().retrieve_info()
        
        try:
            # Handle DocumentationRoot nodes
            if "DocumentationRoot" in self.node['labels']:
                # Get parent node and all its context
                parent_node = await self.db.get_parent_node(self.node_id)
                if parent_node:
                    self.retrieved_info['parent_node'] = parent_node
                    self.retrieved_info['node_context'] = await self.get_complete_node_context(parent_node['id'])

                # Get all sub-sections, ensuring order
                existing_sections = await self.db.get_child_nodes(self.node_id, "Sub_Section")
                self.retrieved_info['sections'] = sorted(
                    existing_sections,
                    key=lambda x: x['properties'].get('Order', 0)
                )
                
                # Get documentation type and mapping
                doc_type = self.node['properties'].get('DocumentationType')

                # Get documentation config
                doc_config = self.documentation_mapping[doc_type]

                node = await self.db.get_node_by_id(self.node_id)

                context = await self.get_context(doc_type, node)

                # doc_root = await self.get_or_create_doc_root(self.node_id, doc_type, context)

                # Create and configure sections immediately
                existing_sections = await self.db.get_child_nodes(self.node_id, "Sub_Section")
                existing_titles = {s['properties'].get('Title') for s in existing_sections}

                
                # Create any missing sections
                for section in doc_config['sections']:
                    if section['title'] not in existing_titles:
                        section_node = await self.db.create_node(
                            ["Documentation", "Sub_Section"],
                            {
                                "Title": section['title'],
                                "Type": "Sub_Section",
                                "SectionType": section['type'],
                                "Description": section['description'],
                                "Order": section.get('order', 0),
                                "Content": "",
                                "configuration_state": "not_configured"
                            },
                            self.node_id
                        )

                if doc_type in self.documentation_mapping:
                    if doc_type == "PRD":
                        project_context = json.loads(self.node['properties'].get('ProjectContext', '{}'))
                        self.retrieved_info.update({
                            'project_context': project_context,
                            'documentation_config': self.documentation_mapping[doc_type]
                        })
                    else:
                        self.retrieved_info['documentation_config'] = self.documentation_mapping[doc_type]

            # Handle Sub_Section nodes
            elif "Sub_Section" in self.node['labels']:
                # Get parent documentation root
                parent_doc = await self.db.get_parent_node(self.node_id)
                if parent_doc:
                    if parent_doc['properties'].get('DocumentationType') == "PRD":
                        project_context = json.loads(parent_doc['properties'].get('ProjectContext', '{}'))
                        self.retrieved_info['project_context'] = project_context
                        doc_type = parent_doc['properties'].get('DocumentationType')
                    else:
                        self.retrieved_info['documentation_root'] = parent_doc
                        doc_type = parent_doc['properties'].get('DocumentationType')
                    
                    # Get section config from mapping
                    if doc_type in self.documentation_mapping:
                        section_type = self.node['properties'].get('SectionType')
                        section_config = next(
                            (section for section in self.documentation_mapping[doc_type]['sections'] 
                             if section['type'] == section_type),
                            None
                        )
                        self.retrieved_info['section_config'] = section_config
                    
                    # Get original parent node and all its context
                    original_parent_node = await self.db.get_parent_node(parent_doc['id'])
                    if original_parent_node:
                        parent_context = await self.get_complete_node_context(original_parent_node['id'])
                        self.retrieved_info['parent_context'] = parent_context

                    # Get sibling sections in order
                    sibling_sections = await self.db.get_sibling_nodes(self.node_id, "Sub_Section")
                    self.retrieved_info['sibling_sections'] = sorted(
                        sibling_sections,
                        key=lambda x: x['properties'].get('Order', 0)
                    )
                 # Get project node and details
            project_node = self.retrieved_info['root_node']
            self.project_id = project_node['id']

            existing_session = await get_or_create_session(self.current_user, self.project_id)
            self.doc_session_id = existing_session.get("session_id")

            # Retrieve existing containers
            containers = await self.db.get_child_nodes(self.node_id, "Container")
            self.retrieved_info['containers'] = containers

            architecture_root = await self.db.get_child_nodes(self.retrieved_info['root_node']['id'], "ArchitectureRoot")
            if architecture_root:
                arch_req_node = await self.db.get_child_nodes(architecture_root[0]['id'], "ArchitecturalRequirement")
                if arch_req_node:
                    # Get full requirement nodes
                    functional_reqs = await self.db.get_child_nodes(arch_req_node[0]['id'], "FunctionalRequirement")
                    nonfunctional_reqs = await self.db.get_child_nodes(arch_req_node[0]['id'], "NonFunctionalRequirement")
                    
                    architectural_reqs = {
                        'functional_requirements': functional_reqs,
                        'architectural_requirements': nonfunctional_reqs
                    }

            # Retrieve existing external systems and users
            system_context = self.retrieved_info['current_node']
            # Retrieve existing external systems and users
            system_context = self.retrieved_info['current_node']
            self.retrieved_info['external_systems'] = system_context['properties'].get('ExternalSystems', '')
            self.retrieved_info['users'] = system_context['properties'].get('Users', '')

            design_node = await self.db.get_node_by_id(self.node_id)
            algorithms = design_node['properties'].get('Algorithms', '')
            class_diagrams = await self.db.get_child_nodes(self.node_id, "ClassDiagram") or None
            state_diagrams = await self.db.get_child_nodes(self.node_id, "StateDiagram") or None
            sequence_diagrams = await self.db.get_child_nodes(self.node_id, "SequenceDiagram") or None
            test_cases = await self.db.get_child_nodes(self.node_id, "Test") or None

            # Retrieve existing containers
            containers = await self.db.get_child_nodes(system_context['id'], "Container")
            parent_node = await self.db.get_parent_node(self.node_id)
            components = await self.db.get_child_nodes(parent_node['id'], "Component")

            container_component_details = []
            container_component_details.append({
                'container': self.node_id,
                'components': components
            })

            # Structure current background info with complete node information
            current_background_info = {
                'project_context': {
                    'node_id': project_node['id'] if project_node else None,
                    'properties': project_node['properties'] if project_node else {}
                },
                'system_context': {
                    'users': self.retrieved_info['users'],
                    'external_systems': self.retrieved_info['external_systems'],
                    'container': parent_node,  # Already storing full container nodes
                    'container_component_details': container_component_details
                },
                'requirements_context': {
                    'functional_requirements': architectural_reqs['functional_requirements'],
                    'architectural_requirements': architectural_reqs['architectural_requirements'],
                    'parent_node': arch_req_node[0] if arch_req_node else None  # Store parent node for context
                },
                'design_context': {
                    'algorithms': algorithms,
                    'state_diagrams': state_diagrams,
                    'sequence_diagrams': sequence_diagrams,
                    'class_diagrams': class_diagrams,   
                    'test_cases': test_cases

                }
            }

            # Handle reconfiguration
            if self.is_reconfig():
                self.model_name = LLMModel.gpt_4_1.value
                stored_background_info = await self.version_manager._get_context(self.node_id)
                
                if stored_background_info:
                    self.retrieved_info.update({
                        'original_node': self.node,
                        'background_info': stored_background_info.get('background_info', current_background_info),
                        'user_interaction': {
                            'input': self.node['properties'].get('user_inputs', '[]')
                        },
                        'change_log': self.node['properties'].get('change_log', []),
                        'change_reason': self.node['properties'].get('change_reason', '')
                    })
                    # Add safety check
                    if not self.retrieved_info['background_info']:
                        self.retrieved_info['background_info'] = current_background_info
                else:
                    self.retrieved_info.update({
                        'original_node': self.node,
                        'background_info': current_background_info,
                        'user_interaction': {
                            'input': self.node['properties'].get('user_inputs', '[]')
                        },
                        'change_log': self.node['properties'].get('change_log', []),
                        'change_reason': self.node['properties'].get('change_reason', '')
                    })
                
                self.retrieved_info['new_background'] = current_background_info
            else:
                # For initial configuration
                self.retrieved_info['background_info'] = current_background_info
                self.background_info = current_background_info  # Set class variable
                self.retrieved_info['new_background'] = current_background_info

            return self.retrieved_info
 
        except Exception as e:
            logger.error(f"Error in retrieve_info: {str(e)}")
            raise

    async def get_context(self, doc_type, target_node):
        """Gather context based on documentation type"""
        try:
            context = {
                'target_node': {
                    'id': target_node,
                    'properties': target_node['properties']
                }
            }

            if doc_type == "PRD":
                # Get epics for PRD
                epics = await self.db.get_descendant_nodes(self.project_id, ["Epic"], max_depth=3)
                context['epics'] = [{
                    'id': epic['id'],
                    'title': epic['properties'].get('Title'),
                    'description': epic['properties'].get('Description')
                } for epic in epics]

            elif doc_type == "SAD":
                # Get architecture components
                components = await self.db.get_descendant_nodes(
                    target_node['id'], 
                    ["Architecture"], 
                    max_depth=4
                )
                context['components'] = components

            return context

        except Exception as e:
            self.update_logger_agent.error(f"Error gathering context: {str(e)}")
            raise

    # temp bug fix , later this should remove
    async def get_child_section_content(self, node_id):
        """Get content from child nodes of a Sub_Section."""
        try:
            # Get all child nodes except Discussion nodes
            query = """
            MATCH (parent)-[:HAS_CHILD]->(child)
            WHERE ID(parent) = $node_id 
            AND NOT 'Discussion' IN labels(child)
            RETURN child
            """
            results = await self.db.async_run(query, node_id=node_id)
            
            # Collect all content
            contents = []
            for record in results[0]:
                child_node = record['child']
                if 'Content' in child_node['properties']:
                    content = child_node['properties']['Content']
                    if content and content.strip():
                        contents.append(content)
            
            return "\n\n".join(contents) if contents else None

        except Exception as e:
            logger.error(f"Error getting child section content: {str(e)}")
            return None

    async def get_complete_node_context(self, node_id):
        """
        Gather complete context for a node including its properties, relationships,
        child nodes, and any other relevant information.
        """
        try:
            context = {}
            
            # Get the node itself with all properties
            node = await self.db.get_node_by_id(node_id)
            if not node:
                return context
            
            context['properties'] = node['properties']
            context['labels'] = node['labels']
            
            # Get all child nodes (regardless of type)
            child_nodes = await self.db.get_child_nodes(node_id)
            context['child_nodes'] = child_nodes
            
            # Get all relationships
            relationships = await self.db.get_relationships_involving_node(node_id, None)
            context['relationships'] = relationships
            
            # For each relationship, get the connected node's details
            connected_nodes = {}
            for rel in relationships:
                if rel['source'] == node_id and rel['target'] not in connected_nodes:
                    connected_node = await self.db.get_node_by_id(rel['target'])
                    if connected_node:
                        connected_nodes[rel['target']] = connected_node
                elif rel['target'] == node_id and rel['source'] not in connected_nodes:
                    connected_node = await self.db.get_node_by_id(rel['source'])
                    if connected_node:
                        connected_nodes[rel['source']] = connected_node
            
            context['connected_nodes'] = connected_nodes
            
            # Group child nodes by their labels for easier access
            grouped_children = {}
            for child in child_nodes:
                for label in child['labels']:
                    if label not in grouped_children:
                        grouped_children[label] = []
                    grouped_children[label].append(child)
            
            context['grouped_children'] = grouped_children
            
            return context

        except Exception as e:
            logger.error(f"Error in get_complete_node_context: {str(e)}")
            raise

    async def get_modifications_from_llm_output(self):
        """Processes the output from LLM into modifications"""
        try:
            if 'modified_node' not in self.modifications:
                self.modifications['modified_node'] = {}
            
            if "DocumentationRoot" in self.node['labels']:
                self.modifications['modified_node'].update({
                    'Type': 'DocumentationRoot',
                    'configuration_state': 'configured'
                })

            elif "Sub_Section" in self.node['labels']:
                # First try to get content from child nodes
                # child_content = await self.get_child_section_content(self.node_id)
                child_node = self.modifications
                child_node_details = child_node.get('new_child_nodes', [])
                child_node_content = child_node_details[0]['Content']
                
                # If no child content, get content from discussion
                # if not child_content:
                #     child_content = self.extract_content_from_discussion()
                
                if not child_node_content:
                    raise ValueError(f"No valid content found for section {self.node_id}")

                self.modifications['modified_node'].update({
                    'Type': 'Sub_Section',
                    'Content': child_node_content,
                    'configuration_state': 'configured'
                })

            # Ensure no new child nodes are created
            if 'new_child_nodes' in self.modifications:
                self.modifications['new_child_nodes'] = []

            # Ensure no child node types are specified
            if 'child_node_types' in self.modifications:
                self.modifications['child_node_types'] = []

            return self.modifications

        except Exception as e:
            self.update_logger_agent.error(f"Error in get_modifications_from_llm_output: {str(e)}")
            raise

    def extract_content_from_discussion(self):
        """Extract meaningful content from discussion messages, focusing on just the actual content"""
        try:
            if not self.discussion_so_far:
                return None

            # Get assistant messages
            for msg in reversed(self.discussion_so_far):  # Start from most recent
                if msg['role'] != 'assistant':
                    continue
                    
                content = msg.get('content')
                if not isinstance(content, str):
                    continue
                    
                # Try to parse as JSON if it starts with {
                if content.strip().startswith('{'):
                    try:
                        json_content = json.loads(content)
                        # Look for content in modifications structure
                        if 'modifications' in json_content:
                            for mod in json_content['modifications']:
                                if 'modified_node' in mod and 'Content' in mod['modified_node']:
                                    return mod['modified_node']['Content']
                    except:
                        pass
                
                # If the message starts with "LLM response:", extract the actual content
                if content.startswith("LLM response:"):
                    try:
                        # Try to parse the JSON after "LLM response:"
                        json_str = content.replace("LLM response:", "").strip()
                        json_content = json.loads(json_str)
                        
                        # If it's in the 'content' structure, extract it
                        if isinstance(json_content, dict):
                            if 'content' in json_content:
                                json_content = json_content['content']
                            
                            if 'modifications' in json_content:
                                for mod in json_content['modifications']:
                                    if 'modified_node' in mod and 'Content' in mod['modified_node']:
                                        return mod['modified_node']['Content']
                                    elif 'Content' in mod:
                                        return mod['Content']
                    except:
                        pass

                # For regular text content, filter out common non-content responses
                if (not content.startswith(("I can help", "Please provide", "If you", "Would you"))
                    and len(content.strip()) > 50
                    and not content.strip().startswith('{')):
                    return content.strip()

            return None

        except Exception as e:
            self.update_logger_agent.error(f"Error extracting content: {str(e)}")
            return None
        
    async def finalize(self):
        """Finalizes the discussion and updates the current node with discussion details"""
        try:
            # Get the discussion node and its details
            discussion_node = await self.db.get_node_by_id(self.discussion_id)
            if not discussion_node:
                self.update_logger.error(f"Discussion node {self.discussion_id} not found")
                return False

            # Extract relevant information from discussion
            formatted_output = discussion_node['properties'].get('formatted_output_text', '')
            
            # Clean up the content using regex to remove "LLM response:" prefix
            formatted_output = re.sub(r'^LLM response:\s*', '', formatted_output.strip())
            # Also clean up any extra newlines at the start
            formatted_output = re.sub(r'^\n+', '', formatted_output)

            discussion_details = {
                'formatted_output': formatted_output,
                'modifications': json.loads(discussion_node['properties'].get('modifications', '{}')),
                'status': 'configured'  # Mark as configured after finalization
            }

            # Update the current node with discussion outcomes
            node_updates = {
                'configuration_state': 'configured',
                'last_discussion_id': self.discussion_id,
                'last_discussion_date': discussion_node['properties'].get('updated_at'),
                'Content': formatted_output  # Use the cleaned content
            }

            # If there are specific modifications from the discussion, add them
            if 'modified_node' in discussion_details['modifications']:
                node_updates.update(discussion_details['modifications']['modified_node'])

            # Update the current node
            await self.db.update_node_by_id(self.node_id, node_updates)

            # Mark the discussion as finalized
            await super().finalize()
            
            self.update_logger.info(f"Successfully finalized discussion {self.discussion_id} for node {self.node_id}")
            return True

        except Exception as e:
            self.update_logger.error(f"Error finalizing discussion: {str(e)}")
            raise


    def is_reconfig(self):
        """Check if this is a reconfiguration scenario."""
        config_state = self.get_config_state(self.discussion_type, self.node_type, self.node)
        return config_state == 'configured'
    
# Register the discussion types
DiscussionFactory.register('configuration', DocumentationDiscussion, "DocumentationRoot")
DiscussionFactory.register('configuration', DocumentationDiscussion, "Sub_Section")