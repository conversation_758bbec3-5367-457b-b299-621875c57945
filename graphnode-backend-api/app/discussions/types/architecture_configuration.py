from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
from jinja2 import Environment, FileSystemLoader, TemplateNotFound
import json
from app.core.function_schema_generator import get_function_schema
from app.discussions.tools.discussion_tool import DiscussionTools
from app.models.user_model import LLMModel
from app.utils.auth_utils import get_current_user
import os
from app.utils.prodefn.docs_tool import DocsTools
import inspect
from app.utils.prodefn.docs_session_manager import get_or_create_session
from app.utils.file_utils.upload_utils import upload_and_process,get_tenant_bucket
from app.connection.tenant_middleware import get_tenant_id
from app.utils.node_version_manager import NodeVersionManager
from datetime import datetime
from app.utils.datetime_utils import generate_timestamp
class ArchitectureConfiguration(Discussion):
    def __init__(self, discussion_type, node_id=None, discussion_id=None, title=None, description=None):
        super().__init__(discussion_type, node_id, discussion_id, title, description, node_type="Architecture")
        self.template_name = "architecture_configuration.prompt"
        self.function_schema_type = "ArchitectureConfiguration"
        self.project_id = None
        self.doc_tools = None
        self.discussion_tools = None
        self.function_mapping = {}
        self.function_executor = None
        self.function_schemas = []
        self.schemas_initialized = False
        self.project_knowledge = False
        # self.model_name = 'claude-3-5-sonnet-20241022'
        self.version_manager = NodeVersionManager()
        self.background_info = None

    async def async_initialize(self):
        
        # Initialization specific to this configuration
        await super().async_initialize()
        # Additional async setup specific to this class can be added here

    
    async def retrieve_info(self):
        await super().retrieve_info()
        
        # Get project node and details (keeping existing code)
        project_node = self.retrieved_info['root_node']
        self.retrieved_info['project_details'] = {
            'description': project_node['properties'].get('Description', ''),
            'scope': project_node['properties'].get('Scope', ''),
            'architecture_strategy': project_node['properties'].get('ArchitectureStrategy', ''),
            'additional_details': project_node['properties'].get('AdditionalDetails', {}),
            'team_composition': project_node['properties'].get('TeamComposition', '')
        }
        self.retrieved_info['architecture_pattern'] = project_node['properties'].get('ArchitecturePattern', 'adaptive')

        # Get parent container information
        parent_container = await self.db.get_parent_node(self.node_id)
        if parent_container:
            self.retrieved_info['container'] = {
                'id': parent_container['id'],
                'title': parent_container['properties'].get('Title', ''),
                'description': parent_container['properties'].get('Description', ''),
                'tech_stack': parent_container['properties'].get('Selected_Tech_Stack', ''), 
                'Platform': parent_container['properties'].get('Selected_Platform', ''),  
                'user_interactions': parent_container['properties'].get('UserInteractions', ''),
                'external_system_interactions': parent_container['properties'].get('ExternalSystemInteractions', '')
            }
            
            # Add: Fetch parent container's USES relationships
            uses_relationships = await self.db.get_relationships_involving_node(
                parent_container['id'], 
                "USES"
            )
            
            # Format USES relationships for LLM consumption
            self.retrieved_info['parent_uses_relationships'] = [
                {
                    'target': rel['target'],
                    'name': rel['properties'].get('name'),
                    'type': rel['properties'].get('type'),
                    'description': rel['properties'].get('description'),
                    'technology': rel['properties'].get('technology')
                }
                for rel in uses_relationships
            ]

         # Get project ID from retrieved_info
        project_id = self.retrieved_info['root_node']['id']
        # Get architecture root
        arch_roots = await self.db.get_child_nodes(project_id, "ArchitectureRoot")
        if arch_roots:
            arch_root_id = arch_roots[0]['id']
            architecture_node = self.retrieved_info['current_node']['id']
            
            # Get both requirements and arch info
            requirements = await self.get_architectural_requirements(arch_root_id)
            arch_info = await self.get_architectural_node_info(arch_root_id, architecture_node)
            
            self.retrieved_info.update(requirements)
            self.retrieved_info.update(arch_info)
        
        # Retrieve existing external systems and users
        system_context = self.retrieved_info['current_node']
        self.retrieved_info['external_systems'] = system_context['properties'].get('ExternalSystems', '')
        self.retrieved_info['users'] = system_context['properties'].get('Users', '')

        # Get project node and details
        project_node = self.retrieved_info['root_node']
        self.project_id = project_node['id']

        existing_session = await get_or_create_session(self.current_user, self.project_id)
        self.doc_session_id = existing_session.get("session_id")

        print(self.doc_session_id)
        
        # Store architectural requirements in a consistent location
        if 'functional_requirements' in self.retrieved_info and 'architectural_requirements' in self.retrieved_info:
            architectural_reqs = {
                'functional_requirements': self.retrieved_info['functional_requirements'],
                'architectural_requirements': self.retrieved_info['architectural_requirements']
            }
            self.retrieved_info['architectural_reqs'] = architectural_reqs
        
        # Retrieve existing containers
        containers = await self.db.get_child_nodes(system_context['id'], "Container") or None
        parent_node = await self.db.get_parent_node(self.node_id)
        components = await self.db.get_child_nodes(parent_node['id'], "Component") or None

        container_component_details = []
        container_component_details.append({
            'container': self.node_id,
            'components': components
        })

        # Structure current background info with complete node information
        current_background_info = {
            'project_context': {
                'node_id': project_node['id'] if project_node else None,
                'properties': project_node['properties'] if project_node else {}
            },
            'system_context': {
                'users': self.retrieved_info['users'],
                'external_systems': self.retrieved_info['external_systems'],
                'container': parent_node['properties'] if project_node else {},  # Already storing full container nodes
                'container_component_details': container_component_details
            },
            'requirements_context': {
                'functional_requirements': self.retrieved_info.get('functional_requirements', []),
                'architectural_requirements': self.retrieved_info.get('architectural_requirements', []),
                'parent_node': None  # Will be set if needed
            }
        }

        # Find the architectural requirement parent node if available
        architecture_root = await self.db.get_child_nodes(self.retrieved_info['root_node']['id'], "ArchitectureRoot")
        if architecture_root:
            arch_req_nodes = await self.db.get_child_nodes(architecture_root[0]['id'], "ArchitecturalRequirement")
            if arch_req_nodes:
                current_background_info['requirements_context']['parent_node'] = arch_req_nodes[0]

        # Handle reconfiguration
        if self.is_reconfig():
            self.model_name = LLMModel.gpt_4_1.value
            stored_background_info = await self.version_manager._get_context(self.node_id)
            
            if stored_background_info:
                self.retrieved_info.update({
                    'original_node': self.node,
                    'background_info': stored_background_info.get('background_info', current_background_info),
                    'user_interaction': {
                        'input': self.node['properties'].get('user_inputs', '[]')
                    },
                    'change_log': self.node['properties'].get('change_log', []),
                    'change_reason': self.node['properties'].get('change_reason', '')
                })
                # Add safety check
                if not self.retrieved_info['background_info']:
                    self.retrieved_info['background_info'] = current_background_info
            else:
                self.retrieved_info.update({
                    'original_node': self.node,
                    'background_info': current_background_info,
                    'user_interaction': {
                        'input': self.node['properties'].get('user_inputs', '[]')
                    },
                    'change_log': self.node['properties'].get('change_log', []),
                    'change_reason': self.node['properties'].get('change_reason', '')
                })
            
            self.retrieved_info['new_background'] = current_background_info
        else:
            # For initial configuration
            self.retrieved_info['background_info'] = current_background_info
            self.background_info = current_background_info  # Set class variable
            self.retrieved_info['new_background'] = current_background_info

        return self.retrieved_info
    
    def get_modifications_from_llm_output(self):
        
        if "ArchitectureRoot" in self.node['labels']:
            self.modifications['child_node_types'] = ['Architecture', 'Component']
        if "Component" in self.node['labels']:
            self.modifications['child_node_types'] = ['Architecture', 'SubComponent']
            self.modifications['modified_node']['Type'] = 'Component'
        if "SubComponent" in self.node['labels']:
            self.modifications['modified_node']['Type'] = 'SubComponent'

        return self.modifications
    
    async def merge_captured_items(self):
        if not self.modifications.get('modified_node'):
            self.update_logger.warning("No modifications to merge")
            return
         # Get background info by calling retrieve_info again
        await self.retrieve_info()
        try:
            if self.is_reconfig():
                if self.modifications['modified_node'].get('changes_needed', False):
                    await super().merge_captured_items()
                    
                    modified_node = self.modifications.get('modified_node', {})
                    
                    # Create design node for leaf components (keeping existing code)
                    existing_design = await self.db.get_child_nodes(self.node_id, "Design")
                    if not existing_design:
                        properties = {
                            "Title": f"Design for {self.node['properties']['Title']}",
                            "Type": "Design"
                        }
                        await self.db.create_node(["Design"], properties, self.node_id)
                        self.update_logger.info(f"Created Design node for leaf component {self.node_id}")
                    
                    # Add: Handle new USES relationships
                    new_relationships = self.modifications.get('new_relationships', [])
                    if new_relationships:
                        for relationship in new_relationships:
                            if relationship.get('type') == 'USES':
                                # Create relationship as specified by LLM
                                await self.db.create_relationship(
                                    self.node_id,  # Design node as consumer
                                    relationship['target'],  # External container
                                    'USES',
                                    {
                                        'name': relationship.get('name'),
                                        'type': relationship.get('type'),
                                        'description': relationship.get('description'),
                                        'technology': relationship.get('technology')
                                    }
                                )
                                self.update_logger.info(
                                    f"Created USES relationship to external container {relationship['target']}"
                                )

                    
                    # Get interfaces from modifications
                    interfaces = self.modifications.get('new_relationships', [])
                    created_nodes = self.modifications.get('created_nodes', [])

                    # Track components and their interfaces
                    components_to_update = set()
                    interface_mappings = {}

                    if interfaces:
                        # First pass: Collect all relationships
                        for interface in interfaces:
                            if interface.get('type') == 'interfacesWith':
                                source_id = self.get_node_id(interface['source'], created_nodes)
                                target_id = self.get_node_id(interface['target'], created_nodes)
                                
                                if source_id and target_id:
                                    # Add target to components that need interface nodes
                                    components_to_update.add(target_id)
                                    if target_id not in interface_mappings:
                                        interface_mappings[target_id] = []
                                    interface_mappings[target_id].append({
                                        'source_id': source_id,
                                        'interface': interface
                                    })
                                # We need to use 'ID' field to find the source and destination nodes
                                if '-' in interface['source']:
                                    source_prefix, source_num = interface['source'].split('-', 1)
                                else:
                                    self.update_logger.info("Skipping interface {} between {} and {}".format(interface['name'], interface['source'], interface['target']))
                                    continue
                                if '-' in interface['target']:
                                    target_prefix, target_num = interface['target'].split('-', 1)
                                else:
                                    self.update_logger.info("Skipping interface {} between {} and {}".format(interface['name'], interface['source'], interface['target']))
                                    continue
                                # Determine if the source is new or existing
                                if source_prefix == 'NEW':
                                    for node in created_nodes:
                                        if node['properties']['ID'] == interface['source']:
                                            source_id = node['id']
                                            break
                                elif source_prefix == 'EXISTING':
                                    # convert the source_num to integer and assign to source_id
                                    source_id = int(source_num)
                                # Determine if the target is new or existing
                                if target_prefix == 'NEW':
                                    for node in created_nodes:
                                        if node['properties']['ID'] == interface['target']:
                                            target_id = node['id']
                                            break
                                elif target_prefix == 'EXISTING':
                                    target_id = int(target_num)
                                interface_properties = {
                                        'Title': interface['name'],
                                        'Type': interface['type'],
                                        'Description': interface['description']
                                    }
                                
                                await self.db.create_relationship(source_id, target_id, 'INTERFACES_WITH', interface_properties)
                            
                        # Second pass: Process each component's interfaces
                        for component_id in components_to_update:
                            # Get or create interface node
                            interface_nodes = await self.db.get_child_nodes(component_id, "Interface")
                            if not interface_nodes:
                                node = await self.db.get_node_by_id(component_id)
                                interface_node = await self.db.create_node(
                                    ["Interface"],
                                    {
                                        "Title": f"Interfaces for {node['properties'].get('Title', 'Component')}",
                                        "incoming_interfaces": "[]"
                                    },
                                    component_id
                                )
                            else:
                                interface_node = interface_nodes[0]

                            # Update the interfaces
                            existing_interfaces = json.loads(
                                interface_node['properties'].get('incoming_interfaces', '[]')
                            )
                            
                            # Add new interfaces
                            for interface_info in interface_mappings[component_id]:
                                source_id = interface_info['source_id']
                                interface = interface_info['interface']
                                
                                new_interface = {
                                    'name': interface['name'],
                                    'type': interface['type'],
                                    'description': interface['description'],
                                    'source_component_id': source_id
                                }
                                existing_interfaces.append(new_interface)

                            # Update the interface node
                            await self.db.update_node_by_id(
                                interface_node['id'],
                                {"incoming_interfaces": json.dumps(existing_interfaces)}
                            )
                        #Remove the ID field.
                        for node in created_nodes:
                            try:
                                # Using Neo4j REMOVE clause to delete the property
                                query = """
                                MATCH (n)
                                WHERE ID(n) = $node_id
                                REMOVE n.ID
                                """
                                await self.db.async_run(query, node_id=node['id'])
                                self.update_logger.info(f"Removed ID field from node {node['id']}")
                            except Exception as e:
                                self.update_logger.error(f"Failed to remove ID from node {node['id']}: {str(e)}")

                        # Handle IMPLEMENTS relationships
                    await self.handle_implements_relationships()

                    # Save version info
                    save_data = {
                        'node_id': self.node_id,
                        'properties': self.modifications['modified_node'],
                        'background_info': self.retrieved_info['new_background'],
                        'user_interaction': {
                            'timestamp': generate_timestamp(),
                            'action': 'reconfig',
                            'input': self.modifications['modified_node'].get('user_inputs', ''),
                            'reason': self.modifications['modified_node'].get('change_reason', '')
                        }
                    }
                    await self.version_manager.save_node_info(save_data)
        
            else:
                await super().merge_captured_items()
                
                modified_node = self.modifications.get('modified_node', {})
                
                # Create design node for leaf components (keeping existing code)
                existing_design = await self.db.get_child_nodes(self.node_id, "Design")
                if not existing_design:
                    properties = {
                        "Title": f"Design for {self.node['properties']['Title']}",
                        "Type": "Design"
                    }
                    await self.db.create_node(["Design"], properties, self.node_id)
                    self.update_logger.info(f"Created Design node for leaf component {self.node_id}")
                
                # Add: Handle new USES relationships
                new_relationships = self.modifications.get('new_relationships', [])
                if new_relationships:
                    for relationship in new_relationships:
                        if relationship.get('type') == 'USES':
                            # Create relationship as specified by LLM
                            await self.db.create_relationship(
                                self.node_id,  # Design node as consumer
                                relationship['target'],  # External container
                                'USES',
                                {
                                    'name': relationship.get('name'),
                                    'type': relationship.get('type'),
                                    'description': relationship.get('description'),
                                    'technology': relationship.get('technology')
                                }
                            )
                            self.update_logger.info(
                                f"Created USES relationship to external container {relationship['target']}"
                            )

                
                # Get interfaces from modifications
                interfaces = self.modifications.get('new_relationships', [])
                created_nodes = self.modifications.get('created_nodes', [])

                # Track components and their interfaces
                components_to_update = set()
                interface_mappings = {}

                if interfaces:
                    # First pass: Collect all relationships
                    for interface in interfaces:
                        if interface.get('type') == 'interfacesWith':
                            source_id = self.get_node_id(interface['source'], created_nodes)
                            target_id = self.get_node_id(interface['target'], created_nodes)
                            
                            if source_id and target_id:
                                # Add target to components that need interface nodes
                                components_to_update.add(target_id)
                                if target_id not in interface_mappings:
                                    interface_mappings[target_id] = []
                                interface_mappings[target_id].append({
                                    'source_id': source_id,
                                    'interface': interface
                                })
                            # We need to use 'ID' field to find the source and destination nodes
                            if '-' in interface['source']:
                                source_prefix, source_num = interface['source'].split('-', 1)
                            else:
                                self.update_logger.info("Skipping interface {} between {} and {}".format(interface['name'], interface['source'], interface['target']))
                                continue
                            if '-' in interface['target']:
                                target_prefix, target_num = interface['target'].split('-', 1)
                            else:
                                self.update_logger.info("Skipping interface {} between {} and {}".format(interface['name'], interface['source'], interface['target']))
                                continue
                            # Determine if the source is new or existing
                            if source_prefix == 'NEW':
                                for node in created_nodes:
                                    if node['properties']['ID'] == interface['source']:
                                        source_id = node['id']
                                        break
                            elif source_prefix == 'EXISTING':
                                # convert the source_num to integer and assign to source_id
                                source_id = int(source_num)
                            # Determine if the target is new or existing
                            if target_prefix == 'NEW':
                                for node in created_nodes:
                                    if node['properties']['ID'] == interface['target']:
                                        target_id = node['id']
                                        break
                            elif target_prefix == 'EXISTING':
                                target_id = int(target_num)
                            interface_properties = {
                                    'Title': interface['name'],
                                    'Type': interface['type'],
                                    'Description': interface['description']
                                }
                            
                            await self.db.create_relationship(source_id, target_id, 'INTERFACES_WITH', interface_properties)
                        
                    # Second pass: Process each component's interfaces
                    for component_id in components_to_update:
                        # Get or create interface node
                        interface_nodes = await self.db.get_child_nodes(component_id, "Interface")
                        if not interface_nodes:
                            node = await self.db.get_node_by_id(component_id)
                            interface_node = await self.db.create_node(
                                ["Interface"],
                                {
                                    "Title": f"Interfaces for {node['properties'].get('Title', 'Component')}",
                                    "incoming_interfaces": "[]"
                                },
                                component_id
                            )
                        else:
                            interface_node = interface_nodes[0]

                        # Update the interfaces
                        existing_interfaces = json.loads(
                            interface_node['properties'].get('incoming_interfaces', '[]')
                        )
                        
                        # Add new interfaces
                        for interface_info in interface_mappings[component_id]:
                            source_id = interface_info['source_id']
                            interface = interface_info['interface']
                            
                            new_interface = {
                                'name': interface['name'],
                                'type': interface['type'],
                                'description': interface['description'],
                                'source_component_id': source_id
                            }
                            existing_interfaces.append(new_interface)

                        # Update the interface node
                        await self.db.update_node_by_id(
                            interface_node['id'],
                            {"incoming_interfaces": json.dumps(existing_interfaces)}
                        )
                    #Remove the ID field.
                    for node in created_nodes:
                        try:
                            # Using Neo4j REMOVE clause to delete the property
                            query = """
                            MATCH (n)
                            WHERE ID(n) = $node_id
                            REMOVE n.ID
                            """
                            await self.db.async_run(query, node_id=node['id'])
                            self.update_logger.info(f"Removed ID field from node {node['id']}")
                        except Exception as e:
                            self.update_logger.error(f"Failed to remove ID from node {node['id']}: {str(e)}")

                    # Handle IMPLEMENTS relationships
                    await self.handle_implements_relationships()

                    # Save version info
                    save_data = {
                        'node_id': self.node_id,
                        'properties': self.modifications['modified_node'],
                        'background_info': self.retrieved_info['new_background'],
                        'user_interaction': {
                            'timestamp': generate_timestamp(),
                            'action': 'reconfig',
                            'input': self.modifications['modified_node'].get('user_inputs', ''),
                            'reason': self.modifications['modified_node'].get('change_reason', '')
                        }
                    }
                    await self.version_manager.save_node_info(save_data)
        except Exception as e:
            self.update_logger.error(f"Error saving changes to node {self.node_id}: {str(e)}")
            
    async def handle_implements_relationships(self):
        """
        Handles the creation of IMPLEMENTS relationships between a Component/Container and Requirements.
        Expects ImplementedRequirements to be an array of integers representing requirement node IDs.
        """
        try:
            node = await self.db.get_node_by_id(self.node_id)
            implemented_req_ids = node['properties'].get('ImplementedRequirementIDs', [])
            # Check if the value is a string and unwrap it
            if isinstance(implemented_req_ids, str):
                # Parse the string into a Python list
                implemented_req_ids = json.loads(implemented_req_ids)
            if not implemented_req_ids:
                return
                
            # Process each requirement ID
            for req_id in implemented_req_ids:
                requirement = await self.db.get_node_by_id(req_id)
                if requirement:
                    await self.db.create_relationship(
                        self.node_id, 
                        requirement['id'],
                        "IMPLEMENTS",
                        {
                            "Type": requirement['labels'][0],
                            "Description": f"Implements requirement"
                        }
                    )
                    self.update_logger.info(f"Created IMPLEMENTS relationship for requirement {req_id}")
                    
        except Exception as e:
            self.update_logger.error(f"Error processing requirements relationships: {str(e)}")
    # Problems:
    # 1. For NEW-ARCH - relies solely on created_nodes
    # 2. For EXISTING - doesn't verify node exists
    # 3. No logging of why resolution failed

    # Fix 1: Modify get_node_id to be synchronous for simple cases
    def get_node_id(self, node_reference, created_nodes):
        """Synchronous node ID resolution for created and existing nodes"""
        if '-' in node_reference:
            prefix, num = node_reference.split('-', 1)
            if prefix == 'NEW':
                # Check created_nodes
                for node in created_nodes:
                    if node['properties'].get('ID') == node_reference:
                        return node['id']
                return None
            elif prefix == 'EXISTING':
                try:
                    return int(num)
                except ValueError:
                    return None
        return None

    def replace_placeholders(self,mermaid_chart, replacements):
        for key, value in replacements.items():
            mermaid_chart = mermaid_chart.replace(key, value)
        return mermaid_chart

    async def finalize(self):
        print(self.node)
        
    def is_reconfig(self):
        """Check if this is a reconfiguration scenario."""
        config_state = self.get_config_state(self.discussion_type, self.node_type, self.node)
        return config_state == 'configured'

    async def get_architectural_requirements(self, root_node_id):
        """
        Override the base method to return actual node objects instead of string properties.
        
        :param root_node_id: ID of the root architecture node
        :return: Dictionary containing requirement node objects
        """
        result = await self.db.get_child_nodes(root_node_id, "ArchitecturalRequirement")
        if not result:
            return {
                'architectural_requirements': None,
                'functional_requirements': None
            }

        arch_req_node = result[0]
        # Get full requirement nodes
        functional_reqs = await self.db.get_child_nodes(arch_req_node['id'], "FunctionalRequirement")
        nonfunctional_reqs = await self.db.get_child_nodes(arch_req_node['id'], "NonFunctionalRequirement")
        
        return {
            'functional_requirements': functional_reqs,
            'architectural_requirements': nonfunctional_reqs
        }

# Registering the subclass in the factory
DiscussionFactory.register('configuration', ArchitectureConfiguration, "Architecture")