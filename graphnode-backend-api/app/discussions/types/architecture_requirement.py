from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
from jinja2 import Environment, FileSystemLoader
from app.core.function_schema_generator import get_function_schema
import json
from app.utils.prodefn.projdefn_utils import ProjectDefinitionManager
from app.discussions.tools.discussion_tool import DiscussionTools
from app.utils.auth_utils import get_current_user
import os
from app.utils.prodefn.docs_tool import DocsTools
import inspect
from app.utils.prodefn.docs_session_manager import get_or_create_session
from app.utils.file_utils.upload_utils import upload_and_process,get_tenant_bucket
from app.connection.tenant_middleware import get_tenant_id
from app.utils.node_version_manager import NodeVersionManager
from datetime import datetime
from app.utils.datetime_utils import generate_timestamp

base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class ArchitectureRequirementDiscussion(Discussion):
    def __init__(self, discussion_type, node_id=None, discussion_id=None, title=None, description=None, batch_size=10):
        super().__init__(discussion_type, node_id, discussion_id, title, description,
                         node_type="ArchitecturalRequirement")
        self.template_name = "architecture_requirement.prompt"
        self.function_schema_type = "ArchitecturalRequirement"
        self.batch_size = 200
        #self.model_name = 'gpt-4o'
        self.project_id = None
        self.doc_tools = None
        self.discussion_tools = None
        self.function_mapping = {}
        self.function_executor = None
        self.function_schemas = []
        self.schemas_initialized = False
        self.project_knowledge = False
        self.version_manager = NodeVersionManager()
        self.background_info = None

    async def retrieve_info(self):
        await super().retrieve_info()
        
        # Get root node (project)
        project_node = self.retrieved_info['root_node']

        root_node = self.retrieved_info['root_node']

        # Get only UserStory nodes, limited to batch size
        requirement_nodes = await self.db.get_descendant_nodes(
            root_node['id'], 
            ["Epic","UserStory"],  # Fetch Epic and UserStory nodes
            ["Title","Description"], 
            self.batch_size
        )
    
        # Get all epics
        epics = [node for node in requirement_nodes
                if 'Epic' in node.get('labels', [])]
        
        # Create structured epics with their complete user stories
        structured_epics = []
        for epic in epics:
            # Get user stories related to this epic
            epic_stories = await self.db.get_child_nodes(epic.get('id'), "UserStory")
            
            structured_epic = {
                'epic': epic,  # Complete epic node
                'user_stories': epic_stories  # Complete user story nodes
            }
            structured_epics.append(structured_epic)
        
        # Add structured epics and epic nodes to retrieved info
        self.retrieved_info['structured_epics'] = structured_epics

        project_info = { 'project': self.root_node}
        if structured_epics:
            #project_info['epic_count'] = len(structured_epics)
            project_info['epics'] = [f"{t['epic']['id']}" for t in structured_epics]
            userstory_count = 0
            project_info['user_stories'] = []
            for item in structured_epics:
                project_info['user_stories'].extend([f"{t['id']}" for t in item['user_stories']])
                userstory_count += len(item['user_stories'])
            #project_info['userstory_count'] = userstory_count
            functional_requirements = await self.db.get_descendant_nodes(self.root_node['id'], ['FunctionalRequirement'], None, 100)
            referenced_userstories = []
            for req in functional_requirements:
                references = req['properties'].get('RelatedUserStoryIDs')
                if isinstance(references,str):
                    import ast
                    references = ast.literal_eval(references)
                for ref in references:
                    if ref not in referenced_userstories:
                        referenced_userstories.append(ref)
            #project_info['userstories_referenced_by_functional_requirements'] = len(referenced_userstories)
            project_info['userstories_referenced_by_functional_requirements'] = referenced_userstories
            project_info['functional_requirements'] = [f"{t['id']}: {t['properties']['Title']}" for t in functional_requirements]
            non_functional_requirements = await self.db.get_descendant_nodes(self.root_node['id'], ['NonFunctionalRequirement'], None, 100)
            project_info['non_functional_requirements'] = [f"{t['id']}: {t['properties']['Title']}" for t in non_functional_requirements]
        self.retrieved_info['project_info'] = project_info

        # Structure current background info
        current_background_info = {
            'project_context': {
                'node_id': project_node['id'] if project_node else None,
                'properties': project_node['properties'] if project_node else {}
            },
            'requirement_context': structured_epics,  # Just pass the structured_epics directly
            'system_context': {}
        }

        architecture_root = await self.db.get_child_nodes(self.retrieved_info['root_node']['id'], "ArchitectureRoot")
        if architecture_root:
            arch_req_node = await self.db.get_child_nodes(architecture_root[0]['id'], "ArchitecturalRequirement")
            if arch_req_node:
                # Get full requirement nodes
                functional_reqs = await self.db.get_child_nodes(arch_req_node[0]['id'], "FunctionalRequirement")
                nonfunctional_reqs = await self.db.get_child_nodes(arch_req_node[0]['id'], "NonFunctionalRequirement")
                
                architectural_reqs = {
                    'functional_requirements': functional_reqs,
                    'architectural_requirements': nonfunctional_reqs
                }

        # Get existing requirements
        current_requirements  = architectural_reqs
        
        self.retrieved_info['current_requirements'] = current_requirements
        
        # Handle reconfiguration
        if self.is_reconfig():
            stored_background_info = await self.version_manager._get_context(self.node_id)
            
            if stored_background_info:
                self.retrieved_info.update({
                    'original_node': self.node,
                    'background_info': stored_background_info.get('background_info', current_background_info),
                    'user_interaction': {
                        'input': self.node['properties'].get('user_inputs', '[]')
                    },
                    'change_log': self.node['properties'].get('change_log', []),
                    'change_reason': self.node['properties'].get('change_reason', '')
                })
                # Add safety check
                if not self.retrieved_info['background_info']:
                    self.retrieved_info['background_info'] = current_background_info
            else:
                self.retrieved_info.update({
                    'original_node': self.node,
                    'background_info': current_background_info,
                    'user_interaction': {
                        'input': self.node['properties'].get('user_inputs', '[]')
                    },
                    'change_log': self.node['properties'].get('change_log', []),
                    'change_reason': self.node['properties'].get('change_reason', '')
                })
            
            self.retrieved_info['new_background'] = current_background_info
        else:
            # For initial configuration
            self.retrieved_info['background_info'] = current_background_info
            self.background_info = current_background_info  # Set class variable
            self.retrieved_info['new_background'] = current_background_info

        return self.retrieved_info

    def is_reconfig(self):
        """Check if this is a reconfiguration scenario."""
        reconfig = False
        config_state = self.get_config_state(self.discussion_type, self.node_type, self.node)
        if config_state == 'configured':
            reconfig = True
            project_info = self.retrieved_info.get('project_info')
            if len(project_info['userstories_referenced_by_functional_requirements']) < len(project_info['user_stories'])/4:
                reconfig = False
        return reconfig
    
    def prompt_for_starting_discussion(self):
        print("Template name ArchitectureRequirementDiscussion: ", self.template_name)
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        tempate_dir = os.path.join(base_dir, 'discussions', 'prompt_templates')
                                   
        env = Environment(loader=FileSystemLoader(tempate_dir))
        agents_config_path = os.path.join(base_dir, 'agents', 'agents.json')
        with open(agents_config_path, 'r') as file:
            ai_agents = json.load(file)

        template = env.get_template(self.template_name)
        config = self.current_configuration
        
        if self.is_reconfig():
            config_state = self.get_config_state(self.discussion_type, self.node_type, self.node)
        else:
            config_state = 'unconfigured'

        function_schema = get_function_schema(self.function_schema_type)

          # Get search criteria for current discussion type
        project_definition_manager = ProjectDefinitionManager(base_path= "./app/utils/prodefn")
        project_knowledge = project_definition_manager.load_project_definition()
        project_info = self.retrieved_info.get('project_info')
        context = {
            "prompt_type": "user",
            "current_node": self.retrieved_info["current_node"]["labels"],
            "details_for_discussion": self.retrieved_info,
            "root_node_type": self.root_node_type,
            "config": config,
            "ai_agents": ai_agents,
            "config_state": config_state,
            "invocation_type": self.invocation_type,
            "function_schema": function_schema,
            "discussion_type": self.discussion_type,
            "node_type": self.node_type,
            "project_info": project_info,
            "requirement_nodes": self.retrieved_info.get('requirement_nodes', []),  # Add this line
            "current_requirements": self.retrieved_info.get('current_requirements', {}),  # Add this line
            "project_knowledge": project_knowledge
        }

        user_prompt = template.render(context)
        system_prompt = template.render(dict(context, prompt_type="system"))
        return user_prompt, system_prompt, function_schema
    

    async def get_relationships(self, root_node_id):
        child_relationships = await self.db.get_all_relationships(root_node_id, "HAS_CHILD", self.node_type, 3)
        interface_relationships = await self.db.get_all_relationships(root_node_id, "INTERFACES_WITH", self.node_type, 3)
        return child_relationships + interface_relationships

    async def get_architectural_requirements(self, root_node_id):
        architecture_root = await self.db.get_child_nodes(root_node_id, "ArchitectureRoot")
        result = await self.db.get_child_nodes(architecture_root[0]['id'], "ArchitecturalRequirement")
        if result and len(result) > 0:
            architectural_requirements_node = result[0]
            return {
                'architectural_requirements': architectural_requirements_node['properties'].get('architectural_requirements', []),
                'functional_requirements': architectural_requirements_node['properties'].get('functional_requirements', [])
            }
        else:
            return {
                'architectural_requirements': [],
                'functional_requirements': []
            }

    def get_modifications_from_llm_output(self):
        super().get_modifications_from_llm_output()  # Call the parent method to set up the basic structure
        
        # Add the specific child node types for architectural requirements
        self.modifications['child_node_types'] = ['FunctionalRequirement', 'NonFunctionalRequirement']
        
        return self.modifications

    async def merge_captured_items(self):
        if not self.modifications.get('modified_node'):
            self.update_logger.warning("No modifications to merge")
            return
        # Get background info by calling retrieve_info again
        await self.retrieve_info()
        user_inputs = self.modifications['modified_node'].get('user_inputs', '')
        change_reason = self.modifications['modified_node'].get('change_reason', '')

        try:
            if self.is_reconfig():
                if self.modifications['modified_node'].get('changes_needed', False):
                    await super().merge_captured_items()
                    
                    created_nodes = self.modifications.get('created_nodes', [])
                    functional_reqs = []
                    nonfunctional_reqs = []
                    
                    for node in created_nodes:
                        req_type = node.get('properties', {}).get('Type')
                        
                        if req_type == 'FunctionalRequirement':
                            functional_reqs.append(f"{node['properties']['Title']}: {node['properties']['Description']}")
                            # Create relationship for each user story ID
                            #fix me : TO Do sometimes the output comes as a string
                            related_userstroy_ids = node['properties'].get('RelatedUserStoryIDs', [])
                            # Check if the value is a string and unwrap it
                            if isinstance(related_userstroy_ids, str):
                                # Parse the string into a Python list
                                related_userstroy_ids = json.loads(related_userstroy_ids)
                            for story_id in related_userstroy_ids:
                                await self.db.create_relationship(
                                    node['id'],
                                    story_id,
                                    "RELATES_TO",
                                    {
                                        "Type": req_type
                                    }
                                )
                                
                        elif req_type == 'NonFunctionalRequirement':
                            nonfunctional_reqs.append(f"{node['properties']['Title']}: {node['properties']['Description']}")
                    
                    # Update the ArchitecturalRequirement node
                    update_properties = {
                        'functional_requirements': "\n".join(f"{i+1}. {req}" for i, req in enumerate(functional_reqs)),
                        'architectural_requirements': "\n".join(f"{i+1}. {req}" for i, req in enumerate(nonfunctional_reqs))
                    }
                    await self.db.update_node_by_id(self.node_id, update_properties, ["ArchitecturalRequirement"])
                    self.modifications['modified_node'].update(update_properties)

                    # Save version info
                    save_data = {
                        'node_id': self.node_id,
                        'properties': self.modifications['modified_node'],
                        'background_info': self.retrieved_info['new_background'],
                        'user_interaction': {
                            'timestamp': generate_timestamp(),
                            'action': 'reconfig',
                            'input': self.modifications['modified_node'].get('user_inputs', ''),
                            'reason': self.modifications['modified_node'].get('change_reason', '')
                        }
                    }
                    await self.version_manager.save_node_info(save_data)
            else:
                await super().merge_captured_items()
                
                created_nodes = self.modifications.get('created_nodes', [])
                functional_reqs = []
                nonfunctional_reqs = []
                
                for node in created_nodes:
                    req_type = node.get('properties', {}).get('Type')
                    
                    if req_type == 'FunctionalRequirement':
                        functional_reqs.append(f"{node['properties']['Title']}: {node['properties']['Description']}")
                        # Create relationship for each user story ID
                        #fix me : TO Do sometimes the output comes as a string
                        related_userstroy_ids = node['properties'].get('RelatedUserStoryIDs', [])
                        # Check if the value is a string and unwrap it
                        if isinstance(related_userstroy_ids, str):
                            # Parse the string into a Python list
                            related_userstroy_ids = json.loads(related_userstroy_ids)
                        for story_id in related_userstroy_ids:
                            await self.db.create_relationship(
                                node['id'],
                                story_id,
                                "RELATES_TO",
                                {
                                    "Type": req_type
                                }
                            )
                            
                    elif req_type == 'NonFunctionalRequirement':
                        nonfunctional_reqs.append(f"{node['properties']['Title']}: {node['properties']['Description']}")
                
                # Update the ArchitecturalRequirement node
                update_properties = {
                    'functional_requirements': "\n".join(f"{i+1}. {req}" for i, req in enumerate(functional_reqs)),
                    'architectural_requirements': "\n".join(f"{i+1}. {req}" for i, req in enumerate(nonfunctional_reqs))
                }
                await self.db.update_node_by_id(self.node_id, update_properties, ["ArchitecturalRequirement"])
                self.modifications['modified_node'].update(update_properties)

                # Save version info
                save_data = {
                    'node_id': self.node_id,
                    'properties': self.modifications['modified_node'],
                    'background_info': self.retrieved_info['new_background'],
                    'user_interaction': {
                        'timestamp': generate_timestamp(),
                        'action': 'reconfig',
                        'input': self.modifications['modified_node'].get('user_inputs', ''),
                        'reason': self.modifications['modified_node'].get('change_reason', '')
                    }
                }
                await self.version_manager.save_node_info(save_data)
        except Exception as e:
            self.update_logger.error(f"Error saving changes to node {self.node_id}: {str(e)}")
            raise
    async def get_all_requirements_details(self):
        functional_reqs = await self.db.get_child_nodes(self.node_id, "FunctionalRequirement")
        architectural_reqs = await self.db.get_child_nodes(self.node_id, "ArchitecturalRequirementNode")
        return {
            'functional_requirements': functional_reqs,
            'architectural_requirements': architectural_reqs
        }
    


DiscussionFactory.register('architecture_requirement', ArchitectureRequirementDiscussion, "ArchitecturalRequirement")