from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
from app.utils.node_version_manager import NodeVersionManager
from app.telemetry.logger_config import get_logger
from datetime import datetime

class SystemContextOverviewDiscussion(Discussion):
    def __init__(self, discussion_type, node_id=None, discussion_id=None, title=None, description=None):
        super().__init__(discussion_type, node_id, discussion_id, title, description, node_type="SystemContext")
        # Template will be set during retrieve_info based on architecture pattern
        self.template_name = None
        self.function_schema_type = "SystemContextOverviewConfiguration"
        self.version_manager = NodeVersionManager()
        self.update_logger = get_logger(__name__)

    async def retrieve_info(self):
        """Retrieve context information for system context overview"""
        await super().retrieve_info()
        try:
            # Get project details
            project_node = self.retrieved_info['root_node']
            self.retrieved_info['project_details'] = project_node.get('properties', {})
            
            # Get architecture pattern and set appropriate template
            architecture_pattern = project_node['properties'].get('ArchitecturePattern', '').lower()
            self.retrieved_info['architecture_pattern'] = architecture_pattern
            
            # Set template based on architecture pattern
            if architecture_pattern in ['monolithic-application', 'monolithic-service']:
                self.template_name = "monolithic_system_context_overview.prompt"
                self.update_logger.info(f"Using monolithic system context overview template for {architecture_pattern}")
            elif architecture_pattern == 'multi-container-service':
                self.template_name = "multicontainer_service_overview.prompt"
                self.update_logger.info(f"Using multi-container service overview template for {architecture_pattern}")
            else:
                self.template_name = "system_context_overview.prompt"
                self.update_logger.info("Using standard system context overview template")

            # Get requirements context
            requirements_context = {
                'functional_requirements': [],
                'architectural_requirements': []
            }

            # Get architectural requirements root
            architecture_root = await self.db.get_child_nodes(project_node['id'], "ArchitectureRoot")
            if architecture_root:
                arch_req_nodes = await self.db.get_child_nodes(architecture_root[0]['id'], "ArchitecturalRequirement")
                if arch_req_nodes:
                    # Get full requirement nodes
                    functional_reqs = await self.db.get_child_nodes(arch_req_nodes[0]['id'], "FunctionalRequirement")
                    nonfunctional_reqs = await self.db.get_child_nodes(arch_req_nodes[0]['id'], "NonFunctionalRequirement")
                    
                    requirements_context = {
                        'functional_requirements': functional_reqs,
                        'architectural_requirements': nonfunctional_reqs
                    }

            self.retrieved_info['requirements_context'] = requirements_context

            # Structure current background info
            current_background_info = {
                'project_context': {
                    'node_id': project_node['id'],
                    'properties': project_node['properties']
                },
                'requirements_context': requirements_context
            }

            # Get existing users and external systems if this is a reconfiguration
            if self.is_reconfig():
                system_context = await self.db.get_node_by_id(self.node_id)
                self.retrieved_info['external_systems'] = system_context['properties'].get('ExternalSystems', '')
                self.retrieved_info['users'] = system_context['properties'].get('Users', '')

                # Handle reconfiguration background info
                stored_background_info = await self.version_manager._get_context(self.node_id)
                if stored_background_info:
                    self.retrieved_info.update({
                    'original_node': self.node,
                    'background_info': stored_background_info.get('background_info', current_background_info),
                    'user_interaction': {
                        'input': self.node['properties'].get('user_inputs', '[]')
                    },
                    'change_log': self.node['properties'].get('change_log', []),
                    'change_reason': self.node['properties'].get('change_reason', '')
                    })
                    # Add safety check
                    if not self.retrieved_info['background_info']:
                        self.retrieved_info['background_info'] = current_background_info
                else:
                    self.retrieved_info.update({
                    'original_node': self.node,
                    'background_info': current_background_info,
                    'user_interaction': {
                        'input': self.node['properties'].get('user_inputs', '[]')
                    },
                    'change_log': self.node['properties'].get('change_log', []),
                    'change_reason': self.node['properties'].get('change_reason', '')
                })
                self.retrieved_info['new_background'] = current_background_info
            else:
                # For initial configuration
                self.retrieved_info['background_info'] = current_background_info
                self.retrieved_info['new_background'] = current_background_info

            return self.retrieved_info

        except Exception as e:
            self.update_logger.error(f"Error retrieving system context overview info: {str(e)}")
            raise

    def get_modifications_from_llm_output(self):
        """Process LLM output to extract modifications"""
        # Set child node types - system context overview creates container nodes for external systems
        self.modifications['child_node_types'] = ['Container']
        
        # Extract external systems and users from LLM output
        llm_output = self.modifications.get('modified_node', {})
        self.modifications['external_systems'] = llm_output.get('ExternalSystems', [])
        self.modifications['users'] = llm_output.get('Users', [])
        
        return self.modifications

    async def merge_captured_items(self):
        """Merge captured items into the database"""
        if not self.modifications.get('modified_node'):
            self.update_logger.warning("No modifications to merge")
            return

        await self.retrieve_info()
        
        try:
            # Set overview configuration state
            self.modifications['modified_node']['overview_config_state'] = 'configured'
            
            if self.is_reconfig():
                if self.modifications['modified_node'].get('changes_needed', False):
                    await super().merge_captured_items()
                    
                    # Save version info
                    save_data = {
                        'node_id': self.node_id,
                        'properties': self.modifications['modified_node'],
                        'background_info': self.retrieved_info['new_background'],
                        'user_interaction': {
                            'timestamp': datetime.now().isoformat(),
                            'action': 'reconfig',
                            'input': self.modifications['modified_node'].get('user_inputs', '[]'),
                            'reason': self.modifications['modified_node'].get('change_reason', '')
                        }
                    }
                    await self.version_manager.save_node_info(save_data)
            else:
                await super().merge_captured_items()
                
                # Save version info for initial configuration
                save_data = {
                    'node_id': self.node_id,
                    'properties': self.modifications['modified_node'],
                    'background_info': self.retrieved_info['new_background'],
                    'user_interaction': {
                        'timestamp': datetime.now().isoformat(),
                        'action': 'initial_config',
                        'input': self.modifications['modified_node'].get('user_inputs', '[]'),
                        'reason': self.modifications['modified_node'].get('change_reason', '')
                    }
                }
                await self.version_manager.save_node_info(save_data)

        except Exception as e:
            self.update_logger.error(f"Error in merge_captured_items: {str(e)}")
            raise

    def is_reconfig(self):
        """Check if this is a reconfiguration scenario."""
        config_state = self.get_config_state(self.discussion_type, self.node_type, self.node)
        return config_state == 'configured'

    def get_config_state(self, discussion_type, node_type, node):
        """Get configuration state for the specific discussion type."""
        if not node or 'properties' not in node:
            return 'not_configured'
            
        properties = node['properties']
        if discussion_type == 'system_context_overview':
            return properties.get('overview_config_state', 'not_configured')
        else:
            return properties.get('config_state', 'not_configured')

    def set_config_state(self, node_properties):
        """Set configuration state for the specific discussion type."""
        if self.discussion_type == 'system_context_overview':
            node_properties.setdefault('overview_config_state', 'configured')
        else:
            node_properties.setdefault('config_state', 'configured')
        return node_properties

# Register the discussion type
DiscussionFactory.register('system_context_overview', SystemContextOverviewDiscussion, "SystemContext") 