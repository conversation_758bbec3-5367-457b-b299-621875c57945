from app.core.function_schema_generator import get_function_schema
from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
from jinja2 import Environment, FileSystemLoader, TemplateNotFound
import json
from datetime import datetime
import numpy as np
from llm_wrapper.core.llm_interface import LLMInterface
from app.models.user_model import LLMModel
from app.utils.logs_utils import get_path
from app.connection.establish_db_connection import get_vector_db
import json


class TestCaseGeneration(Discussion):
    def __init__(self, discussion_type, node_id=None, discussion_id=None, title=None, description=None):
        super().__init__(discussion_type, node_id, discussion_id, title, description, node_type="UserStory")
        self.template_name = "testcase_generation.prompt"
        self.function_schema_type = "TestCaseGenerationConfiguration"
        self.user_story_id = node_id  # Store the original UserStory ID
        self.vector_db = get_vector_db()  # Assuming this function returns an instance of VectorDbHandler

    async def async_initialize(self):
        await super().async_initialize()
        self.test_case_root = await self.get_or_create_test_case_root()
        self.test_case_root_id = self.test_case_root['id']  # Store TestCaseRoot id separately
        # Keep original node_id and node_type from initialization
        
    async def retrieve_info(self):
        """
        Retrieve information about the user story, project and existing test cases
        """
        await super().retrieve_info()
        self.retrieved_info['user_story'] = await self.db.get_node_by_id(self.user_story_id)
        
        # Get the project ID
        self.retrieved_info['test_case_root'] = self.test_case_root
        
        # Get project and epic details
        project_id = await self.db.get_project_id(self.user_story_id)
        self.retrieved_info['project'] = await self.db.get_node_by_id(project_id)

        self.retrieved_info['epic'] = await self.db.get_parent_node(self.user_story_id)
        
        # Retrieve or initialize dynamic categories and common tags
        self.retrieved_info['functional_categories'] = self.test_case_root['properties'].get('FunctionalCategories', [])
        self.retrieved_info['non_functional_categories'] = self.test_case_root['properties'].get('NonFunctionalCategories', [])
        
        # Retrieve common tags
        self.retrieved_info['common_project_tags'] = self.test_case_root['properties'].get('CommonTags', [])

        # If common tags don't exist, we'll create them later in merge_captured_items
        if not self.retrieved_info['common_project_tags']:
            self.update_logger.info("No common tags found. They will be created during merge.")

        # Handle existing test case IDs - will always be a list of integers
        existing_test_case_ids = self.retrieved_info['user_story'].get('existing_test_case_ids', [])
        self.retrieved_info['existing_test_case_ids'] = existing_test_case_ids

        # Prefilter and rank relevant existing test cases
        relevant_test_cases = await self.prefilter_and_rank_test_cases()
        self.retrieved_info['relevant_existing_test_cases'] = relevant_test_cases

        return self.retrieved_info

    async def prefilter_and_rank_test_cases(self):
        try:
            # Get all test cases without prefiltering
            all_test_cases = await self.db.get_child_nodes(self.test_case_root_id, None)
            
            # If there are no test cases, return an empty list
            if not all_test_cases:
                self.update_logger.info("No existing test cases found.")
                return []

            # Temporarily disabled prefiltering and ranking
            self.update_logger.info("Prefiltering and ranking temporarily disabled. Returning all test cases.")
            return all_test_cases[:10]  # Return up to 10 test cases

        except Exception as e:
            self.update_logger.error(f"Error in prefilter_and_rank_test_cases: {str(e)}")
            return []  # Return empty list in case of error

    async def select_relevant_tags(self, test_case_info, common_tags):
        prompt = f"""
        Based on the following test case information, select the most relevant tags from the given list of common tags. Choose 2-5 tags that best describe this test case.

        Test Case:
        {test_case_info}

        Common Tags:
        {', '.join(common_tags)}

        Output the selected tags as a comma-separated list.
        """
        self.llm = LLMInterface(get_path(), 'testcase_generation', self.current_user, self.node_id, f"Discussion_{self.discussion_type}")
        messages = [{'role': 'user', 'content': prompt}]
        
        response = await self.llm.llm_interaction_wrapper(
            messages=messages,
            user_prompt=None,
            system_prompt=None,
            response_format={'type': 'text'},
            model=self.model_name,
            stream=False
        )
        
        tags = [tag.strip() for tag in response.split(',') if tag.strip() in common_tags]
        return tags
    
    def get_modifications_from_llm_output(self):

        self.modifications['child_node_types'] = ['FunctionalTestCase', 'NonFunctionalTestCase', 'StressTest', 'StabilityTest', 'InteroperabilityTest']
        
        return self.modifications

    async def merge_captured_items(self):
        """Merge captured items into the database"""
        # Store original node_id and node_type
        original_node_id = self.node_id
        original_node_type = self.node_type
        
        # Temporarily set node_id and node_type to TestCaseRoot for test case creation
        self.node_id = self.test_case_root_id
        self.node_type = "TestCaseRoot"
        
        try:
            # Call parent's merge_captured_items to create test cases under TestCaseRoot
            await super().merge_captured_items()
            
            # Process the results
            await self.retrieve_info()
            
            modified_node = self.modifications.get('modified_node', {})
            created_nodes = self.modifications.get('created_nodes', [])

            if not modified_node and not created_nodes:
                self.update_logger.warning("No modifications or new nodes to process.")
                return

            if modified_node:
                # Update dynamic categories only if they're empty (first-time setup)
                if not self.retrieved_info['functional_categories'] and not self.retrieved_info['non_functional_categories']:
                    functional_categories = modified_node.get('FunctionalCategories', [])
                    non_functional_categories = modified_node.get('NonFunctionalCategories', [])
                    
                    await self.db.update_node_by_id(
                        self.test_case_root_id,
                        {
                            'FunctionalCategories': functional_categories,
                            'NonFunctionalCategories': non_functional_categories
                        },
                        "TestCaseRoot"
                    )
                    self.update_logger.info(f"Updated test case categories for project")

                # Handle common tags
                if not self.retrieved_info['common_project_tags']:
                    common_tags = modified_node.get('CommonTags', [])
                    if common_tags:
                        await self.db.update_node_by_id(
                            self.test_case_root_id,
                            {'CommonTags': common_tags},
                            "TestCaseRoot"
                        )
                        self.update_logger.info(f"Created common tags for TestCaseRoot")

                # Process existing test cases to be mapped - will be a list of integers
                existing_test_case_ids = modified_node.get('existing_test_case_ids', [])
                self.update_logger.info(f"Processing existing test case IDs: {existing_test_case_ids}")
            
                # Process existing test cases to be mapped
                if existing_test_case_ids:
                    for test_case_id in existing_test_case_ids:
                        # Create VERIFIES relationship if it doesn't exist
                        relationship_exists = await self.db.relationship_exists(test_case_id, self.user_story_id, "VERIFIES")
                        if not relationship_exists:
                            await self.db.create_relationship(test_case_id, self.user_story_id, "VERIFIES")
                            self.update_logger.info(f"Mapped existing test case {test_case_id} to UserStory {self.user_story_id}")
                else:
                    self.update_logger.info("No existing test cases to map.")
            else:
                self.update_logger.info("No modifications to process.")

            if created_nodes:
                # Process new test cases
                for created_node in created_nodes:
                    new_test_case_id = created_node.get('id')
                    if new_test_case_id:
                        # Create VERIFIES relationship between the new test case and the UserStory
                        await self.db.create_relationship(new_test_case_id, self.user_story_id, "VERIFIES")
                        self.update_logger.info(f"Linked new test case {new_test_case_id} to UserStory {self.user_story_id}")
                    else:
                        self.update_logger.warning(f"Skipping linking of test case due to missing ID: {created_node}")
            else:
                self.update_logger.info("No new test cases to link.")

            # Clear the existing_test_case_ids in the TestCaseRoot
            await self.db.update_node_by_id(self.test_case_root_id, {'existing_test_case_ids': []}, "TestCaseRoot")
            self.update_logger.info(f"Cleared existing_test_case_ids in TestCaseRoot")
            
            self.update_logger.info(f"Test case generation completed for UserStory {self.user_story_id}")
            
        finally:
            # Always restore original node_id and node_type, even if an error occurs
            self.node_id = original_node_id
            self.node_type = original_node_type

    async def get_or_create_test_case_root(self):
        project_id = await self.db.get_project_id(self.node_id)
        test_case_root = await self.db.get_child_nodes(project_id, "TestCaseRoot")
        
        if not test_case_root:
            properties = {
                "Title": "Test Cases",
                "Description": "Root node for all test cases in the project",
                "Type": "TestCaseRoot",
                "configuration_state": "not_configured"
            }
            test_case_root = await self.db.create_node(["TestCaseRoot"], properties, project_id)
        else:
            test_case_root = test_case_root[0]
        
        return test_case_root

# Register the new discussion type
DiscussionFactory.register('testcase_generation', TestCaseGeneration, "UserStory")