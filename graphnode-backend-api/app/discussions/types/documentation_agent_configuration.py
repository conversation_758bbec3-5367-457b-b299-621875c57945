from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from datetime import datetime
import json
import asyncio
from app.agents.agent import Agent
from app.discussions.discussion_util import conduct_discussion
from app.connection.establish_db_connection import get_node_db
from app.telemetry.logger_config import get_logger, set_task_id
from app.discussions.tools.discussion_tool import DiscussionTools
from app.utils.auth_utils import get_current_user
import os
from app.utils.prodefn.docs_tool import DocsTools
import inspect
from app.utils.file_utils.upload_utils import upload_and_process,get_tenant_bucket
from app.connection.tenant_middleware import get_tenant_id
from app.utils.datetime_utils import generate_timestamp
from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory

base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
documentation_mapping_path = os.path.join(base_dir, 'discussions', 'types', 'documentation_mapping.json')

class DocumentationAgent(Agent):
    def __init__(self, name, node_id, node_type, root_node_type, discussion_type, logging_context, levels, semaphore, supervisor=None):
        super().__init__(name)
        self.supervisor = supervisor
        self.node_id = node_id
        self.node_type = node_type
        self.root_node_type = root_node_type
        self.discussion_type = discussion_type
        self.db = get_node_db()
        self.semaphore = semaphore
        self.levels = levels
        self.update_logger_agent = get_logger(__name__)
        self.project_id = None
        self.doc_tools = None
        self.discussion_tools = None
        self.function_mapping = {}
        self.function_executor = None
        self.function_schemas = []
        self.schemas_initialized = False

        # Documentation sequence defines order
        self.doc_sequence = ["PRD", "SAD", "API"]
        self.current_doc_index = 0

        # Load documentation mapping
        with open(documentation_mapping_path, 'r') as f:
            self.documentation_mapping = json.load(f)

        if logging_context:
            self.logging_context = logging_context
            self.mongo_handler = logging_context.get('mongo_handler')
            self.task_id = logging_context.get('task_id')
            set_task_id(self.task_id)
        else:
            self.logging_context = {}
            self.task_id = None
            self.mongo_handler = None

    async def process_work_item(self, work_item_dict):
        try:
            self.current_work_item = work_item_dict
            self.project_id = work_item_dict.project_id

            if work_item_dict.entry == "create_documentation":
                await self.create_documentation()
            else:
                self.update_logger_agent.warning(f"Task {work_item_dict.entry} not found")
        except Exception as e:
            self.update_logger_agent.error(f"Error processing work item: {str(e)}")
            raise

    async def create_documentation(self):
        """Main function to create documentation sequentially"""
        try:
            for doc_type in self.doc_sequence:
                if doc_type != 'API':
                    self.update_logger_agent.info(f"Creating {doc_type} documentation")

                    # Get target node and context based on doc type
                    target_node = await self.get_target_node(doc_type)
                    if not target_node:
                        self.update_logger_agent.warning(f"No target node found for {doc_type}")
                        continue

                    # Get context for documentation
                    context = await self.get_context(doc_type, target_node)

                    # Create and configure documentation root with sections
                    await self.process_documentation_type(target_node['id'], doc_type, context)
                else:
                    await self.create_api_documentation()

        except Exception as e:
            self.update_logger_agent.error(f"Error in create_documentation: {str(e)}")
            raise

    async def process_documentation_type(self, target_id, doc_type, context):
        """Process a single documentation type including root and sections"""
        try:
            # Get or create documentation root
            doc_root = await self.get_or_create_doc_root(target_id, doc_type, context)
            if not doc_root:
                self.update_logger_agent.warning(f"Failed to create/get documentation root for {doc_type}")
                return

            # Configure root node if not configured
            if doc_root['properties'].get('configuration_state') != 'configured':
                self.update_logger_agent.info(f"Configuring {doc_type} documentation root")
                await self.configure_node(
                    doc_root['id'],
                    "DocumentationRoot",
                    self.root_node_type,
                    "configuration"
                )

            # Get documentation config
            doc_config = self.documentation_mapping[doc_type]

            # Create and configure sections immediately
            existing_sections = await self.db.get_child_nodes(doc_root['id'], "Sub_Section")
            existing_titles = {s['properties'].get('Title') for s in existing_sections}

            # Create any missing sections
            for section in doc_config['sections']:
                if section['title'] not in existing_titles:
                    section_node = await self.db.create_node(
                        ["Documentation", "Sub_Section"],
                        {
                            "Title": section['title'],
                            "Type": "Sub_Section",
                            "SectionType": section['type'],
                            "Description": section['description'],
                            "Order": section.get('order', 0),
                            "Content": "",
                            "configuration_state": "not_configured"
                        },
                        doc_root['id']
                    )

                    # Configure the newly created section immediately
                    if section_node:
                        self.update_logger_agent.info(f"Configuring section: {section['title']}")
                        await self.configure_node(
                            section_node['id'],
                            "Sub_Section",
                            self.root_node_type,
                            "configuration"
                        )

            # Configure any existing unconfigured sections
            for section in existing_sections:
                if section['properties'].get('configuration_state') != 'configured':
                    self.update_logger_agent.info(f"Configuring existing section: {section['properties'].get('Title')}")
                    await self.configure_node(
                        section['id'],
                        "Sub_Section",
                        self.root_node_type,
                        "configuration"
                    )

            self.update_logger_agent.info(f"Completed {doc_type} documentation processing")

        except Exception as e:
            self.update_logger_agent.error(f"Error in process_documentation_type: {str(e)}")
            raise

    async def create_api_documentation(self):
        """Create API documentation for all interfaces in the project"""
        try:
            # Get all interfaces in the project
            interfaces = await self.get_project_interfaces()
            if not interfaces:
                self.update_logger_agent.info("No interfaces found in project - skipping API documentation")
                return

            for interface in interfaces:
                self.update_logger_agent.info(f"Creating API documentation for interface: {interface['properties'].get('Title')}")
                
                # Get interface context
                context = await self.get_interface_context(interface)
                
                # Create or update API documentation for this interface
                doc_root = await self.get_or_create_doc_root(interface['id'], "API", context)
                if doc_root:
                    await self.create_and_configure_sections(doc_root['id'], "API")

        except Exception as e:
            self.update_logger_agent.error(f"Error creating API documentation: {str(e)}")
            raise

    async def get_project_interfaces(self):
        """Get all Interface nodes in the project"""
        try:
            query = """
            MATCH (p:Project)-[:HAS_CHILD*]->(i:Interface)
            WHERE ID(p) = $project_id
            RETURN i, ID(i) as id, labels(i) as labels, properties(i) as properties
            """
            result = await self.db.async_run(query, project_id=self.project_id)
            return result.data()
        except Exception as e:
            self.update_logger_agent.error(f"Error getting project interfaces: {str(e)}")
            raise

    async def get_interface_context(self, interface):
        """Get complete context for interface documentation, excluding PublicAPIDetails"""
        try:
            # Get the entire interface node with all its details
            interface_id = interface['id']
            
            # Get interface details
            interface_node = await self.db.get_node_by_id(interface_id)
            if not interface_node:
                raise ValueError(f"Interface node {interface_id} not found")

            # Create a copy of the interface node to modify
            filtered_interface = {
                'id': interface_node['id'],
                'labels': interface_node['labels'],
                'properties': {}
            }

            # Copy all properties except PublicAPIDetails
            for key, value in interface_node['properties'].items():
                if key != 'PublicAPIDetails':
                    filtered_interface['properties'][key] = value

            # Use the filtered interface node as context
            context = {
                'target_node': filtered_interface
            }

            return context

        except Exception as e:
            self.update_logger_agent.error(f"Error getting interface context: {str(e)}")
            raise
    
    async def get_existing_documentation(self, target_id, doc_type):
        """Get existing documentation of specified type"""
        try:
            # Check for existing documentation with specific type
            query = """
            MATCH (t)-[:HAS_CHILD]->(d:DocumentationRoot)
            WHERE ID(t) = $target_id AND d.DocumentationType = $doc_type
            RETURN d
            """
            result = await self.db.async_run(query, target_id=target_id, doc_type=doc_type)
            return result.data()
        except Exception as e:
            self.update_logger_agent.error(f"Error checking existing documentation: {str(e)}")
            raise

    async def get_target_node(self, doc_type):
        """Get appropriate target node based on documentation type"""
        try:
            if doc_type == "PRD":
                return await self.db.get_node_by_id(self.project_id)
            elif doc_type == "SAD":
                arch_roots = await self.db.get_child_nodes(self.project_id, "ArchitectureRoot")
                return arch_roots[0] if arch_roots else None
            return None
            
        except Exception as e:
            self.update_logger_agent.error(f"Error getting target node: {str(e)}")
            raise

    async def get_context(self, doc_type, target_node):
        """Gather context based on documentation type"""
        try:
            context = {
                'target_node': {
                    'id': target_node['id'],
                    'properties': target_node['properties']
                }
            }

            if doc_type == "PRD":
                # Get epics for PRD
                epics = await self.db.get_descendant_nodes(self.project_id, ["Epic"], max_depth=3)
                context['epics'] = [{
                    'id': epic['id'],
                    'title': epic['properties'].get('Title'),
                    'description': epic['properties'].get('Description')
                } for epic in epics]

            elif doc_type == "SAD":
                # Get architecture components
                components = await self.db.get_descendant_nodes(
                    target_node['id'], 
                    ["Architecture"], 
                    max_depth=4
                )
                context['components'] = components

            return context

        except Exception as e:
            self.update_logger_agent.error(f"Error gathering context: {str(e)}")
            raise

    async def get_or_create_doc_root(self, target_id, doc_type, context):
        """Get existing documentation root or create new if it doesn't exist"""
        try:
            # Check for existing documentation
            existing_docs = await self.db.get_child_nodes(target_id, "DocumentationRoot")
            for doc in existing_docs:
                if doc['properties'].get('DocumentationType') == doc_type:
                    self.update_logger_agent.info(f"Found existing {doc_type} documentation")
                    return doc

            # Create new documentation root if none exists
            doc_config = self.documentation_mapping[doc_type]
            target_properties = context['target_node']['properties'] if 'target_node' in context else context['interface_node']['properties']

            properties = {
                "Title": f"{doc_config['title_prefix']} - {target_properties.get('Title', 'Untitled')}",
                "Description": f"Documentation for {target_properties.get('Title', 'Untitled')}",
                "DocumentationType": doc_type,
                "Context": json.dumps(context),
                "Version": "1.0",
                "LastUpdated": generate_timestamp(),
                "configuration_state": "not_configured"
            }

            doc_root = await self.db.create_node(
                ["Documentation", "DocumentationRoot"],
                properties,
                target_id
            )

            self.update_logger_agent.info(f"Created new {doc_type} documentation root")
            return doc_root

        except Exception as e:
            self.update_logger_agent.error(f"Error in get_or_create_doc_root: {str(e)}")
            raise

    async def create_and_configure_sections(self, doc_root_id, doc_type):
        """Create and configure documentation sections with duplicate prevention"""
        try:
            doc_config = self.documentation_mapping[doc_type]
            
            # Get existing sections
            existing_sections = await self.db.get_child_nodes(doc_root_id, "Sub_Section")
            existing_section_map = {
                s['properties'].get('SectionType'): s for s in existing_sections
            }

            # Track sections to create and update
            sections_to_create = []
            sections_to_update = []

            # Process each section in config
            for section in doc_config['sections']:
                if section['type'] not in existing_section_map:
                    # New section
                    sections_to_create.append({
                        "Title": section['title'],
                        "Type": "Sub_Section",
                        "SectionType": section['type'],
                        "Description": section['description'],
                        "Order": section.get('order', 0),
                        "Content": "",
                        "configuration_state": "not_configured"
                    })
                else:
                    # Existing section that might need update
                    existing = existing_section_map[section['type']]
                    if existing['properties'].get('configuration_state') != 'configured':
                        sections_to_update.append(existing)

            # Bulk create new sections
            if sections_to_create:
                await self.db.create_nodes(
                    ["Documentation", "Sub_Section"],
                    sections_to_create,
                    doc_root_id
                )

            # Configure sections that need it
            sections_to_configure = [(s['id'], "Sub_Section") for s in sections_to_update]
            for section_id, section_type in sections_to_configure:
                if not await self.is_node_configured(section_id):
                    await self.configure_node(
                        section_id,
                        section_type,
                        self.root_node_type,
                        "configuration"
                    )

            # Configure root node if needed
            if not await self.is_node_configured(doc_root_id):
                await self.configure_node(
                    doc_root_id,
                    "DocumentationRoot",
                    self.root_node_type,
                    "configuration"
                )

        except Exception as e:
            self.update_logger_agent.error(f"Error creating/configuring sections: {str(e)}")
            raise

    async def is_node_configured(self, node_id):
        """Check if a node is configured with proper error handling"""
        try:
            node = await self.db.get_node_by_id(node_id)
            if not node or 'properties' not in node:
                return False
            return node['properties'].get('configuration_state') == 'configured'
        except Exception as e:
            self.update_logger_agent.error(f"Error checking node configuration: {str(e)}")
            return False

    def is_reconfig(self):
        """Check if this is a reconfiguration scenario."""
        config_state = self.get_config_state(self.discussion_type, self.node_type, self.node)
        return config_state == 'configured'
    
    async def configure_node(self, node_id, node_type, root_node_type, discussion_type):
        """Configure a node through discussion"""
        try:
            self.update_logger_agent.info(f"Configuring node {node_id} of type {node_type}")
            
            # Get the node to configure
            node = await self.db.get_node_by_id(node_id)
            if not node:
                raise ValueError(f"Node {node_id} not found")

            # Conduct the discussion to configure the node
            await conduct_discussion(
                node_id,
                node_type=node_type,
                root_node_type=root_node_type,
                discussion_type=discussion_type,
                logging_context=self.logging_context,
                semaphore=self.semaphore,
                levels=self.levels,
                supervisor=self.supervisor
            )

            # Verify the node was configured
            updated_node = await self.db.get_node_by_id(node_id)
            if node_type == "Sub_Section" and not updated_node['properties'].get('Content', '').strip():
                raise ValueError("No content generated")
                
            self.update_logger_agent.info(f"Successfully configured node {node_id}")
            return True

        except Exception as e:
            self.update_logger_agent.error(f"Error in configure_node: {str(e)}")
            raise