import os
from app.discussions.figma.tools.file_tools import FileTools
from llm_wrapper.core.llm_interface import LLMInterface
from app.connection.establish_db_connection import get_mongo_db, get_node_db
from app.connection.tenant_middleware import get_tenant_id
from app.utils.datetime_utils import generate_timestamp
from app.models.user_model import LLMModel
from app.models.uiux.figma_model import FigmaModel
from typing import Dict, Any, Optional
from app.utils.figma_utils import extract_file_key
from app.utils.figma_parser.models import FigmaDocument
import litellm
import uuid
import logging
import json
from app.core.constants import FIGMA_BASE_PATH as BASE_PATH
from app.discussions.figma.tools.work_input_discovery_tool import WorkInputDiscovery, WorkInputMetadata
from app.core.Settings import settings
import boto3
from botocore.exceptions import ClientError
from jinja2 import Environment, FileSystemLoader
import base64
from app.models.figma_model import ExtractionTypes
from typing import AsyncGenerator
from asyncio import Queue
from app.core.websocket.client import WebSocketClient
from app.utils.llm_tool_parser.tool_call_parser import parse_tool_info_streaming
from app.utils.file_utils.image_compressor import download_and_compress_image
FIGMA_DISCUSSION_HISTORY_COLLECTION = "figma_discussion_history"
MAX_LOOP = 10
SYSTEM_PROMPT = """
You are a design expert. Given a figma design file or a screenshot of a figma design, you will extract the design elements and their properties. Based on the design elements and their properties, you will create a HTML, CSS, and JavaScript code to replicate the design.
Important:
1. All the code you write must be in base path {BASE_PATH}.
2. File paths should always start with {BASE_PATH}.
3. Provide full file paths for all the files you create.
2. You will be given a figma design file or a screenshot of a figma design.
3. You will extract the design elements and their properties.
4. You will create a HTML, CSS, and JavaScript code to replicate the design.
5. You will return the code in a file.
6. You can use tool_calls to get the information. 
7. Once you feel the implementation is complete. In your completion message, in block letters, write "FIGMA_EXTRACTION_COMPLETE".
"""

def get_vertex_secret():

        secret_name = "preprod/vertext_ai/service_account"
        region_name = "us-east-1"
        
        aws_access_key_id = os.getenv("CODEGEN_AWS_ACCESS_KEY_ID", os.getenv("AWS_ACCESS_KEY_ID"))
        aws_secret_access_key = os.getenv("CODEGEN_AWS_SECRET_ACCESS_KEY", os.getenv("AWS_SECRET_ACCESS_KEY"))
        session = boto3.session.Session(
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key
        )

        # Create a Secrets Manager client
        session = boto3.session.Session()
        client = session.client(
            service_name='secretsmanager',
            region_name=region_name
        )

        try:
            get_secret_value_response = client.get_secret_value(
                SecretId=secret_name
            )
        except ClientError as e:
            # For a list of exceptions thrown, see
            # https://docs.aws.amazon.com/secretsmanager/latest/apireference/API_GetSecretValue.html
            raise e

        secret = get_secret_value_response['SecretString']
        print("AWS SECRETS : ", secret)
        return secret


class Extraction:
    @staticmethod
    def start_discussion(project_id: int, selected_design_id: str,session_name:str, extraction_type: ExtractionTypes = ExtractionTypes.Figma):
        discussion_id = str(uuid.uuid4().hex[:8])
        mongo_handler = get_mongo_db().db[FIGMA_DISCUSSION_HISTORY_COLLECTION]
        mongo_handler.insert_one({
            "session_name":session_name,
            "discussion_id": discussion_id,
            "selected_design_id": selected_design_id,
            "project_id": project_id,
            "discussion_so_far": [],
            "discussion_type": extraction_type.value,
            "discussion_status": "pending",
            "discussion_created_at": generate_timestamp()
        })
        return discussion_id

    @staticmethod
    def get_messages_history(discussion_id: str):
        #filter out system messages
        mongo_handler = get_mongo_db().db[FIGMA_DISCUSSION_HISTORY_COLLECTION]
        discussion_record = mongo_handler.find_one({"discussion_id": discussion_id})
        return [message for message in discussion_record.get("discussion_so_far", []) if message.get("role") != "system"]

    @staticmethod
    def get_past_discussions(project_id: int, selected_design_id: str = None):
        mongo_handler = get_mongo_db().db[FIGMA_DISCUSSION_HISTORY_COLLECTION]

        find_query = {"project_id": project_id}
        if selected_design_id:
            find_query["selected_design_id"] = selected_design_id
        projection = {
           "_id": 0,
           "discussion_so_far": 0
        }
        
        discussions = mongo_handler.find(find_query, projection)
        discussions = [discussion for discussion in discussions]
        return discussions

    def __init__(self, discussion_id):

        self.file_tools = FileTools(f"{BASE_PATH}/{discussion_id}/.assets/")
        self.discussion_id = discussion_id
        self.figma_discussion_store = get_mongo_db().db[FIGMA_DISCUSSION_HISTORY_COLLECTION]
        self.model_name = LLMModel.bedrock_claude_3_7_sonnet.value
        # self.model_name = LLMModel.claude_3_7_sonnet.value
        #self.model_name = LLMModel.vertex_ai.value
        # self.model_name = "gpt-4o-mini"
        # self.model_name = "gpt-4.1"
        self.project_id = None
        self.selected_design_id = None
        self.db = get_node_db()
        self.retrieved_info = None
        self.file_logger = logging.getLogger("figma_extraction")
        self.file_logger.setLevel(logging.INFO)
        self.file_handler = logging.FileHandler(f"figma_extraction_{self.discussion_id}.log")
        self.file_handler.setLevel(logging.INFO)
        self.file_logger.addHandler(self.file_handler)
        self.loop_count = 0
        self.base_path = f"{BASE_PATH}/{self.discussion_id}"
        self.assets_path = f"{self.base_path}/.assets"
        self.output_file_name = "index.html"
        self.work_item_discovery :WorkInputDiscovery = None
        self.complete = False
        self.llm: LLMInterface = None
        self.current_user = None
        self.vertex_secret = None
        self.kwargs = {}
        self.file_attachments = None
        self.images = []
        self.ws_client: WebSocketClient = None
        self.current_operations = {}

    def set_llm(self, llm: LLMInterface ):
        self.llm = llm
        
    def set_current_user(self, current_user):
        self.current_user = current_user
    def set_selected_frame(self, frame_id):
        self.selected_frame = frame_id

    def set_project_id(self, project_id: int):
        self.project_id = project_id
    
    def set_file_attachments(self, file_attachments):
        self.file_attachments = file_attachments
    
    async def _initialize(self):
        discussion_record = self.figma_discussion_store.find_one({"discussion_id": self.discussion_id})
        self.project_id = discussion_record["project_id"]
        self.selected_design_id = discussion_record["selected_design_id"]
        self.extraction_type = discussion_record["discussion_type"]
        if not discussion_record:
           discussion_record = {
               "discussion_id": self.discussion_id,
               "discussion_so_far": [],
               "project_id": self.project_id,
               "discussion_type": "figma_extraction",
               "initial_extraction_status": 0,
               "discussion_status": "pending",
                "discussion_created_at": generate_timestamp()
           }
           self.figma_discussion_store.insert_one(discussion_record)

        self.discussion_so_far = discussion_record["discussion_so_far"]
        self.llm = LLMInterface(
            self.base_path,
            instance_name=f"figma_discussion_{self.discussion_id}",
            user_id=self.current_user,
            project_id=self.project_id,
            agent_name="FigmaExtractionAgent"
        )
        self.work_item_discovery: WorkInputDiscovery = WorkInputDiscovery(
            callback_functions=None,
            base_path=self.base_path,
            logger=self.file_logger,
            llm=self.llm

        )
        self.vertex_secret = None


    async def load_figma_data(self) -> Optional[Dict[str, Any]]:
        """
        Load Figma data associated with the current project node.
        Uses the new Figma parser to process the data.
        
        Returns:
            Dict containing Figma design data or None if not found
        """
        try:

            figma_design = await FigmaModel.get_one(str(self.selected_design_id))

            if not figma_design:
                print(f"No Figma designs found for project {self.project_id}")
                return None

  


            # Get file data from Figma API
            file_key = figma_design.get('file_key')
            url = figma_design.get('url')

            if not file_key and url:
                file_key = extract_file_key(url)

            if not file_key:
                print("Could not determine Figma file key")
                return None

            figma_data = await self.db.get_figma_file(self.project_id, file_key)
            self.retrieved_info = figma_data
            return figma_data

        except Exception as e:
            print(f"Error loading Figma data: {str(e)}")
            return None

    async def retrieve_info(self):
        """
        Retrieve information needed for the discussion, including Figma-specific data.
        Includes parsed data from the Figma parser.
        
        Returns:
            Dict containing all retrieved information
        """

        info = await self.load_figma_data()
        self.retrieved_info = info
        return info


    def render_prompt(self, prompt_type, context):
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        templates_path = os.path.join(base_dir
                                      , 'prompt_templates', 'figma_prompts')

        env = Environment(loader=FileSystemLoader(templates_path))
        print(env)
        context['prompt_type'] = prompt_type
        template = env.get_template('figma_pre_processor.j2')

        rendered_content = template.render(context)
        return rendered_content
    def _get_selected_images(self):
        """
        Get images based on frame selection logic.
        Returns selected frame image if available, otherwise all images.
        """
        if not hasattr(self, 'selected_frame') or not self.selected_frame:
            print("No specific frame selected, processing all images")
            return self.images
        
        # Find the specific image that matches the selected_frame ID
        selected_image = next(
            (image for image in self.images if image.get("file_id") == self.selected_frame),
            None
        )
        
        if selected_image:
            print(f"Selected frame ID: {self.selected_frame}")
            print(f"Using selected frame image: {selected_image.get('file_id')}")
            return [selected_image]
        else:
            print(f"Warning: No image found for selected frame ID {self.selected_frame}, using all images")
            return self.images
        
    async def main_discussion(self, user_message: str, stream=False,ws_client=None):
        self.retrieved_info = await self.retrieve_info()
        user_message = user_message.strip()
        past_messages = self.discussion_so_far
        if ws_client:
            self.ws_client = ws_client
     

        if self.extraction_type == ExtractionTypes.Images.value:
            image_record = get_mongo_db().db["figma_ext_images"].find_one({"figma_ext_id": self.selected_design_id})
            self.images = image_record.get("images", []) if image_record else []
            
            context = {
                'action': None,
                'request_details': "Extract the design elements from the attached screenshots/figma file.",
                'agent_list': ["AssetExtractionAgent"],
                'agent_capabilities': {
                    'AssetExtractionAgent': ['Extract assets from the provided work item']
                },
                'number_of_allowed_calls': 7,
                'subtask': None,
                'base_path': f"{self.base_path}/",
                'work_input_sh256': None,
                'work_input': None,
                'output_html': f"{self.assets_path}/index.html",
            }
                        
        else:
            self.kwargs = {}
            assets = []
            work_input_discovery : WorkInputDiscovery = self.work_item_discovery
            hashes, work_item = work_input_discovery.process_figma_json(self.retrieved_info)
            next_input : WorkInputMetadata = self.work_item_discovery.get_next_input()

            asset_files_path = os.path.join(self.base_path, ".assets", next_input.sha256)
            # if asset_files.json exists in the asset_files_path, read it and add the assets to the list
            # mark the input as completed and move to the next input
            if os.path.exists(os.path.join(asset_files_path, "asset_files.json")):
                with open(os.path.join(asset_files_path, "asset_files.json"), "r") as f:
                    self.logger.info("Using existing asset_files.json")
                    content = f.read()
                    content_dict = json.loads(content)
                    if content_dict["asset_files"]:
                        assets.extend(content_dict["asset_files"])
                    else:
                        self.logger.error("No assets found in the response")
                        print("ERROR: No assets found in the response")
                work_input_discovery.set_completed(next_input.sha256)
                next_input = work_input_discovery.get_next_input()

                asset_files_path = os.path.join(self.base_path, ".assets", next_input.sha256)
                os.makedirs(asset_files_path, exist_ok=True)

            

            context = {
                'action': None,
                'request_details': "Extract the design elements from the attached screenshots/figma file.",
                'agent_list': ["AssetExtractionAgent"],
                'agent_capabilities': {
                    'AssetExtractionAgent':['Extract assets from the provided work item']
                },
                'number_of_allowed_calls': 7,
                'subtask': None,
                'base_path': f"{self.base_path}/",
                'work_input_sh256' : next_input.sha256,
                'work_input': None,
                'output_html' : f"{self.assets_path}/index.html",
            }

    


        system_prompt = self.render_prompt('system', context)
        user_prompt = self.render_prompt('user', context)

        os.makedirs(f"{BASE_PATH}/{self.discussion_id}/", exist_ok=True)

        full_system_message = {
            "role": "system",
            "message_id": str(uuid.uuid4()),
            "content": system_prompt
        }
        
        full_internal_user_prompt = {
            "role": "user",
            "message_id": str(uuid.uuid4()),
            "content": user_prompt
        }

        full_user_prompt = {
            "role": "user",
            "message_id": str(uuid.uuid4()),
            "content": user_message
        }

        if self.file_attachments:
            new_message_content = []
            new_message_content.append({"type": "text", "text": user_message})
            has_image_urls = False

            for file in self.file_attachments:
                if file.get("file_type").startswith('image/'):
                    has_image_urls = True
                    new_message_content.append({
                        "type": "image_url",
                        "image_url": {
                            "url": file.get("original_url"),
                        }
                    })
                else:
                    new_message_content[0]["text"] += f"\n\nFile: {file.get('file_name')}\nType: {file.get('file_type')}\nContents:\n{file.get('extracted_content')}\n\n"
            
            if has_image_urls:       
                full_user_prompt['content'] = new_message_content
            else:
               full_user_prompt['content'] = new_message_content[0]["text"]

        if past_messages:
            messages = past_messages + [full_user_prompt]
        else:
            messages = [full_system_message, full_internal_user_prompt, full_user_prompt]
            
            if self.images:
                new_message_content = [{'type': 'text', 'text': user_message}]
                
                # Handle frame selection logic
                images_to_process = self._get_selected_images()
                
                for image in images_to_process:
                    image_url = image.get("base64url")
                    if image_url:
                        new_message_content.append({
                            "type": "image_url",
                            "image_url": {"url": image_url}
                        })
                
                full_user_prompt['content'] = new_message_content

            if self.retrieved_info:
                new_message_content = []
                new_user_message = {'role': 'user', 'message_id': str(uuid.uuid4()), 'content': new_message_content}
                
                if self.retrieved_info.get("frames", []):
                    # If we have a selected frame, only process that specific one
                    if hasattr(self, 'selected_frame') and self.selected_frame:
                        # Find the specific frame that matches the selected_frame ID
                        selected_frame_data = next(
                            (frame for frame in self.retrieved_info.get("frames", []) 
                            if frame.get("id") == self.selected_frame),
                            None
                        )
                        
                        # Log the frame data and image URL for debugging
                        print(f"Selected frame ID: {self.selected_frame}")
                        print(f"Selected frame data: {selected_frame_data}")
                        
                        # If we found the frame and it has an image URL, add it
                        if selected_frame_data and selected_frame_data.get("imageUrl"):
                            image_url = selected_frame_data.get("imageUrl")
                            print(f"Using image URL: {image_url}")
                            
                            # Download and compress the image
                            compression_ratio = 0.5  # Adjust as needed
                            image_data, content_type = download_and_compress_image(image_url, compression_ratio)
                            
                            # Add the image to message content (will be either base64 or original URL)
                            new_message_content.append({
                                "type": "image_url",
                                "image_url": {
                                    "url": image_data,
                                }
                            })
                            # Add the message
                            new_user_message['content'] = new_message_content
                            messages.append(new_user_message)
                        else:
                            print(f"Warning: No imageUrl found for selected frame with ID {self.selected_frame}")
                    else:
                        # No selected frame, include all frames (original behavior)
                        print("No specific frame selected, processing all frames")
                        for frame in self.retrieved_info.get("frames", []):
                            if frame.get("imageUrl"):
                                image_url = frame.get("imageUrl")
                                print(f"Including image URL from frame {frame.get('id')}: {image_url}")
                                
                                # Download and compress the image
                                compression_ratio = 0.5  # Adjust as needed
                                image_data, content_type = download_and_compress_image(image_url, compression_ratio)
                                
                                # Add to message content (will be either base64 or original URL)
                                new_message_content.append({
                                    "type": "image_url",
                                    "image_url": {
                                        "url": image_data,
                                    }
                                })
                        
                        if new_message_content:
                            new_user_message['content'] = new_message_content
                            messages.append(new_user_message)




        if 'bedrock' in self.model_name.lower():
            litellm.modify_params = True
            self.aws_access_key = os.getenv("CODEGEN_AWS_ACCESS_KEY_ID", os.getenv("AWS_ACCESS_KEY_ID"))
            self.aws_secret_key = os.getenv("CODEGEN_AWS_SECRET_ACCESS_KEY", os.getenv("AWS_SECRET_ACCESS_KEY"))
            self.aws_region = os.getenv("CODEGEN_AWS_REGION_NAME") or os.getenv('AWS_REGION_NAME', 'us-west-2')

            if not all([self.aws_access_key, self.aws_secret_key]):
                self.file_logger.warning("AWS credentials not found. Some features may not work.")

            os.environ["AWS_ACCESS_KEY_ID"] = self.aws_access_key
            os.environ["AWS_SECRET_ACCESS_KEY"] = self.aws_secret_key
            os.environ["AWS_REGION_NAME"] = self.aws_region

        if 'vertex' in self.model_name.lower():
            VERTEX_PROJECT_ID = os.getenv("VERTEX_PROJECT_ID")
            VERTEX_LOCATION_ID = os.getenv("VERTEX_LOCATION_ID")

            litellm.vertex_project = VERTEX_PROJECT_ID
            litellm.vertex_location = VERTEX_LOCATION_ID 
            
            if isinstance(self.vertex_secret, str):
                self.vertex_secret = json.loads(self.vertex_secret)

            self.kwargs["vertex_credentials"] = self.vertex_secret


        tools = self.file_tools.get_tool_schemas()
        self.file_tools.set_discussion_id(self.discussion_id)
        self.file_logger.info(f"Tools: {tools}")

        if stream:
            # while self.loop_count < MAX_LOOP:
            #     if self.complete:
            #         break
            #     self.loop_count += 1

            async for chunk in self._stream_response_llm_wrapper(messages):
                yield chunk
        else:
            response = await litellm.acompletion(
                model=self.model_name,
                messages=messages,
                stream=False,
                tools=tools,
            )

            self.file_logger.info(f"Response: {response}")
            # update the discussion_so_far with the new message
            if response.choices[0].message.tool_calls:
                # execute the tool call
                tool_call = response.choices[0].message.tool_calls[0]
                tool_name = tool_call.function.name
                tool_args = tool_call.function.arguments
                self.file_tools.file_tool_executor(tool_name, tool_args)
                response_content = f"Tool {tool_name} executed successfully."
            else:
                response_content = response.choices[0].message.content

            messages_to_update = messages + [{"role": "assistant", "content": response_content}]
            self.figma_discussion_store.update_one({"discussion_id": self.discussion_id}, {"$set": {"discussion_so_far": messages_to_update}})
            self.file_logger.info(f"Response: {response_content}")
            # return response_content




    async def _stream_response_llm_wrapper(self, messages: list):

        llm_response = {
            "message_id": str(uuid.uuid4()),
            "role": "assistant",
            "content": "",
            "created_at": generate_timestamp(),
            "message_end": False,
        }
        self.current_operations = {}
        self.previous_tool_call_id = None
        self.previous_tool_data = None
        def _log_tool_data(tool_data,discussion_id):
            """Log tool data to tool_call.log file"""
            try:
                # Configure logger if not already configured
                if not hasattr(self, 'tool_logger'):
                    self.tool_logger = logging.getLogger('tool_call_logger')
                    self.tool_logger.setLevel(logging.INFO)
                    
                    # Check if handler already exists to prevent duplicates
                    if not self.tool_logger.handlers:
                        file_handler = logging.FileHandler(f'tool_call_{discussion_id}.log')
                        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
                        file_handler.setFormatter(formatter)
                        self.tool_logger.addHandler(file_handler)
                
                # Log the data as JSON
                self.tool_logger.info(json.dumps(tool_data))
            except Exception as e:
                print(f"ERROR logging tool data: {str(e)}")
        def toolcall(key, value):
            print(f"DEBUG: key: {key}, value: {value}")
            
            if key == "Tool_Info":
                # Parse the Tool_Info value for streaming updates
                parsed_data, content_updated, self.current_operations = parse_tool_info_streaming(
                    value, 
                    self.current_operations
                )
                
                # Stream update if content has changed
                if content_updated and self.ws_client:
                    # Send a streaming update with current operation state
                    self.previous_tool_call_id = parsed_data["tool_call_id"]
                    self.previous_tool_data ={
                            "message_id": parsed_data["tool_call_id"],
                            "operation": parsed_data["operation"],
                            "file_path": parsed_data["file_path"],
                            "content": parsed_data["content"],
                            "tool_call_id": parsed_data["tool_call_id"],
                            "is_complete": value.get("is_complete", False),
                            "created_at": generate_timestamp()
                            
                        }
                    # Log the tool data to file
                    _log_tool_data(self.previous_tool_data,discussion_id=self.discussion_id)
                    self.ws_client.send_message(
                        message_type="tool_stream", 
                        data=self.previous_tool_data
                    )
            
    
                    
                # Also send the final message when complete
                if value.get("is_complete", False) and self.ws_client:
                    self.ws_client.send_message(
                        message_type="tool_stream", 
                        data={
                            "key": "operation_complete", 
                            "value": parsed_data
                        }
                    )
                    # Clean up tracking after completion
                    if parsed_data["tool_call_id"] in self.current_operations:
                        del self.current_operations[parsed_data["tool_call_id"]]
            else:
                # For other keys, just pass through normally
                pass
                # if self.ws_client:
                #     if self.previous_tool_data:
                #         self.previous_tool_data["is_complete"] = True
                #         self.ws_client.send_message(message_type="tool_stream", data=self.previous_tool_data)

        
        response_stream =  await self.llm.llm_interaction_wrapper(
                    messages=messages,
                    user_prompt=None,
                    system_prompt=None,
                    model=self.model_name,
                    response_format={"type": "text"},
                    function_schemas=self.file_tools.get_tool_schemas(),
                    function_executor=self.file_tools.get_tool_executors(),
                    max_retries=2,
                    stream=True,
                    validation_function=toolcall
                )
        # check the events_queue for any events
        async for content in response_stream:
            if isinstance(content, str):
                llm_response["content"]= llm_response["content"] + content
                yield llm_response

        llm_response["message_end"] = True
        messages.append(llm_response)

        formatted_messages = []
        roles_that_need_to_be_formatted = ["assistant"]
        
        for message in messages:
            if message["role"] in roles_that_need_to_be_formatted:
                if message.get("tool_calls"):
                    assistant_tool_calls = []
                    for tool_call in message.get("tool_calls"):
                        # Check if tool_call is a ChatCompletionMessageToolCall object or a dictionary
                        if hasattr(tool_call, 'id') and hasattr(tool_call, 'function'):
                            # It's a ChatCompletionMessageToolCall object, use attribute access
                            assistant_tool_call = {
                                "id": tool_call.id,
                                "type": "function",
                                "function": {
                                    "name": tool_call.function.name,
                                    "arguments": tool_call.function.arguments
                                }
                            }
                        else:
                            # It's already a dictionary, use dictionary access
                            assistant_tool_call = {
                                "id": tool_call.get("id"),
                                "type": "function",
                                "function": {
                                    "name": tool_call.get("function", {}).get("name"),
                                    "arguments": tool_call.get("function", {}).get("arguments")
                                }
                            }
                        assistant_tool_calls.append(assistant_tool_call)
                    message["tool_calls"] = assistant_tool_calls
                formatted_messages.append(message)
            else:
                formatted_messages.append(message)
 
        self.figma_discussion_store.update_one(
            {"discussion_id": self.discussion_id}, 
            {"$set": {"discussion_so_far": formatted_messages}}
        )
        yield llm_response
        return

    async def _stream_response(self, messages) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream the response from the LLM and handle tool calls."""
        response_stream = await litellm.acompletion(
            model=self.model_name,
            messages=messages,
            stream=True,
            tools=self.file_tools.get_tool_schemas(),
            **self.kwargs
        )

        full_response = ""
        llm_response = {
            "message_id": str(uuid.uuid4()),
            "role": "assistant",
            "content": "",
            "created_at": generate_timestamp(),
            "message_end": False,
        }

        # Tool call tracking


        current_tool_call = None

        tool_calls_buffer = {}         #Ideal mapping of index to tool call id
                                        #example: {
                                        #    0: "{
                                        #        "tool_call_id": "call_uuid",
                                        #        "func_name": "create_file",
                                        #        "func_args": "{}"
                                        #    }",
                                        # }

        async for chunk in response_stream:
            try:
                delta = chunk["choices"][0]["delta"]
                # self.file_logger.info(f"Chunk: {chunk}")
                if hasattr(delta, "tool_calls") and delta.tool_calls:
                    for tool_call in delta.tool_calls:
                        tool_calls_buffer[tool_call.index] = tool_calls_buffer.get(tool_call.index, {})
                        current_tool_call = tool_calls_buffer[tool_call.index]

                        if hasattr(tool_call, "id") and tool_call.id:
                            current_tool_call["tool_call_id"] = tool_call.id
                            current_tool_call["message_id"] = str(uuid.uuid4())
                        if hasattr(tool_call, "function"):
                            if hasattr(tool_call.function, "name") and tool_call.function.name:
                                current_tool_call["func_name"] = tool_call.function.name
                            if hasattr(tool_call.function, "arguments") and tool_call.function.arguments:
                                current_tool_call["func_args"] = current_tool_call.get("func_args", "") + tool_call.function.arguments

                        yield current_tool_call
                        tool_calls_buffer[tool_call.index] = current_tool_call


                # Handle regular content
                content = getattr(delta, "content", "") or ""
                if content:
                    llm_response["content"] = (llm_response.get("content") or "") + content
                    if "FIGMA_EXTRACTION_COMPLETE" in llm_response["content"]:
                        self.complete = True
                    yield llm_response
                    full_response = llm_response["content"]

            except Exception as e:
                self.file_logger.error(f"Error in streaming response: {str(e)}")
                continue

        # Execute tool calls after completion
        if tool_calls_buffer:
            for current_tool_call in tool_calls_buffer.values():
                try:

                    tool_name = current_tool_call["func_name"]
                    tool_args = current_tool_call["func_args"]
                    if tool_args:
                        tool_args = json.loads(tool_args)
                    # Execute the tool
                    status_code, result = await self.file_tools.file_tool_executor(tool_name, tool_args)
                    result_str = str(result) if result is not None else ""

                    # Convert the ChatCompletionMessageToolCall to a serializable dictionary
                    assistant_tool_call = {
                        "id": current_tool_call["tool_call_id"],
                        "type": "function",
                        "function": {
                            "name": tool_name,
                            "arguments": json.dumps(tool_args) if isinstance(tool_args, dict) else tool_args
                        }
                    }

                    tool_assistant_response = {
                        "role": "assistant",
                        "message_id": current_tool_call["message_id"],
                        "tool_calls": [assistant_tool_call]
                    }

                    yield tool_assistant_response

                    messages.append(
                       tool_assistant_response
                    )


                    tool_response = {
                        "message_id": str(uuid.uuid4()),
                        "tool_call_id": current_tool_call["tool_call_id"],
                        "role": "tool",
                        "name": tool_name,
                        "content": result_str
                    }

                    yield tool_response

                    messages.append(tool_response)



                except Exception as e:
                    error_msg = f"Error executing tool {tool_name}: {str(e)}"
                    self.file_logger.info(error_msg)
                    yield {
                        "message_id": str(uuid.uuid4()),
                        "role": "assistant",
                        "content": error_msg,
                        "created_at": generate_timestamp(),
                        "message_end": False,
                        "tool_execution_error": str(e)
                    }
                    full_response = (full_response or "") + f"\n{error_msg}"

        # Final update with message end
        llm_response["message_end"] = True
        llm_response["content"] = full_response or ""

        messages_to_update = messages

        if full_response:
            messages_to_update.append(llm_response)
            yield llm_response

        self.figma_discussion_store.update_one(
            {"discussion_id": self.discussion_id}, 
            {"$set": {"discussion_so_far": messages_to_update}}
        )

        if "FIGMA_EXTRACTION_COMPLETE" in full_response:
            self.complete = True
            yield llm_response
            return
     