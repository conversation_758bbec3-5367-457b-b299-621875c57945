{% extends "base_discussion.prompt" %}

You are an expert software architect.

{% block task_description_common_preface %}
{% if config_state != "configured" %}
As an expert software architect, review the project details, Functional and Non-Fucntional Requirements, System context information provided, and configure this Container following architectural decomposition and interface principles:

{% else %}
You are an expert system architect reviewing the Container for potential reconfiguration.


1. Original Context:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - Project context: {{ bg_info.get('project_context', {}) | tojson(indent=2) }}
   - System context: 
     - Users: {{ bg_info.get('system_context', {}).get('users') | tojson(indent=2) }}
     - External Systems: {{ bg_info.get('system_context', {}).get('external_systems') | tojson(indent=2) }}
     - Containers: {{ bg_info.get('system_context', {}).get('containers') | tojson(indent=2) }}
     - Components: {{ bg_info.get('system_context', {}).get('components') | tojson(indent=2) }}

   - Requirements context:
     {% set req_context = bg_info.get('requirements_context', {}) %}
     - Functional Requirements:
       {% for req in req_context.get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in req_context.get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

2. Current Context (Changes to Review):
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Updated project context: {{ new_bg.get('project_context', {}) | tojson(indent=2) }}
   - Updated system context:
     - Users: {{ new_bg.get('system_context', {}).get('users') | tojson(indent=2) }}
     - External Systems: {{ new_bg.get('system_context', {}).get('external_systems') | tojson(indent=2) }}
   - Updated requirements context:
     {% set new_req_context = new_bg.get('requirements_context', {}) %}
     - Functional Requirements:
       {% for req in new_req_context.get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in new_req_context.get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

3. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log', []) | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason', '') }}
4. User Interaction History:
   - Previous user inputs: {{ details_for_discussion.get('user_interaction').get('input') | tojson(indent=2) }}


Analysis Instructions:

1. Impact Analysis:
   - Compare original and new functional and non-functional requirements, system_context, and project context.
   - Identify potential changes in user interactions and external system interactions.

2. Required Changes:
  -  Modify Existing container, add or modify or remove any required components, corresponding interfaces  based on the above analysis.
  - Follow the below guidelines as a reference while modifying the existing containers and its children.
  - IMPORTANT: Set the "changes_needed" flag to true if modifications are done else set to false.

{% endif %} 

GENERIC GUIDELINES:

1.Provide a description of the container - explaining its core functionalities its role in the overall system and its interaction with other containers, users and external systems. 

2. Tech Stack Selection:
   - Review the container's purpose, responsibilities, and project details
   - Consider and evaluate potential technology options such as:
     * Programming languages and frameworks
     * Infrastructure components
     * Integration technologies
   - Select the optimal technology stack and provide a brief rationale for your decision
   
3.User Interactions:
     * Identify which user types from the system context interact with this container
     * Define the container's role in supporting user needs
   
4.External System Interactions:
     * Identify which external systems this container interacts with
     * Define the container's interaction with the external systems
   Create relationship with External System:
    - Create USES Relationship with the external container - with Properties:
    type: Fixed value "USES" indicating external system dependency
    name: External service or API name that is being consumed
    description: Purpose and functionality being used from the external service
    source id: Node id of the current container consuming the service
    target id: Node id of the external container providing the service
    technology: Protocol or method used to integrate with the external service
   
   Database Configuration:
      - Set HasDatabase property TRUE only if this container has a Database child node.

      ### Database Design
      Based on the container's requirements, create Databases as child nodes of this container and configure them following the below guidelines:

      - **Database Selection**: Choose appropriate database types
      - **Schema Design**: Define entities, relationships, and constraints
      - **Connection Configuration**: Set up secure database connections
      - **Performance Optimization**: Configure indexing and query optimization

5.Component Creation:
   - Analyze container responsibilities and create Components as child nodes considering:
      Architecture strategy and 
      Fucntional and Non-Fucntional requirements 
   - Ensure to create one or more components
   - For new Components:
     * Assign IDs starting with 'NEW-ARCH-1', incrementing for each new Component
     * Example: NEW-ARCH-1, NEW-ARCH-2, etc.

6. Component Properties:
   Each Component must have:
   - Title: Clear, descriptive name
   - Description: Detailed purpose and responsibilities
   - Type: Component classification

7. Define Interfaces as relationships between components. Interfaces are software interfaces that the two components use to communicate with each other, like APIs, routes, message bus, etc:  
    - Ensure appropriate interfaces are defined between the components within this container - an Interface should be defined as a new relationship between them.
    - For each interface, determine and provide:
     * Appropriate interface type based on:
       - Communication patterns required
       - Performance requirements
       - Reliability needs
       - Integration constraints
     * Clear descriptive name 
        -- Examples of good interface names
        * "SMS notification Interface for Weather Alerts"
        * "API for accessing User settings from database"
        * "Retrieve weather data from backend"
     * Detailed description

8. Repository Name:
   The container's repository name - provide suitable name based on its functionality:
   - Repository Name: <container-name-in-kebab-case>

9.Map Implemented Requirements
- Analyze the Container Purpose and Responsibilities 
- Review the requirements given below and identify the functional requirements that this container possibly implements 
- Add only those requirement node IDs to the ImplementedRequirementIDs property, as an array of integers 

       {{ details_for_discussion.get('functional_requirements') | tojson(indent=2) }}
       {{ details_for_discussion.get('architectural_requirements') | tojson(indent=2) }}

10. Generate a C4 Container diagram using Mermaid syntax.
  -Please generate a Mermaid code without any syntax error, ensure to avoid double headed arrow.

{% block mermaid_diagram_requirements %}
    {% include 'includes/mermaid_guidance.jinja2' %}
{% endblock %}



 -C4 Container Diagram Example given below - use for reference purpose only - generate diagrams only based on the given Container details.
```
graph TB
    %% External Systems
    ApiGateway[API Gateway]
    CurrencyService[Currency Service]
    LoggingService[Logging Service]
    
    subgraph MobileApp[Mobile Application Container]
        UIComponent[UI Component]
        LogicComponent[Business Logic Component]
        ApiClientComponent[API Client Component]
    end
    
    %% External Interactions
    ApiGateway -->|"REST API"| ApiClientComponent
    ApiClientComponent -->|"REST"| CurrencyService
    ApiClientComponent -->|"Logging"| LoggingService
    
    %% Internal Component Interactions
    UIComponent -->|"User Events"| LogicComponent
    LogicComponent -->|"API Calls"| ApiClientComponent
    
    %% Styling
    classDef container fill:#1168bd,stroke:#0b4884,color:#ffffff
    classDef component fill:#85bbf0,stroke:#5d82a8,color:#000000
    classDef external fill:#666666,stroke:#333333,color:#ffffff
    
    class MobileApp container
    class UIComponent,LogicComponent,ApiClientComponent component
    class ApiGateway,CurrencyService,LoggingService external
```

Change Needed: 
   - Set to False if changes are not required.


Change Log :
    - capture history of changes.

{% endblock %}

{% block autoconfig %}
Create a comprehensive c4 Container level configuration based on the guidelines provided above.
{% endblock %}


{% block node_details_interactive_reconfig %}
Follow the above guidelines and propose the required changes based on your analysis.
Take feedback from the user on the proposal and consolidate. Make sure to capture all the changes in new child nodes or modified child nodes if the exixting nodes do not include them

{% endblock %}

{% block auto_reconfig %}
Create updated Containers based on the above guidelines.
{% endblock %}

{% block node_details_interactive_reconfig_update_specifications %}
Container configuration, including its internal components, adhering to C4 model principles. Suggest improvements based on software architecture best practices and C4 modeling guidelines, particularly focusing on appropriate component decomposition.
{% endblock %}

{% block information_about_task %}
    {{ super() }}
    Existing relationships with other containers: {{ details_for_discussion.get('existing_uses_relationships') | tojson(indent=2) }}
    Existing components within this container: {{ details_for_discussion.get('existing_components') | tojson(indent=2) }}
{% endblock %}

{% block background_information %}
    {{ super() }}
    Project Details :  {{ details_for_discussion.get('project_details') }} 
     System Context Information:
    Users: {{ details_for_discussion.system_context.users | join(', ') }}
    
    External Systems: {{ details_for_discussion.system_context.external_systems | join(', ') }}
    
    System Context Description:
    {{ details_for_discussion.system_context.description }}
    {{ details_for_discussion.project_details.architecture_strategy }}
    Architectural Requirements: {{ details_for_discussion.get('architectural_requirements') | tojson(indent=2) }}
    Functional Requirements: {{ details_for_discussion.get('functional_requirements') | tojson(indent=2) }}
    
{% endblock %}