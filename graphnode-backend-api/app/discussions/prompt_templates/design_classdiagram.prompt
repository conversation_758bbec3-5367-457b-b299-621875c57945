{% extends "base_discussion.prompt" %}

{% block task_description_common_preface %}

{% if config_state != "configured" %}
Review the purpose and functionalities of the component, its interface details:
- Identify suitable classes for the given component
- Design suitable UML class diagram to show the structure and relationships between those classes - create as child nodes of type ClassDiagram.
- Please generate Mermaid code without any syntax error

{% block mermaid_diagram_requirements %}
    {% include 'includes/mermaid_guidance.jinja2' %}
{% endblock %}

Here is a properly formatted example - this is for reference purpose only - generate Class Diagram based on the given component details.

{% else %}
You are an expert system architect reviewing the design behaviour for potential reconfiguration.

Current Configuration Context:

1. Existing State:
   - Current configuration: {{ details_for_discussion.get('original_node') | tojson(indent=2) }}
   - Configuration state: {{ details_for_discussion.get('config_state') }}

2. Original Context:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - System context: 
     - class_diagrams: {{ bg_info.get('design_context', {}).get('class_diagrams') | tojson(indent=2) }}
     - parent_component_node: {{ bg_info.get('system_context', {}).get('parent_component_node') | tojson(indent=2) }}
   

3. Current Context (Changes to Review):
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Updated system context:
     - class_diagrams: {{ new_bg.get('design_context', {}).get('class_diagrams') | tojson(indent=2) }}
     - parent_component_node: {{ new_bg.get('system_context', {}).get('parent_component_node') | tojson(indent=2) }}
  

4. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log', []) | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason', '') }}
5. User Interaction History:
   - Previous user inputs: {{ details_for_discussion.get('user_interaction').get('input') | tojson(indent=2) }}

6. Current Component Configuration:
   Existing Containers: {{ details_for_discussion.get('parent_component') | tojson(indent=2) }}
   Algorithms: {{ details_for_discussion.get('algorithms') | tojson(indent=2) }}

Analysis Instructions:


1. Component Architecture Analysis:
   - Review the parent component against new requirements
   - Evaluate component responsibilities and boundaries
   - Assess need for new algorithm components or modifications

2. Required Changes:
   Modify Existing class Diagrams, or add any required class Diagrams following the below guidelines 

{% endif %}


```
classDiagram
class Order {
    +int orderId
    +String customerName
    +Date orderDate
    +validate()
    +process()
}
class Payment {
    +int paymentId
    +double amount
    +processPayment()
}
class Shipping {
    +int shippingId
    +String address
    +shipOrder()
}
Order ..> Payment: depends on
Order *-- OrderDetails: contains
Order o-- Shipping: uses
```
Ensure your diagram includes all relevant classes, attributes, methods, and relationships.
The output MUST be a valid Mermaid class diagram with:
- Proper class definitions
- Valid relationship syntax (e.g., "--", "-->", "..>", "o--", "*--")

Change Needed: 
    - Set to True if changes are required.

Change Log :
    - capture history of changes.

{% endblock %}

{% block autoconfig %}
Your task is to create a detailed class diagram covering all necessary classes and relationships for the component. 
{% endblock %}

{% block auto_reconfig %}
Create or update the funtional and / or non-functional requirements following the above guidelines.
{% endblock %}


{% block node_details_interactive_reconfig %}
class diagram (as a Mermaid chart)
{% endblock %}

{% block node_details_interactive_reconfig_update_specifications %}
Analyze their specific workflow management needs and their requirements, then derive appropriate class structures and relationships that align with UML best practices. Provide the updated diagram as a complete Mermaid class diagram.
{% endblock %}

{% block information_about_task %}
Here is the architectural node for the component for which the detail design node is being configured:
{{details_for_discussion.get('architecture_node') | tojson(indent=2)}}

{{details_for_discussion.get('existing_class_diagrams') | tojson(indent=2)}}
Current component interfaces: {{ details_for_discussion.get('interface_details') | tojson(indent=2) }}
{% endblock %}

{% block background_information %}
None
{% endblock %}