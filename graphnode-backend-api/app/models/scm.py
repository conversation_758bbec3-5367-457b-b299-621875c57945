from pydantic import BaseModel
from typing import Optional, List, Dict
from enum import Enum
from datetime import datetime


class ACCESS_TOKEN_PATH(str, Enum):
    SCM = "scm_configurations"
    PERSONAL = "users_github"
    KAVIA_MANANGED = "settings"
    
class SCMType(str, Enum):
    GITHUB = "github"
    GITLAB = "gitlab"

class SCMAuthType(str, Enum):
    OAUTH = "oauth"
    PAT = "personal_access_token"
    SSH = "ssh"

class SCMCredentials(BaseModel):
    auth_type: SCMAuthType
    access_token: Optional[str] = None
    refresh_token: Optional[str] = None
    token_expires_at: Optional[datetime] = None
    organization: str
    ssh_key: Optional[str] = None
    ssh_key_passphrase: Optional[str] = None

class SCMConfiguration(BaseModel):
    scm_type: SCMType
    credentials: SCMCredentials
    encrypted_scm_id: Optional[str] = None
    webhook_url: Optional[str] = None
    webhook_secret: Optional[str] = None
    api_url: Optional[str] = None  # For self-hosted GitLab instances
    user_id: Optional[str] = None
class SCMResponse(BaseModel):
    status: str
    message: str
    data: Optional[Dict] = None 