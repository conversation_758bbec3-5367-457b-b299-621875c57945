from pydantic import BaseModel, EmailStr, Field
from app.core.Settings import settings
from app.utils.hash import encrypt_tenant_id
from typing import Optional, List
from datetime import datetime
class CognitoUser(BaseModel):
    organization_id: str = Field(default=encrypt_tenant_id(settings.KAVIA_ROOT_TENANT_ID), description="Organization ID")
    email: EmailStr = Field(..., description="User's email address (used as the username in Cognito)")
    password: str = Field(..., description="User's password")
    
class SignUpUser(CognitoUser):
    name: str = Field(..., description="User's full name")
    designation: str = Field(..., description="User's designation")
    department: str = Field(..., description="User's department")
    has_accepted_terms: bool = Field(default=False, description="Indicates if the user has accepted the terms and conditions")
    referral_code: Optional[str] = Field(None, description="Optional referral code")  

class GoogleAuthUser(BaseModel):
    organization_id: str = Field(default=encrypt_tenant_id(settings.KAVIA_ROOT_TENANT_ID), description="Organization ID")
    email: EmailStr = Field(..., description="User's email address")
    name: str = Field(..., description="User's full name")
    picture: Optional[str] = Field(None, description="User's profile picture URL")
    sub: Optional[str] = Field(None, description="Google subject identifier")
    designation: str = Field("", description="User's designation")
    department: str = Field("", description="User's department")

class ReferralUsage(BaseModel):
    """Individual referral usage record"""
    user_id: str
    user_email: str
    user_name: str
    referred_at: datetime
    verified_at: Optional[datetime] = None
    status: str = "pending"
    

class ReferralStats(BaseModel):
    total_referrals: int = 0
    verified_referrals: int = 0
    last_referral_date: Optional[datetime] = None
    referral_history: List[ReferralUsage] = []

class ReferredBy(BaseModel):
    user_id: str
    referral_code: str
    organization_id: str
    referred_at: datetime

class CreateReferralCodeRequest(BaseModel):
    user_id: str
    force_regenerate: Optional[bool] = False

class ReferralCodeResponse(BaseModel):
    referral_code: str
    user_id: str
    user_name: str
    organization_id: str
    created_at: datetime
    stats: ReferralStats

class ValidateReferralResponse(BaseModel):
    valid: bool
    referrer_name: Optional[str] = None
    referrer_organization: Optional[str] = None
    message: Optional[str] = None
