from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List, Union
from datetime import datetime
from enum import Enum

class PublicProjectModel(BaseModel):
    """Model representing a public project."""
    project_id: int
    title: str
    tenant_id: str
    description: Optional[str] = None
    created_at: Optional[str] = None
    created_by: Optional[str] = None
    creator_name: Optional[str] = None
    creator_email: Optional[str] = None
    creator_picture: Optional[str] = None
    made_public_at: Optional[str] = None
    made_public_by: Optional[str] = None
    visibility: Optional[str] = None
class ProjectVisibilityRequest(BaseModel):
    """Request model for changing project visibility."""
    project_id: int
    make_public: bool = True

class FeaturesInfo(BaseModel):
    # id: str = Field(..., description="Unique identifier for the feature [Example:'1']")
    name: str = Field(..., description="Name of the feature")
    description: str = Field(..., description="Detailed description of the feature")
    isEnabled: bool = Field(..., description="Flag indicating if the feature is enabled [Example:true]")

# class Colors(BaseModel):
#     primary: str = Field("#F15A24", description="Primary color of the project")
#     secondary: str = Field("#2563EB", description="Secondary color of the project")
#     accent: str = Field("#64748B", description="Accent color of the project")

class Colors(BaseModel):
    primary: str = Field(..., description="Primary color of the project")
    secondary: str = Field(..., description="Secondary color of the project") 
    accent: str = Field(..., description="Accent color of the project")
    background: str = Field(..., description="Background color of the project")

class ContainerDetails(BaseModel):
    features: List[FeaturesInfo] = Field(..., description="List of features for the whole project")
    colors: Colors = Field(None, description="Color scheme for frontend containers")
    theme: str = Field(None, description="Theme: light|dark|custom|selectable")
    layoutDescription: str = Field(None, description="Layout description for frontend")
    style: str = Field(None, description="Style description for frontend, e.g. modern, minimalistic, etc.")

class BEContainerDetails(BaseModel):
    features: List[FeaturesInfo] = Field(..., description="List of features for the whole project")

# Framework Enums
class FrontendFramework(str, Enum):
    REACT = "React"
    VUE = "Vue"
    ANGULAR = "Angular"
    SVELTE = "Svelte"
    NEXTJS = "NextJS"
    NUXT = "Nuxt"
    QWIK = "Qwik"
    ASTRO = "Astro"
    REMIX = "Remix"
    REMOTION = "Remotion"
    LIGHTNINGJS = "LightningJS"

class BackendFramework(str, Enum):
    FASTAPI = "FastAPI"
    DJANGO = "Django"
    EXPRESS = "Express"
    FLASK = "Flask"

class DatabaseFramework(str, Enum):
    POSTGRESQL = "PostgreSQL"
    MONGODB = "MongoDB"
    MYSQL = "MySQL"
    SQLITE = "SQLite"

class ProjectOverview(BaseModel):
    project_name: str = Field(..., description="Project name")
    description: str = Field(..., description="Project description")
    backend_framework: Optional[BackendFramework] = Field(None, description="Select appropriate Backend framework")
    frontend_framework: Optional[FrontendFramework] = Field(None, description="Select appropriate Frontend framework")
    database_framework: Optional[DatabaseFramework] = Field(None, description="Select appropriate DB type")

class ContainerInfo(BaseModel):
    container_name: str = Field(..., description="Container name")
    description: str = Field(..., description="Container description")
    interfaces: str = Field(..., description="Container interfaces")
    dependent_containers: List[str] = Field(default_factory=list, description="List of dependent containers")
    container: Optional[ContainerDetails] = Field(None, description="Additional container details for frontend")

class FrontEndContainerInfo(BaseModel):
    container_name: str = Field(..., description="Container name")
    description: str = Field(..., description="Container description")
    interfaces: str = Field(..., description="Container interfaces")
    dependent_containers: List[str] = Field(default_factory=list, description="List of dependent containers, example : The backend container name if available")
    container: ContainerDetails = Field(None, description="Additional container details for frontend")

class BackendContainerInfo(BaseModel):
    container_name: str = Field(..., description="Container name")
    description: str = Field(..., description="Container description")
    interfaces: str = Field(..., description="API interfaces information")
    dependent_containers: List[str] = Field(default_factory=list, description="List of dependent containers,  example : The database container name if available")
    container: BEContainerDetails = Field(None, description="Additional container details for frontend")

class DbContainerInfo(BaseModel):
    container_name: str = Field(..., description="Container name")
    description: str = Field(..., description="Container description")
    interfaces: str = Field(..., description="DB schema information")
    dependent_containers: List[str] = Field(default_factory=list, description="List of dependent containers")

class EnhancedProjectStructure(BaseModel):
    overview: ProjectOverview = Field(..., description="Project overview information")
    frontend: Optional[ContainerInfo] = Field(None, description="Frontend container configuration")
    backend: Optional[ContainerInfo] = Field(None, description="Backend container configuration")
    database: Optional[ContainerInfo] = Field(None, description="Database container configuration")



class StartProjectInit(BaseModel):
    usercomment: str
    type: Optional[str] = None  
    title: Optional[str] = None 
    frontend_frameworks: Optional[str] = None
    backend_frameworks: Optional[str] = None
    platform: Optional[Union[str, List[str]]] = None

class ArchitecturePattern(str, Enum):
    MonolithicApplication = "monolithic-application"
    MonolithicService = "monolithic-service"
    MultiContainerSingleComponent = "multi-container-single-component"
    MultiContainerService = "multi-container-service"
    Adaptive = "adaptive"

class ProjectInfoDirectCodeGen(BaseModel):
    description: str = Field(..., description="Description of the project")
    features: List[FeaturesInfo] = Field(..., description="List of features associated with the project")
    layoutDescription: str = Field(..., description="Description of the project layout how it should look in the UI")
    projectTitle: str = Field(..., description="Brief name for the project (Should be very brief less than 20 charactes)")
    colors: Optional[Colors] = Field(default_factory=Colors, description="Color scheme of the project.")

class ProjectInfoManual(BaseModel): 
    description: str = Field(..., description="Description of the project")
    features: List[FeaturesInfo] = Field(..., description="List of features associated with the project")
    layoutDescription: str = Field(..., description="Description of the project layout how it should look in the UI")
    colors: Optional[Colors] = Field(default_factory=Colors, description="Color scheme of the project.")
    architecturePattern: Optional[ArchitecturePattern] = Field(..., description="Architecture pattern for the project")            

# NEW: Updated ProjectInfoDirectCodeGen model
class ProjectInfoDirectCodeGen(BaseModel):
    overview: ProjectOverview = Field(..., description="Project overview information")
    frontend: FrontEndContainerInfo = Field(None, description="Frontend container configuration")
    backend: Optional[BackendContainerInfo] = Field(None, description="Backend container configuration")
    database: Optional[DbContainerInfo] = Field(None, description="Database container configuration")
    layoutDescription: str = Field(..., description="Description of the project layout how it should look in the UI")


# Keep the old model as legacy if needed elsewhere
class ProjectInfoDirectCodeGenLegacy(BaseModel):
    description: str = Field(..., description="Description of the project")
    features: List[FeaturesInfo] = Field(..., description="List of features associated with the project")
    layoutDescription: str = Field(..., description="Description of the project layout how it should look in the UI")
    projectTitle: str = Field(..., description="Brief name for the project (Should be very brief less than 20 characters)")
    colors: Optional[Colors] = Field(default_factory=Colors, description="Color scheme of the project.")


ArcitectureInfo = """AVAILABLE ARCHITECTURE PATTERNS (STRICT - ONLY THESE ARE ALLOWED):
a) "monolithic-application"
b) "monolithic-service"
c) "multi-container-single-component"
d) "multi-container-service"
e) "adaptive"

Architectural Pattern Selection Guidelines:
1. monolithic-application suitable for:
    - Single container with single component
    - All functionality in one unified application
    - Single deployment unit
    - Unified technology stack
    - Self-contained, no external API exposure
    - Direct UI interactions only
    - Suitable for standalone applications

2. monolithic-service suitable for:
    - Single container with single component
    - All functionality in one unified service
    - Single deployment unit
    - Unified technology stack
    - Exposes well-defined external APIs
    - Designed for service integration
    - Suitable for reusable business services

3. multi-container-single-component suitable for:
    - Multiple containers, each with exactly one component
    - Each container represents a distinct functional domain
    - Independent deployment per container
    - Clear interfaces between containers
    - One focused component per functional area

4. multi-container-service suitable for:
    - Multiple containers, each encapsulating distinct functionality
    - Each container has exactly one component by default that implements all container functionality
    - Each container must expose well-defined provider interfaces
    - Clear separation of concerns between containers
    - Independent deployment and scaling per service
    - Well-defined interfaces between services
    - External system interactions defined through USES relationships
    - Designed for complex service-oriented architectures

5. adaptive suitable for:
    - Multiple containers with multiple components
    - Flexible component organization
    - Independent scaling and deployment
    - Technology choices per component"""