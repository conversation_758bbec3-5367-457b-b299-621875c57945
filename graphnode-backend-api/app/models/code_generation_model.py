from pydantic import BaseModel, <PERSON>
from typing import Optional, Dict
from dataclasses import dataclass
from datetime import datetime
import uuid
from app.utils.datetime_utils import generate_timestamp

class CodeGenerationRequest(BaseModel):
    project_repository_url: str = Field(..., description="The URL of the Git repository to clone.")
    # Add more fields here for optional parameters:
    branch: Optional[str] = Field(None, description="The specific branch to clone (optional).")
    ssh_key: Optional[str] = Field(None, description="SSH private key string for authentication (optional).")

class AgentParams(BaseModel):
    llm_model: str
    agent_name: str = Field(default="CodeGeneration", description="Name of the agent to use")

@dataclass
class Message:
    content: str
    sender: str
    timestamp: str = None
    user_id: str = "admin"
    id: str = None
    status: str = "RESOLVED"
    requires_resolution: bool = False
    resolution_id: Optional[str] = None
    parent_id: Optional[str] = None
    metadata: Dict = None
    msg_type: str = "SYSTEM"

    def __init__(self, content, sender, timestamp=None, user_id = "admin", id=None):
        self.content = content
        self.sender = sender
        self.timestamp = timestamp or generate_timestamp()
        self.user_id = user_id
        self.id = id or str(uuid.uuid4())

    def to_dict(self):
        return_dict = {
            "content": self.content,
            "sender": self.sender,
            "timestamp": self.timestamp,
            "user_id":self.user_id
        }
        if self.id:
            return_dict["id"] = self.id
        if self.status:
            return_dict["status"] = self.status
        if self.requires_resolution:
            return_dict["requires_resolution"] = self.requires_resolution
        if self.resolution_id:
            return_dict["resolution_id"] = self.resolution_id
        if self.parent_id:
            return_dict["parent_id"] = self.parent_id
        if self.metadata:
            return_dict["metadata"] = self.metadata
        if self.msg_type:
            return_dict["msg_type"] = self.msg_type
            
        return return_dict
    
@dataclass
class ChatContext:
  project_id: int
  user_id: str
  message: list | str
  discussion_id: int
  agent_name: str
  session: dict = None



