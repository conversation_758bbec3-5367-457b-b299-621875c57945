from datetime import datetime, timedelta
from typing import Dict, Optional
from .models import DiscussionTiming, TimingSummary
from .formatters import TimingReportFormatter
from app.telemetry.logger_config import get_logger
import os
from app.connection.establish_db_connection import get_mongo_db

class DiscussionTimer:
    def __init__(self):
        self.discussion_timings: Dict[str, DiscussionTiming] = {}
        self.current_discussions: Dict[str, DiscussionTiming] = {}
        self.logger = get_logger(__name__)
        self.by_node_type: Dict[str, Dict] = {
            "architecture": {"count": 0, "total_duration": timedelta()},
            "component": {"count": 0, "total_duration": timedelta()},
            "interface": {"count": 0, "total_duration": timedelta()}
        }
        self.by_discussion_type: Dict[str, Dict] = {
            "configuration": {"count": 0, "total_duration": timedelta()},
            "design": {"count": 0, "total_duration": timedelta()},
            "implementation": {"count": 0, "total_duration": timedelta()}
        }
        self.total_discussions = 0
        self.total_duration = timedelta()
        
    def start_discussion(self, discussion_id: str, node_type: str, discussion_type: str) -> None:
        """Start timing a discussion"""
        self.current_discussions[discussion_id] = DiscussionTiming(
            discussion_id=discussion_id,
            node_type=node_type,
            discussion_type=discussion_type,
            start_time=datetime.now()
        )
        self.logger.debug(f"Started timing discussion {discussion_id}")
    
    def end_discussion(self, discussion_id: str) -> Optional[timedelta]:
        """End timing a discussion and record its duration"""
        if discussion_id in self.current_discussions:
            timing = self.current_discussions[discussion_id]
            timing.end_time = datetime.now()
            timing.duration = timing.end_time - timing.start_time
            
            self.discussion_timings[discussion_id] = timing
            del self.current_discussions[discussion_id]
            
            self.logger.debug(f"Ended timing discussion {discussion_id}, duration: {timing.duration}")
            
            # Update statistics
            self.total_discussions += 1
            self.total_duration += timing.duration
            
            # Update by node type
            if timing.node_type in self.by_node_type:
                self.by_node_type[timing.node_type]["count"] += 1
                self.by_node_type[timing.node_type]["total_duration"] += timing.duration
            
            # Update by discussion type
            if timing.discussion_type in self.by_discussion_type:
                self.by_discussion_type[timing.discussion_type]["count"] += 1
                self.by_discussion_type[timing.discussion_type]["total_duration"] += timing.duration
            
            return timing.duration
        return None

    def get_timing_summary(self) -> TimingSummary:
        """Generate a summary of all discussion timings"""
        summary = TimingSummary(
            total_discussions=self.total_discussions,
            total_duration=self.total_duration,
            by_node_type=self.by_node_type,
            by_discussion_type=self.by_discussion_type,
            discussions=list(self.discussion_timings.values()),
            task_id=None
        )
        
        return summary

    def format_summary(self, summary: TimingSummary) -> str:
        """Format the timing summary into a readable string"""
        return TimingReportFormatter.format_summary(summary)

    def format_datadog_summary(self, summary: Dict, task_id: Optional[str] = None) -> Dict:
        """Format a timing summary in Datadog format"""

        db = get_mongo_db().db
        result = db["tf_tasks"].find_one({"_id": task_id})
        if result is None:
            return None
        
        eta_info = result.get('eta_info', 0)

    
        total_duration_seconds = int(summary['total_duration'].total_seconds())
        
        # Convert timedelta to seconds for each type
        node_types = {
            node_type: {
                "count": data["count"],
                "total_duration": int(data["total_duration"].total_seconds())
            }
            for node_type, data in summary['by_node_type'].items()
        }
        
        discussion_types = {
            disc_type: {
                "count": data["count"],
                "total_duration": int(data["total_duration"].total_seconds())
            }
            for disc_type, data in summary['by_discussion_type'].items()
        }
        
        # Format discussions list
        discussions = []
        if 'discussions' in summary:
            for disc in summary['discussions']:
                if hasattr(disc, 'to_dict'):
                    discussions.append(disc.to_dict())
                else:
                    # Handle dictionary format
                    discussions.append({
                        "discussion_id": disc.get("discussion_id"),
                        "node_type": disc.get("node_type"),
                        "discussion_type": disc.get("discussion_type"),
                        "start_time": disc.get("start_time"),
                        "end_time": disc.get("end_time"),
                        "duration_seconds": int(disc.get("duration", timedelta()).total_seconds())
                    })
        
        return {
            "ddsource": "kavia_ai",
            "service": "auto_config",
            "ddtags": f"task_id:{task_id}" if task_id else "",
            "hostname": os.getenv('HOSTNAME', 'backend-api'),
            "status": "info",
            "task_id": task_id,
            "total_discussions": summary['total_discussions'],
            "total_duration_seconds": total_duration_seconds,
            "by_node_type": node_types,
            "by_discussion_type": discussion_types,
            "discussions": discussions,
            "metrics": {
                "discussions.count": summary['total_discussions'],
                "duration.seconds": total_duration_seconds
                # "discussions.by_node_type.architecture": node_types["architecture"]["count"],
                # "discussions.by_node_type.component": node_types["component"]["count"],
                # "discussions.by_node_type.interface": node_types["interface"]["count"],
                # "discussions.by_type.configuration": discussion_types["configuration"]["count"],
                # "discussions.by_type.design": discussion_types["design"]["count"],
                # "discussions.by_type.implementation": discussion_types["implementation"]["count"]
            },
            "eta_info": eta_info,
            "message": f"Task {task_id} completed with {summary['total_discussions']} discussions in {total_duration_seconds} seconds"
        }