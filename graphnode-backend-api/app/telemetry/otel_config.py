import os
from opentelemetry import trace, metrics
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.sdk.metrics import MeterProvider
from opentelemetry.exporter.jaeger.thrift import <PERSON><PERSON>gerExporter
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from opentelemetry.instrumentation.pymongo import PymongoInstrumentor
from opentelemetry.sdk.resources import Resource
from opentelemetry.semconv.resource import ResourceAttributes
import logging

# Configure logging for OpenTelemetry
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def setup_telemetry():
    """Initialize OpenTelemetry configuration"""
    
    # Create resource with service information
    resource = Resource.create({
        ResourceAttributes.SERVICE_NAME: "kavia-ai-api",
        ResourceAttributes.SERVICE_VERSION: "1.0.0",
        ResourceAttributes.SERVICE_NAMESPACE: "kavia",
    })
    
    # Set up tracing
    trace_provider = TracerProvider(resource=resource)
    trace.set_tracer_provider(trace_provider)
    
    # Configure Jaeger exporter (you can change this to console exporter for testing)
    jaeger_exporter = JaegerExporter(
        agent_host_name=os.getenv("JAEGER_HOST", "localhost"),
        agent_port=int(os.getenv("JAEGER_PORT", 6831)),
    )
    
    # Add span processor
    span_processor = BatchSpanProcessor(jaeger_exporter)
    trace_provider.add_span_processor(span_processor)
    
    # Set up metrics
    metrics.set_meter_provider(MeterProvider(resource=resource))
    
    logger.info("OpenTelemetry configured successfully")
    
    return trace.get_tracer(__name__)

def instrument_app(app):
    """Instrument FastAPI app with OpenTelemetry"""
    
    # Auto-instrument FastAPI
    FastAPIInstrumentor.instrument_app(app)
    
    # Auto-instrument requests library
    RequestsInstrumentor().instrument()
    
    # Auto-instrument PyMongo
    PymongoInstrumentor().instrument()
    
    logger.info("FastAPI app instrumented with OpenTelemetry")