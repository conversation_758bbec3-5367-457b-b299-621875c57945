from app.repository.mongodb.base_repository import BaseMongoRepository
from app.repository.mongodb.client import Database
from bson import ObjectId
from typing import List, Optional, Dict, Any

class NotificationRepository(BaseMongoRepository):
    def __init__(self):
        super().__init__('notifications')
        self.collection: str = "notifications"

    async def create_notification(self, notification_data: dict, db: Database) -> bool:
        """Creates a new notification."""
        try:
            notification_dict = notification_data.dict(by_alias=True)
            collection = await self.get_collection(db)
            await collection.insert_one(notification_dict)
            return True
        except Exception as e:
            print(f"Error creating notification: {e}")
            return False
    async def delete_project_notifications(self, project_id: int, db: Database) -> bool:
        """Deletes all notifications for a project."""
        try:
            collection = await self.get_collection(db)
            result = await collection.delete_many({"data.project_id": project_id})
            return result.deleted_count > 0
        except Exception as e:
            print(f"Error deleting project notifications: {e}")
            return False

    async def mark_all_read(self, user_id: str, db: Database) -> bool:
        """Mark all notifications as read for a user."""
        try:
            collection = await self.get_collection(db)
            result = await collection.update_many(
                {"receiver_id": user_id, "is_read": False},
                {"$set": {"is_read": True}}
            )
            return result.modified_count > 0
        except Exception as e:
            print(f"Error marking notifications as read: {e}")
            return False

    async def get_notifications(
        self, 
        user_id: str, 
        is_read: Optional[bool], 
        page: int, 
        limit: int, 
        db: Database
    ) -> List[Dict[str, Any]]:
        """Get notifications for a user with pagination."""
        try:
            filter_dict = {"receiver_id": user_id}
            if is_read is not None:
                filter_dict["is_read"] = is_read

            skip = (page - 1) * limit
            collection = await self.get_collection(db)
            cursor = collection.find(filter_dict).sort([("_id", -1)]).skip(skip).limit(limit)
            return await cursor.to_list(length=None)
        except Exception as e:
            print(f"Error getting notifications: {e}")
            return []

    async def get_unread_count(self, user_id: str, db: Database) -> int:
        """Get count of unread notifications for a user."""
        try:
            collection = await self.get_collection(db)
            return await collection.count_documents({"receiver_id": user_id, "is_read": False})
        except Exception as e:
            print(f"Error getting unread count: {e}")
            return 0

    async def mark_as_read(self, notification_id: str, db: Database) -> bool:
        """Mark a specific notification as read."""
        try:
            query = {"_id": ObjectId(notification_id)}
            collection = await self.get_collection(db)
            notification = await collection.find_one(query)
            if not notification:
                raise ValueError("Notification not found")

            result = await collection.update_one(query, {"$set": {"is_read": True}})
            return result.modified_count > 0
        except Exception as e:
            print(f"Error marking notification as read: {e}")
            return False

    async def delete_notification(self, notification_id: str, db: Database) -> bool:
        """Delete a specific notification."""
        try:
            query = {"_id": ObjectId(notification_id)}
            collection = await self.get_collection(db)
            notification = await collection.find_one(query)
            if not notification:
                raise ValueError("Notification not found")

            result = await collection.delete_one(query)
            return result.deleted_count > 0
        except Exception as e:
            print(f"Error deleting notification: {e}")
            return False

    async def delete_all_notifications(self, user_id: str, db: Database) -> bool:
        """Delete all notifications for a user."""
        try:
            collection = await self.get_collection(db)
            result = await collection.delete_many({"receiver_id": user_id})
            return result.deleted_count > 0
        except Exception as e:
            print(f"Error deleting all notifications: {e}")
            return False