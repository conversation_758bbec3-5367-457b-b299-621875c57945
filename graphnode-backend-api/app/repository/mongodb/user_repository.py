from app.repository.mongodb.base_repository import BaseMongoRepository
from app.repository.mongodb.client import Database
from typing import List, Dict, Optional
from pymongo import ASCENDING, DESCENDING
from app.repository.mongodb.client import Database
from app.utils.datetime_utils import generate_timestamp
class UserRepository(BaseMongoRepository):
    def __init__(self, collection: str = "user_activity" ):
        super().__init__('user')
        self.collection: str = collection
        
    async def track_project_usage(self, user_id: str, project_id: int,  db: Database, project_name: str = None, max_entries=20):
        """Tracks project usage for users in a single collection."""
        try:

            # Upsert the usage record (update if exists, insert if not)
            upsert_query = {
                "user_id": user_id,
                "project_id": project_id
            }

            set_query = {
                "timestamp": generate_timestamp()
            }
            if project_name:
                set_query["project_name"] = project_name

            # Update existing or insert new record
            collection = await self.get_collection(db)
            await collection.update_one(
                upsert_query,
                {"$set": set_query},
                upsert=True
            )

            # Get the total count of entries for the user
            count = await collection.count_documents({"user_id": user_id})
            
            # If count exceeds max_entries, delete oldest entries
            if count > max_entries:
                oldest_entry = await collection.find_one(
                    {"user_id": user_id},
                    sort=[("timestamp", ASCENDING)]  # Sort by timestamp ascending
                )
                if oldest_entry:
                    await collection.delete_one({"_id": oldest_entry["_id"]})

        except Exception as e:
            print("Error tracking project usage: ", e)

    async def get_recent_project_usage(self, user_id: str, db: Database, max_entries=20):
        """Retrieves the most recent project usage records for the user."""
        try:
            collection = await self.get_collection(db)
            recent_projects = await collection.find(
                {"user_id": user_id},
                projection={"_id": 0},  # Exclude the _id field
                sort=[("timestamp", DESCENDING)],
                limit=max_entries
            ).to_list(length=max_entries)  # Add length parameter here
            # print("Recent projects", recent_projects)
            
            return recent_projects
        except Exception as e:
            print("Error fetching recent project usage:", e)
            return []

    async def delete_project_usage(self, project_id: int, db: Database):
        """Deletes all usage records for the given project_id."""
        try:
            collection = await self.get_collection(db)
            result = await collection.delete_many({"project_id": int(project_id)})
            return result.deleted_count > 0
        except Exception as e:
            print("Error deleting project usage:", e)
            return False