
import asyncio
import subprocess
from datetime import datetime
import hashlib
import os
from typing import List
import uuid
import time
from git import Repo
from urllib.parse import urlparse
import logging
import shutil
import zipfile
import tarfile
import gzip
from app.connection.tenant_middleware import get_tenant_id
from app.knowledge.redis_kg import add_redis_support_to_knowledge
from app.knowledge.manifest_download import RepoManager


from github import Github
from app.core.Settings import settings
from fastapi import HTTPException

from app.connection.establish_db_connection import get_mongo_db
from app.core.websocket.client import WebSocketClient
from app.utils.kg_build.import_codebase import get_latest_commit_hash
from app.utils.kg_build.knowledge import Knowledge, KnowledgeCodeBase
from app.utils.kg_build.knowledge_helper import Knowledge_Helper
from app.utils.kg_build.knowledge_reporter import Reporter
from app.routes.scm_route import SCMManager
from app.utils.datetime_utils import generate_timestamp
class KnowledegeBuild:

    def _setup_logger(self, name, base_path, log_level=logging.INFO):
        print("Setting up the logger")
        print(base_path)
        """
        Set up a logger with a file handler, preventing logs from going to the console.

        :param name: Name of the logger and the log file
        :param base_path: Base path for the log directory
        :param log_level: Logging level (default: logging.INFO)
        :return: Configured logger
        """
        logger = logging.getLogger(name)
        logger.setLevel(log_level)

        # Remove any existing handlers (including the default StreamHandler)
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        # Prevent the logger from propagating messages to the root logger
        logger.propagate = False

        # Create logs directory if it doesn't exist
        log_dir = os.path.join(base_path, "logs")
        os.makedirs(log_dir, exist_ok=True)

        # Create file handler
        file_handler = logging.FileHandler(os.path.join(log_dir, f"{name}.log"))
        file_handler.setLevel(log_level)

        # Create formatter
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)

        # Add file handler to logger
        logger.addHandler(file_handler)
        logger.propagate = False

        return logger

    async def generate_unique_hash(self) -> str:
        """Generate a unique hash for branch name"""
        # Combine current timestamp and UUID for uniqueness
        unique_string = f"{datetime.utcnow().timestamp()}-{uuid.uuid4()}"
        # Create SHA-1 hash and take first 7 characters
        hash_object = hashlib.sha1(unique_string.encode())
        return hash_object.hexdigest()[:7]

    async def create_pull_request(self, github_token: str, repo_owner: str, repo_name: str, branch_name: str, base_branch: str, project_id, build_ids) -> dict:
        
        logging.info(github_token)
        
        """Create a pull request using GitHub API"""
        try:
            g = Github(github_token)
            repo = g.get_repo(f"{repo_owner}/{repo_name}")
            
            pr = repo.create_pull(
                title="Knowledge Graph Creation Updates",
                body="This PR contains updates from the knowledge graph creation process.",
                head=branch_name,
                base=base_branch
            )
            if (pr):
            
                return {
                    "pr_url": pr.html_url,
                    "pr_number": pr.number,
                    "branch_name": branch_name
                }
            else:
                await self.update_kg_status(-1, project_id, build_ids)
                
        except Exception as e:
            
            await self.update_kg_status(-1, project_id, build_ids)
            # Log the error to MongoDB
            error_handler = get_mongo_db(
                db_name=settings.MONGO_DB_NAME,
                collection_name='kg_errors'  # New collection for errors
            )
            
            error_data = {
                "error_type": "pr_creation_error",
                "error_message": str(e),
                "repository": f"{repo_owner}/{repo_name}",
                "branch_name": branch_name,
                "timestamp": datetime.utcnow(),
                "status_code": 403 if "forbidden" in str(e).lower() else None,
                "retry_count": 0  # Track number of retries
            }
            
            await error_handler.insert(error_data, error_handler.db)
            
            logging.error(f"Error creating PR: {str(e)}")
            raise e

    async def update_pr_details(self, project_id: int, build_id: str, pr_details: dict):
        """Update PR details in database"""
        kg_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='project_repositories'
        )

        filter = {
            "project_id": project_id,
            "repositories.branches.builds.build_id": build_id
        }
        
        update = {
            f"repositories.$[].branches.$[branch].builds.pr_created": 1,
            f"repositories.$[].branches.$[branch].builds.pr_details": pr_details
        }
            
        array_filters = [
            {"branch.builds.build_id": build_id}
        ]

        result = await kg_handler.update_with_nested_object_and_filters(
            filter=filter,
            update=update,
            array_filters=array_filters,
            db=kg_handler.db
        )

        print(f"Modified {result.modified_count} documents")

    async def create_and_push_branch(self, repo_path: str, base_branch: str) -> tuple[str, bool]:
        """Create new branch and push changes"""
        try:
            # Generate unique hash for branch name
            unique_hash = await self.generate_unique_hash()
            new_branch = f"knowledge-creation-{unique_hash}"
            
            os.chdir(repo_path)
            
            # Create and checkout new branch
            os.system(f'git checkout -b {new_branch}')
            
            # Add and commit changes
            os.system('git add .')
            os.system('git commit -m "Knowledge graph creation completed"')
            
            # Try pushing the branch
            push_result = os.system(f'git push -u origin {new_branch}')
            
            return new_branch, push_result == 0
            
        except Exception as e:
            logging.error(f"Error in branch operations: {str(e)}")
            return None, False
        finally:
            os.chdir(os.path.dirname(os.path.dirname(repo_path)))
        
    async def check_pr_status(self, project_id: int, repo_name: str) -> bool:
        """Check if there's any pending PR for this repository"""
        kg_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='project_repositories'
        )
        
        result = await kg_handler.get_one(
            {"project_id": project_id, "repositories.repository_name": repo_name},
            kg_handler.db
        )
        
        if result:
            for repo in result.get('repositories', []):
                if repo['repository_name'] == repo_name:
                    for branch in repo.get('branches', []):
                        if branch.get('builds', {}).get('pr_created') == 1:
                            return True
        return False

    async def try_to_commit(self, repo_path: str, branch_name: str) -> bool:
        """Attempt to push changes and return whether direct push is allowed"""
        try:
           # Change directory to build path
            os.chdir(repo_path)
            os.system(f'git switch {branch_name}')
            # Add and commit changes
            os.system('git add .')
            os.system('git commit -m "Knowledge graph creation completed"')
            # os.system('git push')
            
            return True
        
        except Exception as e:
            logging.error("Error getting latest commit hash: %s", str(e))
            return False            
            
    async def clone_repository(self, repo_path: str, git_url: str, token: str = None, branch: str = None, logger=None) -> bool:
        try:
            log = logger if logger else logging
            log.info("Entered clone_repository")
            # Check if repository path exists and have files other than logs
            if os.path.exists(repo_path):
                log.info(f"Repository already exists at: {repo_path}")
                return True
            
            if token:
                parsed_url = urlparse(git_url)
                auth_url = f"https://{token}@{parsed_url.netloc}{parsed_url.path}"
            else:
                auth_url = git_url
        
            # Create a temporary path for cloning
            temp_clone_path = f"{repo_path}_temp"
            
            try:
                # Attempt to clone to temporary location
                Repo.clone_from(auth_url, temp_clone_path, branch=branch)
                
                return True, temp_clone_path
                
            except Exception as e:
                log.error(f"Failed to clone repo, error from Git: {e}")
                return False, repo_path
            
        except Exception as e:
            log.error(f"Failed to clone repository: {str(e)}")
            
            return False, repo_path
    
    async def update_kg_status(self, status: int, project_id: int, build_ids: list, session_id=None, upstream=False, user_id=None):

        for build_id in build_ids:
            kg_handler = get_mongo_db(
                db_name=settings.MONGO_DB_NAME,
                collection_name='project_repositories',
                user_id=user_id
            )

            filter = {
                "project_id": project_id,
                "repositories.branches.builds.build_id": build_id
            }
            
            update = {
                f"repositories.$[repo].branches.$[branch].builds.kg_creation_status": status,
                f"repositories.$[repo].branches.$[branch].upstream" : False
            }
            
            if session_id is not None:
                update[f"repositories.$[repo].branches.$[branch].builds.build_session_id"] = session_id
                
            array_filters = [
                {"repo.service": "github", "repo.branches.builds.build_id": build_id},
                {"branch.builds.build_id": build_id}
            ]

            result = await kg_handler.update_with_nested_object_and_filters(
                filter=filter,
                update=update,
                array_filters=array_filters
            )
            
            print(f"Modified {result.modified_count} documents")
    
    async def update_commit_hash(self, hash: str, project_id: int, build_id: str, user_id:str = None, logger=None):

        log = logger if logger else logging
        
        kg_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='project_repositories',
            user_id=user_id
        )

        filter = {
            "project_id": project_id,
            "repositories.branches.builds.build_id": build_id
        }
        
        update = {
            "repositories.$[repo].branches.$[branch].latest_commit_hash": hash
        }
            
        array_filters = [
            {"repo.service": "github", "repo.branches.builds.build_id": build_id},
            {"branch.builds.build_id": build_id}
        ]

        result = await kg_handler.update_with_nested_object_and_filters(
            filter=filter,
            update=update,
            array_filters=array_filters
        )

        log.info(f"Mongo db updated with commit hash, Modified {result.modified_count} documents")
        
    async def update_kg_status_by_id(self, status: int, project_id: int, build_id: str, session_id=None, upstream=False, service="github", user_id=None):
        kg_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='project_repositories',
            user_id = user_id
        )
        
        if(service == "localFiles"):
            filter = {
                "project_id": project_id,
                "repositories.builds.build_id": build_id
            }

            update = {
                f"repositories.$.builds.kg_creation_status": status
            }

            if session_id is not None:
                update[f"repositories.$.builds.build_session_id"] = session_id

            result = await kg_handler.update_one(
                filter=filter,
                element=update,
                db=kg_handler.db
            )

            print(f"Modified {result.modified_count} local documents")
        else:
            filter = {
                "project_id": project_id,
                "repositories.branches.builds.build_id": build_id
            }
            
            update = {
                f"repositories.$[repo].branches.$[branch].builds.kg_creation_status": status
            }
            
            if session_id is not None:
                update[f"repositories.$[repo].branches.$[branch].builds.build_session_id"] = session_id

            if upstream is not None:
                update[f"repositories.$[repo].branches.$[branch].upstream"] = upstream
                
            array_filters = [
                {"repo.service": {"$ne": "localFiles"}, "repo.branches.builds.build_id": build_id},
                {"branch.builds.build_id": build_id}
            ]

            result = await kg_handler.update_with_nested_object_and_filters(
                filter=filter,
                update=update,
                array_filters=array_filters
            )

            print(f"Modified {result.modified_count} documents")

    async def update_build_times(self, project_id: int, build_ids: list, time_type: str, set_others_null = False, service="github", user_id = None):
        current_time = generate_timestamp()

        # For updating times in mongo_db
        kg_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='project_repositories',
            user_id=user_id
        )

        if(service == "github"):
            for build_id in build_ids: 
                filter = {
                    "project_id": project_id,
                    "repositories.branches.builds.build_id": build_id
                }

                if set_others_null and time_type == "start_time":
                    update = {
                        f"repositories.$[repo].branches.$[branch].builds.build_info.{time_type}": current_time,
                        "repositories.$[repo].branches.$[branch].builds.build_info.last_updated": None,
                        "repositories.$[repo].branches.$[branch].builds.build_info.end_time": None
                    }
                else:
                    update = {
                        f"repositories.$[repo].branches.$[branch].builds.build_info.{time_type}": current_time
                    }
                    
                array_filters = [
                    {"repo.service": service, "repo.branches.builds.build_id": build_id},
                    {"branch.builds.build_id": build_id}
                ]

                await kg_handler.update_with_nested_object_and_filters(
                    filter=filter,
                    update=update,
                    array_filters=array_filters,
                )
        else:
            build_id = build_ids[0]
            
            filter = {
                "project_id": project_id,
                "repositories.builds.build_id": build_id
            }

            if set_others_null and time_type == "start_time":
                update = {
                    f"repositories.$.builds.build_info.{time_type}": current_time,
                    "repositories.$.builds.build_info.last_updated": None,
                    "repositories.$.builds.build_info.end_time": None
                }
            else:
                update = {
                   f"repositories.$.builds.build_info.{time_type}": current_time
                }

                await kg_handler.update_one(
                    filter=filter,
                    element=update,
                    db=kg_handler.db
                )

    async def build_knowledge_graph(self, reporter, session_id: str, user_id: str, project_id: int, repo: dict, build_ids: list, codebases, logger):
        # Store session information
        session_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='kg_sessions',
            user_id=user_id
        )
            
        try:
            logger.info("Entered Build Knowledge Graph:")
            logger.info(f"Further logs will be continued on kg_creation_{session_id}.log file")
            base_path = codebases[0].base_path
            root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            
            # Create logs directory if it doesn't exist
            logs_dir = os.path.join(os.path.dirname(base_path), "logs")
            os.makedirs(logs_dir, exist_ok=True)
            
            # Create job name and log file name
            job_name = f"kg_creation_{session_id}"
            log_file = f"{logs_dir}/kg_creation_{session_id}.log"
            
            # Create a temporary JSON file with all the parameters
            import tempfile
            import json
            
            # Prepare all the data to pass to the subprocess
            subprocess_data = {
                "session_id": session_id,
                "user_id": user_id,
                "project_id": project_id,
                "repo": repo,
                "build_ids": build_ids,
                "base_path": base_path,
                "codebases": [
                    {
                        "base_path": cb.base_path,
                        "name": cb.name,
                        "service": cb.service
                    } for cb in codebases
                ],
                "settings": {
                    "mongo_db_name": settings.MONGO_DB_NAME,
                    "websocket_uri": settings.WEBSOCKET_URI
                }
            }
            
            # Write data to temporary file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
                json.dump(subprocess_data, temp_file, indent=2, default=str)
                temp_file_path = temp_file.name
            
            print(f"Created temporary file: {temp_file_path}")
            tenant_id = get_tenant_id()
            
            print(f"Tenant ID", tenant_id)
            # Prepare input arguments as JSON string
            input_arguments = json.dumps({
                "base_path": base_path,
                "json_data_file": temp_file_path,
                "session_id": session_id,
                "project_id": str(project_id),
                "user_id": user_id,
                "tenant_id": tenant_id
            })
            
            stage = "knowledge_creation"
            
            # Build screen command
            screen_cmd = [
                "screen", 
                "-L", 
                "-Logfile", log_file,
                "-dmS", job_name,
                "python", f"{root_dir}/app/batch_jobs/kg_creation.py",
                "--input_args", input_arguments,
                "--stage", stage
            ]
            
            current_env = os.environ.copy()
            current_env["PYTHONPATH"] = f"{root_dir}"

            print(f"Running screen command: {' '.join(screen_cmd)}")
            print(f"Log file will be created at: {log_file}")
            
            # Run screen command (non-blocking)
            result = subprocess.run(
                screen_cmd,
                env=current_env,
                capture_output=True,
                text=True
            )

            if result.returncode == 0:
                print("Screen session started successfully!")
                print(f"Screen session name: {job_name}")
                print(f"Log file: {log_file}")
                if result.stdout:
                    print(f"Output: {result.stdout}")
            else:
                print(f"Failed to start screen session. Return code: {result.returncode}")
                if result.stderr:
                    print(f"Error: {result.stderr}")
                raise subprocess.CalledProcessError(result.returncode, screen_cmd, result.stderr)
            
            logging.info("Screen session started successfully")
            
            # Store initial session data
            session_data = {
                "project_id": project_id,
                "session_id": session_id,
                "session_status": "Progress",
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
                "build_ids": build_ids,
                "screen_session": job_name,
                "log_file": log_file,
                "temp_file": temp_file_path
            }
            
            await session_handler.insert(session_data, session_handler.db)
            
            print("Knowledge ingestion started in background screen session!")
            
        except subprocess.CalledProcessError as e:
            print(f"Failed to start screen session with return code {e.returncode}")
            print(f"Error output: {e.stderr}")
            
            # Clean up temp file if it exists
            try:
                if 'temp_file_path' in locals():
                    os.unlink(temp_file_path)
            except:
                pass
                
            await session_handler.update_one(
                filter={"session_id": session_id},
                element={
                    "session_status": "Failed",
                    "error_message": f"Failed to start background process: {e.stderr}",
                    "updated_at": datetime.utcnow()
                },
                db=session_handler.db
            )
            
            await self.update_kg_status_by_id(-1, project_id, build_ids[0], session_id, False, repo['service'], user_id)
            raise
            
        except Exception as e:
            print(f"Unexpected error in build_knowledge_graph: {str(e)}")
            
            # Clean up temp file if it exists
            try:
                if 'temp_file_path' in locals():
                    os.unlink(temp_file_path)
            except:
                pass
                
            # Update session status to failed
            await session_handler.update_one(
                filter={"session_id": session_id},
                element={
                    "session_status": "Failed",
                    "error_message": f"Unexpected error: {str(e)}",
                    "updated_at": datetime.utcnow()
                },
                db=session_handler.db
            )
            
            await self.update_kg_status_by_id(-1, project_id, build_ids[0], session_id, False, repo['service'], user_id)
            logging.error(f"Error in build_knowledge_graph: {str(e)}")
            raise
    
    def check_existing_knowledge_graph(self, repo_path: str) -> bool:
        """
        Check if knowledge graph already exists for repository
        
        Args:
            repo_path: Path to repository directory

        Returns:
            bool: True if knowledge graph exists, False otherwise
        """
        try:
            knowledge_path = os.path.join(repo_path, '.knowledge')
            return os.path.exists(knowledge_path) and os.path.isdir(knowledge_path)
        except Exception as e:
            logging.error(f"Error checking knowledge graph: {str(e)}")
            return False

    async def get_branch_details_by_build_id(self, project_id: int, build_id: str) -> dict:
        """
        Retrieve branch name and path for a given build_id
        
        Args:
            project_id: The ID of the project
            build_id: The ID of the build
            
        Returns:
            dict: Contains branch_name and path, or None if not found
        """
        try:
            kg_handler = get_mongo_db(
                db_name=settings.MONGO_DB_NAME,
                collection_name='project_repositories'
            )
            
            # Query to find the specific project and build_id
            result = await kg_handler.get_one(
                {
                    "project_id": project_id,
                    "repositories.branches.builds.build_id": build_id
                },
                kg_handler.db
            )
            
            print(result)
            
            if result and 'repositories' in result:
                for repo in result['repositories']:
                    if 'branches' in repo:
                        for branch in repo['branches']:
                            if 'builds' in branch and branch['builds'].get('build_id') == build_id:
                                return {
                                    "branch_name": branch['name'],
                                    "path": branch['builds']['path'],
                                    "git_url": repo['git_url']
                                }
            
            return None
            
        except Exception as e:
            logging.error(f"Error retrieving branch details: {str(e)}")
            return None
    
    async def upstream(self, project_id,build_session_id,  build_id, user_id):
        
        
        branch_details = await self.get_branch_details_by_build_id(project_id=project_id, build_id=build_id)

        branch_name = branch_details["branch_name"]
        build_path = branch_details["path"]
        git_url = branch_details["git_url"]
        code_base = f"{git_url}:{branch_name}"
        
        log_dir = os.path.dirname(build_path)
        logger = self._setup_logger("Upstream", log_dir)

        ws_client = WebSocketClient(build_session_id, settings.WEBSOCKET_URI)
        reporter = Reporter(ws_client)
        reporter.send_message("code_ingestion", {
            'info': "Build_Status_Update",
            'message': 'Setting up the repository',
            'status': 'Processing',
            'buildId': build_id 
        }) 
        
        # Change directory to the build path
        cwd = os.getcwd() 
        os.chdir(build_path)

        try:
            logger.info("Trying to switch to Build Path") 
            logger.info("Running OS commands")
            # Change directory to the build path
            os.chdir(build_path)
            os.system(f'git config --global --add safe.directory {build_path}')
            # Switch to the specified branch
            os.system(f'git switch {branch_name}')
            os.system('git stash')
            # Pull latest changes
            os.system('git pull --rebase')  
            #Pop stashed changes
            os.system('git stash pop')     
            
            lock_file = os.path.join(build_path, '.knowledge', '.vector_db', '.milvus.db.lock')
    
            # Check if lock file exists and remove it
            if os.path.exists(lock_file):
                try:
                    os.remove(lock_file)
                    logger.info(f"Successfully removed {lock_file}")
                    print(f"Successfully removed {lock_file}")
                except Exception as e:
                    logger.error(f"Error removing lock file: {e}")
                    print(f"Error removing lock file: {e}")
                                     
            # Return to original directory
            os.chdir(cwd)
            
            logger.info("Successfully setup the repository. Starting the re-build process")
            reporter.send_message("code_ingestion", {
                'info': "Build_Status_Update",
                'message': 'Setting up the repository',
                'status': 'Success',
                'buildId': build_id 
            }) 
            
            codebases = [KnowledgeCodeBase(build_path,code_base)]

            reporter.send_message("code_ingestion", {
                'info': "Build_Status_Update",
                'message': 'Starting Re-build Process',
                'status': 'Processing',
                'buildId': build_id
            })  
            
            
            knowledge_helper = await asyncio.to_thread(
                lambda: Knowledge_Helper(build_session_id, reporter, os.getcwd(), codebases, user_id, project_id, logger=logger)
            )

            logger.info("Getting knowledge instance")
            knowledge = Knowledge.getKnowledge(id=build_session_id)

            logger.info("Adding redis support to knowledge")
            add_redis_support_to_knowledge(knowledge)

            time.sleep(1)
            reporter.send_message("code_ingestion", {
                'info': "Build_Status_Update",
                'message': 'Starting Re-build Process',
                'status': 'Success',
                'buildId': build_id
            })  
        
            # Start knowledge processing
            logger.info("Starting knowledge generation...")
            await asyncio.to_thread(knowledge.start)
            
            reporter.send_message("code_ingestion", {
                'status': 'Knowledge Generating...'
            })

            reporter.send_message("code_ingestion", {
                'info': "Build_Status_Update",
                'message': 'Fetching Progress Data',
                'status': 'Success',
                'buildId': build_id
            })  
            while(True):   
                await self.update_build_times(project_id, [build_id], "last_updated", user_id=user_id)           
                if knowledge._state == 2:
                    await asyncio.to_thread(knowledge.save_to_redis)
                    await self.update_build_times(project_id, [build_id], "end_time", user_id=user_id)
                    logger.info(f"Knowlege updation completed")
                    can_push = await self.try_to_commit(build_path, branch_name)
                    if can_push:
                        
                        # Update commit hash
                        os.chdir(build_path)
                        _hash = os.popen('git rev-parse HEAD').read().strip()
                        logger.info(f"Updating commmit hash: {_hash}")
                        await self.update_commit_hash(_hash, project_id,build_id, user_id, logger)
                        await self.update_kg_status(2, project_id, [build_id])
                        #reporter.send_message("code_ingestion", knowledge.get_upstream_progress(total_files))
                    else:
                        logger.error("Error while pushing the code")
                        await self.update_kg_status(-1, project_id, [build_id])
                    
                    break
                
                await asyncio.sleep(1)
            
            
            logger.info("Knowledge graph generation completed successfully")
            
        except Exception as e:
            logger.error(f"Error during git operations: {str(e)}")
            reporter.send_message("code_ingestion", {
                    'info': "Build_Status_Update",
                    'message': 'Setting up the repository',
                    'status': 'Failed',
                    'buildId': build_id 
                }) 
            raise HTTPException(status_code=500, detail=f"Failed to update repository: {str(e)}")

    async def build(
        self,
        reporter,
        build_session_id: str,
        build_ids: List,
        project_id: int,
        repo: dict,
        user_id: str,
        logger
    ):
        logger.info("Build Started ")
        project_repositories = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='project_repositories',
            user_id = user_id
        )

        reporter.send_message("code_ingestion", {
            'info': "Build_Status_Update",
            'message': 'Starting Build Process',
            'status': 'Processing',
            'buildId': build_ids[0]
        })  
        
        codebase = []
        skipped_builds = []
        if(repo['service'] == "localFiles"):
            build_path = repo['builds']['path']
            if build_path:
                codebase.append(KnowledgeCodeBase(build_path,f"{repo['repository_name']}", repo['service'] ))
        else:
            for branch in repo['branches']:
                if branch['builds']['build_id'] in build_ids:
                    build_path = branch['builds']['path']
                    if build_path:
                        codebase.append(KnowledgeCodeBase(build_path,f"{repo['git_url']}:{branch['name']}" ))
                            
        print(codebase)
        logger.info(f"Codebase: {codebase}")
        
        if not codebase:
            logger.error("Codebase not found")

            reporter.send_message("code_ingestion", {
                'info': "Build_Status_Update",
                'message': 'Starting Build Process',
                'status': 'Failed',
                'buildId': build_ids[0]
            })  

            return {
                "status": "skipped",
                "message": "All requested builds already have knowledge graphs",
                "data": {"skipped_builds": skipped_builds}
            }
                    
        try:
            logger.info("Build Started, calling build_knowledge_graph")
            await self.build_knowledge_graph(
                reporter=reporter,
                session_id=build_session_id,
                user_id=user_id,
                repo=repo,
                project_id=project_id,
                codebases=codebase,
                build_ids=build_ids,
                logger=logger
            )
        except Exception as e:
            await self.update_kg_status(-1, project_id, build_ids)
            logger.error(f"Background task failed: {str(e)}")
        
        return {
            "message": "Knowledge graph generation started",
            "build_session_id": build_session_id
        }

    async def clone(self, project_id, build_session_id, build_id, user_id, data_dir, repositories, upstream, current_user_obj):
        from app.routes.kg_route import import_codebase, CodebaseImportRequest

        log_dir = os.path.dirname(repositories[0]['branches'][0]['builds']['path'])
        logger = self._setup_logger("CloneNdBuild", log_dir)
        
        ws_client = WebSocketClient(build_session_id, settings.WEBSOCKET_URI)
        reporter = Reporter(ws_client)
        logger.info("Sending Initial Message")
        reporter.send_message("code_ingestion", {
            'info': "Build_Status_Update",
            'message': 'Cloning the repository',
            'status': 'Processing',
            'buildId': build_id 
        })    

        build_ids = []
        ready_to_build = False
        
        # Now repositories contains only one repository
        logger.info("Getting information from the repository data")
        repo = repositories[0]
        branch_name = repo['branches'][0]['name']
        _url = repo['git_url']
        checking_path = os.path.join(data_dir, build_id, repo['repository_name'].split('/')[-1])
        scm_id = repo.get("scm_id", "")
        scm_manager = None
        scm_access_token = None
        if scm_id:
            scm_manager = SCMManager()
            scm_configuration = scm_manager.get_valid_configuration(scm_id=scm_id)
            scm_access_token = scm_configuration.credentials.access_token

        logger.info("Iterating through branches and attempting to clone")
        logger.info(f"Ready to build: {ready_to_build}")
        logger.info(f"branches: {repo['branches']}")
        for branch in repo['branches']:
            branch_name = branch['name']
            build_ids.append(branch['builds']['build_id'])
                                
            try:
                os.makedirs(data_dir, exist_ok=True)
                os.makedirs(os.path.dirname(checking_path), exist_ok=True)
                
                branch['builds']['path'] = checking_path
                
                if repo['repo_type'] == 'private':
                    
                    if scm_id:
                        github_token = scm_access_token
                    else:
                    
                        user_github = get_mongo_db(
                            db_name=settings.MONGO_DB_NAME,
                            collection_name='users_github'
                        )
                        
                        user_data = await user_github.git_get_by_user_id(user_id)
                        if not user_data and scm_access_token:
                            logger.error("Neither Github token nor SCM token available. Raising error.")
                            raise HTTPException(status_code=404, detail="GitHub token not found. Please login again.")
                        
                        github_token = user_data["access_token"]
                        
                    latest_commit_hash = get_latest_commit_hash(_url, branch_name, github_token)
                    logger.info("just before calling clone repo")
                    clone_successful, _path = await self.clone_repository(checking_path, _url, token=github_token, branch=branch_name, logger=logger)
                    logger.info(f"Clone successful: {clone_successful}")
                else:
                    logger.info("Attempting to clone public repository")
                    github_token = settings.RATE_LIMIT_TOKEN_V1
                    latest_commit_hash = get_latest_commit_hash(_url, branch_name)
                    clone_successful, _path = await self.clone_repository(checking_path, _url,token=github_token, branch=branch_name, logger=logger)

                if clone_successful:
                    logger.info("Clone Successful. Updating commit hash.")
                    await self.update_commit_hash(latest_commit_hash, project_id, branch['builds']['build_id'], user_id, logger=logger)
                    ready_to_build = True
                    os.rename(_path, checking_path)
                    branch['builds']['path'] = checking_path
                    os.system(f'git config --global --add safe.directory {checking_path}')
                    xml_files = [f for f in os.listdir(checking_path) if f.endswith('.xml')]

                    for file in xml_files:
                        #initialize Repo manager with no manifest URL
                        new_repos = []
                        repo_man = RepoManager("")
                        manifest_path = os.path.join(checking_path, file)
                        manifest_repos = repo_man._parse_manifest(manifest_path, github_token)
                        if manifest_repos:
                            logger.info(f"Found {len(manifest_repos)} manifest repos.")
                            for manifest_repo in manifest_repos:
                                new_repos.append({
                                    "repo_name": manifest_repo['name'],
                                    "branch_name": manifest_repo['revision'],
                                    "repo_type": repo['repo_type'],
                                    "repo_id": manifest_repo['id'],
                                    'associated': False
                                })
                            
                            print("setting request")

                            request = {
                                'project_id': project_id,
                                'repositories': new_repos,
                                'encrypted_scm_id': ""
                            }

                            status = await import_codebase(CodebaseImportRequest(**request), current_user=current_user_obj)
                            if(status['status'] == "success"):
                                reporter.send_message("code_ingestion", {
                                    'info': "Manifest_Repository_Update",
                                    'message': 'Importing newer repositories found in the manifest file.',
                                })
                else:          
                    await self.update_kg_status_by_id(-1, project_id, branch['builds']['build_id'], user_id=user_id)
                            
            except Exception as e:
                reporter.send_message("code_ingestion", {
                    'info': "Build_Status_Update",
                    'message': 'Cloning the repository',
                    'status': 'Failed',
                    'buildId': build_id 
                })  
                await self.update_kg_status_by_id(-1, project_id, branch['builds']['build_id'], user_id=user_id)
                logger.error(f"Failed to clone repository {_url} branch {branch_name}: {str(e)}")
                
        if ready_to_build:
            logger.info("Clone successful, calling the build function for further tasks") 
            reporter.send_message("code_ingestion", {
                'info': "Build_Status_Update",
                'message': 'Cloning the repository',
                'status': 'Success',
                'buildId': build_id 
            })  
            await self.build(reporter, build_session_id, build_ids, project_id, repo, user_id, logger)
        
        else:
            logger.info("Clone task failed, sending failed status")
            reporter.send_message("code_ingestion", {
                'info': "Build_Status_Update",
                'message': 'Cloning the repository',
                'status': 'Failed',
                'buildId': build_id 
            })  

    def runCompleteExtraction(self, base_path, logger=None):
        import py7zr
        log = logger if logger else logging

        extracted = True #Flag that handles nested extraction (zip inside zips)

        log.info("Checking and extracting any compressed files in the uploads.")
        while extracted:
            extracted = False
            
            for root, dirs, files in os.walk(base_path):
                for filename in files:
                    file_path = os.path.join(root, filename)
                    lower = filename.lower()

                    try: 
                        if lower.endswith(".zip"):
                            with zipfile.ZipFile(file_path, 'r') as zip_ref:
                                zip_ref.extractall(root)
                            os.remove(file_path)
                            extracted = True

                        elif lower.endswith(".tar") or lower.endswith(".tar.gz") or lower.endswith(".tgz"):
                            with tarfile.open(file_path, 'r:*') as tar_ref:
                                tar_ref.extractall(root)
                            os.remove(file_path)
                            extracted = True

                        elif lower.endswith(".7z"):
                            with py7zr.SevenZipFile(file_path, mode='r') as sevenz_ref:
                                sevenz_ref.extractall(root)
                            os.remove(file_path)
                            extracted = True

                        elif lower.endswith(".gz"):
                            output_file = os.path.splitext(file_path)[0]
                            with gzip.open(file_path, 'rb') as f_in:
                                with open(output_file, 'wb') as f_out:
                                    shutil.copyfileobj(f_in, f_out)
                            os.remove(file_path)
                            extracted = True

                        
                        log.info(f"Contents of {root}: {os.listdir(root)}")
                        #true flag tells to re-do the walk to check for further compressed files after extraction
                    except Exception as e:
                        log.error(f"Failed to extract {file_path}: {e}")
                        raise



    async def ingest(self, project_id, project_name, build_session_id, build_id, user_id, data_dir, files_data):
        ws_client = WebSocketClient(build_session_id, settings.WEBSOCKET_URI)
        reporter = Reporter(ws_client)

        log_dir = os.path.dirname(files_data['builds']['path'])
        logger = self._setup_logger("Ingest", log_dir)

        logger.info("Sending Initial Message")

        reporter.send_message("code_ingestion", {
            'info': "Build_Status_Update",
            'message': 'Copying the files',
            'status': 'Processing',
            'buildId': build_id 
        })   

        checking_path = os.path.join(data_dir, build_id, project_name)

        logger.info(f"Trying to copy the files in {checking_path}")

        try: 
            files_data['builds']['path'] = checking_path

            self.runCompleteExtraction(checking_path, logger)
            
            reporter.send_message("code_ingestion", {
                'info': "Build_Status_Update",
                'message': 'Copying the files',
                'status': 'Success',
                'buildId': build_id 
            })

            logger.info(f"Successfully copied all the files to {checking_path}.")
            logger.info(f"Starting build process")

            await self.build(reporter, build_session_id, [build_id], project_id, files_data, user_id, logger)

        except Exception as e:
            reporter.send_message("code_ingestion", {
                'info': "Build_Status_Update",
                'message': 'Copying the files',
                'status': 'Failed',
                'buildId': build_id 
            })  
            await self.update_kg_status_by_id(-1, project_id, build_id, None, False, files_data['service'], user_id=user_id)
            logger.error(f"Failed to copy files : {str(e)}")
