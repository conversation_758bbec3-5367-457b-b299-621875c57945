import subprocess
import os
import sys
import time
import json
import asyncio
import argparse
from datetime import datetime
from pathlib import Path

# Add your project path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.connection.establish_db_connection import get_mongo_db
from app.knowledge.redis_kg import add_redis_support_to_knowledge
from app.utils.kg_build.knowledge import Knowledge, KnowledgeCodeBase
from app.utils.kg_build.knowledge_helper import Knowledge_Helper
from app.utils.kg_build.knowledge_reporter import Reporter
from app.core.websocket.client import WebSocketClient
from app.core.Settings import settings
from app.knowledge.code_query import KnowledegeBuild
import logging
from app.celery_app import user_context

def set_tenant_context(tenant_id):
    """Set tenant ID in environment variables for get_tenant_id() function"""
    try:
        # Get existing input_arguments or create new ones
        existing_args = json.loads(os.environ.get("input_arguments", "{}"))
        
        # Update with tenant_id
        existing_args["tenant_id"] = tenant_id
        
        # Set back to environment
        os.environ["input_arguments"] = json.dumps(existing_args)
        
        print(f"✅ Tenant ID set in environment: {tenant_id}")
        print(f"📄 Updated input_arguments: {os.environ.get('input_arguments')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error setting tenant context: {e}")
        return False

def set_user_context(user_id):
    """Set user ID in the context variable"""
    from app.connection.tenant_middleware import user_id_context
    user_id_context.set(user_id)
    return True

async def run_knowledge_build(data):
    """Run the knowledge building process with provided data"""
    
    session_id = data['session_id']
    user_id = data['user_id']
    project_id = int(data['project_id'])
    repo = data['repo']
    build_ids = data['build_ids']
    base_path = data['base_path']

    
    # Reconstruct codebases
    codebases = []
    for cb_data in data['codebases']:
        codebase = KnowledgeCodeBase(
            cb_data['base_path'],
            cb_data['name'],
            cb_data.get('service', 'github')
        )
        codebases.append(codebase)
    
    print(f"Starting knowledge build for session: {session_id}")
    print(f"Base path: {base_path}")
    print(f"Project ID: {project_id}")
    print(f"Build IDs: {build_ids}")
    print(f"run_knowledge_build -> user_id {user_id}")
    
    # Initialize session handler
    session_handler = get_mongo_db(
        db_name=data['settings']['mongo_db_name'],
        collection_name='kg_sessions',
        user_id=user_id
    )
    
    # Initialize WebSocket client and reporter
    ws_client = WebSocketClient(session_id, data['settings']['websocket_uri'])
    reporter = Reporter(ws_client)
    
    # Initialize KnowledgeBuild for status updates
    kg = KnowledegeBuild()
    
    try:
        reporter.send_message("code_ingestion", {
            'info': "Build_Status_Update",
            'message': 'Starting Build Process',
            'status': 'Success',
            'buildId': build_ids[0]
        })
        
        # Update session status to progress
        await session_handler.update_one(
            filter={"session_id": session_id},
            element={
                "session_status": "Progress",
                "updated_at": datetime.utcnow()
            },
            db=session_handler.db
        )
        
        # Initialize Knowledge_Helper
        knowledge_helper = Knowledge_Helper(
            session_id, 
            reporter, 
            os.getcwd(), 
            codebases, 
            user_id, 
            project_id, 
            service=repo.get('service', 'github')
        )

        # Get knowledge instance
        knowledge = Knowledge.getKnowledge(id=session_id)
        add_redis_support_to_knowledge(knowledge)
        
        # Get total files count
        total_files = knowledge.get_file_count(base_path)
        print(f"Total files to process: {total_files}")
        
        # Start knowledge processing
        knowledge.start()
        
        # Update status
        await kg.update_kg_status_by_id(
            1, project_id, build_ids[0], session_id, False, 
            repo.get('service', 'github'), user_id
        )
        
        reporter.send_message("code_ingestion", {
            'info': "Build_Status_Update",
            'message': 'Fetching Progress Data',
            'status': 'Success',
            'buildId': build_ids[0]
        })
        
        # Main processing loop
        while True:
            await kg.update_build_times(
                project_id, [knowledge.build_id], "last_updated", 
                False, repo.get('service', 'github'), user_id
            )
            
            if knowledge._state == 2:  # Completed
                # Save to Redis
                knowledge.save_to_redis()
                
                # Update end time
                await kg.update_build_times(
                    project_id, build_ids, "end_time", 
                    False, repo.get('service', 'github'), user_id
                )
                
                # Handle different service types
                if repo.get('service') == 'github' and repo.get('repo_type') == "private":
                    for branch in repo.get('branches', []):
                        if branch['builds']['build_id'] in build_ids:
                            build_path = branch['builds']['path']
                            if build_path:
                                # Try direct push
                                can_push = await kg.try_to_commit(build_path, branch['name'])
                                if can_push:
                                    # Update commit hash
                                    os.chdir(build_path)
                                    _hash = os.popen('git rev-parse HEAD').read().strip()
                                    await kg.update_commit_hash(_hash, project_id, branch['builds']['build_id'])
                                    logging.info(f"Successfully pushed changes to {repo.get('repository_name', 'repo')}")
                                    await kg.update_kg_status(2, project_id, build_ids, user_id=user_id)
                                    reporter.send_message("code_ingestion", knowledge.get_kg_progress(total_files))
                                else:
                                    logging.info("Error while pushing the code")
                                    await kg.update_kg_status(-1, project_id, build_ids, user_id=user_id)
                else:
                    await kg.update_kg_status_by_id(
                        2, project_id, build_ids[0], None, False, 
                        repo.get('service', 'github'), user_id
                    )
                
                # Update session status to completed
                await session_handler.update_one(
                    filter={"session_id": session_id},
                    element={
                        "session_status": "Completed",
                        "updated_at": datetime.utcnow()
                    },
                    db=session_handler.db
                )

                reporter.send_message("code_ingestion", {'info': 'Repo_Data_Update'})
                print("Knowledge ingestion completed. Exiting...")
                break
            
            await asyncio.sleep(1)
        
        # Cleanup after completion
        Knowledge_Helper.cleanup(str(session_id))
        
        print("Knowledge graph generation completed successfully")
        return True
        
    except Exception as e:
        # Update session status to failed
        try:
            await session_handler.update_one(
                filter={"session_id": session_id},
                element={
                    "session_status": "Failed",
                    "error_message": str(e),
                    "updated_at": datetime.utcnow()
                },
                db=session_handler.db
            )
        except Exception as update_error:
            print(f"Failed to update session status: {update_error}")
        
        print(f"Error in knowledge build: {str(e)}")
        raise

def main():
    parser = argparse.ArgumentParser(description='Knowledge Graph Creation Background Job')
    parser.add_argument('--input_args', required=True, help='JSON string containing input arguments')
    parser.add_argument('--stage', required=True, help='Processing stage')
    
    args = parser.parse_args()
    
    print(f"Starting knowledge creation job")
    print(f"Stage: {args.stage}")
    print(f"Input args: {args.input_args}")
    
    try:
        # Parse input arguments
        input_data = json.loads(args.input_args)
        
        base_path = input_data['base_path']
        json_data_file = input_data['json_data_file']
        session_id = input_data['session_id']
        project_id = input_data['project_id']
        user_id = input_data['user_id']
        tenant_id = input_data['tenant_id']
        set_tenant_context(tenant_id)
        set_user_context(user_id)
        user_context.set(user_id)
        
        print(f"Parsed parameters:")
        print(f"  Base path: {base_path}")
        print(f"  JSON data file: {json_data_file}")
        print(f"  Session ID: {session_id}")
        print(f"  Project ID: {project_id}")
        print(f"  User ID: {user_id}")
        print(f"  Tenant ID: {tenant_id}")
        
        # Load JSON data
        with open(json_data_file, 'r') as f:
            data = json.load(f)
        
        print(f"Loaded data from {json_data_file}")
        print(f"Data keys: {list(data.keys())}")
        
        # Run the knowledge build process based on stage
        if args.stage == "knowledge_creation":
            success = asyncio.run(run_knowledge_build(data))
            
            if success:
                print("Knowledge build completed successfully")
                sys.exit(0)
            else:
                print("Knowledge build failed")
                sys.exit(1)
        else:
            print(f"Unknown stage: {args.stage}")
            sys.exit(1)
            
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in input arguments: {e}")
        sys.exit(1)
    except FileNotFoundError:
        print(f"Error: JSON data file not found: {json_data_file}")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)
    finally:
        # Clean up the JSON data file
        try:
            if 'json_data_file' in locals():
                if os.path.exists(json_data_file):
                    os.unlink(json_data_file)
                    print(f"Cleaned up JSON data file: {json_data_file}")
        except Exception as cleanup_error:
            print(f"Warning: Could not clean up JSON data file: {cleanup_error}")

if __name__ == "__main__":
    main()
