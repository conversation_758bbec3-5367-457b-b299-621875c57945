#!/usr/bin/env python3
"""
Knowledge Ingestion Optimization Script

This script provides additional optimizations and monitoring for the knowledge ingestion process.
Run this alongside your ingestion to get better performance insights and apply runtime optimizations.
"""

import os
import sys
import time
import psutil
import threading
from pathlib import Path

class IngestionOptimizer:
    def __init__(self):
        self.start_time = time.time()
        self.last_file_count = 0
        self.processing_rates = []
        
    def monitor_system_resources(self):
        """Monitor system resources and provide optimization recommendations"""
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk_io = psutil.disk_io_counters()
        
        print(f"\n=== System Resource Monitor ===")
        print(f"CPU Usage: {cpu_percent:.1f}%")
        print(f"Memory Usage: {memory.percent:.1f}% ({memory.used // (1024**3):.1f}GB / {memory.total // (1024**3):.1f}GB)")
        print(f"Available Memory: {memory.available // (1024**3):.1f}GB")
        
        # Provide recommendations based on resource usage
        recommendations = []
        
        if cpu_percent < 50:
            recommendations.append("✅ CPU usage is low - you can increase thread count for faster processing")
        elif cpu_percent > 90:
            recommendations.append("⚠️  CPU usage is high - consider reducing thread count")
            
        if memory.percent < 60:
            recommendations.append("✅ Memory usage is good - you can increase cache size")
        elif memory.percent > 85:
            recommendations.append("⚠️  Memory usage is high - consider reducing cache size")
            
        if recommendations:
            print("\n📊 Optimization Recommendations:")
            for rec in recommendations:
                print(f"  {rec}")
                
        return {
            'cpu_percent': cpu_percent,
            'memory_percent': memory.percent,
            'available_memory_gb': memory.available // (1024**3)
        }
    
    def estimate_completion_time(self, files_processed, total_files):
        """Estimate completion time based on current processing rate"""
        if files_processed == 0:
            return "Calculating..."
            
        elapsed_time = time.time() - self.start_time
        processing_rate = files_processed / elapsed_time  # files per second
        
        if processing_rate > 0:
            remaining_files = total_files - files_processed
            estimated_seconds = remaining_files / processing_rate
            
            hours = int(estimated_seconds // 3600)
            minutes = int((estimated_seconds % 3600) // 60)
            seconds = int(estimated_seconds % 60)
            
            if hours > 0:
                return f"{hours}h {minutes}m {seconds}s"
            elif minutes > 0:
                return f"{minutes}m {seconds}s"
            else:
                return f"{seconds}s"
        
        return "Calculating..."
    
    def calculate_processing_rate(self, files_processed):
        """Calculate current processing rate"""
        elapsed_time = time.time() - self.start_time
        if elapsed_time > 0:
            rate = files_processed / elapsed_time
            return rate
        return 0
    
    def print_optimization_tips(self):
        """Print optimization tips for faster processing"""
        print("\n🚀 Performance Optimization Tips:")
        print("1. ✅ Increased thread count from 15 to 25")
        print("2. ✅ Reduced timeout from 120s to 60s")
        print("3. ✅ Reduced chunk size from 64KB to 32KB")
        print("4. ✅ Increased cache size from 100 to 200")
        print("5. ✅ Added fast-track processing for simple files")
        print("6. ✅ Optimized rate limiting backoff")
        print("7. ✅ Reduced progress update frequency")
        
        print("\n💡 Additional Optimizations You Can Try:")
        print("• Set environment variable: export OMP_NUM_THREADS=1")
        print("• Increase system file descriptor limit: ulimit -n 4096")
        print("• Use SSD storage for faster file I/O")
        print("• Close unnecessary applications to free up memory")
        print("• Consider using a faster LLM model if available")
        
    def monitor_ingestion_progress(self, check_interval=10):
        """Monitor ingestion progress and provide real-time feedback"""
        print("🔍 Starting ingestion monitoring...")
        print("Press Ctrl+C to stop monitoring\n")
        
        self.print_optimization_tips()
        
        try:
            while True:
                # Monitor system resources
                resources = self.monitor_system_resources()
                
                print(f"\n⏱️  Monitoring interval: {check_interval}s")
                print("=" * 50)
                
                time.sleep(check_interval)
                
        except KeyboardInterrupt:
            print("\n👋 Monitoring stopped by user")
            
    def suggest_runtime_optimizations(self, cpu_usage, memory_usage):
        """Suggest runtime optimizations based on current system state"""
        suggestions = []
        
        if cpu_usage < 30:
            suggestions.append("Consider increasing num_ingest_threads to 30-35 for better CPU utilization")
        
        if memory_usage < 50:
            suggestions.append("Consider increasing max_cache_size to 300-500 for better file caching")
            
        if cpu_usage > 95:
            suggestions.append("Consider reducing num_ingest_threads to 15-20 to prevent system overload")
            
        return suggestions

def main():
    print("🚀 Knowledge Ingestion Optimizer")
    print("=" * 40)
    
    optimizer = IngestionOptimizer()
    
    if len(sys.argv) > 1 and sys.argv[1] == "monitor":
        # Run monitoring mode
        check_interval = int(sys.argv[2]) if len(sys.argv) > 2 else 10
        optimizer.monitor_ingestion_progress(check_interval)
    else:
        # Just show optimization tips
        optimizer.print_optimization_tips()
        print("\n📈 To start monitoring, run:")
        print("python optimize_ingestion.py monitor [interval_seconds]")
        print("Example: python optimize_ingestion.py monitor 5")

if __name__ == "__main__":
    main()
