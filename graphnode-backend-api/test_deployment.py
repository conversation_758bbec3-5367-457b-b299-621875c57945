from app.connection.task_queue import TaskQueue
import asyncio
import time
import json
import uuid
from datetime import datetime, timezone

# Task configuration
task_id = "cg3c3cec55"

# Sample React projects to deploy
react_projects = [
    {
        "deployment_id": "react_e2e_app_001",
        "deployment_type": "web",
        "build_command": "npm run build",
        "root_path": "/home/<USER>/workspace/react-e2e-app",
        "project_id": "proj_e2e_001",
        "branch_name": "main",
        "package_manager": "npm"
    },
    {
        "deployment_id": "react_dashboard_002", 
        "deployment_type": "spa",
        "build_command": "yarn build",
        "root_path": "/home/<USER>/workspace/react-dashboard",
        "project_id": "proj_dash_002",
        "branch_name": "develop",
        "package_manager": "yarn"
    },
    {
        "deployment_id": "vite_react_003",
        "deployment_type": "web",
        "build_command": "npm run build",
        "root_path": "/home/<USER>/workspace/vite-react-app",
        "project_id": "proj_vite_003", 
        "branch_name": "feature/new-ui",
        "package_manager": "npm"
    }
]

# Sample build logs for different scenarios
successful_build_logs = [
    "> react-app@1.0.0 build",
    "> react-scripts build",
    "",
    "Creating an optimized production build...",
    "Compiled successfully.",
    "",
    "File sizes after gzip:",
    "",
    "  132.45 KB  build/static/js/main.8f654321.js",
    "  42.31 KB   build/static/css/main.a1b2c3d4.css",
    "  1.25 KB    build/static/js/runtime-main.f9e8d7c6.js",
    "",
    "The project was built to the build folder.",
    "The build folder is ready to be deployed.",
    "",
    "✨ Done in 45.23s."
]

failing_build_logs = [
    "> react-app@1.0.0 build", 
    "> react-scripts build",
    "",
    "Creating an optimized production build...",
    "Failed to compile.",
    "",
    "Module not found: Error: Can't resolve './nonexistent' in '/home/<USER>/workspace/react-app/src'",
    "    at resolve esm",
    "    at process.nextTick",
    "",
    "error Command failed with exit code 1.",
    "info Visit https://yarnpkg.com/en/docs/cli/run for documentation about this command."
]

vite_build_logs = [
    "> vite-react-app@0.1.0 build",
    "> vite build",
    "",
    "vite v4.4.5 building for production...",
    "✓ 127 modules transformed.",
    "✓ 45 modules transformed.",
    "dist/index.html                   0.46 kB │ gzip:  0.30 kB",
    "dist/assets/index-D4f5g6h7.css    1.23 kB │ gzip:  0.62 kB", 
    "dist/assets/index-A1b2c3d4.js   143.21 kB │ gzip: 46.11 kB",
    "",
    "✓ built in 1.89s"
]

def get_current_timestamp():
    """Get current timestamp in ISO 8601 format"""
    return datetime.now(timezone.utc).isoformat()

async def enqueue_build_started(queue, project_data):
    """Enqueue build_started event"""
    event_data = {
        "type": "deployment_queue",
        "event_type": "build_started",
        "deployment_id": project_data["deployment_id"],
        "deployment_type": project_data["deployment_type"],
        "build_command": project_data["build_command"],
        "root_path": project_data["root_path"],
        "project_id": project_data["project_id"],
        "branch_name": project_data["branch_name"],
        "message": "🚀 Build process started",
        "timestamp": get_current_timestamp()
    }
    
    queue.enqueue(task_id, event_data)
    print(f"✅ Enqueued: build_started for {project_data['deployment_id']}")

async def enqueue_build_logs(queue, logs, delay=0.5):
    """Enqueue build_log events for a series of log lines"""
    for log_line in logs:
        if log_line.strip():  # Skip empty lines for cleaner output
            event_data = {
                "type": "deployment_queue", 
                "event_type": "build_log",
                "message": f"🔨 {log_line}",
                "timestamp": get_current_timestamp(),
                "log_level": "info"
            }
            
            queue.enqueue(task_id, event_data)
            print(f"📝 Enqueued log: {log_line[:50]}...")
            await asyncio.sleep(delay)

async def enqueue_build_completed(queue, success=True, exit_code=0):
    """Enqueue build_completed event"""
    status = "success" if success else "failed"
    message = "✅ Build process completed successfully!" if success else f"❌ Build process failed with exit code: {exit_code}"
    
    event_data = {
        "type": "deployment_queue",
        "event_type": "build_completed", 
        "status": status,
        "message": message,
        "exit_code": exit_code,
        "timestamp": get_current_timestamp()
    }
    
    queue.enqueue(task_id, event_data)
    print(f"✅ Enqueued: build_completed ({status})")

async def enqueue_build_final_status(queue, project_data, success=True, return_code=0, error_type=None):
    """Enqueue build_final_status event"""
    status = "success" if success else "failed"
    
    event_data = {
        "type": "deployment_queue",
        "event_type": "build_final_status",
        "deployment_id": project_data["deployment_id"],
        "status": status,
        "return_code": return_code,
        "timestamp": get_current_timestamp()
    }
    
    if success:
        event_data.update({
            "message": "Build completed successfully",
            "build_path": f"{project_data['root_path']}/build"
        })
    else:
        event_data.update({
            "message": f"Build failed with return code {return_code}",
            "error_type": error_type or "Exception"
        })
    
    queue.enqueue(task_id, event_data)
    print(f"✅ Enqueued: build_final_status ({status})")

async def simulate_successful_deployment(queue, project_data):
    """Simulate a complete successful deployment flow"""
    print(f"\n🎯 Starting SUCCESSFUL deployment simulation for: {project_data['deployment_id']}")
    
    # 1. Build started
    await enqueue_build_started(queue, project_data)
    await asyncio.sleep(1)
    
    # 2. Build logs (choose appropriate logs based on project)
    if "vite" in project_data["deployment_id"]:
        logs = vite_build_logs
    else:
        logs = successful_build_logs
        
    await enqueue_build_logs(queue, logs, delay=0.3)
    await asyncio.sleep(1)
    
    # 3. Build completed successfully
    await enqueue_build_completed(queue, success=True, exit_code=0)
    await asyncio.sleep(0.5)
    
    # 4. Final status - success
    await enqueue_build_final_status(queue, project_data, success=True, return_code=0)
    
    print(f"🎉 Completed SUCCESSFUL deployment simulation for: {project_data['deployment_id']}")

async def simulate_failed_deployment(queue, project_data):
    """Simulate a complete failed deployment flow"""
    print(f"\n💥 Starting FAILED deployment simulation for: {project_data['deployment_id']}")
    
    # 1. Build started
    await enqueue_build_started(queue, project_data)
    await asyncio.sleep(1)
    
    # 2. Build logs (partial - will fail)
    await enqueue_build_logs(queue, failing_build_logs, delay=0.4)
    await asyncio.sleep(1)
    
    # 3. Build completed with failure
    await enqueue_build_completed(queue, success=False, exit_code=1)
    await asyncio.sleep(0.5)
    
    # 4. Final status - failed
    await enqueue_build_final_status(queue, project_data, success=False, return_code=1, error_type="CompilationError")
    
    print(f"💥 Completed FAILED deployment simulation for: {project_data['deployment_id']}")

async def simulate_exception_deployment(queue, project_data):
    """Simulate deployment with immediate exception (e.g., path not found)"""
    print(f"\n⚠️ Starting EXCEPTION deployment simulation for: {project_data['deployment_id']}")
    
    # 1. Build started
    await enqueue_build_started(queue, project_data)
    await asyncio.sleep(0.5)
    
    # 2. Immediate final status with error (no logs, no build completed)
    event_data = {
        "type": "deployment_queue",
        "event_type": "build_final_status",
        "deployment_id": project_data["deployment_id"],
        "status": "failed",
        "message": f"Path error: Root path does not exist: {project_data['root_path']}",
        "error_type": "FileNotFoundError",
        "return_code": -1,
        "timestamp": get_current_timestamp()
    }
    
    queue.enqueue(task_id, event_data)
    print(f"✅ Enqueued: build_final_status (FileNotFoundError)")
    
    print(f"⚠️ Completed EXCEPTION deployment simulation for: {project_data['deployment_id']}")

async def message_handler(message):
    """Handle incoming deployment queue messages"""
    print(f"📨 Processing deployment event: {json.dumps(message, indent=2)}")
    
    # Access the data from the queue item structure
    if 'data' in message and message['data'].get('type') == 'deployment_queue':
        data = message['data']
        event_type = data.get('event_type')
        deployment_id = data.get('deployment_id', 'Unknown')
        
        print(f"🎯 Event: {event_type} | Deployment: {deployment_id}")
        
        if event_type == 'build_started':
            print(f"🚀 Started build for {data.get('deployment_type')} project")
            print(f"📁 Path: {data.get('root_path')}")
            print(f"🔧 Command: {data.get('build_command')}")
            
        elif event_type == 'build_log':
            log_content = data.get('message', '').replace('🔨 ', '')
            print(f"📝 LOG: {log_content}")
            
        elif event_type == 'build_completed':
            status = data.get('status')
            exit_code = data.get('exit_code')
            if status == 'success':
                print(f"✅ Build completed successfully (exit code: {exit_code})")
            else:
                print(f"❌ Build failed (exit code: {exit_code})")
                
        elif event_type == 'build_final_status':
            status = data.get('status')
            return_code = data.get('return_code')
            if status == 'success':
                print(f"🎉 FINAL STATUS: SUCCESS | Path: {data.get('build_path')}")
            else:
                error_type = data.get('error_type', 'Unknown')
                print(f"💥 FINAL STATUS: FAILED | Error: {error_type} | Code: {return_code}")

def show_queue_status(queue):
    """Display current queue status"""
    queue_length = queue.get_queue_length(task_id)
    print(f"\n📋 Deployment Queue Status")
    print(f"   Task ID: {task_id}")
    print(f"   Items in queue: {queue_length}")
    
    if queue_length > 0:
        peek_items = queue.peek(task_id, min(5, queue_length))
        print(f"   Next {len(peek_items)} items:")
        for i, item in enumerate(peek_items, 1):
            event_type = item.get('data', {}).get('event_type', 'unknown')
            deployment_id = item.get('data', {}).get('deployment_id', 'N/A')
            print(f"     {i}. {event_type} | {deployment_id}")

async def process_deployment_queue(queue, count=None):
    """Process deployment queue items with proper handling"""
    processed = 0
    print(f"\n🔄 Processing deployment queue items...")
    
    while True:
        item = queue.dequeue(task_id)
        if not item:
            break
            
        print(f"\n📦 Processing item {item['id']}:")
        await message_handler(item)
        processed += 1
        
        if count and processed >= count:
            break
            
        await asyncio.sleep(0.2)  # Brief delay between processing
    
    if processed == 0:
        print("ℹ️ No items to process")
    else:
        print(f"\n✅ Processed {processed} deployment events")

async def run_deployment_scenarios(queue):
    """Run all deployment scenarios"""
    print("🚀 Running comprehensive React deployment scenarios...\n")
    
    # Scenario 1: Successful React app deployment
    await simulate_successful_deployment(queue, react_projects[0])
    await asyncio.sleep(2)
    
    # Scenario 2: Failed React dashboard deployment  
    await simulate_failed_deployment(queue, react_projects[1])
    await asyncio.sleep(2)
    
    # Scenario 3: Exception during Vite app deployment
    await simulate_exception_deployment(queue, react_projects[2])
    
    print(f"\n🎯 All deployment scenarios completed!")

async def main():
    # Create TaskQueue instance
    queue = TaskQueue(queue_path="/tmp/kavia/workspace/.queue")
    
    print(f"🚀 Deployment Queue System Initialized")
    print(f"📋 Task ID: {task_id}")
    
    # Show initial queue status
    show_queue_status(queue)
    
    # Menu options
    print("\n📋 Available Options:")
    print("1. Run All Deployment Scenarios (Success + Failure + Exception)")
    print("2. Simulate Successful React Deployment")
    print("3. Simulate Failed React Deployment") 
    print("4. Simulate Exception Deployment (Path Error)")
    print("5. Show Queue Status")
    print("6. Process Queue Items")
    print("7. Clear Deployment Queue")
    print("8. Custom React Project Deployment")
    
    option = input("\nEnter your choice (1-8): ")
    await asyncio.sleep(0.5)
    
    if option == "1":
        await run_deployment_scenarios(queue)
        
    elif option == "2":
        project = react_projects[0]  # Use first project
        await simulate_successful_deployment(queue, project)
        
    elif option == "3":
        project = react_projects[1]  # Use second project
        await simulate_failed_deployment(queue, project)
        
    elif option == "4":
        project = react_projects[2]  # Use third project
        await simulate_exception_deployment(queue, project)
        
    elif option == "5":
        show_queue_status(queue)
        return
        
    elif option == "6":
        count_input = input("Enter number of items to process (or press Enter for all): ")
        count = int(count_input) if count_input.strip() else None
        await process_deployment_queue(queue, count)
        
    elif option == "7":
        cleared = queue.clear_task(task_id)
        if cleared:
            print(f"✅ Cleared deployment queue for task_id: {task_id}")
        else:
            print(f"ℹ️ No queue found for task_id: {task_id}")
        return
        
    elif option == "8":
        # Custom deployment
        deployment_id = f"custom_react_{uuid.uuid4().hex[:8]}"
        custom_project = {
            "deployment_id": deployment_id,
            "deployment_type": "web",
            "build_command": "npm run build",
            "root_path": "/home/<USER>/workspace/custom-react-app",
            "project_id": f"proj_custom_{uuid.uuid4().hex[:6]}",
            "branch_name": "main",
            "package_manager": "npm"
        }
        
        scenario = input("Choose scenario (success/failure/exception): ").lower()
        if scenario == "success":
            await simulate_successful_deployment(queue, custom_project)
        elif scenario == "failure":
            await simulate_failed_deployment(queue, custom_project)
        elif scenario == "exception":
            await simulate_exception_deployment(queue, custom_project)
        else:
            print("Invalid scenario. Using success by default.")
            await simulate_successful_deployment(queue, custom_project)
    else:
        print("❌ Invalid option selected")
        return

    # Show final queue status
    show_queue_status(queue)
    
    # Ask if user wants to process the queue
    if queue.get_queue_length(task_id) > 0:
        process_now = input("\n🔄 Process the queue now? (y/n): ").lower()
        if process_now == 'y':
            await process_deployment_queue(queue)
            show_queue_status(queue)

    print("\n🎉 Deployment queue operations completed!")

if __name__ == "__main__":
    asyncio.run(main())
