# Connect with password authentication
```
redis-cli -a "RedisKAVia$IDpassPROD"
```

# OR if you prefer not to expose password in command history
```
redis-cli
AUTH RedisKAVia$IDpassPROD
```

## To Find and Backup Redis RDB File

### After authenticating, get the directory where <PERSON><PERSON> saves files
```
CONFIG GET dir
```

### Get the filename of the RDB file
```
CONFIG GET dbfilename
```

### Then you can execute SAVE or BGSAVE command
```
SAVE  
```

check the path -> /var/lib/redis/dump.rdb