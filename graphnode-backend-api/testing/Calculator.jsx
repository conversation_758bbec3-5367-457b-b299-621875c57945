import React, { useState } from 'react';

const Calculator = () => {
  const [display, setDisplay] = useState('0');
  const [previousValue, setPreviousValue] = useState(null);
  const [operation, setOperation] = useState(null);
  const [waitingForOperand, setWaitingForOperand] = useState(false);

  const clearAll = () => {
    setDisplay('0');
    setPreviousValue(null);
    setOperation(null);
    setWaitingForOperand(false);
  };

  const clearDisplay = () => {
    setDisplay('0');
  };

  const toggleSign = () => {
    setDisplay(display.charAt(0) === '-' ? display.substr(1) : '-' + display);
  };

  const inputPercent = () => {
    const value = parseFloat(display);
    setDisplay(String(value / 100));
  };

  const inputDot = () => {
    if (waitingForOperand) {
      setDisplay('0.');
      setWaitingForOperand(false);
      return;
    }
    
    if (display.indexOf('.') === -1) {
      setDisplay(display + '.');
    }
  };

  const inputDigit = (digit) => {
    if (waitingForOperand) {
      setDisplay(String(digit));
      setWaitingForOperand(false);
    } else {
      setDisplay(display === '0' ? String(digit) : display + digit);
    }
  };

  const performOperation = (nextOperation) => {
    const inputValue = parseFloat(display);
    
    if (previousValue === null) {
      setPreviousValue(inputValue);
    } else if (operation) {
      const currentValue = previousValue || 0;
      let newValue;
      
      switch (operation) {
        case '+':
          newValue = currentValue + inputValue;
          break;
        case '-':
          newValue = currentValue - inputValue;
          break;
        case '×':
          newValue = currentValue * inputValue;
          break;
        case '÷':
          newValue = currentValue / inputValue;
          break;
        default:
          newValue = inputValue;
      }
      
      setPreviousValue(newValue);
      setDisplay(String(newValue));
    }
    
    setWaitingForOperand(true);
    setOperation(nextOperation);
  };
  
  return (
    <div className="flex flex-col items-center w-full max-w-xs mx-auto">
      <div className="w-full bg-gray-800 p-4 rounded-t-lg">
        <div className="text-right text-white text-3xl font-bold overflow-hidden">
          {display}
        </div>
      </div>
      
      <div className="w-full bg-gray-700 grid grid-cols-4 gap-1 p-1 rounded-b-lg">
        <button 
          onClick={clearAll} 
          className="bg-gray-500 text-white p-3 text-xl font-semibold rounded hover:bg-gray-400"
        >
          AC
        </button>
        <button 
          onClick={toggleSign}
          className="bg-gray-500 text-white p-3 text-xl font-semibold rounded hover:bg-gray-400"
        >
          +/-
        </button>
        <button 
          onClick={inputPercent}
          className="bg-gray-500 text-white p-3 text-xl font-semibold rounded hover:bg-gray-400"
        >
          %
        </button>
        <button 
          onClick={() => performOperation('÷')}
          className="bg-orange-500 text-white p-3 text-xl font-semibold rounded hover:bg-orange-400"
        >
          ÷
        </button>
        
        <button 
          onClick={() => inputDigit(7)}
          className="bg-gray-600 text-white p-3 text-xl font-semibold rounded hover:bg-gray-500"
        >
          7
        </button>
        <button 
          onClick={() => inputDigit(8)}
          className="bg-gray-600 text-white p-3 text-xl font-semibold rounded hover:bg-gray-500"
        >
          8
        </button>
        <button 
          onClick={() => inputDigit(9)}
          className="bg-gray-600 text-white p-3 text-xl font-semibold rounded hover:bg-gray-500"
        >
          9
        </button>
        <button 
          onClick={() => performOperation('×')}
          className="bg-orange-500 text-white p-3 text-xl font-semibold rounded hover:bg-orange-400"
        >
          ×
        </button>
        
        <button 
          onClick={() => inputDigit(4)}
          className="bg-gray-600 text-white p-3 text-xl font-semibold rounded hover:bg-gray-500"
        >
          4
        </button>
        <button 
          onClick={() => inputDigit(5)}
          className="bg-gray-600 text-white p-3 text-xl font-semibold rounded hover:bg-gray-500"
        >
          5
        </button>
        <button 
          onClick={() => inputDigit(6)}
          className="bg-gray-600 text-white p-3 text-xl font-semibold rounded hover:bg-gray-500"
        >
          6
        </button>
        <button 
          onClick={() => performOperation('-')}
          className="bg-orange-500 text-white p-3 text-xl font-semibold rounded hover:bg-orange-400"
        >
          -
        </button>
        
        <button 
          onClick={() => inputDigit(1)}
          className="bg-gray-600 text-white p-3 text-xl font-semibold rounded hover:bg-gray-500"
        >
          1
        </button>
        <button 
          onClick={() => inputDigit(2)}
          className="bg-gray-600 text-white p-3 text-xl font-semibold rounded hover:bg-gray-500"
        >
          2
        </button>
        <button 
          onClick={() => inputDigit(3)}
          className="bg-gray-600 text-white p-3 text-xl font-semibold rounded hover:bg-gray-500"
        >
          3
        </button>
        <button 
          onClick={() => performOperation('+')}
          className="bg-orange-500 text-white p-3 text-xl font-semibold rounded hover:bg-orange-400"
        >
          +
        </button>
        
        <button 
          onClick={() => inputDigit(0)}
          className="bg-gray-600 text-white p-3 text-xl font-semibold rounded col-span-2 hover:bg-gray-500"
        >
          0
        </button>
        <button 
          onClick={inputDot}
          className="bg-gray-600 text-white p-3 text-xl font-semibold rounded hover:bg-gray-500"
        >
          .
        </button>
        <button 
          onClick={() => performOperation('=')}
          className="bg-orange-500 text-white p-3 text-xl font-semibold rounded hover:bg-orange-400"
        >
          =
        </button>
      </div>
    </div>
  );
};

export default Calculator;