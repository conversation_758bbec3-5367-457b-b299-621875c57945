const readline = require('readline');

class Calculator {
  constructor() {
    this.result = 0;
    this.currentValue = '0';
    this.previousValue = null;
    this.operation = null;
    this.resetInput = true;
    
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    this.showPrompt();
  }
  
  showPrompt() {
    console.log('\n==== Node.js Calculator ====');
    console.log('Available operations: + - * / =');
    console.log('Special commands: c (clear), q (quit)');
    console.log('Current value:', this.currentValue);
    
    this.rl.question('Enter number or operation: ', (input) => {
      this.processInput(input.trim());
    });
  }
  
  processInput(input) {
    // Check for quit command
    if (input.toLowerCase() === 'q') {
      console.log('Calculator closed.');
      this.rl.close();
      return;
    }
    
    // Check for clear command
    if (input.toLowerCase() === 'c') {
      this.clear();
      this.showPrompt();
      return;
    }
    
    // Check if input is an operation
    if (['+', '-', '*', '/', '='].includes(input)) {
      this.handleOperation(input);
      this.showPrompt();
      return;
    }
    
    // Check if input is a number
    const num = parseFloat(input);
    if (!isNaN(num)) {
      this.handleNumber(num);
      this.showPrompt();
      return;
    }
    
    // Invalid input
    console.log('Invalid input. Please try again.');
    this.showPrompt();
  }
  
  handleNumber(num) {
    if (this.resetInput) {
      this.currentValue = num.toString();
      this.resetInput = false;
    } else {
      this.currentValue = this.currentValue + num.toString();
    }
  }
  
  handleOperation(op) {
    const currentNum = parseFloat(this.currentValue);
    
    // If this is the first operation or after a clear
    if (this.previousValue === null) {
      this.previousValue = currentNum;
      this.operation = op;
      this.resetInput = true;
      return;
    }
    
    // If = is pressed or a new operation is selected, calculate the result
    if (op === '=' || this.operation) {
      let result;
      
      switch (this.operation) {
        case '+':
          result = this.previousValue + currentNum;
          break;
        case '-':
          result = this.previousValue - currentNum;
          break;
        case '*':
          result = this.previousValue * currentNum;
          break;
        case '/':
          if (currentNum === 0) {
            console.log('Error: Division by zero!');
            this.clear();
            return;
          }
          result = this.previousValue / currentNum;
          break;
        default:
          result = currentNum;
      }
      
      // Format the result
      this.result = result;
      this.currentValue = this.formatNumber(result);
      console.log(`Result: ${this.currentValue}`);
      
      // If = was pressed, reset for new calculation
      if (op === '=') {
        this.previousValue = null;
        this.operation = null;
      } else {
        // Store result and new operation
        this.previousValue = result;
        this.operation = op;
      }
      
      this.resetInput = true;
    }
  }
  
  formatNumber(num) {
    // Convert to string and limit decimal places for readability
    if (Number.isInteger(num)) {
      return num.toString();
    } else {
      return num.toFixed(8).replace(/\.?0+$/, '');
    }
  }
  
  clear() {
    this.result = 0;
    this.currentValue = '0';
    this.previousValue = null;
    this.operation = null;
    this.resetInput = true;
    console.log('Calculator cleared.');
  }
}

// Start the calculator
console.log('Starting Node.js Calculator...');
new Calculator();