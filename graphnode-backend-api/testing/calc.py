import tkinter as tk
from tkinter import ttk

class Calculator:
    def __init__(self, root):
        self.root = root
        self.root.title("Python Calculator")
        self.root.geometry("300x400")
        self.root.resizable(False, False)
        
        # Set theme colors
        self.root.configure(bg="#2d2d2d")
        
        # Display
        self.display_var = tk.StringVar(value="0")
        self.display = ttk.Entry(
            self.root, 
            textvariable=self.display_var, 
            font=("Arial", 24), 
            justify="right"
        )
        self.display.grid(row=0, column=0, columnspan=4, sticky="nsew", padx=5, pady=5)
        
        # Create calculator state
        self.previous_value = None
        self.operation = None
        self.waiting_for_operand = False
        
        # Create buttons
        self.create_buttons()
        
        # Configure rows and columns to expand
        for i in range(6):
            self.root.grid_rowconfigure(i, weight=1)
        for i in range(4):
            self.root.grid_columnconfigure(i, weight=1)
    
    def create_buttons(self):
        # Button style
        style = ttk.Style()
        style.configure("TButton", font=("Arial", 15))
        
        # Define buttons layout
        buttons = [
            ("C", 1, 0), ("±", 1, 1), ("%", 1, 2), ("÷", 1, 3),
            ("7", 2, 0), ("8", 2, 1), ("9", 2, 2), ("×", 2, 3),
            ("4", 3, 0), ("5", 3, 1), ("6", 3, 2), ("-", 3, 3),
            ("1", 4, 0), ("2", 4, 1), ("3", 4, 2), ("+", 4, 3),
            ("0", 5, 0, 2), (".", 5, 2), ("=", 5, 3)
        ]
        
        # Create all buttons
        for button_info in buttons:
            text = button_info[0]
            row = button_info[1]
            col = button_info[2]
            
            # Check if button spans multiple columns
            col_span = 1
            if len(button_info) > 3:
                col_span = button_info[3]
            
            # Create button
            button = ttk.Button(
                self.root, 
                text=text, 
                command=lambda t=text: self.button_click(t)
            )
            button.grid(row=row, column=col, columnspan=col_span, sticky="nsew", padx=2, pady=2)
    
    def button_click(self, value):
        if value.isdigit():
            self.input_digit(int(value))
        elif value == ".":
            self.input_dot()
        elif value == "C":
            self.clear_all()
        elif value == "±":
            self.toggle_sign()
        elif value == "%":
            self.input_percent()
        elif value in "+-×÷":
            self.perform_operation(value)
        elif value == "=":
            self.calculate_result()
    
    def input_digit(self, digit):
        current = self.display_var.get()
        
        if self.waiting_for_operand:
            self.display_var.set(str(digit))
            self.waiting_for_operand = False
        else:
            if current == "0":
                self.display_var.set(str(digit))
            else:
                self.display_var.set(current + str(digit))
    
    def input_dot(self):
        current = self.display_var.get()
        
        if self.waiting_for_operand:
            self.display_var.set("0.")
            self.waiting_for_operand = False
        elif "." not in current:
            self.display_var.set(current + ".")
    
    def clear_all(self):
        self.display_var.set("0")
        self.previous_value = None
        self.operation = None
        self.waiting_for_operand = False
    
    def toggle_sign(self):
        current = self.display_var.get()
        if current.startswith("-"):
            self.display_var.set(current[1:])
        else:
            self.display_var.set("-" + current)
    
    def input_percent(self):
        current = float(self.display_var.get())
        self.display_var.set(str(current / 100))
    
    def perform_operation(self, op):
        current = float(self.display_var.get())
        
        if self.previous_value is None:
            self.previous_value = current
        elif self.operation:
            self.calculate_result()
            self.previous_value = float(self.display_var.get())
        
        self.operation = op
        self.waiting_for_operand = True
    
    def calculate_result(self):
        if self.previous_value is None or self.operation is None:
            return
        
        current = float(self.display_var.get())
        result = 0
        
        if self.operation == "+":
            result = self.previous_value + current
        elif self.operation == "-":
            result = self.previous_value - current
        elif self.operation == "×":
            result = self.previous_value * current
        elif self.operation == "÷":
            if current != 0:
                result = self.previous_value / current
            else:
                self.display_var.set("Error")
                self.previous_value = None
                self.operation = None
                self.waiting_for_operand = True
                return
        
        # Format result to avoid unnecessary decimal places
        if result == int(result):
            formatted_result = str(int(result))
        else:
            formatted_result = str(result)
        
        self.display_var.set(formatted_result)
        self.previous_value = None
        self.operation = None
        self.waiting_for_operand = True

if __name__ == "__main__":
    root = tk.Tk()
    app = Calculator(root)
    root.mainloop()