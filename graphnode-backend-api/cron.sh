- |
  #!/bin/sh
  START_TIME=$(date +%s) 
  while true; do
    CURRENT_TIME=$(date +%s)
    ELAPSED=$((CURRENT_TIME - START_TIME))

    if [ "$ELAPSED" -gt 55 ]; then
      echo "Time window of 55 seconds elapsed. Exiting..."
      break
    fi
    
    ENV_NAME=beta
    NAMESPACE="duploservices-kavia-beta"
    REQUIRED_COUNT=15
    LABEL_SELECTOR="pod-status=available"
    DEPLOYMENT_LABEL="owner=codegen-pool"

    RUNNING_PODS_RAW=$(kubectl get pods -n "$NAMESPACE" -l "$LABEL_SELECTOR" -o json 2>/dev/null | jq -r ' 
      .items[] | 
      select(.status.phase == "Running" or 
            .status.phase == "Pending" or 
            (.status.containerStatuses[]? | .state.waiting?.reason == "ContainerCreating")) | 
      "\(.metadata.name)\t\(.spec.nodeName)\t\(.status.phase)" ')

    RUNNING_PODS=$(echo "$RUNNING_PODS_RAW" | wc -l)

    echo "Running pods : $RUNNING_PODS"

    if [ "$RUNNING_PODS" -lt "$REQUIRED_COUNT" ]; then
      PODS_TO_CREATE=$((REQUIRED_COUNT - RUNNING_PODS))
      echo "Creating $PODS_TO_CREATE new deployment(s)..."

      for i in $(seq 1 $PODS_TO_CREATE); do
        POD_ID=$(date +%s%N | md5sum | cut -c1-6 | tr -dc '0-9')
        DEPLOY_NAME="codegen-${POD_ID}-beta"
        deployment_name="${POD_ID}-beta"
        echo $deployment_name

        echo "Creating deployment $DEPLOY_NAME..."
        sed "s/{{PROJECT_ID}}/${POD_ID}/g; s/{{ENV_NAME}}/beta/g" /app/codegenservice-deployment.yaml > /tmp/${DEPLOY_NAME}.yaml
        sed "s/{{PROJECT_ID}}/${POD_ID}/g; s/{{ENV_NAME}}/beta/g" /app/pvc/codegenservice-pvc.yaml > /tmp/${DEPLOY_NAME}-pvc.yaml

        echo "Adding tracking label to deployment manifest..."
        yq e '.metadata.labels.owner = "codegen-pool"' -i /tmp/${DEPLOY_NAME}.yaml

        kubectl apply -f /tmp/${DEPLOY_NAME}-pvc.yaml -n "$NAMESPACE"
        kubectl apply -f /tmp/${DEPLOY_NAME}.yaml -n "$NAMESPACE"

        echo " Starting ingress update script..."
        CONFIG_FILE="/tmp/custom_${POD_ID}.conf"
        NGINX_POD_NAME="nginx-0"
        SERVER_NAME="vscode-internal-${POD_ID}-${ENV_NAME}.beta.cloud.kavia.ai"

        PROXY_port1="http://internal-clusterip-${deployment_name}:3000"
        PROXY_port2="http://internal-clusterip-${deployment_name}:3001"
        PROXY_port3="http://internal-clusterip-${deployment_name}:3002"
        PROXY_port4="http://internal-clusterip-${deployment_name}:3003"
        PROXY_port5="http://internal-clusterip-${deployment_name}:3004"
        PROXY_port6="http://internal-clusterip-${deployment_name}:3005"
        PROXY_port7="http://internal-clusterip-${deployment_name}:3006"
        PROXY_port8="http://internal-clusterip-${deployment_name}:3007"
        PROXY_port9="http://internal-clusterip-${deployment_name}:3008"
        PROXY_port10="http://internal-clusterip-${deployment_name}:3009"
        PROXY_port11="http://internal-clusterip-${deployment_name}:3010"
        PROXY_port12="http://internal-clusterip-${deployment_name}:5000"
        PROXY_port13="http://internal-clusterip-${deployment_name}:5001"
        PROXY_port14="http://internal-clusterip-${deployment_name}:5002"
        PROXY_port15="http://internal-clusterip-${deployment_name}:5003"
        PROXY_port16="http://internal-clusterip-${deployment_name}:5004"
        PROXY_port17="http://internal-clusterip-${deployment_name}:5005"
        PROXY_port18="http://internal-clusterip-${deployment_name}:5006"
        PROXY_port19="http://internal-clusterip-${deployment_name}:5007"
        PROXY_port20="http://internal-clusterip-${deployment_name}:5008"
        PROXY_port21="http://internal-clusterip-${deployment_name}:5009"
        PROXY_port22="http://internal-clusterip-${deployment_name}:5010"
        PROXY_port23="http://internal-clusterip-${deployment_name}:8000"
        PROXY_port24="http://internal-clusterip-${deployment_name}:8001"
        PROXY_port25="http://internal-clusterip-${deployment_name}:8002"
        PROXY_port26="http://internal-clusterip-${deployment_name}:8003"
        PROXY_port27="http://internal-clusterip-${deployment_name}:8004"
        PROXY_port28="http://internal-clusterip-${deployment_name}:8005"
        PROXY_port29="http://internal-clusterip-${deployment_name}:8006"
        PROXY_port30="http://internal-clusterip-${deployment_name}:8007"
        PROXY_port31="http://internal-clusterip-${deployment_name}:8008"
        PROXY_port32="http://internal-clusterip-${deployment_name}:8009"
        PROXY_port33="http://internal-clusterip-${deployment_name}:8010"
        PROXY="http://internal-${deployment_name}:8080"

        echo " SERVER_NAME to be added: $SERVER_NAME"
        echo " PROXY to be routed: $PROXY"

        sed "s|{{SERVER_NAME}}|${SERVER_NAME}|g; \
            s|{{PROXY}}|${PROXY}|g; \
            s|{{PROXY_port1}}|${PROXY_port1}|g; \
            s|{{PROXY_port2}}|${PROXY_port2}|g; \
            s|{{PROXY_port3}}|${PROXY_port3}|g; \
            s|{{PROXY_port4}}|${PROXY_port4}|g; \
            s|{{PROXY_port5}}|${PROXY_port5}|g; \
            s|{{PROXY_port6}}|${PROXY_port6}|g; \
            s|{{PROXY_port7}}|${PROXY_port7}|g; \
            s|{{PROXY_port8}}|${PROXY_port8}|g; \
            s|{{PROXY_port9}}|${PROXY_port9}|g; \
            s|{{PROXY_port10}}|${PROXY_port10}|g; \
            s|{{PROXY_port11}}|${PROXY_port11}|g; \
            s|{{PROXY_port12}}|${PROXY_port12}|g; \
            s|{{PROXY_port13}}|${PROXY_port13}|g; \
            s|{{PROXY_port14}}|${PROXY_port14}|g; \
            s|{{PROXY_port15}}|${PROXY_port15}|g; \
            s|{{PROXY_port16}}|${PROXY_port16}|g; \
            s|{{PROXY_port17}}|${PROXY_port17}|g; \
            s|{{PROXY_port18}}|${PROXY_port18}|g; \
            s|{{PROXY_port19}}|${PROXY_port19}|g; \
            s|{{PROXY_port20}}|${PROXY_port20}|g; \
            s|{{PROXY_port21}}|${PROXY_port21}|g; \
            s|{{PROXY_port22}}|${PROXY_port22}|g; \
            s|{{PROXY_port23}}|${PROXY_port23}|g; \
            s|{{PROXY_port24}}|${PROXY_port24}|g; \
            s|{{PROXY_port25}}|${PROXY_port25}|g; \
            s|{{PROXY_port26}}|${PROXY_port26}|g; \
            s|{{PROXY_port27}}|${PROXY_port27}|g; \
            s|{{PROXY_port28}}|${PROXY_port28}|g; \
            s|{{PROXY_port29}}|${PROXY_port29}|g; \
            s|{{PROXY_port30}}|${PROXY_port30}|g; \
            s|{{PROXY_port31}}|${PROXY_port31}|g; \
            s|{{PROXY_port32}}|${PROXY_port32}|g; \
            s|{{PROXY_port33}}|${PROXY_port33}|g" /app/nginx/nginx > "${CONFIG_FILE}"

        echo " Created local config: $CONFIG_FILE"
        DEST_FILE="/etc/nginx/conf.d/$(basename "$CONFIG_FILE")"

        kubectl cp "$CONFIG_FILE" "$NAMESPACE/$NGINX_POD_NAME:$DEST_FILE"
        echo " Copied config into pod: $NGINX_POD_NAME"

        kubectl exec -n "$NAMESPACE" "$NGINX_POD_NAME" -- nginx -s reload
        echo "Reloaded nginx"
      done
    else
      echo "Sufficient pods are running. No action needed."
    fi
    sleep 2
  done
