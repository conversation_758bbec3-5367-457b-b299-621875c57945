"""
This is the controller of code generation.
Do not import any files from app/ as it will break the code generation.
Please check twice before deploying after making changes to this file,
otherwise, the code generation will break.
"""
from fastapi import FastAPI, HTTPException, Query, status, Request, Response, Body
from fastapi.responses import JSONResponse, HTMLResponse
from contextlib import asynccontextmanager
from pydantic import BaseModel, HttpUrl
from typing import Dict, Optional, Literal, Any
from datetime import datetime, timezone
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi.middleware.cors import CORSMiddleware
from pathlib import Path
import subprocess
import logging
import json
from fastapi import APIRouter, HTTPException, BackgroundTasks
import re
import html
import os
import uuid
from functools import lru_cache
import hashlib
import httpx


class Settings:
    MAX_WAIT_TIME: int = 60
    SESSION_PREFIX: str = "codegen"  # Changed from SESSION_NAME to SESSION_PREFIX
    WARMUP_SESSION_NAME: str = "warmup-session"  # Added for the warm-up script
    BASE: Path = Path("/root/app")
    TARGET_DIR: Path = Path("/home/<USER>/workspace")
    DOTENV: Path = BASE / ".env"
    LOGS_DIR: Path = BASE / "logs"
    APP_LOG_FILE: Path = LOGS_DIR / "app.log"
    CODEGEN_LOG_FILE: Path = LOGS_DIR / "codegen.log"
    WARMUP_LOG_FILE: Path = LOGS_DIR / "warmup.log"  # Added for warm-up logs
    STAGES: list[str] = ["dev", "qa", "prod", "experimental"]
    KAVIA_ROOT_TENANT_ID: str = "T0000"
    CODEGEN: bool = True
    LLM_MODEL: str = "claude-3-5-sonnet"

    
    @classmethod
    def get_session_name(cls, container_id: int) -> str:
        """Generate session name using container ID"""
        return f"{cls.SESSION_PREFIX}-{container_id}"
    
    @classmethod
    def get_screen_commands(cls, container_id: int) -> Dict[str, list[str]]:
        session_name = cls.get_session_name(container_id)
        return {
            "start": ['screen', '-L', '-Logfile', str(cls.CODEGEN_LOG_FILE), '-dmS', 
                     session_name, 'python', 'app/batch_jobs/jobs.py'],
            "stop": ['screen', '-S', session_name, '-X', 'quit']
        }

settings = Settings()

class InputArguments(BaseModel):
    project_id: int
    architecture_id: Optional[int] = None  
    container_id: Optional[int] = None     
    task_id: str
    llm_model: str
    tenant_id: str = settings.KAVIA_ROOT_TENANT_ID
    agent_name: str = "CodeGeneration"     # Added this field
    platform: str = "common"
    retry: bool = False

    class Config:
        frozen = True

class SessionData(BaseModel):
    project_id: int
    architecture_id: Optional[int] = None
    task_id: str
    session_name: str
    start_time: datetime
    path: str

class CustomFormatter(logging.Formatter):
    def format(self, record):
        if isinstance(record.msg, dict):
            record.msg = json.dumps(record.msg)
        return super().format(record)

class LogManager:
    def __init__(self, app_log_file: Path, codegen_log_file: Path):
        self.app_log_file = app_log_file
        self.codegen_log_file = codegen_log_file
        
    def initialize_logs(self):
        """Initialize both app and codegen log files"""
        timestamp = datetime.now().isoformat()
        header = f"-------------- LOGS STARTED AT - {timestamp} --------------\n"
        
        for log_file in [self.app_log_file, self.codegen_log_file]:
            log_file.parent.mkdir(parents=True, exist_ok=True)
            if not log_file.exists():
                log_file.write_text(header)

    def setup_logging(self):
        """Configure logging settings"""
        # Ensure the log directory exists
        self.app_log_file.parent.mkdir(parents=True, exist_ok=True)

        # Get the root logger
        logger = logging.getLogger()
        logger.setLevel(logging.INFO)

        # Remove all existing handlers
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # File Handler
        file_handler = logging.FileHandler(str(self.app_log_file))
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

        # Console Handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

        logger.info("Logging initialized")

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Only log paths that are part of our application
        app_paths = ["/start", "/stop", "/status", "/clone_repo", "/view_logs"]
        if request.url.path not in app_paths:
            return await call_next(request)

        request_id = str(uuid.uuid4())
        start_time = datetime.now(timezone.utc)

        # Log the request
        request_log = {
            "request_id": request_id,
            "method": request.method,
            "path": request.url.path,
            "query_params": dict(request.query_params)
        }

        logging.info(f"Request: {json.dumps(request_log, indent=2)}")

        # Process the request and capture the response
        response = await call_next(request)
        
        # Log the response
        process_time = (datetime.now(timezone.utc) - start_time).total_seconds()
        
        response_log = {
            "request_id": request_id,
            "status_code": response.status_code,
            "processing_time": f"{process_time:.3f}s"
        }

        # Try to capture response body for JSON responses
        if response.headers.get("content-type") == "application/json":
            response_body = b""
            async for chunk in response.body_iterator:
                response_body += chunk
            
            try:
                response_log["body"] = json.loads(response_body.decode())
            except:
                response_log["body"] = None

            # Create a new response with the same body
            return Response(
                content=response_body,
                status_code=response.status_code,
                headers=dict(response.headers),
                media_type=response.media_type
            )

        logging.info(f"Response: {json.dumps(response_log, indent=2)}")
        return response

from fastapi import FastAPI, HTTPException, Query, status, Request, Response
from fastapi.responses import JSONResponse
from typing import Dict, Optional
import subprocess
import logging

class ScreenSessionManager:
    @staticmethod
    def is_running(session_name: str) -> bool:
        try:
            # Run screen -ls and capture output
            output = subprocess.check_output(
                ['screen', '-ls'], 
                stderr=subprocess.STDOUT,
                text=True
            )
            
            # More robust session detection using proper parsing
            session_lines = [line.strip() for line in output.splitlines() if '\t' in line]
            for line in session_lines:
                if f".{session_name}\t" in line and not line.endswith('(Dead)'):
                    logging.info(f"Screen session '{session_name}' is currently running.")
                    return True
            logging.info(f"No running session found for name: '{session_name}'")
            return False
        except subprocess.CalledProcessError as e:
            if "No Sockets found" in str(e.output):
                logging.info("No screen sessions found")
                return False
            logging.error(f"Error checking screen sessions: {e.output}")
            return False
        except Exception as e:
            logging.error(f"Unexpected error checking screen sessions: {str(e)}")
            return False

    @staticmethod
    def get_session_details(session_name: str) -> Dict[str, str]:
        """Get detailed information about a screen session"""
        try:
            output = subprocess.check_output(
                ['screen', '-ls'], 
                stderr=subprocess.STDOUT,
                text=True
            )
            
            # More robust session parsing
            session_lines = [line.strip() for line in output.splitlines() if '\t' in line]
            for line in session_lines:
                if f".{session_name}\t" in line:
                    parts = line.strip().split('\t')
                    session_id = parts[0].strip()
                    status = 'Detached'
                    if len(parts) > 2:
                        status_part = parts[-1].strip('()')
                        status = status_part if status_part != 'Detached' else 'Detached'
                    
                    return {
                        "session_id": session_id,
                        "name": session_name,
                        "status": status
                    }
            return {}
            
        except subprocess.CalledProcessError as e:
            if "No Sockets found" in str(e.output):
                logging.info("No screen sessions found")
                return {}
            logging.error(f"Error getting session details: {e.output}")
            return {}
        except Exception as e:
            logging.error(f"Unexpected error getting session details: {str(e)}")
            return {}

    @staticmethod
    def stop_session(session_name: str) -> bool:
        """Stop a screen session safely"""
        try:
            if not ScreenSessionManager.is_running(session_name):
                return True
                
            # Try graceful quit first
            subprocess.run(
                ['screen', '-S', session_name, '-X', 'quit'],
                check=True,
                capture_output=True,
                text=True
            )
            
            # Verify session was stopped
            if ScreenSessionManager.is_running(session_name):
                # If still running, try force kill
                logging.warning(f"Session {session_name} still running after quit, attempting force kill")
                session_details = ScreenSessionManager.get_session_details(session_name)
                if session_details and "session_id" in session_details:
                    subprocess.run(
                        ['screen', '-S', session_details["session_id"], '-X', 'kill'],
                        check=True,
                        capture_output=True,
                        text=True
                    )
            
            return not ScreenSessionManager.is_running(session_name)
            
        except Exception as e:
            logging.error(f"Error stopping session {session_name}: {str(e)}")
            return False



class EnvManager:
    def __init__(self, base_path: Path):
        self.base_path = base_path

    def update_env_file(self, stage: str, input_args: InputArguments, custom_env_vars: Dict[str, str] = {}) -> None:
        env_stage_file = self.base_path / f'.env.{stage}'
        env_file = self.base_path / '.env'

        if not env_stage_file.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Environment file for stage {stage} not found"
            )

        env_vars = self._read_env_file(env_stage_file)
        env_vars.update({
            'FEATURE_FLAG_GIT_TOOL':True,
            'FEATURE_FLAG_USE_DOCKER':True,
            'BATCH_JOB_TRIGGER':True,
            'BATCH_JOB_STAGE': stage,
            'input_arguments': json.dumps(input_args.dict())
        })
        if custom_env_vars != {}:
            env_vars.update(custom_env_vars)
        print(f"Updated env vars: {env_vars}")
        logging.info(f"Updated env vars: {env_vars}")
        self._write_env_file(env_file, env_vars)

    @staticmethod
    def _read_env_file(path: Path) -> Dict[str, str]:
        if not path.exists():
            return {}
        
        env_vars = {}
        for line in path.read_text().splitlines():
            if '=' in line and not line.startswith('#'):
                key, value = line.strip().split('=', 1)
                env_vars[key] = value
        return env_vars

    @staticmethod
    def _write_env_file(path: Path, env_vars: Dict[str, str]) -> None:
        content = '\n'.join(f"{k}={v}" for k, v in env_vars.items())
        path.write_text(content + '\n')

# Response Models
class StartResponse(BaseModel):
    message: str
    session_data: Optional[SessionData]

class StatusResponse(BaseModel):
    status: str
    session_data: Optional[SessionData]

class StopResponse(BaseModel):
    message: str
    session_data: Optional[SessionData]

class CloneResponse(BaseModel):
    message: str
    output: Optional[str]
    path: str

class LogResponse(BaseModel):
    log_type: str
    log_content: str

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logging.info("Application starting up...")
    # Start the warm-up script in background
    # start_warmup_script()
    yield
    # Shutdown
    logging.info("Application shutting down...")
    # Optionally stop the warm-up script on shutdown
    # stop_warmup_script()

# Function to start the warm-up script in a separate screen session
def start_warmup_script() -> None:
    """Start the warm-up script in a separate screen session"""
    session_name = settings.WARMUP_SESSION_NAME
    
    # Check if the warmup script is already running
    if ScreenSessionManager.is_running(session_name):
        logging.info("Warm-up script is already running")
        return
    
    try:
        # Create log file if it doesn't exist
        warmup_log_file = settings.WARMUP_LOG_FILE
        if not warmup_log_file.parent.exists():
            warmup_log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Start the warm-up script in a screen session
        env = os.environ.copy()
        env["PYTHONPATH"] = str(settings.BASE)
        env["STAGE"] ="dev"
        subprocess.run(
            ['screen', '-L', '-Logfile', str(warmup_log_file), 
             '-dmS', session_name, 'python', 'warm_up.py'],
            check=True,
            env=env
        )
        
        logging.info("Warm-up script started successfully in background")
    except Exception as e:
        logging.error(f"Error starting warm-up script: {e}")
        # We don't want to stop the application if the warm-up script fails

# Function to stop the warm-up script
def stop_warmup_script() -> None:
    """Stop the warm-up script screen session"""
    session_name = settings.WARMUP_SESSION_NAME
    
    if not ScreenSessionManager.is_running(session_name):
        logging.info("No warm-up script is currently running")
        return
    
    try:
        if ScreenSessionManager.stop_session(session_name):
            logging.info("Warm-up script has been stopped")
        else:
            logging.error("Failed to stop the warm-up script")
    except Exception as e:
        logging.error(f"Error stopping warm-up script: {e}")

## Initialize settings first
settings = Settings()

# Initialize managers
log_manager = LogManager(settings.APP_LOG_FILE, settings.CODEGEN_LOG_FILE)

# Create log directories and initialize logging
settings.LOGS_DIR.mkdir(parents=True, exist_ok=True)
log_manager.initialize_logs()
log_manager.setup_logging()

# Initialize FastAPI app with lifespan
app = FastAPI(
    title="CodeGen API", 
    version="2.0.0",
    lifespan=lifespan
)

# Add middleware
app.add_middleware(RequestLoggingMiddleware)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize other components
env_manager = EnvManager(settings.BASE)
session_data: Optional[SessionData] = None

# Ensure PYTHONPATH is set
os.environ["PYTHONPATH"] = str(settings.BASE)
logging.info(f"PYTHONPATH set to: {os.environ['PYTHONPATH']}")


def restart_codeserver() -> None:
    try:
        subprocess.run(
            ["docker", "restart", "my-codeserver"],
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        logging.info("Successfully restarted codeserver container")
    except subprocess.CalledProcessError as e:
        logging.error(f"Error restarting codeserver container: {e.stderr.decode()}")
        raise
    except Exception as e:
        logging.error(f"Unexpected error restarting codeserver: {e}")
        raise

def setup_workspace_sync(tenant_id: str, project_id: str) -> None:
    try:
        # Create the workspace sync script path
        script_path = './workspace_sync.sh'
        
        # Run the script with tenant_id and project_id
        subprocess.Popen(
            [script_path, tenant_id, project_id],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        logging.info(f"Started workspace sync for tenant {tenant_id}, project {project_id}")
    except Exception as e:
        logging.error(f"Error starting workspace sync: {e}")
        raise


@app.get("/outbound-check")
async def outbound_check():
    test_url = "https://example.com"  # Choose a reliable, known URL
    timeout = 5.0  # seconds

    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(test_url)
            if response.status_code == 200:
                return {"status": "ok", "message": "Outbound connectivity is working."}
            else:
                raise HTTPException(status_code=503, detail="Unable to reach the test endpoint.")
    except httpx.RequestError as e:
        # This could happen due to DNS issues, network errors, etc.
        raise HTTPException(status_code=503, detail=f"Outbound check failed: {e}")

@app.get("/start", response_model=StartResponse)
async def start_command(
    project_id: int = Query(..., gt=0),
    architecture_id: int = Query(None, gt=0),  # Make optional for maintenance
    container_id: int = Query(None, gt=0),     # Make optional for maintenance
    task_id: str = Query(..., min_length=1),
    stage: str = Query('dev', description="Stage to use for code generation"),
    llm_model: str = Query(settings.LLM_MODEL, description="LLM model to use for code generation"),
    tenant_id: str = Query(settings.KAVIA_ROOT_TENANT_ID, description="Tenant ID for multi-tenant support"),
    agent_name: str = Query("CodeGeneration", description="Agent name to use (CodeGeneration or CodeMaintenance)"),
    platform: str = Query("common", description="Platform to use for code generation"),
    custom_env_vars: Dict[str, str] = Body(default={}, description="Custom environment variables to update in the .env file")
):
    global session_data
    
    # For maintenance, use a fixed container_id since it's project-wide
    if agent_name =="DocumentCreation":
        session_name = f"document-creation-{project_id}"
    elif agent_name == "CodeMaintenance":
        container_id = project_id  # Use project_id as container_id for maintenance
        session_name = f"maintenance-{task_id}-{project_id}"
    else:
        if not container_id or not architecture_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="container_id and architecture_id are required for code generation"
            )
        session_name = settings.get_session_name(container_id)
    
    if ScreenSessionManager.is_running(session_name):
        return StartResponse(
            message=f"{agent_name} Already Started!",
            session_data=session_data
        )

    try:
        # Create input args based on agent type
        input_args_dict = {
            "project_id": project_id,
            "task_id": task_id,
            "llm_model": llm_model,
            "tenant_id": tenant_id,
            "agent_name": agent_name,
            "platform": platform
        }

        # Add architecture_id and container_id only for code generation
        if agent_name == "CodeGeneration":
            input_args_dict.update({
                "architecture_id": architecture_id,
                "container_id": container_id
            })

        input_args = InputArguments(**input_args_dict)
        
        custom_env_vars.update({
            'KAVIA_ROOT_TENANT_ID': tenant_id,
            'CODEGEN': str(settings.CODEGEN)
        })

        env_manager.update_env_file(stage, input_args, custom_env_vars)
        
        # Update the screen command to include PYTHONPATH
        env = os.environ.copy()
        env["PYTHONPATH"] = str(settings.BASE)
        
        setup_workspace_sync(tenant_id=tenant_id, project_id=str(project_id))
        
        # Create screen command based on agent type
        screen_cmd = ['screen', '-L', '-Logfile', str(settings.CODEGEN_LOG_FILE), 
                     '-dmS', session_name, 'python', 'app/batch_jobs/jobs.py']
        
        subprocess.run(
            screen_cmd,
            check=True,
            env=env
        )
        
        session_data = SessionData(
            project_id=project_id,
            architecture_id=architecture_id if agent_name == "CodeGeneration" else None,
            task_id=task_id,
            session_name=session_name,
            start_time=datetime.now(),
            path=str(settings.TARGET_DIR)
        )
        return StartResponse(
            message=f"{agent_name} executed successfully in the background",
            session_data=session_data
        )
    
    except Exception as e:
        logging.error(f"Error starting {agent_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@app.get("/retry", response_model=StartResponse)
async def retry(
    project_id: int = Query(..., gt=0),
    architecture_id: int = Query(..., gt=0),
    container_id: int = Query(..., gt=0),
    task_id: str = Query(..., min_length=1),
    tenant_id: str = Query('', description="tenant_id"),
    stage: str = Query('dev', description="Stage to use for code generation"),
    llm_model: str = Query('claude-3-5-sonnet', description="LLM model to use for code generation")
):
    global session_data
    
    session_name = settings.get_session_name(container_id)
    
    if ScreenSessionManager.is_running(session_name):
        return StartResponse(
            message="Code Generation Already Started!",
            session_data=session_data
        )

    try:
        input_args = InputArguments(
            project_id=project_id,
            architecture_id=architecture_id,
            task_id=task_id,
            container_id=container_id,
            llm_model=llm_model,
            tenant_id=tenant_id,
            retry=True
        )
        
        env_manager.update_env_file(stage, input_args)
        
        # Update the screen command to include PYTHONPATH
        env = os.environ.copy()
        env["PYTHONPATH"] = str(settings.BASE)

        
        subprocess.run(
            settings.get_screen_commands(container_id)["start"],
            check=True,
            env=env
        )
        
        session_data = SessionData(
            project_id=project_id,
            architecture_id=architecture_id,
            task_id=task_id,
            session_name=session_name,
            start_time=datetime.now(),
            path=str(settings.TARGET_DIR)
        )
        
        return StartResponse(
            message="Command executed successfully in the background",
            session_data=session_data
        )
    
    except Exception as e:
        logging.error(f"Error starting command: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@app.get("/status", response_model=StatusResponse)
async def get_status(container_id: int = Query(..., gt=0)):
    global session_data
    
    session_name = settings.get_session_name(container_id)
    # Get detailed session information
    session_details = ScreenSessionManager.get_session_details(session_name)
    is_running = bool(session_details)
    
    # Log detailed status for debugging
    logging.info(f"Session status check: {json.dumps(session_details, indent=2)}")
    
    if is_running:
        if session_data is None:
            # Session is running but we don't have session data
            logging.warning("Screen session is running but no session data found")
            return StatusResponse(
                status="running",
                session_data=None
            )
        return StatusResponse(
            status="running",
            session_data=session_data
        )
    else:
        # Clear session data and env file if not running
        if session_data is not None:
            logging.info("Clearing session data as process is not running")
            session_data = None
            settings.DOTENV.write_text('')
        
        return StatusResponse(
            status="not running",
            session_data=None
        )

@app.get("/stop", response_model=StopResponse)
async def stop_command(
    container_id: Optional[int] = Query(None, gt=0, description="Container ID for code generation"),
    project_id: Optional[int] = Query(None, gt=0, description="Project ID for code maintenance"),
    agent_name: str = Query("CodeGeneration", description="Agent name (CodeGeneration or CodeMaintenance)"),
    task_id: Optional[str] = Query(None, min_length=1, description="Task ID for code maintenance")
):
    global session_data
    
    # Validate inputs based on agent type
    if agent_name == "CodeMaintenance":
        if not project_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="project_id is required for code maintenance"
            )
        if not task_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="task_id is required for code maintenance"
            )
        session_name = f"maintenance-{task_id}-{project_id}"
        
    elif agent_name == "DocumentCreation":
        if not project_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="project_id is required for document creation"
            )
        session_name = f"document-creation-{project_id}"

    else:
        if not container_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="container_id is required for code generation"
            )
        session_name = settings.get_session_name(container_id)
    
    if not ScreenSessionManager.is_running(session_name):
        return StopResponse(
            message=f"No {agent_name} process is currently running.",
            session_data=None
        )
    
    try:
        if ScreenSessionManager.stop_session(session_name):
            # Clear session data and env file after successful stop
            session_data = None
            settings.DOTENV.write_text('')
            
            return StopResponse(
                message=f"{agent_name} process has been stopped.",
                session_data=None
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to stop the {agent_name} process"
            )
            
    except Exception as e:
        logging.error(f"Error stopping {agent_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@app.get("/clone_repo", response_model=CloneResponse)
async def clone_repo(
    repo_url: HttpUrl = Query(..., description="The full URL of the AWS CodeCommit repository"),
    target_dir: str = Query(str(settings.TARGET_DIR), description="Optional target directory for cloning the repository")
):
    target_path = Path(target_dir)
    
    if not target_path.is_dir():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Target directory does not exist or is not a directory"
        )
    
    # Validate AWS CodeCommit URL
    if not re.match(
        r'^https://git-codecommit\.[a-z0-9-]+\.amazonaws\.com/v1/repos/[a-zA-Z0-9._-]+$',
        str(repo_url)
    ):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid AWS CodeCommit repository URL format"
        )
    
    repo_name = str(repo_url).split('/')[-1]
    repo_path = target_path / repo_name
    
    if repo_path.is_dir():
        return CloneResponse(
            message="Repo has been already cloned!",
            path=str(repo_path)
        )
    
    try:
        process = subprocess.run(
            ["git", "clone", str(repo_url), repo_name],
            cwd=str(target_path),
            capture_output=True,
            text=True,
            check=True
        )
        
        logging.info(f"Repository cloned successfully into {target_path}: {process.stdout}")
        
        return CloneResponse(
            message=f"AWS CodeCommit repository '{repo_name}' cloned successfully",
            output=process.stdout,
            path=str(repo_path)
        )
    
    except subprocess.CalledProcessError as e:
        logging.error(f"Error cloning repository: {e.stderr}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error cloning repository: {e.stderr}"
        )

@app.get("/view_logs", response_class=HTMLResponse)
async def view_log(
    log_type: Literal["app", "codegen"] = Query(..., description="Type of log to view: 'app' or 'codegen'"),
    password: str = Query(..., description="Password required to view logs")
):
    # kaviacodegen232
    # Check password hash
    expected_hash = "ed29e38addfafb71b9c8139bad4a9e94e513b9ba4f8001da75a6fbe902b04568"  # SHA256 hash of kavialogs232@
    provided_hash = hashlib.sha256(password.encode()).hexdigest()
    
    if provided_hash != expected_hash:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid password"
        )
        
    log_file = settings.APP_LOG_FILE if log_type == "app" else settings.CODEGEN_LOG_FILE

    if not log_file.exists():
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Log file '{log_type}' not found"
        )
    
    try:
        log_content = log_file.read_text()
        
        html_content = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{log_type.title()} Logs</title>
            <style>
                body {{
                    font-family: 'Courier New', monospace;
                    background: #1e1e1e;
                    color: #d4d4d4;
                    padding: 20px;
                    margin: 0;
                }}
                .container {{
                    max-width: 1200px;
                    margin: 0 auto;
                }}
                .header {{
                    background: #2d2d2d;
                    padding: 15px;
                    border-radius: 5px;
                    margin-bottom: 20px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    position: sticky;
                    top: 0;
                    z-index: 100;
                    background: #2d2d2d;
                }}
                .title {{
                    font-size: 24px;
                    font-weight: bold;
                    color: #61dafb;
                }}
                .controls {{
                    display: flex;
                    gap: 10px;
                }}
                .log-viewer {{
                    background: #252526;
                    border-radius: 5px;
                    padding: 15px;
                    max-height: calc(100vh - 100px);
                    overflow-y: auto;
                }}
                .log-entry {{
                    border-bottom: 1px solid #333;
                    padding: 10px;
                    cursor: pointer;
                }}
                .log-entry:hover {{
                    background: #2d2d2d;
                }}
                .entry-header {{
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }}
                .timestamp {{
                    color: #569cd6;
                    white-space: nowrap;
                }}
                .level {{
                    padding: 2px 6px;
                    border-radius: 3px;
                    font-weight: bold;
                    min-width: 60px;
                    text-align: center;
                }}
                .level-INFO {{
                    background: #294a3d;
                    color: #3c9;
                }}
                .level-WARNING {{
                    background: #4a4629;
                    color: #fc3;
                }}
                .level-ERROR {{
                    background: #4a2929;
                    color: #f66;
                }}
                .preview {{
                    color: #d4d4d4;
                    margin: 5px 0;
                    padding-left: 20px;
                }}
                .details {{
                    display: none;
                    background: #1e1e1e;
                    padding: 15px;
                    margin: 10px 0;
                    border-radius: 3px;
                    white-space: pre-wrap;
                    margin-left: 20px;
                }}
                .search {{
                    background: #3c3c3c;
                    border: 1px solid #555;
                    color: #fff;
                    padding: 5px 10px;
                    border-radius: 3px;
                    min-width: 200px;
                }}
                button {{
                    background: #0078d4;
                    color: white;
                    border: none;
                    padding: 5px 15px;
                    border-radius: 3px;
                    cursor: pointer;
                }}
                button:hover {{
                    background: #106ebe;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="title">{log_type.title()} Logs</div>
                    <div class="controls">
                        <input type="text" id="searchInput" class="search" 
                               placeholder="Search logs...">
                        <button onclick="window.location.reload()">Refresh</button>
                    </div>
                </div>
                <div class="log-viewer">
        """
        
        # Process log lines
        for line in log_content.splitlines():
            if not line.strip():
                continue
                
            # Parse log line with regex
            match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - (\w+) - (.+)', line)
            if match:
                timestamp, level, message = match.groups()
                
                # Try to parse message as JSON if it starts with "Request:" or "Response:"
                try:
                    if message.startswith(('Request:', 'Response:')):
                        msg_type, msg_content = message.split(':', 1)
                        data = json.loads(msg_content)
                        preview = f"{msg_type}: {json.dumps(data, indent=2)}"
                    else:
                        preview = message
                        data = None
                except:
                    preview = message
                    data = None
                
                html_content += f"""
                    <div class="log-entry" onclick="toggleDetails(this)">
                        <div class="entry-header">
                            <span class="timestamp">{timestamp}</span>
                            <span class="level level-{level}">{level}</span>
                        </div>
                        <div class="preview">{html.escape(preview)}</div>
                        {"" if not data else f'<div class="details">{json.dumps(data, indent=2)}</div>'}
                    </div>
                """
            else:
                html_content += f"""
                    <div class="log-entry">
                        <div class="preview">{html.escape(line)}</div>
                    </div>
                """
        
        html_content += """
                </div>
            </div>
            <script>
                function toggleDetails(element) {
                    const details = element.querySelector('.details');
                    if (!details) return;
                    
                    const wasHidden = details.style.display === 'none' || !details.style.display;
                    
                    // Hide all other details
                    document.querySelectorAll('.details').forEach(detail => {
                        detail.style.display = 'none';
                    });
                    
                    // Toggle this detail
                    details.style.display = wasHidden ? 'block' : 'none';
                }
                
                function filterLogs() {
                    const searchText = document.getElementById('searchInput').value.toLowerCase();
                    document.querySelectorAll('.log-entry').forEach(entry => {
                        const text = entry.textContent.toLowerCase();
                        entry.style.display = text.includes(searchText) ? 'block' : 'none';
                    });
                }
                
                document.getElementById('searchInput').addEventListener('input', filterLogs);
                
                // Auto-scroll to bottom on load
                window.addEventListener('load', () => {
                    const logViewer = document.querySelector('.log-viewer');
                    logViewer.scrollTop = logViewer.scrollHeight;
                });
            </script>
        </body>
        </html>
        """
        
        return HTMLResponse(content=html_content, status_code=200)
        
    except Exception as e:
        logging.error(f"Error reading log file '{log_type}': {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error reading log file '{log_type}': {str(e)}"
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8765,
        log_level="info"
    )