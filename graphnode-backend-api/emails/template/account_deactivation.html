<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Deactivation</title>
    <style>
        :root {
            --primary-color: #3b82f6;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --text-color: #1f2937;
            --light-text: #6b7280;
            --bg-color: #ffffff;
            --light-bg: #f3f4f6;
            --border-color: #e5e7eb;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9fafb;
        }
        
        .container {
            background-color: var(--bg-color);
            border-radius: 12px;
            padding: 40px;
            border: 1px solid var(--border-color);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        
        .header {
            text-align: center;
            margin-bottom: 32px;
        }
        
        .notice-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 64px;
            height: 64px;
            background-color: #fee2e2;
            color: var(--danger-color);
            font-size: 32px;
            font-weight: bold;
            border-radius: 50%;
            margin-bottom: 16px;
        }
        
        h1 {
            color: var(--danger-color);
            margin-bottom: 8px;
            font-size: 24px;
        }
        
        .subtitle {
            color: var(--light-text);
            margin-top: 0;
            font-size: 16px;
        }
        
        .highlight {
            background-color: #fff7ed;
            padding: 16px;
            border-radius: 8px;
            border-left: 4px solid var(--warning-color);
            margin: 24px 0;
        }
        
        .btn-container {
            text-align: center;
            margin: 32px 0;
        }
        
        .btn {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            transition: background-color 0.2s;
        }
        
        .btn:hover {
            background-color: #2563eb;
        }
        
        .divider {
            height: 1px;
            background-color: var(--border-color);
            margin: 32px 0;
        }
        
        .footer {
            margin-top: 24px;
            font-size: 14px;
            color: var(--light-text);
            text-align: center;
        }
        
        @media (max-width: 480px) {
            .container {
                padding: 24px;
                border-radius: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="notice-icon">!</div>
            <h1>Account Deactivated</h1>
            <p class="subtitle">Your account has been temporarily disabled</p>
        </div>
        
        <p>Hello %name%,</p>
        <p>We're writing to inform you that your account has been deactivated as requested.</p>
        
        <div class="highlight">
            <p><strong>Important:</strong> If you did not request this action, please contact our support team immediately by clicking the button below.</p>
        </div>
        
        <div class="btn-container">
            <a href="mailto:<EMAIL>" class="btn">Contact Support</a>
        </div>
        
        <p>If you requested this deactivation, no further action is needed. We appreciate the time you've spent with us and hope to see you again in the future.</p>
        
        <div class="divider"></div>
        
        <div class="footer">
            <p>This is an automated message. Please do not reply directly to this email.</p>
            <p>If you need assistance, contact <a href="mailto:<EMAIL>" style="color: var(--primary-color);"><EMAIL></a></p>
            <p>&copy; 2025. All rights reserved.</p>
        </div>
    </div>
</body>
</html>