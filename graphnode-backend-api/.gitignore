# Distribution / packaging
.Python
.vscode/
.pytest_cache/
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
node_modules/
pyvenv/
**/__pycache__/
__pycache__/
pyvenv/*
layers/**/python/
engine/node_modules/
node_modules/
playground/
venv/

# Serverless directories
.serverless
logfile.txt
llm.log
*.log
# Test
test/*
test/
.env
.env.*
!.env.sample
.coverage
.vscode
graph_output.dot
playground/
app/core/tryouts.ipynb
app/utils/prodefn
.DS_Store
test.py 
3rdparty/

logs/*.log
temp/
tmp/
tmp/*
3rdparty/
data/*
app/.DS_Store
query_log.csv
volumes/
.venv
venv/
venv3.10/
pyvenv2/
config.ini
venv2/
.diff
deployments/infrastructure
.idea
.aider*
pyvenv_test/
examples/mermaid_code/container_diagrams.json
examples/mermaid_code/invalid_container_diagrams.json
sessions/
lambda-layer/
testenv/
backups/mongodb_backup
redis_export.json
app/routes/playground-1.mongodb.js
application-dev-key.json
google_credentials.json
docker_screen_clean.sh