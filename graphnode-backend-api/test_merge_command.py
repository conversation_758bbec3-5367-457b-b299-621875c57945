#!/usr/bin/env python3

import sys
import os

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.core.git_controller import GitController
from app.core.git_tools import EnhancedGitTools

def test_merge_command():
    """Test the merge_to_kavia_main command"""
    
    # Mock data for testing
    task_id = "test-task-123"
    directories = ["/tmp/test-repo"]
    
    # Create a mock git tool
    git_tool = EnhancedGitTools(
        callback_functions=None,
        base_path="/tmp/test-repo",
        logger=None,
        repository_metadata=None,
        tenant_id="test-tenant"
    )
    
    # Create GitController instance
    git_controller = GitController(
        task_id=task_id,
        git_tool=git_tool,
        directories=directories,
        ws_client=None,  # Mock websocket client
        db=None,  # Mock database
        repository_metadata=[{
            'repositoryId': 'test-repo-123',
            'repositoryName': 'test-repository',
            'repositoryPath': '/tmp/test-repo'
        }]
    )
    
    print("Testing merge_to_kavia_main command...")
    print(f"Available commands: {list(git_controller._command_map.keys())}")
    
    # Test if the command exists
    if 'merge_to_kavia_main' in git_controller._command_map:
        print("✅ merge_to_kavia_main command found in command map")
        
        # Test calling the command
        try:
            result = git_controller.handle_command('merge_to_kavia_main', {
                'commit_message': 'Test merge commit'
            })
            print(f"✅ Command executed successfully, returned: {result}")
        except Exception as e:
            print(f"❌ Error executing command: {e}")
    else:
        print("❌ merge_to_kavia_main command NOT found in command map")
    
    # Test with a non-existent command
    print("\nTesting with non-existent command...")
    result = git_controller.handle_command('non_existent_command', {})
    print(f"Non-existent command result: {result}")

if __name__ == "__main__":
    test_merge_command() 