1. Login with profile duplo

```
aws ecr get-login-password --region us-west-2 --profile duplo | docker login --username AWS --password-stdin 127214169382.dkr.ecr.us-west-2.amazonaws.com
```

2. Build the docker image for vscode from our repository

```
docker build -t vscode-server -f Dockerfile.vscode .
```

```
docker tag vscode-server:custom_v5 127214169382.dkr.ecr.us-west-2.amazonaws.com/vscode-server:custom_v5
```

```
docker push 127214169382.dkr.ecr.us-west-2.amazonaws.com/vscode-server:custom_v5
```