from dotenv import load_dotenv
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
import sys


load_dotenv()

## SES
IAM_USER_NAME = "noreply"
SMTP_USERNAME = "AKIAQ3EGPRLTYAG6DA4I"
SMTP_PASSWORD = "BMAmjkWh/L3kEE+W9vuwjem6Wv1PLk1P4MA5rwd/ExVm"

subject = "Test Email from Kavia AI"
body = """
Hello Praveen,

This is a test email sent using AWS SES from the Kavia AI system.

Thank you for testing our email delivery system.

Best regards,
Kavia AI Team
"""
to = "@kavia.ai"

smtp_server = "email-smtp.us-east-1.amazonaws.com"
port = 587
sender = "<EMAIL>"  # Using verified domain kavia.ai

print(f"Sending email from {sender} to {to}")
print(f"Using SMTP server: {smtp_server}:{port}")

# Create message
message = MIMEMultipart()
message["From"] = f"Kavia AI <{sender}>"
message["To"] = to
message["Subject"] = subject
message["X-Priority"] = "3"
message["List-Unsubscribe"] = f"<mailto:{sender}?subject=unsubscribe>"
message["X-Mailer"] = "Kavia AI Mailer"
message.attach(MIMEText(body, "plain"))

try:
    # Create SMTP connection
    print("Connecting to SMTP server...")
    server = smtplib.SMTP(smtp_server, port)
    
    # Set debug level
    server.set_debuglevel(1)
    
    print("Starting TLS...")
    server.starttls()  # Secure the connection
    
    print("Logging in...")
    # Login to server
    server.login(SMTP_USERNAME, SMTP_PASSWORD)
    
    # Send email
    print("Sending email...")
    server.sendmail(sender, to, message.as_string())
    print(f"Email sent successfully to {to}")
    
except Exception as e:
    print(f"Error sending email: {e}", file=sys.stderr)
    raise
    
finally:
    # Close connection
    print("Closing connection...")
    try:
        server.quit()
    except Exception as e:
        print(f"Error closing connection: {e}")
        pass

print("Script completed.")

