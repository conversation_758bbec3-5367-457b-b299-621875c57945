import time
from app.knowledge.redis_kg import getRedisKnowledge

# Calculate start time
start_time = time.time()

# Get knowledge from Redis
try:
    
    # 1BujQl-b8233ot-b82377y-b8244m7-b82506o
    existing_knowledge = getRedisKnowledge(id=["b8233ot","b82377y","b8244m7","b82506o"], verbose=True)
    
    # Calculate end time
    end_time = time.time()
    
    # Calculate execution time
    execution_time = end_time - start_time
    
    print(f"Knowledge retrieved in {execution_time:.4f} seconds")
except Exception as e:
    print(f"Error retrieving knowledge: {e}")