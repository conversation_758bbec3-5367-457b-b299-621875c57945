import asyncio
import time
import logging
from app.core.Settings import Settings
from app.knowledge.code_query import KnowledegeBuild

# Setup logging to see progress
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

settings = Settings()
github_token_c1 = settings.RATE_LIMIT_TOKEN_V2

async def clone_until_rate_limit():
    kg = KnowledegeBuild()
    folder_number = 1
    successful_clones = 0
    failed_clones = 0
    start_time = time.time()
    
    print("Starting clone stress test until rate limit...")
    print(f"Target repository: https://github.com/Kavia-ai/filetrace")
    print("-" * 60)
    
    while True:
        clone_path = f'/tmp/clone_repository/v2/{folder_number}'
        
        try:
            print(f"Attempting clone #{folder_number} to {clone_path}")
            
            # Record time before clone
            clone_start = time.time()
            
            # Attempt to clone
            success, path = await kg.clone_repository(
                clone_path,
                'https://github.com/Kavia-ai/filetrace',
                github_token_c1
            )
            
            clone_duration = time.time() - clone_start
            
            if success:
                successful_clones += 1
                elapsed_time = time.time() - start_time
                avg_time_per_clone = elapsed_time / successful_clones
                
                print(f"✅ Clone #{folder_number} successful!")
                print(f"   Duration: {clone_duration:.2f}s")
                print(f"   Total successful: {successful_clones}")
                print(f"   Average time per clone: {avg_time_per_clone:.2f}s")
                print(f"   Total elapsed: {elapsed_time:.2f}s")
                
            else:
                failed_clones += 1
                print(f"❌ Clone #{folder_number} failed!")
                print(f"   This might be due to rate limiting or other issues")
                print(f"   Total failed: {failed_clones}")
                
                # If we start getting failures, it might be rate limiting
                if failed_clones >= 3:
                    print("\n🚫 Multiple failures detected - likely hit rate limit!")
                    break
            
            folder_number += 1
            
            # Small delay between clones to be respectful
            await asyncio.sleep(0.5)
            
        except Exception as e:
            failed_clones += 1
            error_msg = str(e).lower()
            
            print(f"💥 Clone #{folder_number} threw exception: {str(e)}")
            
            # Check if it's a rate limit error
            if any(keyword in error_msg for keyword in ['rate limit', 'forbidden', '403', 'abuse']):
                print(f"\n🚫 RATE LIMIT HIT at clone #{folder_number}!")
                break
            else:
                print(f"   Other error - continuing...")
                if failed_clones >= 5:  # Stop after too many errors
                    print("Too many errors - stopping test")
                    break
            
            folder_number += 1
            await asyncio.sleep(1)  # Wait longer after errors
    
    # Summary
    total_time = time.time() - start_time
    total_attempts = folder_number - 1
    
    print("\n" + "="*60)
    print("CLONE STRESS TEST RESULTS")
    print("="*60)
    print(f"Total clone attempts: {total_attempts}")
    print(f"Successful clones: {successful_clones}")
    print(f"Failed clones: {failed_clones}")
    print(f"Success rate: {(successful_clones/total_attempts)*100:.1f}%")
    print(f"Total test duration: {total_time:.2f} seconds")
    print(f"Average requests per second: {total_attempts/total_time:.2f}")
    
    if successful_clones > 0:
        print(f"Average time per successful clone: {total_time/successful_clones:.2f}s")

# Run the test
if __name__ == "__main__":
    asyncio.run(clone_until_rate_limit())