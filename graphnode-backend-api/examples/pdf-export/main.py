#!/usr/bin/env python3
"""
Optimized Markdown to PDF Converter
Requirements: pip install weasyprint markdown
"""

import markdown
from weasyprint import HTML, CSS
import logging
from pathlib import Path
from typing import Optional, Dict

logger = logging.getLogger(__name__)

def markdown_to_pdf(
    markdown_content: str = None,
    markdown_file: str = None,
    output_pdf: str = "output.pdf",
    css_styles: str = None,
    page_size: str = "A4",
    margin: str = "20mm"
) -> bool:
    """
    Convert Markdown content or file to PDF with professional styling
    
    Args:
        markdown_content: Raw markdown string (optional if markdown_file provided)
        markdown_file: Path to markdown file (optional if markdown_content provided)
        output_pdf: Output PDF file path
        css_styles: Custom CSS styles (optional)
        page_size: Page size (A4, Letter, etc.)
        margin: Page margins
        
    Returns:
        bool: True if successful, False otherwise
        
    Example:
        # From string
        success = markdown_to_pdf(markdown_content="# Hello World")
        
        # From file
        success = markdown_to_pdf(markdown_file="document.md", output_pdf="document.pdf")
    """
    try:
        # Read markdown content
        if markdown_file:
            with open(markdown_file, 'r', encoding='utf-8') as f:
                md_content = f.read()
        elif markdown_content:
            md_content = markdown_content
        else:
            raise ValueError("Either markdown_content or markdown_file must be provided")
        
        # Convert markdown to HTML with extensions
        html_content = markdown.markdown(
            md_content,
            extensions=[
                'tables',           # Table support
                'fenced_code',      # Code blocks
                'toc',             # Table of contents
                'codehilite',      # Syntax highlighting
                'attr_list',       # Attribute lists
                'def_list',        # Definition lists
                'footnotes',       # Footnotes
                'md_in_html'       # Markdown in HTML
            ]
        )
        
        # Professional CSS styling
        default_css = f"""
        @page {{
            size: {page_size};
            margin: {margin};
            @bottom-right {{
                content: "Page " counter(page) " of " counter(pages);
                font-size: 10px;
                color: #666;
            }}
        }}
        
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            font-size: 11pt;
            margin: 0;
            padding: 0;
        }}
        
        h1, h2, h3, h4, h5, h6 {{
            color: #2c3e50;
            margin-top: 24pt;
            margin-bottom: 12pt;
            font-weight: 600;
            page-break-after: avoid;
        }}
        
        h1 {{ 
            font-size: 24pt; 
            border-bottom: 2px solid #3498db; 
            padding-bottom: 8pt;
            page-break-before: always;
        }}
        h1:first-child {{ page-break-before: avoid; }}
        
        h2 {{ 
            font-size: 18pt; 
            border-bottom: 1px solid #bdc3c7; 
            padding-bottom: 4pt;
        }}
        
        h3 {{ font-size: 14pt; }}
        h4 {{ font-size: 12pt; }}
        
        p {{ 
            margin-bottom: 12pt; 
            text-align: justify;
            orphans: 2;
            widows: 2;
        }}
        
        /* Code styling */
        code {{
            background-color: #f8f9fa;
            padding: 2pt 4pt;
            border-radius: 3pt;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 10pt;
            color: #e83e8c;
            border: 1px solid #e9ecef;
        }}
        
        pre {{
            background-color: #f8f9fa;
            padding: 12pt;
            border-radius: 6pt;
            border-left: 4pt solid #007bff;
            overflow-x: auto;
            page-break-inside: avoid;
            margin: 12pt 0;
        }}
        
        pre code {{
            background: none;
            padding: 0;
            border: none;
            color: #333;
        }}
        
        /* Table styling */
        table {{
            border-collapse: collapse;
            width: 100%;
            margin: 16pt 0;
            page-break-inside: avoid;
        }}
        
        th, td {{
            border: 1pt solid #dee2e6;
            padding: 8pt 12pt;
            text-align: left;
            vertical-align: top;
        }}
        
        th {{
            background-color: #007bff;
            color: white;
            font-weight: 600;
            page-break-after: avoid;
        }}
        
        tr:nth-child(even) {{
            background-color: #f8f9fa;
        }}
        
        /* List styling */
        ul, ol {{
            padding-left: 20pt;
            margin-bottom: 12pt;
        }}
        
        li {{
            margin-bottom: 6pt;
            page-break-inside: avoid;
        }}
        
        /* Blockquote styling */
        blockquote {{
            border-left: 4pt solid #007bff;
            margin: 16pt 0;
            padding: 12pt 16pt;
            background-color: #f8f9fa;
            font-style: italic;
            page-break-inside: avoid;
        }}
        
        /* Link styling */
        a {{
            color: #007bff;
            text-decoration: none;
        }}
        
        a:hover {{
            text-decoration: underline;
        }}
        
        /* Image styling */
        img {{
            max-width: 100%;
            height: auto;
            display: block;
            margin: 12pt auto;
        }}
        
        /* Horizontal rule */
        hr {{
            border: none;
            border-top: 1pt solid #dee2e6;
            margin: 24pt 0;
        }}
        
        /* Footnotes */
        .footnote {{
            font-size: 9pt;
            color: #666;
        }}
        
        /* Table of contents */
        .toc {{
            background-color: #f8f9fa;
            padding: 16pt;
            border-radius: 6pt;
            margin-bottom: 24pt;
        }}
        
        .toc ul {{
            list-style-type: none;
            padding-left: 0;
        }}
        
        .toc li {{
            margin-bottom: 4pt;
        }}
        
        /* Print optimizations */
        .page-break {{
            page-break-before: always;
        }}
        
        .no-break {{
            page-break-inside: avoid;
        }}
        """
        
        # Combine with custom CSS
        final_css = default_css
        if css_styles:
            final_css += f"\n\n/* Custom Styles */\n{css_styles}"
        
        # Create complete HTML document
        html_document = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Document</title>
</head>
<body>
    {html_content}
</body>
</html>"""
        
        # Convert to PDF
        html_obj = HTML(string=html_document)
        css_obj = CSS(string=final_css)
        
        html_obj.write_pdf(output_pdf, stylesheets=[css_obj])
        
        logger.info(f"✅ PDF created: {output_pdf}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating PDF: {e}")
        return False

def batch_markdown_to_pdf(input_dir: str, output_dir: str = None) -> Dict[str, bool]:
    """
    Convert multiple markdown files to PDF
    
    Args:
        input_dir: Directory containing .md files
        output_dir: Output directory (defaults to input_dir)
        
    Returns:
        Dict[str, bool]: Results for each file
    """
    input_path = Path(input_dir)
    output_path = Path(output_dir or input_dir)
    output_path.mkdir(exist_ok=True)
    
    results = {}
    
    for md_file in input_path.glob("*.md"):
        pdf_file = output_path / f"{md_file.stem}.pdf"
        success = markdown_to_pdf(
            markdown_file=str(md_file),
            output_pdf=str(pdf_file)
        )
        results[md_file.name] = success
        
    return results

# Example usage and testing
def main():
    """Example usage of the converter"""
    
    # Example 1: Convert from string
    sample_markdown = """
# Technical Documentation

## Overview
This document demonstrates **advanced markdown to PDF conversion** with professional styling.

### Features
- High-quality PDF output
- Professional formatting
- Code syntax highlighting
- Table support

## Code Example

```python
def hello_world():
    print("Hello, World!")
    return True
```

## Data Table

| Feature | Status | Priority |
|---------|--------|----------|
| PDF Export | ✅ Complete | High |
| Styling | ✅ Complete | High |
| Performance | ✅ Optimized | Medium |

> **Note**: This converter provides enterprise-grade PDF generation capabilities.

## Conclusion
The markdown to PDF converter offers professional document generation with minimal dependencies.
"""
    
    # Convert from string
    print("Converting from markdown string...")
    success1 = markdown_to_pdf(
        markdown_content=sample_markdown,
        output_pdf="sample_document.pdf"
    )
    
    # Convert from file (if exists)
    sample_file = "README.md"
    if Path(sample_file).exists():
        print(f"Converting from file: {sample_file}")
        success2 = markdown_to_pdf(
            markdown_file=sample_file,
            output_pdf="readme_document.pdf"
        )
    else:
        print(f"File {sample_file} not found, skipping file conversion example")
        success2 = True
    
    # Results
    if success1:
        print("✅ String conversion successful")
    if success2:
        print("✅ File conversion successful")
    
    print(f"\n📁 Check current directory for generated PDF files")

if __name__ == "__main__":
    main()