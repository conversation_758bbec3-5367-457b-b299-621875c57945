import time
import re
from kubernetes import client, config
from ssm_param_loader import load_ssm_qa_param
from kubernetes.client.rest import ApiException

def create_project_for_beta(project_id):
    env_name = 'beta'
    action = 'create'
    try:
        try:
            config.load_incluster_config()
        except config.ConfigException:
            # Fall back to local kubeconfig
            try:
                config.load_kube_config()
            except config.ConfigException:
                # Fall back to kubeconfig from SSM
                kubeconfig = load_ssm_qa_param()
                config.load_kube_config_from_dict(kubeconfig)
    except Exception as e:
        raise
    
    # Create Kubernetes API client
    batch_v1 = client.BatchV1Api()
    core_v1 = client.CoreV1Api()

    # Define the job name and namespace
    job_name = f"codegen-{project_id}-{env_name}-{action}"
    namespace = "duploservices-kavia-beta"
    previous_create_job = f"codegen-{project_id}-{env_name}-create"
    hostname = f"vscode-internal-{project_id}.{env_name}.cloud.kavia.ai"
    
    # Function to extract pod ID from logs
    def extract_pod_id_from_logs(logs):
        # Pattern to match pod ID in format like "5824-dev-7d54b64dff-q6sjv"
        pod_pattern = r"Using pod: (\d+)-beta-[a-z0-9]+-[a-z0-9]+"
        match = re.search(pod_pattern, logs)
        
        if match:
            return match.group(1)  # Return just the numeric ID part
        return None

    # Function to check if a job exists
    def check_job_exists(job_name, namespace):
        try:
            job = batch_v1.read_namespaced_job(name=job_name, namespace=namespace)
            return True, job
        except ApiException as e:
            if e.status == 404:
                return False, None
            raise e

    # Function to get logs from a job
    def get_job_logs(job_name, namespace):
        try:
            # Get pods associated with the job
            pods = core_v1.list_namespaced_pod(
                namespace=namespace,
                label_selector=f"job-name={job_name}"
            )
            
            code_gen_pod_id = None
            job_logs = ""
            
            # Get logs from each pod and extract pod ID
            for pod in pods.items:
                pod_name = pod.metadata.name
                try:
                    logs = core_v1.read_namespaced_pod_log(
                        name=pod_name,
                        namespace=namespace
                    )
                    job_logs += logs
                    print(f"Logs from pod {pod_name}:")
                    print(logs)
                    
                    # Extract the pod ID from logs
                    temp_id = extract_pod_id_from_logs(logs)
                    if temp_id:
                        code_gen_pod_id = temp_id
                        print(f"Extracted code_gen_pod_id = {code_gen_pod_id}")
                    
                except Exception as e:
                    print(f"Error retrieving logs for pod {pod_name}: {e}")
            
            return code_gen_pod_id, job_logs
        except ApiException as e:
            print(f"Error getting job logs: {e}")
            return None, ""

    # Function to wait for job completion
    def wait_for_job_completion(job_name, namespace):
        while True:
            job = batch_v1.read_namespaced_job(name=job_name, namespace=namespace)
            if job.status.succeeded:
                code_gen_pod_id, _ = get_job_logs(job_name, namespace)
                return code_gen_pod_id
            time.sleep(5)

    # Define the command based on the action
    if action == "create":
        servicename = f"internal-{project_id}-{env_name}"
        kubectl_command = f"""
        echo "Creating deployment for {job_name}..."
        #sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; s/{{{{ENV_NAME}}}}/{env_name}/g" /app/codegenservice-deployment.yaml > /tmp/codegenservice-{project_id}-{env_name}.yaml
        #sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; \
        # s/{{{{ENV_NAME}}}}/{env_name}/g; \
        # s/{{{{hostname}}}}/{hostname}/g; \
        # s/{{{{servicename}}}}/{servicename}/g" \
        # /app/ingress/ingress-service.yaml > /tmp/ingress-patch-{project_id}-{env_name}.yaml
        #sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; \
        #     s/{{{{ENV_NAME}}}}/{env_name}/g" \
        #     /app/pvc/codegenservice-pvc.yaml > /tmp/codegenservice-pvc-{project_id}-{env_name}.yaml
        #sleep 99999
        #kubectl apply -f /tmp/codegenservice-pvc-{project_id}-{env_name}.yaml
        #kubectl apply -f /tmp/codegenservice-{project_id}-{env_name}.yaml
        #kubectl apply -f /tmp/ingress-patch-{project_id}-{env_name}.yaml
        #!/bin/sh
        echo " Starting ingress update script..."
        CONFIG_FILE="/tmp/custom_${{PROJECT_ID}}.conf"
        POD_NAME="nginx-0"
        echo $NAMESPACE
        echo " Environment: $ENV_NAME"
        echo " Finding oldest pod with label 'pod-status=available'..."
        POD_APP=`kubectl get pods -n "$NAMESPACE" -l pod-status=available \\
        --sort-by=.metadata.creationTimestamp \\
        -o jsonpath="{{.items[0].metadata.name}} {{.items[0].metadata.labels.app}}"`
        POD=`echo "$POD_APP" | awk '{{print $1}}'`
        APP=`echo "$POD_APP" | awk '{{print $2}}'`
        echo " Using pod: $POD"
        echo " App label: $APP"
        SERVER_NAME="vscode-internal-${{PROJECT_ID}}-${{ENV_NAME}}.beta.cloud.kavia.ai"
        PROXY_port1="http://internal-clusterip-${{APP}}:3000"
        PROXY_port2="http://internal-clusterip-${{APP}}:3001"
        PROXY_port3="http://internal-clusterip-${{APP}}:3002"
        PROXY_port4="http://internal-clusterip-${{APP}}:3003"
        PROXY_port5="http://internal-clusterip-${{APP}}:3004"
        PROXY_port6="http://internal-clusterip-${{APP}}:3005"
        PROXY_port7="http://internal-clusterip-${{APP}}:3006"
        PROXY_port8="http://internal-clusterip-${{APP}}:3007"
        PROXY_port9="http://internal-clusterip-${{APP}}:3008"
        PROXY_port10="http://internal-clusterip-${{APP}}:3009"
        PROXY_port11="http://internal-clusterip-${{APP}}:3010"
        PROXY_port12="http://internal-clusterip-${{APP}}:5000"
        PROXY_port13="http://internal-clusterip-${{APP}}:5001"
        PROXY_port14="http://internal-clusterip-${{APP}}:5002"
        PROXY_port15="http://internal-clusterip-${{APP}}:5003"
        PROXY_port16="http://internal-clusterip-${{APP}}:5004"
        PROXY_port17="http://internal-clusterip-${{APP}}:5005"
        PROXY_port18="http://internal-clusterip-${{APP}}:5006"
        PROXY_port19="http://internal-clusterip-${{APP}}:5007"
        PROXY_port20="http://internal-clusterip-${{APP}}:5008"
        PROXY_port21="http://internal-clusterip-${{APP}}:5009"
        PROXY_port22="http://internal-clusterip-${{APP}}:5010"
        PROXY_port23="http://internal-clusterip-${{APP}}:8000"
        PROXY_port24="http://internal-clusterip-${{APP}}:8001"
        PROXY_port25="http://internal-clusterip-${{APP}}:8002"
        PROXY_port26="http://internal-clusterip-${{APP}}:8003"
        PROXY_port27="http://internal-clusterip-${{APP}}:8004"
        PROXY_port28="http://internal-clusterip-${{APP}}:8005"
        PROXY_port29="http://internal-clusterip-${{APP}}:8006"
        PROXY_port30="http://internal-clusterip-${{APP}}:8007"
        PROXY_port31="http://internal-clusterip-${{APP}}:8008"
        PROXY_port32="http://internal-clusterip-${{APP}}:8009"
        PROXY_port33="http://internal-clusterip-${{APP}}:8010"    
        PROXY="http://internal-${{APP}}:8080"
        echo " SERVER_NAME to be added: $SERVER_NAME"
        echo " PROXY to be routed: $PROXY"
        sed "s|{{{{SERVER_NAME}}}}|${{SERVER_NAME}}|g; \
        s|{{{{PROXY}}}}|${{PROXY}}|g; \
        s|{{{{PROXY_port1}}}}|${{PROXY_port1}}|g; \
        s|{{{{PROXY_port2}}}}|${{PROXY_port2}}|g; \
        s|{{{{PROXY_port3}}}}|${{PROXY_port3}}|g; \
        s|{{{{PROXY_port4}}}}|${{PROXY_port4}}|g; \
        s|{{{{PROXY_port5}}}}|${{PROXY_port5}}|g; \
        s|{{{{PROXY_port6}}}}|${{PROXY_port6}}|g; \
        s|{{{{PROXY_port7}}}}|${{PROXY_port7}}|g; \
        s|{{{{PROXY_port8}}}}|${{PROXY_port8}}|g; \
        s|{{{{PROXY_port9}}}}|${{PROXY_port9}}|g; \
        s|{{{{PROXY_port10}}}}|${{PROXY_port10}}|g; \
        s|{{{{PROXY_port11}}}}|${{PROXY_port11}}|g; \
        s|{{{{PROXY_port12}}}}|${{PROXY_port12}}|g; \
        s|{{{{PROXY_port13}}}}|${{PROXY_port13}}|g; \
        s|{{{{PROXY_port14}}}}|${{PROXY_port14}}|g; \
        s|{{{{PROXY_port15}}}}|${{PROXY_port15}}|g; \
        s|{{{{PROXY_port16}}}}|${{PROXY_port16}}|g; \
        s|{{{{PROXY_port17}}}}|${{PROXY_port17}}|g; \
        s|{{{{PROXY_port18}}}}|${{PROXY_port18}}|g; \
        s|{{{{PROXY_port19}}}}|${{PROXY_port19}}|g; \
        s|{{{{PROXY_port20}}}}|${{PROXY_port20}}|g; \
        s|{{{{PROXY_port21}}}}|${{PROXY_port21}}|g; \
        s|{{{{PROXY_port22}}}}|${{PROXY_port22}}|g; \
        s|{{{{PROXY_port23}}}}|${{PROXY_port23}}|g; \
        s|{{{{PROXY_port24}}}}|${{PROXY_port24}}|g; \
        s|{{{{PROXY_port25}}}}|${{PROXY_port25}}|g; \
        s|{{{{PROXY_port26}}}}|${{PROXY_port26}}|g; \
        s|{{{{PROXY_port27}}}}|${{PROXY_port27}}|g; \
        s|{{{{PROXY_port28}}}}|${{PROXY_port28}}|g; \
        s|{{{{PROXY_port29}}}}|${{PROXY_port29}}|g; \
        s|{{{{PROXY_port30}}}}|${{PROXY_port30}}|g; \
        s|{{{{PROXY_port31}}}}|${{PROXY_port31}}|g; \
        s|{{{{PROXY_port32}}}}|${{PROXY_port32}}|g; \
        s|{{{{PROXY_port33}}}}|${{PROXY_port33}}|g" /app/nginx/nginx > "${{CONFIG_FILE}}"

        echo " Created local config: $CONFIG_FILE"

        DEST_FILE="/etc/nginx/conf.d/$(basename "$CONFIG_FILE")"
        kubectl cp "$CONFIG_FILE" "$NAMESPACE/$POD_NAME:$DEST_FILE"
        echo " Copied config into pod: $POD_NAME"
        kubectl exec -n "$NAMESPACE" "$POD_NAME" -- nginx -s reload
        echo " Reloaded nginx in pod: $POD_NAME"
        echo " Labeling pod '$POD' as used..."
        kubectl label pod "$POD" -n "$NAMESPACE" pod-status=used --overwrite
        kubectl label deployment "$APP" -n "$NAMESPACE" pod-status=used --overwrite
        """
    elif action == "delete":
        kubectl_command = f"""
        echo "Deleting deployment for {job_name}..."
        sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; s/{{{{ENV_NAME}}}}/{env_name}/g" /app/codegenservice-deployment.yaml > /tmp/codegenservice-{project_id}-{env_name}.yaml
        sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; \
        s/{{{{ENV_NAME}}}}/{env_name}/g; \
        s/{{{{hostname}}}}/{hostname}/g; \
        s/{{{{servicename}}}}/internal-{project_id}-{env_name}/g" \
        /app/ingress/ingress-service.yaml > /tmp/ingress-patch-{project_id}-{env_name}.yaml
        sed "s/{{{{PROJECT_ID}}}}/{project_id}/g; \
            s/{{{{ENV_NAME}}}}/{env_name}/g" \
            /app/pvc/codegenservice-pvc.yaml > /tmp/codegenservice-pvc-{project_id}-{env_name}.yaml
        kubectl delete -f /tmp/codegenservice-{project_id}-{env_name}.yaml --ignore-not-found=true 
        kubectl delete -f /tmp/codegenservice-pvc-{project_id}-{env_name}.yaml --ignore-not-found=true
        kubectl delete -f /tmp/ingress-patch-{project_id}-{env_name}.yaml -n {namespace} --ignore-not-found=true
        echo "Deleting previous job: {previous_create_job}..."
        kubectl delete job {previous_create_job} -n {namespace} --ignore-not-found=true
        echo "Cleanup completed!"
        """

    # Define the job manifest
    job_manifest = {
        "apiVersion": "batch/v1",
        "kind": "Job",
        "metadata": {
            "name": job_name,
            "namespace": namespace,
            "labels": {
                "app": job_name,
                "owner": "duploservices",
                "tenantname": namespace,
            },
        },
        "spec": {
            "parallelism": 1,
            "completions": 1,
            "backoffLimit": 6,
            "template": {
                "metadata": {
                    "labels": {
                        "app": job_name,
                        "owner": "duploservices",
                        "tenantname": namespace,
                    },
                },
                "spec": {
                    "serviceAccountName": "duploservices-kavia-beta-edit-user",
                    "restartPolicy": "Never",
                    "containers": [
                        {
                            "name": "duplocloudcodegen",
                            "image": "bitnami/kubectl:latest",
                            "command": ["/bin/sh", "-c"],
                            "args": [kubectl_command],
                            "env": [
                                {"name": "ACTION", "value": action},
                                {"name": "PROJECT_ID", "value": project_id},
                                {"name": "ENV_NAME", "value": env_name},
                                {"name": "NAMESPACE", "value": namespace},
                            ],
                            "volumeMounts": [
                                {"name": "codegenservicedeploymentbeta", "mountPath": "/app"},
                                {"name": "ingressservicebeta", "mountPath": "/app/ingress"},
                                {"name": "codegenpvc", "mountPath": "/app/pvc"},
                                {"name": "nginx", "mountPath": "/app/nginx"},
                            ],
                        }
                    ],
                    "volumes": [
                        {
                            "name": "codegenservicedeploymentbeta",
                            "configMap": {"name": "codegenservicedeploymentbeta"},
                        },
                        {
                            "name": "ingressservicebeta",
                            "configMap": {"name": "ingressservicebeta"},
                        },
                        {
                            "name": "codegenpvc",
                            "configMap": {"name": "codegenpvc"},
                        },
                        {
                            "name": "nginx",
                            "configMap": {"name": "nginx"},
                        },
                    ],
                },
            },
        },
    }

    # Check if the job already exists
    job_exists, existing_job = check_job_exists(job_name, namespace)
    
    if job_exists:
        print(f"Job {job_name} already exists")
        
        # If job is already completed, get logs and extract pod ID
        if existing_job.status.succeeded:
            print(f"Job {job_name} is already completed")
            code_gen_pod_id, _ = get_job_logs(job_name, namespace)
            
            if action == "create":
                print(f"Ingress hostname: {hostname}")
                
            return code_gen_pod_id
        else:
            # Job exists but not completed yet, wait for it
            print(f"Job {job_name} exists but not completed, waiting...")
            code_gen_pod_id = wait_for_job_completion(job_name, namespace)
            
            if action == "create":
                print(f"Ingress hostname: {hostname}")
                
            return code_gen_pod_id
    
    # Create the job if it doesn't exist
    try:
        response = batch_v1.create_namespaced_job(namespace=namespace, body=job_manifest)
        print(f"Job created: {response.metadata.name}")
        code_gen_id = wait_for_job_completion(job_name, namespace)
        
        if action == "create":
            print(f"Ingress hostname: {hostname}")

        return code_gen_id
    
    except ApiException as e:
        # If job is created by another process while we're checking
        if e.status == 409:  # Conflict error - already exists
            print(f"Job {job_name} was created by another process, waiting for completion...")
            code_gen_id = wait_for_job_completion(job_name, namespace)
            
            if action == "create":
                print(f"Ingress hostname: {hostname}")
                
            return code_gen_id
        else:
            print(f"An error occurred: {e}")
            raise


create_project_for_beta("rdk5656")