#!/usr/bin/env python3
import argparse
import logging
import sys
import re
from kubernetes import client, config
from kubernetes.client.rest import ApiException

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def extract_project_id(pod_name):
    """
    Extract project ID from pod name
    
    Args:
        pod_name (str): Name of the pod
        
    Returns:
        str: Project ID
    """
    # Extract the project ID part (before -dev)
    match = re.match(r'^([\w-]+)-dev', pod_name)
    if match:
        return match.group(1)
    return None

def delete_resources_for_pending_pods(namespace="duploservices-kavia-dev", 
                                     dry_run=True, 
                                     env_name="dev",
                                     pod_list=None):
    """
    Delete all resources (deployments, services, ingress, pods) for pending pods
    
    Args:
        namespace (str): Kubernetes namespace
        dry_run (bool): If True, only print what would be deleted without actually deleting
        env_name (str): Environment name suffix
        pod_list (list): List of pod names to target. If None, will find all pending pods
        
    Returns:
        dict: Summary of deleted resources
    """
    try:
        # Load kubernetes configuration
        config.load_kube_config()
        
        # Initialize clients
        apps_v1 = client.AppsV1Api()
        core_v1 = client.CoreV1Api()
        networking_v1 = client.NetworkingV1Api()
        
        # Initialize counters
        deleted = {
            "deployments": 0,
            "services": 0,
            "ingress": 0,
            "pods": 0
        }
        
        # If no pod list is provided, find all pending pods
        if pod_list is None:
            logger.info(f"No pod list provided. Finding all pending pods with '-{env_name}-' in name...")
            pending_pods = core_v1.list_namespaced_pod(
                namespace=namespace,
                field_selector="status.phase=Pending"
            )
            pod_list = [pod.metadata.name for pod in pending_pods.items if f"-{env_name}-" in pod.metadata.name]
            logger.info(f"Found {len(pod_list)} pending pods")
        
        # Extract project IDs from pod names
        project_ids = set()
        for pod_name in pod_list:
            project_id = extract_project_id(pod_name)
            if project_id:
                project_ids.add(project_id)
        
        logger.info(f"Extracted {len(project_ids)} unique project IDs from pod names")
        
        # Delete resources for each project ID
        action_text = "Would delete" if dry_run else "Deleting"
        
        for project_id in project_ids:
            logger.info(f"Processing resources for project ID: {project_id}")
            
            # 1. Delete deployment
            deployment_name = f"{project_id}-{env_name}"
            logger.info(f"{action_text} deployment: {deployment_name}")
            
            if not dry_run:
                try:
                    apps_v1.delete_namespaced_deployment(
                        name=deployment_name,
                        namespace=namespace,
                        body=client.V1DeleteOptions(
                            propagation_policy='Foreground',
                            grace_period_seconds=0
                        )
                    )
                    deleted["deployments"] += 1
                except ApiException as e:
                    if e.status != 404:  # Ignore "not found" errors
                        logger.error(f"Failed to delete deployment {deployment_name}: {e}")
            
            # 2. Delete service
            service_name = f"internal-{project_id}-{env_name}"
            logger.info(f"{action_text} service: {service_name}")
            
            if not dry_run:
                try:
                    core_v1.delete_namespaced_service(
                        name=service_name,
                        namespace=namespace
                    )
                    deleted["services"] += 1
                except ApiException as e:
                    if e.status != 404:  # Ignore "not found" errors
                        logger.error(f"Failed to delete service {service_name}: {e}")
            
            # 3. Delete ingress
            ingress_name = f"{project_id}-{env_name}"
            logger.info(f"{action_text} ingress: {ingress_name}")
            
            if not dry_run:
                try:
                    networking_v1.delete_namespaced_ingress(
                        name=ingress_name,
                        namespace=namespace
                    )
                    deleted["ingress"] += 1
                except ApiException as e:
                    if e.status != 404:  # Ignore "not found" errors
                        logger.error(f"Failed to delete ingress {ingress_name}: {e}")
            
            # 4. Delete pods related to this project
            related_pods = [pod for pod in pod_list if pod.startswith(f"{project_id}-{env_name}")]
            for pod_name in related_pods:
                logger.info(f"{action_text} pod: {pod_name}")
                if not dry_run:
                    try:
                        core_v1.delete_namespaced_pod(
                            name=pod_name,
                            namespace=namespace,
                            body=client.V1DeleteOptions(
                                grace_period_seconds=0
                            )
                        )
                        deleted["pods"] += 1
                    except ApiException as e:
                        if e.status != 404:  # Ignore "not found" errors
                            logger.error(f"Failed to delete pod {pod_name}: {e}")
        
        # Print summary
        if dry_run:
            logger.info("=" * 50)
            logger.info("DRY RUN SUMMARY (no actual deletions performed):")
            logger.info(f"Would delete {len(project_ids)} projects with all related resources")
            logger.info("=" * 50)
        else:
            logger.info("=" * 50)
            logger.info("Deletion Summary:")
            for resource_type, count in deleted.items():
                logger.info(f"  {resource_type}: {count}")
            logger.info(f"Total resources deleted: {sum(deleted.values())}")
            logger.info("=" * 50)
        
        return deleted
    
    except Exception as e:
        logger.error(f"Error while deleting resources: {str(e)}")
        return deleted

def main():
    parser = argparse.ArgumentParser(description='Delete resources for pending pods in Kubernetes.')
    parser.add_argument('--namespace', '-n', default='duploservices-kavia-dev', 
                      help='Kubernetes namespace (default: duploservices-kavia-dev)')
    parser.add_argument('--env-name', '-e', default='dev', 
                      help='Environment name suffix (default: dev)')
    parser.add_argument('--dry-run', '-d', action='store_true', 
                      help='Dry run mode - only print what would be deleted')
    parser.add_argument('--pod-list-file', '-f', 
                      help='File containing list of pod names (one per line)')
    
    args = parser.parse_args()
    
    # Load pod list from file if provided
    pod_list = None
    if args.pod_list_file:
        try:
            with open(args.pod_list_file, 'r') as f:
                pod_list = [line.strip() for line in f if line.strip()]
            logger.info(f"Loaded {len(pod_list)} pod names from {args.pod_list_file}")
        except Exception as e:
            logger.error(f"Error loading pod list from file: {e}")
            sys.exit(1)
    
    # Use predefined list of pending pods (from the output provided)
    else:
        pod_list = [
            "110474-dev-547588565b-4kjj5", 
            "110557-dev-f948b4b8d-4pcc2", 
            "110578-dev-7cb7999956-zl5fz", 
            "110587-dev-76b99f7868-scnsk", 
            "110599-dev-8d65f9544-hfh2h", 
            "110608-dev-7d67bdffb6-6kqmf", 
            "110709-dev-64dc69c4b-cwbzj", 
            "110967-dev-6d9cf66d5d-sn7mz", 
            "117521-dev-6d599cfbcb-lnxxk", 
            "138514-dev-8944458dd-l6xcz", 
            "145251-dev-7c5db49dcb-55rvw", 
            "148514-dev-68f4d4688b-27hnw", 
            "324-dev-58854d965f-xqvcl", 
            "5739-dev-79b96ccb6b-fnmms", 
            "79274-dev-6575585cb4-gwq4w"
        ]
        logger.info(f"Using predefined list of {len(pod_list)} pending pods")
    
    delete_resources_for_pending_pods(
        namespace=args.namespace,
        dry_run=args.dry_run,
        env_name=args.env_name,
        pod_list=pod_list
    )

if __name__ == "__main__":
    main()