- >
  #!/bin/sh 


  set -e  


  echo "Starting Kubernetes Pod Monitor setup..."


  if ! command -v pip &> /dev/null && ! command -v pip3 &> /dev/null; then
      echo "pip installed successfully."
  else
      echo "PIP installation not found"
      exit 1
  fi


  PIP_CMD="pip"

  if ! command -v pip &> /dev/null && command -v pip3 &> /dev/null; then
      PIP_CMD="pip3"
  fi


  TMP_DIR="/tmp/pod_monitor_$(date +%s)"

  mkdir -p "$TMP_DIR"

  echo "Created temporary directory at: $TMP_DIR"


  LOG_FILE="$TMP_DIR/pod_monitor.log"

  touch "$LOG_FILE"

  echo "Log file created at: $LOG_FILE"


  PYTHON_FILE="$TMP_DIR/pod_monitor.py"

  echo "Creating Python script at: $PYTHON_FILE"


  echo "Installing required Python dependencies..."

  $PIP_CMD install pymongo kubernetes --no-cache-dir


  cat > "$PYTHON_FILE" << 'EOL'

  #!/usr/bin/env python3

  """

  Kubernetes Pod Monitor - Using native Kubernetes client

  """

  import datetime

  import time

  from typing import Dict, List

  import pymongo

  from kubernetes import client, config


  # MongoDB connection settings

  MONGO_URI =
  "mongodb://root:<EMAIL>:27017/?authSource=admin"

  DB_NAME = "kubernetes_monitor"

  COLLECTION_NAME = "pods_available"


  def get_mongo_client():
      """Create and return a MongoDB client"""
      try:
          client = pymongo.MongoClient(MONGO_URI)
          # Ping the server to check connection
          client.admin.command('ping')
          print("Connected to MongoDB")
          return client
      except Exception as e:
          print(f"Failed to connect to MongoDB: {e}")
          raise

  def get_available_codegen_pods(namespace="duploservices-k-qa01",
  mongo_uri="mongodb://root:<EMAIL>:27017/?authSource=admin"):
    """
    Get all running pods with label 'service=codegen' that have status 'available'
    by checking their corresponding ConfigMaps. Also removes used pods from database.
    
    Args:
        namespace: The Kubernetes namespace
        mongo_uri: MongoDB connection URI
        
    Returns:
        Tuple of (list of pod detail dictionaries, count of available pods)
    """
    from kubernetes import client, config
    import datetime
    import time
    import re
    import pymongo
    
    start_time = time.time()
    
    # Load Kubernetes configuration
    try:
        try:
            config.load_incluster_config()
        except config.ConfigException:
            config.load_kube_config()
    except Exception as e:
        print(f"Error loading Kubernetes config: {e}")
        return [], 0
    
    # Initialize the Kubernetes API clients
    core_v1 = client.CoreV1Api()
    
    # Get all running pods with the specified label
    try:
        pods = core_v1.list_namespaced_pod(
            namespace=namespace,
            label_selector="service=codegen",
            field_selector="status.phase=Running"
        )
    except Exception as e:
        print(f"Error listing pods: {e}")
        return [], 0
    
    pod_details = []
    used_pod_names = []  # Track pods that are marked as "used"
    
    # For each pod, check if status is "available" using ConfigMap
    for pod in pods.items:
        pod_name = pod.metadata.name
        print(f"---- {pod_name} ----")
        
        try:
            # Extract the project ID from the pod name
            match = re.match(r'([\w\d]+)-qa-', pod_name)
            if not match:
                print(f"Could not extract project ID from pod name: {pod_name}")
                continue
                
            project_id = match.group(1)
            configmap_name = f"pod-status-{project_id}-qa"
            
            # Get the ConfigMap for this pod
            try:
                config_map = core_v1.read_namespaced_config_map(
                    name=configmap_name,
                    namespace=namespace
                )
                
                # Check pod status from ConfigMap
                pod_status = config_map.data.get("pod-status", "")
                
                print(f"Pod {pod_name} status from ConfigMap: {pod_status}")
                
                if pod_status == "available":
                    # Get container statuses
                    container_statuses = pod.status.container_statuses or []
                    
                    # Calculate ready status (all containers ready / total containers)
                    ready_containers = sum(1 for container in container_statuses if container.ready)
                    total_containers = len(container_statuses)
                    ready = f"{ready_containers}/{total_containers}"
                    
                    # Get restart count (sum of all container restarts)
                    restart_count = sum(container.restart_count for container in container_statuses)
                    
                    # Create pod info dictionary
                    pod_info = {
                        "name": pod_name,
                        "ready": ready,
                        "status": pod.status.phase,
                        "restarts": restart_count,
                        "age": pod.metadata.creation_timestamp.isoformat(),
                        "ip": pod.status.pod_ip or "",
                        "node": pod.spec.node_name or "",
                        "timestamp": datetime.datetime.utcnow(),
                        "project_id": project_id
                    }
                    
                    pod_details.append(pod_info)
                    print(f"Pod {pod_name} is available")
                else:
                    print(f"Pod {pod_name} status not 'available': {pod_status}")
                    used_pod_names.append(pod_name)  # Add to list of used pods
            
            except client.exceptions.ApiException as api_e:
                if api_e.status == 404:
                    # If ConfigMap doesn't exist, assume pod is available
                    print(f"ConfigMap {configmap_name} not found, assuming pod is available")
                    
                    # Get container statuses
                    container_statuses = pod.status.container_statuses or []
                    
                    # Calculate ready status
                    ready_containers = sum(1 for container in container_statuses if container.ready)
                    total_containers = len(container_statuses)
                    ready = f"{ready_containers}/{total_containers}"
                    
                    # Get restart count
                    restart_count = sum(container.restart_count for container in container_statuses)
                    
                    # Create pod info dictionary
                    pod_info = {
                        "name": pod_name,
                        "ready": ready,
                        "status": pod.status.phase,
                        "restarts": restart_count,
                        "age": pod.metadata.creation_timestamp.isoformat(),
                        "ip": pod.status.pod_ip or "",
                        "node": pod.spec.node_name or "",
                        "timestamp": datetime.datetime.utcnow(),
                        "project_id": project_id
                    }
                    
                    pod_details.append(pod_info)
                    print(f"Pod {pod_name} is assumed available (no ConfigMap)")
                else:
                    print(f"Error getting ConfigMap for pod {pod_name}: {api_e}")
                
        except Exception as e:
            print(f"Error processing pod {pod_name}: {e}")
    
    count = len(pod_details)
    print(f"Available pods: {count}")
    
    # Connect to MongoDB to update pod information
    try:
        # Create MongoDB client
        mongo_client = pymongo.MongoClient(mongo_uri)
        db = mongo_client["kubernetes_monitor"]
        collection = db["pods_available"]
        
        # Get list of current available pod names
        current_pod_names = {pod["name"] for pod in pod_details}
        
        # Find pods in DB that are either:
        # 1. No longer running, or
        # 2. Marked as "used" in ConfigMaps
        pods_to_remove = collection.find({
            "$or": [
                {"name": {"$nin": list(current_pod_names)}},
                {"name": {"$in": used_pod_names}}
            ]
        })
        
        # Count and list pods to be removed
        remove_count = 0
        remove_names = []
        
        for pod in pods_to_remove:
            remove_count += 1
            remove_names.append(pod["name"])
            print(f"Pod to remove from database: {pod['name']}")
        
        if remove_count > 0:
            print(f"Found {remove_count} pods to remove from database")
            # Automatically remove without prompting
            result = collection.delete_many({
                "$or": [
                    {"name": {"$nin": list(current_pod_names)}},
                    {"name": {"$in": used_pod_names}}
                ]
            })
            print(f"Removed {result.deleted_count} pods from database")
            
        # Update MongoDB with current available pods
        for pod_info in pod_details:
            # Check if pod exists in DB
            existing = collection.find_one({"name": pod_info["name"]})
            
            if existing:
                # Update existing pod
                collection.update_one(
                    {"name": pod_info["name"]},
                    {"$set": pod_info}
                )
            else:
                # Insert new pod
                collection.insert_one(pod_info)
        
        print(f"Successfully updated {len(pod_details)} available pods in database")
        
    except Exception as e:
        print(f"Error updating MongoDB: {e}")
    
    end_time = time.time()
    print(f"Execution time: {end_time - start_time:.2f} seconds")
    
    return pod_details

  def ensure_unique_index(collection):
      """Ensure a unique index exists on the name field"""
      # Check existing indexes
      existing_indexes = collection.index_information()
      
      # If name_1 index exists but is not unique, drop it
      if "name_1" in existing_indexes:
          # Check if it's already unique
          if existing_indexes["name_1"].get("unique", False):
              print("Unique index already exists")
              return
          
          # Drop the non-unique index
          print("Dropping non-unique index")
          collection.drop_index("name_1")
      
      # Create unique index
      print("Creating unique index on 'name' field")
      collection.create_index([("name", pymongo.ASCENDING)], unique=True)

  def update_mongo_pods(pod_details: List[Dict]):
      """
      Update MongoDB with pod details, ensuring no duplicates
      """
      client = get_mongo_client()
      db = client[DB_NAME]
      collection = db[COLLECTION_NAME]
      
      # Ensure unique index exists
      ensure_unique_index(collection)
      
      update_count = 0
      insert_count = 0
      
      # Update each pod in the collection
      for pod_info in pod_details:
          try:
              # Check if pod exists first
              existing_pod = collection.find_one({"name": pod_info["name"]})
              
              if existing_pod:
                  # Update existing pod
                  result = collection.update_one(
                      {"name": pod_info["name"]},
                      {"$set": pod_info}
                  )
                  if result.modified_count > 0:
                      update_count += 1
              else:
                  # Insert new pod
                  collection.insert_one(pod_info)
                  insert_count += 1
                  
          except Exception as e:
              print(f"Error updating pod {pod_info['name']}: {e}")
      
      print(f"Updated {update_count} existing pods and inserted {insert_count} new pods in MongoDB")
      
      # Get total count of pods in collection
      total_pods = collection.count_documents({})
      print(f"Total pods in collection: {total_pods}")
      
      # Check for any pods in DB that are no longer running
      current_pod_names = {pod["name"] for pod in pod_details}
      old_pods = collection.find({"name": {"$nin": list(current_pod_names)}})
      old_pod_count = 0
      
      for old_pod in old_pods:
          old_pod_count += 1
          print(f"Pod in database but not running: {old_pod['name']}")
      
      if old_pod_count > 0:
          print(f"Found {old_pod_count} pods in database that are no longer running")
          remove = input("Would you like to remove these pods from the database? (y/n): ")
          if remove.lower() == 'y':
              result = collection.delete_many({"name": {"$nin": list(current_pod_names)}})
              print(f"Removed {result.deleted_count} old pods from database")
      
      client.close()

  def main():
      start_time = time.time()
      
      try:
          # Get pod details from Kubernetes
          print("Fetching pod details from Kubernetes...")
          pod_details = get_available_codegen_pods(mongo_uri=MONGO_URI)
          print(f"Found {len(pod_details)} running pods")
          
          # Update MongoDB with pod details
          print("Updating MongoDB...")
          update_mongo_pods(pod_details)
          
      except Exception as e:
          print(f"Error: {e}")
      
      # Calculate execution time
      execution_time = time.time() - start_time
      print(f"Script completed in {execution_time:.2f} seconds")

  if __name__ == "__main__":
      for i in range(0,6):
          main()
  EOL


  sed -i "s|LOG_FILE = \"/tmp/pod_monitor.log\"|LOG_FILE = \"$LOG_FILE\"|"
  "$PYTHON_FILE"


  chmod +x "$PYTHON_FILE"


  echo "Running Kubernetes Pod Monitor..."

  python3 "$PYTHON_FILE"


  if [ $? -eq 0 ]; then
      echo "Kubernetes Pod Monitor executed successfully."
      echo "Log file is available at: $LOG_FILE"
  else
      echo "Error: Kubernetes Pod Monitor execution failed."
      exit 1
  fi


  echo "Script execution complete."

  echo "--- Log File Contents ---" 

  cat "$LOG_FILE"
