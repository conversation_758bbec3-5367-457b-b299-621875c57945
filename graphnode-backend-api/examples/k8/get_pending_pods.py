#!/usr/bin/env python3
import argparse
import logging
import sys
import json
from kubernetes import client, config
from kubernetes.client.rest import ApiException
from tabulate import tabulate

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


def get_pending_pods(namespace="duploservices-kavia-dev", name_filter="-dev-", 
                     output_format="table", field_selector=None):
    """
    Get all pending pods in the specified namespace that have the given name filter.
    
    Args:
        namespace (str): Kubernetes namespace to search in
        name_filter (str): String to filter pod names (default: "-dev-")
        output_format (str): Output format - "table", "json", or "yaml"
        field_selector (str): Additional field selector for filtering pods
        
    Returns:
        list: List of pending pods matching the criteria
    """
    try:
        # Load kubernetes configuration
        config.load_kube_config()
        
        # Initialize core API client
        core_v1 = client.CoreV1Api()
        
        # Build the field selector for pending pods
        if field_selector:
            field_selector = f"status.phase=Pending,{field_selector}"
        else:
            field_selector = "status.phase=Pending"
        
        logger.info(f"Searching for pending pods in namespace '{namespace}' with name filter '{name_filter}'")
        
        # List all pods with the Pending status
        pods = core_v1.list_namespaced_pod(
            namespace=namespace,
            field_selector=field_selector
        )
        
        # Filter pods that have the name filter
        filtered_pods = []
        for pod in pods.items:
            pod_name = pod.metadata.name
            if name_filter in pod_name:
                pod_data = {
                    "name": pod_name,
                    "status": pod.status.phase,
                    "reason": get_pod_pending_reason(pod),
                    "age": get_pod_age(pod),
                    "node": pod.spec.node_name if pod.spec.node_name else "N/A",
                    "ip": pod.status.pod_ip if pod.status.pod_ip else "N/A",
                    "namespace": pod.metadata.namespace
                }
                filtered_pods.append(pod_data)
        
        logger.info(f"Found {len(filtered_pods)} pending pods matching the filter.")
        
        # Output the results based on requested format
        if output_format == "json":
            print(json.dumps(filtered_pods, indent=2))
        elif output_format == "yaml":
            import yaml
            print(yaml.dump(filtered_pods, default_flow_style=False))
        else:  # Default to table format
            if filtered_pods:
                headers = ["Name", "Status", "Reason", "Age", "Node", "IP", "Namespace"]
                table_data = [[pod["name"], pod["status"], pod["reason"], pod["age"], 
                              pod["node"], pod["ip"], pod["namespace"]] 
                              for pod in filtered_pods]
                print(tabulate(table_data, headers=headers, tablefmt="grid"))
            else:
                print("No pending pods found matching the criteria.")
        
        return filtered_pods
        
    except ApiException as e:
        logger.error(f"Kubernetes API error: {e}")
        return []
    except Exception as e:
        logger.error(f"Error getting pending pods: {str(e)}")
        return []


def get_pod_pending_reason(pod):
    """
    Extract the reason why a pod is pending
    
    Args:
        pod: Kubernetes pod object
        
    Returns:
        str: Reason for pending status
    """
    # Check container statuses for waiting reasons
    if pod.status.container_statuses:
        for container_status in pod.status.container_statuses:
            if container_status.state.waiting:
                return container_status.state.waiting.reason
    
    # Check pod conditions
    if pod.status.conditions:
        for condition in pod.status.conditions:
            if condition.status == 'False' and condition.reason:
                return condition.reason
    
    # Check for unschedulable condition
    if pod.status.conditions:
        for condition in pod.status.conditions:
            if condition.type == 'PodScheduled' and condition.status == 'False':
                if condition.reason == 'Unschedulable':
                    return f"Unschedulable: {condition.message}"
    
    # Check init container statuses
    if pod.status.init_container_statuses:
        for container_status in pod.status.init_container_statuses:
            if container_status.state.waiting:
                return f"Init:{container_status.state.waiting.reason}"
    
    return "Unknown"


def get_pod_age(pod):
    """
    Calculate the age of a pod from its creation timestamp
    
    Args:
        pod: Kubernetes pod object
        
    Returns:
        str: Age in human-readable format
    """
    from datetime import datetime, timezone
    
    if pod.metadata.creation_timestamp:
        creation_time = pod.metadata.creation_timestamp
        current_time = datetime.now(timezone.utc)
        
        # Calculate the time difference
        time_diff = current_time - creation_time
        
        # Convert to human-readable format
        days = time_diff.days
        hours, remainder = divmod(time_diff.seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        if days > 0:
            return f"{days}d{hours}h"
        elif hours > 0:
            return f"{hours}h{minutes}m"
        else:
            return f"{minutes}m{seconds}s"
    
    return "Unknown"


def main():
    parser = argparse.ArgumentParser(description='Get pending pods in Kubernetes with specified name pattern.')
    parser.add_argument('--namespace', '-n', default='duploservices-kavia-dev', 
                      help='Kubernetes namespace to search (default: duploservices-kavia-dev)')
    parser.add_argument('--name-filter', '-f', default='-dev-', 
                      help='String to filter pod names (default: -dev-)')
    parser.add_argument('--output', '-o', choices=['table', 'json', 'yaml'], default='table',
                      help='Output format (default: table)')
    parser.add_argument('--field-selector', '-s', 
                      help='Additional field selector for filtering pods')
    
    args = parser.parse_args()
    
    get_pending_pods(
        namespace=args.namespace,
        name_filter=args.name_filter,
        output_format=args.output,
        field_selector=args.field_selector
    )


if __name__ == "__main__":
    main()