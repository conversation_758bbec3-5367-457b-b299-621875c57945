
import time
from app.utils.k8.create_project_v2 import change_to_used_for_dev, change_to_used_for_qa, change_to_used_for_beta, create_project_for_beta

start_time = time.time()

change_to_used_for_dev("7980")

  # Record end time
end_time = time.time()
elapsed_time = end_time - start_time
elapsed_seconds = int(elapsed_time)

print(f"Finished execution of create_project_for_qa at {end_time}")
print(f"Total execution time: {elapsed_seconds} seconds")