from github import Github
import os
import argparse

def get_latest_commit_files(repo_name, branch_name, access_token):
    g = Github(access_token)
    
    try:
        repo = g.get_repo(repo_name)
        branch = repo.get_branch(branch_name)
        latest_commit = branch.commit
        files = latest_commit.files
        
        print(f"Files changed in latest commit ({latest_commit.sha}) on branch {branch_name}:")
        for file in files:
            print(f"File: {file.filename}")
            print(f"Status: {file.status}")
            print(f"Changes: +{file.additions}, -{file.deletions}")
            print("---")
            
    except Exception as e:
        print(f"Error: {str(e)}")
    
    finally:
        g.close()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Get latest commit files from GitHub')
    parser.add_argument('--token', required=True, help='GitHub access token')
    parser.add_argument('--repo', default="Kavia-ai/CodeGenerationAgent", help='Repository name')
    parser.add_argument('--branch', default="kavia-main", help='Branch name')
    
    args = parser.parse_args()
    
    get_latest_commit_files(args.repo, args.branch, args.token)