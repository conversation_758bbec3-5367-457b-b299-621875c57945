import os
import time
import threading
import json
import hashlib

class ParallelKnowledgeFileCreator:
    def __init__(self, base_path, num_ingest_threads=7):
        self.base_path = base_path
        self.num_ingest_threads = num_ingest_threads
        self.discovered_files = []
        self.created_files = []
        self._lock = threading.Lock()  # Same as original Knowledge class
        self._ingest_queue = []  # Same as original - simple list, not Queue
        self.thread_workers = []
        self.stop_workers = False
        self.workers_stopped = 0
        
        # Create .knowledge directory
        self.knowledge_dir = os.path.join(base_path, '.knowledge')
        os.makedirs(self.knowledge_dir, exist_ok=True)
        
        # Same ingestible file types as original Knowledge class
        self.ingestible_filetypes = [
            # Web Development and Frontend
            '.js', '.jsx', '.ts', '.tsx', '.html', '.htm', '.xml', '.xhtml', 
            '.css', '.scss', '.less', 
            # Data and Configuration
            '.json', '.yaml', '.yml', 
            # JavaScript Modules
            '.mjs', '.cjs',
            # Python
            '.py', '.pyi', '.ipynb',
            # C/C++ Family
            '.c', '.h', '.cpp', '.cc', '.cxx', '.c++', '.hpp', '.hxx', '.hh',
            # Modern Systems Programming
            '.rs', 
            # JVM Languages
            '.java', '.kt', '.kts', '.scala', '.sc',
            # Apple Development
            '.swift', '.m', '.mm',
            # Other Major Languages
            '.cs', '.go', 
            # PHP
            '.php', '.php3', '.php4', '.php5', '.phps', '.phtml',
            # Ruby
            '.rb', '.erb', '.rhtml',
            # Perl
            '.pl', '.pm', '.t',
            # Shell and Scripting
            '.sh', '.bash', '.zsh', '.fish', '.csh', '.tcsh', '.ksh',
            # Build Systems
            '.Makefile', '.makefile', '.mk', '.cmake', '.ninja', '.gradle', '.sbt',
            # Lisp Family
            '.lisp', '.cl', '.el', '.scm', '.rkt',
            # Low-level Programming
            '.asm', '.s', '.a51', '.inc',
            # Functional Programming
            '.hs', '.lhs', '.erl', '.hrl', '.ex', '.exs', '.fs', '.fsi', '.fsx',
            # Other Languages
            '.dart', '.lua', '.r', '.R', '.m', '.mat', '.fig', '.jl', '.pl',
            '.pro', '.cob', '.cbl', '.cpy',
            # Scientific Computing
            '.f', '.for', '.f90', '.f95', '.f03', '.f08',
            # Legacy Systems
            '.pas', '.pp', '.inc', '.dpr', '.dfm',
            # Database
            '.sql',
            # Templates and Configuration
            '.j2', '.jinja', '.jinja2', '.toml', '.ini',
            # Documentation
            '.md', '.rst', '.adoc', '.asciidoc',
            # Special Formats
            '.gitignore', '.prompt', '.ebnf', '.bnf', '.peg'
        ]

    def _is_ingestible(self, item_path):
        """Same filtering logic as original Knowledge class"""
        item = os.path.basename(item_path)
        ingestible = True
        
        if item.startswith('.'):
            ingestible = False
        if item_path.endswith('.log'):
            ingestible = False
        if item_path.endswith('files.yaml'):
            ingestible = False
        if item_path.endswith('package-lock.json'):
            ingestible = False
            
        good_suffix = False
        for suffix in self.ingestible_filetypes:
            if item_path.lower().endswith(suffix):
                good_suffix = True
                break
        if not good_suffix:
            ingestible = False
            
        return ingestible

    def _list_important_files(self, base_folder):
        """Same file discovery logic as original Knowledge class"""
        filelist = []

        def list_files_in_folder(folder_path):
            files = []
            try:
                items = os.listdir(folder_path)
            except (FileNotFoundError, PermissionError, NotADirectoryError):
                return files
                
            for item in items:
                item_path = os.path.join(folder_path, item)
                try:
                    nest = os.listdir(item_path + "/")
                    if len(nest) > 0:
                        skip_folder = False
                        if item.startswith('.'):
                            skip_folder = True
                        for suffix in ['/coverage', '/node_modules', 'venv', 'env', 'pyenv', 'data', 'logs']:
                            if item_path.endswith(suffix):
                                skip_folder = True
                        if not skip_folder:
                            files.extend(list_files_in_folder(item_path))
                    else:
                        # It's a file
                        add = self._is_ingestible(item_path)
                        if add:
                            # Convert to relative path from base_path
                            relative_path = os.path.relpath(item_path, self.base_path)
                            # Normalize path separators to forward slashes
                            relative_path = relative_path.replace(os.sep, '/')
                            files.append(relative_path)
                except (FileNotFoundError, PermissionError, NotADirectoryError):
                    # It's a file, not a directory
                    add = self._is_ingestible(item_path)
                    if add:
                        # Convert to relative path from base_path
                        relative_path = os.path.relpath(item_path, self.base_path)
                        # Normalize path separators to forward slashes
                        relative_path = relative_path.replace(os.sep, '/')
                        files.append(relative_path)
            return files

        filelist.extend(list_files_in_folder(base_folder))
        return filelist

    def _convert_path_to_knowledge_filename(self, file_path):
        """Convert file path to .knowledge filename format (same as original)"""
        # Replace forward slashes with underscores (same as original: filename.replace('/', '_'))
        filename = file_path.replace('/', '_')
        # Replace backslashes with underscores (Windows compatibility)
        filename = filename.replace('\\', '_')
        return filename

    class _Worker:
        """Same Worker pattern as original Knowledge class"""
        def __init__(self, creator, name):
            self.creator = creator
            self.name = name
            self.thread_worker = None
            self.stop_worker = False
            self.worker_stopped = False

        def start(self):
            self.thread_worker = threading.Thread(target=self._worker_loop, daemon=True)
            self.thread_worker.start()

        def stop(self):
            self.stop_worker = True
            if self.thread_worker:
                self.thread_worker.join()

        def _service_ingest_queue(self):
            """Same queue servicing logic as original Knowledge class"""
            filename = None
            creator = self.creator
            
            # Same locking pattern as original
            with creator._lock:
                if creator._ingest_queue:
                    filename = creator._ingest_queue[0]
                    creator._ingest_queue = creator._ingest_queue[1:]
                    
            if filename:
                print(f"Worker {self.name}: Processing {filename}")
                self._create_knowledge_file(filename)

        def _create_knowledge_file(self, file_path):
            """Create .knowledge file (same format as original)"""
            try:
                # Convert to .knowledge filename format
                knowledge_filename = self.creator._convert_path_to_knowledge_filename(file_path)
                output_path = os.path.join(self.creator.knowledge_dir, knowledge_filename)
                
                # Get file info
                full_file_path = os.path.join(self.creator.base_path, file_path)
                file_size = 0
                file_hash = ""
                try:
                    file_size = os.path.getsize(full_file_path)
                    # Same hash calculation as original
                    with open(full_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        file_hash = hashlib.md5(content.encode("utf-8")).hexdigest()
                except:
                    pass
                
                # Get file extension
                _, ext = os.path.splitext(file_path)
                
                # Create file content (same format as original Knowledge system)
                file_data = {
                    "filename": file_path,
                    "format-version": 4,  # Same as original _KNOWLEDGE_FORMAT_VERSION
                    "is_source_file": True,
                    "file_id": len(self.creator.created_files) + 1,
                    "git_revision": f"test_revision_{self.name}",
                    "state": 1,  # _STATE_INITIAL (files ready)
                    "description": f"Test file processed by worker {self.name}",
                    "external_files": [],
                    "external_methods": [],
                    "published": [],
                    "classes": [],
                    "methods": [],
                    "calls": [],
                    "search-terms": [
                        os.path.basename(file_path),
                        ext[1:] if ext else "unknown",
                        f"worker_{self.name}",
                        "parallel_test"
                    ],
                    "format": ext[1:] if ext else "unknown",
                    "knowledge_revision": 1,
                    "revision_history": [{"1": f"test_revision_{self.name}"}],
                    "hash": file_hash,
                    "code-base-name": "default",
                    "ctags": [],
                    "size": file_size
                }
                
                # Write file to disk (same as original)
                with open(output_path, 'w') as f:
                    json.dump(file_data, f, indent=2)
                
                # Thread-safe addition to created files list
                with self.creator._lock:
                    self.creator.created_files.append(output_path)
                
                # Simulate processing time
                time.sleep(0.001)  # 1ms per file
                
            except Exception as e:
                print(f"Worker {self.name}: Error creating knowledge file for {file_path}: {e}")

        def _worker_loop(self):
            """Same worker loop pattern as original Knowledge class"""
            print(f"Knowledge worker {self.name} start")
            
            while True:
                try:
                    self._service_ingest_queue()
                except Exception as e:
                    print(f"Exception servicing ingest queue in {self.name}: {str(e)}")

                # Same completion logic as original
                with self.creator._lock:
                    if not self.creator._ingest_queue:
                        self.creator.workers_stopped += 1
                        if self.creator.workers_stopped >= len(self.creator.thread_workers):
                            break

                if self.stop_worker:
                    self.worker_stopped = True
                    break
                    
                # Small sleep to prevent busy waiting
                time.sleep(0.001)
                
            print(f"Knowledge worker {self.name} done")

    def run_parallel_knowledge_creation(self):
        """Main method following same pattern as original Knowledge.start()"""
        print(f"Starting parallel knowledge file creation with {self.num_ingest_threads} threads")
        print(f"Base path: {self.base_path}")
        print(f"Knowledge directory: {self.knowledge_dir}")
        
        # Step 1: Discover files (same as original)
        print("\nStep 1: Discovering files...")
        discovery_start = time.time()
        
        self.discovered_files = self._list_important_files(self.base_path)
        
        discovery_end = time.time()
        discovery_time = discovery_end - discovery_start
        
        print(f"Files discovered: {len(self.discovered_files)}")
        print(f"Discovery time: {discovery_time:.4f} seconds")
        
        if not self.discovered_files:
            print("No ingestible files found!")
            return []
        
        # Step 2: Populate ingest queue (same as original)
        with self._lock:
            self._ingest_queue.extend(self.discovered_files)
        
        # Step 3: Create workers (same pattern as original)
        print(f"\nStep 2: Creating .knowledge files with {self.num_ingest_threads} threads...")
        creation_start = time.time()
        
        # Same worker creation logic as original
        num_workers = min(self.num_ingest_threads, len(self._ingest_queue))
        
        worker_index = 0
        while worker_index < num_workers:
            worker_index += 1
            thread_worker = self._Worker(self, f"kw{worker_index}")
            thread_worker.start()
            self.thread_workers.append(thread_worker)
        
        # Wait for all workers to complete (same as original synchronous mode)
        for worker in self.thread_workers:
            worker.thread_worker.join()
        
        # End timing
        creation_end = time.time()
        creation_time = creation_end - creation_start
        total_time = discovery_time + creation_time
        
        # Step 4: Count files in .knowledge folder
        knowledge_files_count = self._count_knowledge_files()
        
        # Results
        print(f"\n{'='*70}")
        print(f"PARALLEL KNOWLEDGE FILE CREATION RESULTS")
        print(f"{'='*70}")
        print(f"Base path: {self.base_path}")
        print(f"Knowledge directory: {self.knowledge_dir}")
        print(f"Threads used: {num_workers}")
        print(f"Files discovered: {len(self.discovered_files)}")
        print(f"Knowledge files created: {len(self.created_files)}")
        print(f"Files in .knowledge folder: {knowledge_files_count}")
        print(f"")
        print(f"Discovery time: {discovery_time:.4f} seconds")
        print(f"Creation time: {creation_time:.4f} seconds")
        print(f"Total time: {total_time:.4f} seconds")
        
        if len(self.created_files) > 0:
            print(f"Files per second: {len(self.created_files)/creation_time:.2f}")
            print(f"Average time per file: {creation_time/len(self.created_files):.6f} seconds")
        
        print(f"{'='*70}")
        
        # Show sample files
        if self.discovered_files:
            print(f"\nSample discovered files:")
            for i, file_path in enumerate(self.discovered_files[:10], 1):
                print(f"  {i}. {file_path}")
        
        if self.created_files:
            print(f"\nSample .knowledge files created:")
            for i, file_path in enumerate(self.created_files[:10], 1):
                filename = os.path.basename(file_path)
                print(f"  {i}. {filename}")
        
        return self.created_files

    def _count_knowledge_files(self):
        """Count actual files in .knowledge directory"""
        try:
            knowledge_files = [f for f in os.listdir(self.knowledge_dir) 
                             if os.path.isfile(os.path.join(self.knowledge_dir, f)) 
                             and not f.startswith('.')]
            return len(knowledge_files)
        except:
            return 0


def main():
    """Test parallel knowledge file creation using original Knowledge patterns"""
    base_path = input("Enter base path (or press Enter for current directory): ").strip()
    if not base_path:
        base_path = os.getcwd()
    
    if not os.path.exists(base_path):
        print(f"Error: Path {base_path} does not exist!")
        return
    
    num_threads = input("Enter number of threads (default 7): ").strip()
    if not num_threads:
        num_threads = 7
    else:
        num_threads = int(num_threads)
    
    # Create and run knowledge file creation
    creator = ParallelKnowledgeFileCreator(base_path, num_threads)
    created_files = creator.run_parallel_knowledge_creation()
    
    print(f"\nFinal .knowledge folder count: {creator._count_knowledge_files()}")


if __name__ == "__main__":
    main()