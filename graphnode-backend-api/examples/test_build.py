import asyncio
from datetime import datetime
import sys
import os

from app.connection.establish_db_connection import get_mongo_db
from app.knowledge.redis_kg import add_redis_support_to_knowledge
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import time
import uuid
from app.utils.kg_inspect.knowledge import Knowledge, KnowledgeCodeBase
from app.utils.kg_inspect.knowledge_reporter import Reporter
from app.utils.kg_inspect.knowledge_helper import Knowledge_Helper
from app.core.websocket.client import WebSocketClient
from app.core.Settings import settings
from app.knowledge.code_query import KnowledegeBuild
import logging

async def main():
    print("knowledge_cli")
    
    session_id = 'test-123'
    project_id = '123'
    user_id = '123'
    build_id = "b4950384"
    base_path = '/home/<USER>/kavia-ai/graphnode-backend-api/data/Kavia-ai/filetrace/main'
    code_base_path = [
        KnowledgeCodeBase(base_path, 'filetrace')
    ]
    
    repo = {
        
    }

    session_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='kg_sessions',
            user_id = user_id
        )
    
    ws_client = WebSocketClient(session_id, settings.WEBSOCKET_URI)
    reporter = Reporter(ws_client)
    reporter.send_message("code_ingestion", {
        'info': "Build_Status_Update",
        'message': 'Setting up the repository',
        'status': 'Processing',
        'buildId': build_id 
    }) 
    build_ids = ""
    knowledge_helper = Knowledge_Helper(session_id, reporter, os.getcwd(), code_base_path)    
    knowledge = Knowledge.getKnowledge(id=session_id)
    add_redis_support_to_knowledge(knowledge)
    total_files = knowledge.get_file_count(base_path) 
    knowledge.start()
    
    kg = KnowledegeBuild()

    while( True ):
        await kg.update_build_times(project_id, [knowledge.build_id], "last_updated", False, repo['service'], user_id)  
                   
        if knowledge._state == 2:
            knowledge.save_to_redis()
            await kg.update_build_times(project_id, build_ids, "end_time", False, repo['service'], user_id)
            if repo['service'] == 'github' and repo['repo_type'] == "private":
                for branch in repo['branches']:
                    if branch['builds']['build_id'] in build_ids:
                        build_path = branch['builds']['path']
                        if build_path:
                            # Try direct push first
                            
                            can_push = await kg.try_to_commit(build_path, branch['name'])
                            if can_push:
                                
                                # Update commit hash
                                os.chdir(build_path)
                                _hash = os.popen('git rev-parse HEAD').read().strip()
                                await kg.update_commit_hash(_hash, project_id, branch['builds']['build_id'])
                                logging.info(f"Successfully pushed changes to {repo['repository_name']}")
                                await kg.update_kg_status(2, project_id, build_ids, user_id=user_id)
                                reporter.send_message("code_ingestion", knowledge.get_kg_progress(total_files))
        
                            else:
                                logging.info("Error while pushing the code")
                                await kg.update_kg_status(-1, project_id, build_ids, user_id=user_id)
            else:
                await kg.update_kg_status_by_id(2, project_id, build_ids[0], None, False, repo['service'], user_id)
                            
            # Update session status to completed
            await session_handler.update_one(
                filter={"session_id": session_id},
                element={
                    "session_status": "Completed",
                    "updated_at": datetime.utcnow()
                },db=session_handler.db
            )

            reporter.send_message("code_ingestion", {'info': 'Repo_Data_Update' })
            print("Knowledge ingestion completed. Exiting...")
            break
        
        await asyncio.sleep(1)
    
    # Cleanup after completion
    Knowledge_Helper.cleanup(str(session_id))
    
    logging.info("Knowledge graph generation completed successfully")
    
main()