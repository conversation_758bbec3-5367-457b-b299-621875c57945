#!/bin/bash

# AWS DataSync Task Monitor Script
# This script starts or checks status of a DataSync task and monitors its execution

# Exit immediately if a command exits with a non-zero status
set -e

# Default Configuration
REGION="us-west-2"
# How often to check task status (in seconds)
CHECK_INTERVAL=30
# Maximum execution time in seconds (4 hours by default)
MAX_EXECUTION_TIME=14400
# Log directory
LOG_DIR="/tmp/logs"

# Set AWS credentials
export AWS_ACCESS_KEY_ID="********************"
export AWS_SECRET_ACCESS_KEY="lKrtEC4QQHIBINYL3Wl1ujlDkuzaXzYuHWr68SIP"

# Display usage information
usage() {
    echo "Usage: $0 COMMAND [OPTIONS]"
    echo
    echo "Commands:"
    echo "  --start TASK_ID      Start the specified task in background (non-blocking)"
    echo "  --status TASK_ID     Get detailed status of the specified task"
    echo "  --monitor EXEC_ID    Monitor a specific task execution"
    echo "  --help               Display this help message"
    echo
    echo "Options:"
    echo "  --region REGION      AWS Region (default: us-west-2)"
    echo "  --interval SECONDS   Monitoring check interval in seconds (default: 30)"
    echo "  --timeout SECONDS    Maximum execution monitoring time in seconds (default: 14400)"
    echo
    echo "Examples:"
    echo "  $0 --start task-0c19347f3a7f8e910"
    echo "  $0 --status task-0c19347f3a7f8e910"
    echo "  $0 --monitor exec-056ee49a077783797"
    exit 1
}

# Parse command-line options
TASK_ID=""
EXECUTION_ID=""
OPERATION=""

# First argument should be a command
if [[ $# -lt 1 ]]; then
    usage
fi

# Parse command
case "$1" in
    --start)
        OPERATION="start"
        if [[ $# -lt 2 ]]; then
            echo "Error: TASK_ID is required for --start command"
            usage
        fi
        TASK_ID="$2"
        shift 2
        ;;
    --status)
        OPERATION="status"
        if [[ $# -lt 2 ]]; then
            echo "Error: TASK_ID is required for --status command"
            usage
        fi
        TASK_ID="$2"
        shift 2
        ;;
    --monitor)
        OPERATION="monitor"
        if [[ $# -lt 2 ]]; then
            echo "Error: EXEC_ID is required for --monitor command"
            usage
        fi
        EXECUTION_ID="$2"
        shift 2
        ;;
    --help)
        usage
        ;;
    *)
        echo "Unknown command: $1"
        usage
        ;;
esac

# Parse remaining options
while [[ $# -gt 0 ]]; do
    case "$1" in
        --region)
            REGION="$2"
            shift 2
            ;;
        --interval)
            CHECK_INTERVAL="$2"
            shift 2
            ;;
        --timeout)
            MAX_EXECUTION_TIME="$2"
            shift 2
            ;;
        *)
            echo "Unknown option: $1"
            usage
            ;;
    esac
done

# Function to check if AWS CLI is installed
check_aws_cli() {
    if ! command -v aws &> /dev/null; then
        echo "AWS CLI is not installed. Please install it first."
        exit 1
    fi
}

# Function to get task details
get_task_details() {
    echo "Getting details for task: $TASK_ARN"
    
    # Get task details
    task_details=$(aws datasync describe-task --task-arn "$TASK_ARN" 2>&1)
    if [[ $? -ne 0 ]]; then
        echo "Error retrieving task details: $task_details"
        exit 1
    fi
    
    # Extract and display task details
    task_name=$(aws datasync describe-task --task-arn "$TASK_ARN" --output json --query 'Name' --output text 2>/dev/null || echo "N/A")
    task_status=$(aws datasync describe-task --task-arn "$TASK_ARN" --output json --query 'Status' --output text 2>/dev/null || echo "N/A")
    source_location=$(aws datasync describe-task --task-arn "$TASK_ARN" --output json --query 'SourceLocationArn' --output text 2>/dev/null || echo "N/A")
    destination_location=$(aws datasync describe-task --task-arn "$TASK_ARN" --output json --query 'DestinationLocationArn' --output text 2>/dev/null || echo "N/A")
    
    echo "================ Task Details ================"
    echo "Task Name: $task_name"
    echo "Task ARN: $TASK_ARN"
    echo "Status: $task_status"
    echo "Source Location ARN: $source_location"
    echo "Destination Location ARN: $destination_location"
    
    # Get recent executions
    echo "============ Recent Executions =============="
    aws datasync list-task-executions --task-arn "$TASK_ARN" --output table --query 'TaskExecutions[*].{ExecutionID:TaskExecutionArn,Status:Status,StartTime:StartTime,Result:Result}'
    
    # Get detailed information for each recent execution
    echo "========= Detailed Execution Status =========="
    executions=$(aws datasync list-task-executions --task-arn "$TASK_ARN" --output json --query 'TaskExecutions[*].TaskExecutionArn' | jq -r '.[]')
    
    for exec_arn in $executions; do
        get_execution_details "$exec_arn"
        echo "----------------------------------------------"
    done
}

# Function to get detailed information about a task execution
get_execution_details() {
    local exec_arn=$1
    local exec_id=${exec_arn##*/}
    
    echo "Execution ID: $exec_id"
    
    # Get execution details
    exec_details=$(aws datasync describe-task-execution --task-execution-arn "$exec_arn" 2>&1)
    if [[ $? -ne 0 ]]; then
        echo "  Error retrieving execution details: $exec_details"
        return 1
    fi
    
    # Extract key metrics
    status=$(aws datasync describe-task-execution --task-execution-arn "$exec_arn" --output json --query 'Status' --output text 2>/dev/null || echo "UNKNOWN")
    start_time=$(aws datasync describe-task-execution --task-execution-arn "$exec_arn" --output json --query 'StartTime' --output text 2>/dev/null || echo "N/A")
    end_time=$(aws datasync describe-task-execution --task-execution-arn "$exec_arn" --output json --query 'EndTime' --output text 2>/dev/null || echo "N/A")
    
    # Calculate duration if both start and end times are available
    duration="Still running"
    if [[ "$end_time" != "N/A" && "$start_time" != "N/A" ]]; then
        start_seconds=$(date -d "$start_time" +%s 2>/dev/null)
        end_seconds=$(date -d "$end_time" +%s 2>/dev/null)
        
        if [[ $? -eq 0 ]]; then
            duration_seconds=$((end_seconds - start_seconds))
            duration=$(printf "%02d:%02d:%02d" $((duration_seconds/3600)) $((duration_seconds%3600/60)) $((duration_seconds%60)))
        fi
    elif [[ "$start_time" != "N/A" ]]; then
        # Calculate duration until now for running tasks
        start_seconds=$(date -d "$start_time" +%s 2>/dev/null)
        current_seconds=$(date +%s)
        
        if [[ $? -eq 0 ]]; then
            duration_seconds=$((current_seconds - start_seconds))
            duration=$(printf "%02d:%02d:%02d (still running)" $((duration_seconds/3600)) $((duration_seconds%3600/60)) $((duration_seconds%60)))
        fi
    fi
    
    # Get file and byte metrics
    files_transferred=$(aws datasync describe-task-execution --task-execution-arn "$exec_arn" --output json --query 'FilesTransferred' --output text 2>/dev/null || echo "0")
    est_files=$(aws datasync describe-task-execution --task-execution-arn "$exec_arn" --output json --query 'EstimatedFilesToTransfer' --output text 2>/dev/null || echo "0")
    bytes_transferred=$(aws datasync describe-task-execution --task-execution-arn "$exec_arn" --output json --query 'BytesTransferred' --output text 2>/dev/null || echo "0")
    est_bytes=$(aws datasync describe-task-execution --task-execution-arn "$exec_arn" --output json --query 'EstimatedBytesToTransfer' --output text 2>/dev/null || echo "0")
    
    # Calculate percentages
    files_percent="0%"
    bytes_percent="0%"
    
    if [[ "$est_files" != "0" && "$est_files" != "null" ]]; then
        files_percent=$(awk "BEGIN {printf \"%.1f%%\", ($files_transferred/$est_files)*100}")
    fi
    
    if [[ "$est_bytes" != "0" && "$est_bytes" != "null" ]]; then
        bytes_percent=$(awk "BEGIN {printf \"%.1f%%\", ($bytes_transferred/$est_bytes)*100}")
    fi
    
    # Format bytes for human readability
    bytes_hr=$(format_bytes "$bytes_transferred")
    est_bytes_hr=$(format_bytes "$est_bytes")
    
    # Display the information
    echo "  Status: $status"
    echo "  Start Time: $start_time"
    echo "  End Time: ${end_time}"
    echo "  Duration: $duration"
    echo "  Files: $files_transferred / $est_files ($files_percent complete)"
    echo "  Data: $bytes_hr / $est_bytes_hr ($bytes_percent complete)"
    
    # If there's an error, show error details
    if [[ "$status" == "ERROR" ]]; then
        echo "  Error Details:"
        aws datasync describe-task-execution --task-execution-arn "$exec_arn" --output json --query 'Result' | jq '.' || echo "    Unable to retrieve error details"
    fi
    
    # Show preparation status if available
    prep_status=$(aws datasync describe-task-execution --task-execution-arn "$exec_arn" --output json --query 'PrepareStatus' --output text 2>/dev/null)
    if [[ -n "$prep_status" && "$prep_status" != "null" ]]; then
        echo "  Preparation Phase: $prep_status"
    fi
    
    # Show transfer status if available
    transfer_status=$(aws datasync describe-task-execution --task-execution-arn "$exec_arn" --output json --query 'TransferStatus' --output text 2>/dev/null)
    if [[ -n "$transfer_status" && "$transfer_status" != "null" ]]; then
        echo "  Transfer Phase: $transfer_status"
    fi
    
    # Show verify status if available
    verify_status=$(aws datasync describe-task-execution --task-execution-arn "$exec_arn" --output json --query 'VerifyStatus' --output text 2>/dev/null)
    if [[ -n "$verify_status" && "$verify_status" != "null" ]]; then
        echo "  Verification Phase: $verify_status"
    fi
}

# Function to format bytes to human-readable format
format_bytes() {
    local bytes=$1
    
    if [[ ! "$bytes" =~ ^[0-9]+$ ]]; then
        echo "$bytes"
        return
    fi
    
    if [ "$bytes" -ge 1099511627776 ]; then
        awk "BEGIN {printf \"%.2f TB\", $bytes/1099511627776}"
    elif [ "$bytes" -ge 1073741824 ]; then
        awk "BEGIN {printf \"%.2f GB\", $bytes/1073741824}"
    elif [ "$bytes" -ge 1048576 ]; then
        awk "BEGIN {printf \"%.2f MB\", $bytes/1048576}"
    elif [ "$bytes" -ge 1024 ]; then
        awk "BEGIN {printf \"%.2f KB\", $bytes/1024}"
    else
        echo "${bytes} B"
    fi
}

# Function to start the DataSync task
start_task() {
    # Ensure log directory exists
    mkdir -p "$LOG_DIR"
    
    # Generate log filename based on task ID and timestamp
    timestamp=$(date +%Y%m%d_%H%M%S)
    log_file="${LOG_DIR}/datasync_${TASK_ID}_${timestamp}.log"
    
    echo "Starting DataSync task: $TASK_ARN"
    start_response=$(aws datasync start-task-execution --task-arn "$TASK_ARN" 2>&1)
    
    if [[ $? -ne 0 ]]; then
        echo "Failed to start task: $start_response"
        exit 1
    fi
    
    EXECUTION_ARN=$(echo "$start_response" | grep -o 'arn:aws:datasync:[^"]*')
    
    if [ -z "$EXECUTION_ARN" ]; then
        # Try alternative parsing if the first method fails
        EXECUTION_ARN=$(echo "$start_response" | awk '/TaskExecutionArn/ {print $2}' | tr -d '"' | tr -d ',')
    fi
    
    if [ -z "$EXECUTION_ARN" ]; then
        echo "Failed to extract execution ARN from response: $start_response"
        exit 1
    fi
    
    EXECUTION_ID=${EXECUTION_ARN##*/}
    
    echo "Task execution started with ARN: $EXECUTION_ARN"
    echo "Log file: $log_file"
    
    # Start monitoring in background
    (
        echo "=== AWS DataSync Task Monitor ===" > "$log_file"
        echo "Start time: $(date)" >> "$log_file"
        echo "Region: $REGION" >> "$log_file"
        echo "Task ARN: $TASK_ARN" >> "$log_file"
        echo "Execution ARN: $EXECUTION_ARN" >> "$log_file"
        echo "=====================================" >> "$log_file"
        echo "" >> "$log_file"
        
        # Call monitor_task with execution ARN
        monitor_task_background "$EXECUTION_ARN" >> "$log_file" 2>&1
        
        # Update log when monitoring completes
        echo "" >> "$log_file"
        echo "=====================================" >> "$log_file"
        echo "Monitoring completed at: $(date)" >> "$log_file"
    ) &
    
    # Return execution ID so caller can use it
    echo "$EXECUTION_ID"
}

# Function to monitor task execution (used for background monitoring)
monitor_task_background() {
    local execution_arn=$1
    local start_time=$(date +%s)
    local status=""
    local last_status=""
    local last_files=0
    local last_bytes=0
    
    echo "Monitoring task execution: $execution_arn"
    echo "Time | Status | Progress | Transfer Rate | Details"
    echo "-----|--------|----------|--------------|--------"
    
    while true; do
        # Get current execution status
        status_output=$(aws datasync describe-task-execution --task-execution-arn "$execution_arn" 2>&1)
        
        if [[ $? -ne 0 ]]; then
            echo "Error retrieving execution status: $status_output"
            return 1
        fi
        
        status=$(aws datasync describe-task-execution --task-execution-arn "$execution_arn" --output text --query 'Status' 2>/dev/null || echo "UNKNOWN")
        
        # Get additional details if available
        bytes_transferred=$(aws datasync describe-task-execution --task-execution-arn "$execution_arn" --output json --query 'BytesTransferred' 2>/dev/null || echo "0")
        files_transferred=$(aws datasync describe-task-execution --task-execution-arn "$execution_arn" --output json --query 'FilesTransferred' 2>/dev/null || echo "0")
        est_bytes=$(aws datasync describe-task-execution --task-execution-arn "$execution_arn" --output json --query 'EstimatedBytesToTransfer' 2>/dev/null || echo "0")
        est_files=$(aws datasync describe-task-execution --task-execution-arn "$execution_arn" --output json --query 'EstimatedFilesToTransfer' 2>/dev/null || echo "0")
        
        # Calculate progress percentage
        files_percent="0%"
        bytes_percent="0%"
        overall_percent="0%"
        
        if [[ "$est_files" != "0" && "$est_files" != "null" ]]; then
            files_percent=$(awk "BEGIN {printf \"%.1f%%\", ($files_transferred/$est_files)*100}")
        fi
        
        if [[ "$est_bytes" != "0" && "$est_bytes" != "null" ]]; then
            bytes_percent=$(awk "BEGIN {printf \"%.1f%%\", ($bytes_transferred/$est_bytes)*100}")
            overall_percent=$bytes_percent
        fi
        
        # Calculate transfer rate since last update
        transfer_rate="Calculating..."
        if [[ $last_status != "" ]]; then
            current_time_seconds=$(date +%s)
            time_diff=$((current_time_seconds - start_time))
            
            if [[ $time_diff -gt 0 && "$bytes_transferred" =~ ^[0-9]+$ && "$last_bytes" =~ ^[0-9]+$ ]]; then
                bytes_diff=$((bytes_transferred - last_bytes))
                rate_bps=$((bytes_diff / CHECK_INTERVAL))
                
                if [ $rate_bps -ge 1048576 ]; then
                    transfer_rate=$(awk "BEGIN {printf \"%.2f MB/s\", $rate_bps/1048576}")
                elif [ $rate_bps -ge 1024 ]; then
                    transfer_rate=$(awk "BEGIN {printf \"%.2f KB/s\", $rate_bps/1024}")
                else
                    transfer_rate="${rate_bps} B/s"
                fi
            fi
        fi
        
        # Format bytes for human readability
        bytes_hr=$(format_bytes "$bytes_transferred")
        est_bytes_hr=$(format_bytes "$est_bytes")
        
        # Create progress details string
        if [[ "$est_bytes" != "0" && "$est_bytes" != "null" && "$bytes_transferred" =~ ^[0-9]+$ ]]; then
            details="$files_transferred/$est_files files, $bytes_hr/$est_bytes_hr"
        else
            details="$files_transferred files, $bytes_hr"
        fi
        
        # Get current phase information
        current_phase=""
        prep_status=$(aws datasync describe-task-execution --task-execution-arn "$execution_arn" --output json --query 'PrepareStatus' --output text 2>/dev/null)
        transfer_status=$(aws datasync describe-task-execution --task-execution-arn "$execution_arn" --output json --query 'TransferStatus' --output text 2>/dev/null)
        verify_status=$(aws datasync describe-task-execution --task-execution-arn "$execution_arn" --output json --query 'VerifyStatus' --output text 2>/dev/null)
        
        if [[ -n "$prep_status" && "$prep_status" != "null" && "$prep_status" != "COMPLETE" ]]; then
            current_phase="Preparing"
        elif [[ -n "$transfer_status" && "$transfer_status" != "null" && "$transfer_status" != "COMPLETE" ]]; then
            current_phase="Transferring"
        elif [[ -n "$verify_status" && "$verify_status" != "null" && "$verify_status" != "COMPLETE" ]]; then
            current_phase="Verifying"
        fi
        
        if [[ -n "$current_phase" ]]; then
            status="$status ($current_phase)"
        fi
        
        # Get current time
        current_time=$(date '+%Y-%m-%d %H:%M:%S')
        
        # Print status update
        echo "$current_time | $status | $overall_percent | $transfer_rate | $details"
        
        # Update last status variables for next iteration
        last_status=$status
        last_files=$files_transferred
        last_bytes=$bytes_transferred
        
        # Check if task has completed or failed
        if [ "$status" = "SUCCESS" ] || [[ "$status" =~ SUCCESS ]]; then
            echo "Task execution completed successfully!"
            
            # Display detailed summary
            echo "============== Execution Summary ==============="
            duration_seconds=$(($(date +%s) - start_time))
            duration=$(printf "%02d:%02d:%02d" $((duration_seconds/3600)) $((duration_seconds%3600/60)) $((duration_seconds%60)))
            
            echo "Duration: $duration"
            echo "Files Transferred: $files_transferred"
            echo "Data Transferred: $bytes_hr"
            
            aws datasync describe-task-execution --task-execution-arn "$execution_arn" --output json --query '{Status:Status,BytesTransferred:BytesTransferred,FilesTransferred:FilesTransferred,BytesVerified:BytesVerified,FilesVerified:FilesVerified,StartTime:StartTime,EndTime:EndTime,EstimatedBytesToTransfer:EstimatedBytesToTransfer,EstimatedFilesToTransfer:EstimatedFilesToTransfer}' | jq '.' || echo "Unable to retrieve detailed summary"
            
            return 0
        elif [ "$status" = "ERROR" ] || [[ "$status" =~ ERROR ]]; then
            echo "Task execution failed!"
            
            # Get error details
            echo "================ Error Details ================="
            aws datasync describe-task-execution --task-execution-arn "$execution_arn" --output json --query 'Result' | jq '.' || echo "Unable to retrieve error details"
            
            return 1
        fi
        
        # Check if maximum execution time has been reached
        current_time_seconds=$(date +%s)
        elapsed_time=$((current_time_seconds - start_time))
        if [ $elapsed_time -ge $MAX_EXECUTION_TIME ]; then
            echo "Maximum execution time reached. Stopping monitoring."
            echo "Task is still running. You can check its status later using:"
            echo "  $0 --status $TASK_ID"
            echo "  $0 --monitor $EXECUTION_ID"
            return 2
        fi
        
        # Wait before checking again
        sleep $CHECK_INTERVAL
    done
}

# Function to monitor task execution (used for foreground monitoring)
monitor_task() {
    local execution_arn=$1
    local start_time=$(date +%s)
    local status=""
    local last_status=""
    local last_files=0
    local last_bytes=0
    
    echo "Monitoring task execution: $execution_arn"
    echo "Time | Status | Progress | Transfer Rate | Details"
    echo "-----|--------|----------|--------------|--------"
    
    while true; do
        # Get current execution status
        status_output=$(aws datasync describe-task-execution --task-execution-arn "$execution_arn" 2>&1)
        
        if [[ $? -ne 0 ]]; then
            echo "Error retrieving execution status: $status_output"
            return 1
        fi
        
        status=$(aws datasync describe-task-execution --task-execution-arn "$execution_arn" --output text --query 'Status' 2>/dev/null || echo "UNKNOWN")
        
        # Get additional details if available
        bytes_transferred=$(aws datasync describe-task-execution --task-execution-arn "$execution_arn" --output json --query 'BytesTransferred' 2>/dev/null || echo "0")
        files_transferred=$(aws datasync describe-task-execution --task-execution-arn "$execution_arn" --output json --query 'FilesTransferred' 2>/dev/null || echo "0")
        est_bytes=$(aws datasync describe-task-execution --task-execution-arn "$execution_arn" --output json --query 'EstimatedBytesToTransfer' 2>/dev/null || echo "0")
        est_files=$(aws datasync describe-task-execution --task-execution-arn "$execution_arn" --output json --query 'EstimatedFilesToTransfer' 2>/dev/null || echo "0")
        
        # Calculate progress percentage
        files_percent="0%"
        bytes_percent="0%"
        overall_percent="0%"
        
        if [[ "$est_files" != "0" && "$est_files" != "null" ]]; then
            files_percent=$(awk "BEGIN {printf \"%.1f%%\", ($files_transferred/$est_files)*100}")
        fi
        
        if [[ "$est_bytes" != "0" && "$est_bytes" != "null" ]]; then
            bytes_percent=$(awk "BEGIN {printf \"%.1f%%\", ($bytes_transferred/$est_bytes)*100}")
            overall_percent=$bytes_percent
        fi
        
        # Calculate transfer rate since last update
        transfer_rate="Calculating..."
        if [[ $last_status != "" ]]; then
            current_time_seconds=$(date +%s)
            time_diff=$((current_time_seconds - start_time))
            
            if [[ $time_diff -gt 0 && "$bytes_transferred" =~ ^[0-9]+$ && "$last_bytes" =~ ^[0-9]+$ ]]; then
                bytes_diff=$((bytes_transferred - last_bytes))
                rate_bps=$((bytes_diff / CHECK_INTERVAL))
                
                if [ $rate_bps -ge 1048576 ]; then
                    transfer_rate=$(awk "BEGIN {printf \"%.2f MB/s\", $rate_bps/1048576}")
                elif [ $rate_bps -ge 1024 ]; then
                    transfer_rate=$(awk "BEGIN {printf \"%.2f KB/s\", $rate_bps/1024}")
                else
                    transfer_rate="${rate_bps} B/s"
                fi
            fi
        fi
        
        # Format bytes for human readability
        bytes_hr=$(format_bytes "$bytes_transferred")
        est_bytes_hr=$(format_bytes "$est_bytes")
        
        # Create progress details string
        if [[ "$est_bytes" != "0" && "$est_bytes" != "null" && "$bytes_transferred" =~ ^[0-9]+$ ]]; then
            details="$files_transferred/$est_files files, $bytes_hr/$est_bytes_hr"
        else
            details="$files_transferred files, $bytes_hr"
        fi
        
        # Get current phase information
        current_phase=""
        prep_status=$(aws datasync describe-task-execution --task-execution-arn "$execution_arn" --output json --query 'PrepareStatus' --output text 2>/dev/null)
        transfer_status=$(aws datasync describe-task-execution --task-execution-arn "$execution_arn" --output json --query 'TransferStatus' --output text 2>/dev/null)
        verify_status=$(aws datasync describe-task-execution --task-execution-arn "$execution_arn" --output json --query 'VerifyStatus' --output text 2>/dev/null)
        
        if [[ -n "$prep_status" && "$prep_status" != "null" && "$prep_status" != "COMPLETE" ]]; then
            current_phase="Preparing"
        elif [[ -n "$transfer_status" && "$transfer_status" != "null" && "$transfer_status" != "COMPLETE" ]]; then
            current_phase="Transferring"
        elif [[ -n "$verify_status" && "$verify_status" != "null" && "$verify_status" != "COMPLETE" ]]; then
            current_phase="Verifying"
        fi
        
        if [[ -n "$current_phase" ]]; then
            status="$status ($current_phase)"
        fi
        
        # Get current time
        current_time=$(date '+%Y-%m-%d %H:%M:%S')
        
        # Print status update
        echo "$current_time | $status | $overall_percent | $transfer_rate | $details"
        
        # Update last status variables for next iteration
        last_status=$status
        last_files=$files_transferred
        last_bytes=$bytes_transferred
        
        # Check if task has completed or failed
        if [ "$status" = "SUCCESS" ] || [[ "$status" =~ SUCCESS ]]; then
            echo "Task execution completed successfully!"
            
            # Display detailed summary
            echo "============== Execution Summary ==============="
            duration_seconds=$(($(date +%s) - start_time))
            duration=$(printf "%02d:%02d:%02d" $((duration_seconds/3600)) $((duration_seconds%3600/60)) $((duration_seconds%60)))
            
            echo "Duration: $duration"
            echo "Files Transferred: $files_transferred"
            echo "Data Transferred: $bytes_hr"
            
            aws datasync describe-task-execution --task-execution-arn "$execution_arn" --output json --query '{Status:Status,BytesTransferred:BytesTransferred,FilesTransferred:FilesTransferred,BytesVerified:BytesVerified,FilesVerified:FilesVerified,StartTime:StartTime,EndTime:EndTime,EstimatedBytesToTransfer:EstimatedBytesToTransfer,EstimatedFilesToTransfer:EstimatedFilesToTransfer}' | jq '.' || echo "Unable to retrieve detailed summary"
            
            return 0
        elif [ "$status" = "ERROR" ] || [[ "$status" =~ ERROR ]]; then
            echo "Task execution failed!"
            
            # Get error details
            echo "================ Error Details ================="
            aws datasync describe-task-execution --task-execution-arn "$execution_arn" --output json --query 'Result' | jq '.' || echo "Unable to retrieve error details"
            
            return 1
        fi
        
        # Check if maximum execution time has been reached
        current_time_seconds=$(date +%s)
        elapsed_time=$((current_time_seconds - start_time))
        if [ $elapsed_time -ge $MAX_EXECUTION_TIME ]; then
            echo "Maximum execution time reached. Stopping monitoring."
            echo "Task is still running. You can check its status later using:"
            echo "  $0 --status $TASK_ID"
            echo "  $0 --monitor $(echo $execution_arn | awk -F/ '{print $NF}')"
            return 2
        fi
        
        # Wait before checking again
        sleep $CHECK_INTERVAL
    done
}

# Main function
main() {
    # Set AWS default region
    export AWS_DEFAULT_REGION="$REGION"
    
    # Check if AWS CLI is installed
    check_aws_cli
    
    # Check if jq is installed (for JSON output formatting)
    if ! command -v jq &> /dev/null; then
        echo "Warning: 'jq' is not installed. JSON output will not be formatted nicely."
        echo "Consider installing jq for better output formatting."
    fi
    
    # Construct ARNs
    if [[ -n "$TASK_ID" ]]; then
        TASK_ARN="arn:aws:datasync:${REGION}:127214169382:task/${TASK_ID}"
    fi
    
    # Perform the requested operation
    case "$OPERATION" in
        "status")
            echo "=== AWS DataSync Task Monitor ==="
            echo "Region: $REGION"
            echo "Task ARN: $TASK_ARN"
            echo "===============================\n"
            
            # Display task details and status
            get_task_details
            ;;
        "monitor")
            # Monitor a specific execution
            if [ -z "$EXECUTION_ID" ]; then
                echo "Error: Execution ID is required for monitoring"
                exit 1
            fi
            
            # Construct full execution ARN if only ID was provided
            if [[ ! "$EXECUTION_ID" =~ ^arn:aws:datasync ]]; then
                # Extract task ID from execution ID (if possible)
                if [[ "$EXECUTION_ID" =~ ^exec- ]]; then
                    # First, check if we have a task ID
                    if [[ -n "$TASK_ID" ]]; then
                        EXECUTION_ARN="arn:aws:datasync:${REGION}:127214169382:task/${TASK_ID}/execution/${EXECUTION_ID}"
                    else
                        # Try to list all task executions and find the matching one
                        echo "Searching for execution ID: $EXECUTION_ID"
                        matching_arn=$(aws datasync list-task-executions --output json | jq -r ".TaskExecutions[] | select(.TaskExecutionArn | endswith(\"/${EXECUTION_ID}\")) | .TaskExecutionArn")
                        
                        if [[ -n "$matching_arn" ]]; then
                            EXECUTION_ARN="$matching_arn"
                            echo "Found matching execution ARN: $EXECUTION_ARN"
                        else
                            echo "Error: Could not find execution with ID $EXECUTION_ID"
                            echo "Please specify the complete task ID with --monitor"
                            exit 1
                        fi
                    fi
                else
                    # Assume it's a full ARN
                    EXECUTION_ARN="$EXECUTION_ID"
                fi
            else
                EXECUTION_ARN="$EXECUTION_ID"
            fi
            
            echo "=== AWS DataSync Task Monitor ==="
            echo "Region: $REGION"
            echo "Execution ARN: $EXECUTION_ARN"
            echo "===============================\n"
            
            # Monitor in foreground
            monitor_task "$EXECUTION_ARN"
            ;;
        "start")
            echo "=== AWS DataSync Task Monitor ==="
            echo "Region: $REGION"
            echo "Task ARN: $TASK_ARN"
            echo "===============================\n"
            
            # Start the task and get execution ID
            EXECUTION_ID=$(start_task)
            
            # Show a clear message about where to find logs
            echo ""
            echo "DataSync task started successfully!"
            echo "Execution ID: $EXECUTION_ID"
            echo "Logs are being written to: $LOG_DIR/datasync_${TASK_ID}_*.log"
            echo ""
            echo "To check status, run:"
            echo "  $0 --status $TASK_ID"
            echo ""
            echo "To monitor this specific execution, run:"
            echo "  $0 --monitor $EXECUTION_ID"
            echo ""
            echo "To view logs in real-time:"
            echo "  tail -f $LOG_DIR/datasync_${TASK_ID}_*.log"
            ;;
    esac
    
    return $?
}

# Run the main function
main
exit $?