#!/usr/bin/env python3
import redis
import json
import argparse
import sys
import pickle
from pathlib import Path
from typing import Dict, Any, Optional, Union

# Import settings the same way as in your provided code
try:
    from app.core.Settings import Settings
    settings = Settings()
    DEFAULT_REDIS_HOST = settings.REDIS_HOST
    DEFAULT_REDIS_PASSWORD = settings.REDIS_PASSWORD
except ImportError:
    print("Warning: Could not import Settings. Using default values.")
    DEFAULT_REDIS_HOST = "localhost"
    DEFAULT_REDIS_PASSWORD = None

def connect_to_redis(host: str = None, 
                     port: int = 6379, 
                     db: int = 0, 
                     password: Optional[str] = None) -> redis.Redis:
    """Establish connection to Redis server with error handling."""
    try:
        # Use settings values as defaults if not provided
        if host is None:
            host = DEFAULT_REDIS_HOST
        if password is None:
            password = DEFAULT_REDIS_PASSWORD
            
        client = redis.Redis(
            host=host,
            port=port,
            db=db,
            password=password,
            decode_responses=False  # Keep binary responses for proper unpickling
        )
        # Test connection
        client.ping()
        return client
    except redis.ConnectionError as e:
        sys.exit(f"Failed to connect to Redis: {e}")

def get_all_keys(client: redis.Redis, pattern: str = "*") -> list:
    """Get all keys matching the pattern using scan for better performance."""
    keys = []
    cursor = 0
    while True:
        cursor, batch = client.scan(cursor, match=pattern, count=100)
        keys.extend(batch)
        if cursor == 0:
            break
    return keys

def process_key(client: redis.Redis, key: bytes) -> Dict[str, Any]:
    """Process a single key and return its data with metadata."""
    key_str = key.decode('utf-8') if isinstance(key, bytes) else key
    key_type = client.type(key_str).decode('utf-8')
    
    value = None
    
    try:
        if key_type == "string":
            raw_value = client.get(key_str)
            
            # Try to unpickle if it looks like pickled data
            try:
                value = pickle.loads(raw_value)
                data_format = "pickle"
            except:
                # If unpickling fails, try to decode as string
                try:
                    value = raw_value.decode('utf-8')
                    data_format = "string"
                except:
                    # If decoding fails, store as base64
                    import base64
                    value = base64.b64encode(raw_value).decode('utf-8')
                    data_format = "base64"
                
        elif key_type == "list":
            items = client.lrange(key_str, 0, -1)
            value = [try_decode_or_unpickle(item) for item in items]
            data_format = "list"
            
        elif key_type == "set":
            items = client.smembers(key_str)
            value = [try_decode_or_unpickle(item) for item in items]
            data_format = "set"
            
        elif key_type == "zset":
            items = client.zrangebyscore(key_str, "-inf", "+inf", withscores=True)
            value = {try_decode_or_unpickle(k).decode('utf-8') if isinstance(try_decode_or_unpickle(k), bytes) else try_decode_or_unpickle(k): 
                    score for k, score in items}
            data_format = "zset"
            
        elif key_type == "hash":
            raw_hash = client.hgetall(key_str)
            value = {
                k.decode('utf-8') if isinstance(k, bytes) else k: 
                try_decode_or_unpickle(v) 
                for k, v in raw_hash.items()
            }
            data_format = "hash"
            
        else:
            value = f"Unsupported type: {key_type}"
            data_format = "unknown"
        
        return {
            "key": key_str,
            "type": key_type,
            "format": data_format,
            "value": value
        }
    except Exception as e:
        return {
            "key": key_str,
            "type": key_type,
            "error": str(e),
            "value": None
        }

def try_decode_or_unpickle(data):
    """Try to unpickle data, or decode it as a string if that fails."""
    if data is None:
        return None
        
    try:
        return pickle.loads(data)
    except:
        try:
            return data.decode('utf-8')
        except:
            return data

def export_redis_data(client: redis.Redis, 
                     output_file: Union[str, Path], 
                     pattern: str = "*",
                     include_metadata: bool = True) -> None:
    """Export Redis data to a JSON file."""
    keys = get_all_keys(client, pattern)
    
    if not keys:
        print("No keys found in Redis database matching pattern:", pattern)
        return
    
    print(f"Found {len(keys)} keys in Redis matching pattern: {pattern}")
    
    result = {}
    errors = []
    
    for i, key in enumerate(keys):
        if i % 100 == 0 and i > 0:
            print(f"Processed {i}/{len(keys)} keys...")
        
        key_data = process_key(client, key)
        
        if "error" in key_data:
            errors.append(key_data)
            print(f"Error processing key '{key_data['key']}': {key_data['error']}")
            continue
            
        key_str = key_data["key"]
        
        if include_metadata:
            result[key_str] = key_data
        else:
            result[key_str] = key_data["value"]
    
    # Add error summary if there were any
    if errors and include_metadata:
        result["__errors__"] = errors
    
    # Convert to JSON-compatible format and write to file
    def convert_to_json_compatible(obj):
        if isinstance(obj, bytes):
            try:
                return obj.decode('utf-8')
            except:
                import base64
                return {"__bytes__": base64.b64encode(obj).decode('utf-8')}
        elif isinstance(obj, (list, tuple)):
            return [convert_to_json_compatible(item) for item in obj]
        elif isinstance(obj, dict):
            return {str(k): convert_to_json_compatible(v) for k, v in obj.items()}
        else:
            return obj
    
    json_compatible = convert_to_json_compatible(result)
    
    # Write to file
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(json_compatible, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\nSuccessfully exported {len(result) - (1 if errors and include_metadata else 0)} keys to {output_file}")
    if errors:
        print(f"Encountered errors with {len(errors)} keys. Details saved in export file.")

def main():
    """Main function to parse arguments and execute export."""
    parser = argparse.ArgumentParser(description="Export Redis data to JSON file")
    parser.add_argument("--host", help=f"Redis server host (default: {DEFAULT_REDIS_HOST})")
    parser.add_argument("--port", type=int, default=6379, help="Redis server port")
    parser.add_argument("--db", type=int, default=0, help="Redis database number")
    parser.add_argument("--password", help="Redis password (default: from settings)")
    parser.add_argument("--pattern", default="*", help="Key pattern to export (default: *)")
    parser.add_argument("--output", default="redis_export.json", help="Output file path")
    parser.add_argument("--simple", action="store_true", help="Export values only without metadata")
    
    args = parser.parse_args()
    
    client = connect_to_redis(args.host, args.port, args.db, args.password)
    export_redis_data(client, args.output, args.pattern, not args.simple)

if __name__ == "__main__":
    main()