#!/bin/bash

# Script to export from source MongoDB and import to target MongoDB
# Usage: ./mongo_transfer.sh <db_name>

# Check if database name is provided
if [ $# -eq 0 ]; then
    # Default database if none provided
    DB_NAME="pre_prod_tatadigitaltwinplatform9461"
    echo "No database name provided, using default: $DB_NAME"
else
    # Database name from the argument
    DB_NAME="$1"
    echo "Using provided database name: $DB_NAME"
fi

# MongoDB connection details
SOURCE_URI="mongodb+srv://labeeb:<EMAIL>/?retryWrites=false"
TARGET_URI="************************************************************************************************************************************************"

# Output directory with timestamp for uniqueness
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
WORK_DIR="/tmp/mongo_transfer_${DB_NAME}_${TIMESTAMP}"

# Create work directory
mkdir -p "$WORK_DIR"

# Log file
LOG_FILE="${WORK_DIR}/transfer_log.txt"

echo "==== MongoDB Database Transfer Script ====" | tee -a "$LOG_FILE"
echo "Database: $DB_NAME" | tee -a "$LOG_FILE"
echo "Started at: $(date)" | tee -a "$LOG_FILE"
echo "Working directory: $WORK_DIR" | tee -a "$LOG_FILE"
echo "" | tee -a "$LOG_FILE"

# EXPORT PHASE
echo "=== EXPORT PHASE ===" | tee -a "$LOG_FILE"

# Check if the database exists in source
echo "Checking if database $DB_NAME exists in source..." | tee -a "$LOG_FILE"
DB_EXISTS=$(mongosh "$SOURCE_URI" --quiet --eval "db.adminCommand('listDatabases').databases.map(function(d) { return d.name; }).indexOf('$DB_NAME') !== -1 ? 'exists' : 'not_exists'")

if [ "$DB_EXISTS" != "exists" ]; then
    echo "Error: Database '$DB_NAME' does not exist on the source server" | tee -a "$LOG_FILE"
    exit 1
fi

echo "Database '$DB_NAME' exists in source. Proceeding with export..." | tee -a "$LOG_FILE"

# Run mongodump for the specified database only
echo "Running mongodump..." | tee -a "$LOG_FILE"
mongodump --uri="$SOURCE_URI" \
    --db="$DB_NAME" \
    --out="$WORK_DIR" \
    --gzip \
    2>&1 | tee -a "$LOG_FILE"

# Check if the export was successful
if [ $? -eq 0 ]; then
    echo "Export completed successfully" | tee -a "$LOG_FILE"
    EXPORT_SIZE=$(du -sh "$WORK_DIR/$DB_NAME" | cut -f1)
    echo "Exported data size: $EXPORT_SIZE" | tee -a "$LOG_FILE"
else
    echo "Export failed. Check the log file for details: $LOG_FILE" | tee -a "$LOG_FILE"
    exit 1
fi

echo "" | tee -a "$LOG_FILE"

# IMPORT PHASE
echo "=== IMPORT PHASE ===" | tee -a "$LOG_FILE"

# Check if target is accessible
echo "Checking connection to target MongoDB..." | tee -a "$LOG_FILE"
TARGET_CONN=$(mongosh "$TARGET_URI" --quiet --eval "db.adminCommand('ping').ok" 2>/dev/null)

if [[ "$TARGET_CONN" != "1" ]]; then
    echo "Error: Cannot connect to target MongoDB server" | tee -a "$LOG_FILE"
    exit 1
fi

echo "Target MongoDB connection successful" | tee -a "$LOG_FILE"

# Check if database already exists in target
echo "Checking if database $DB_NAME already exists in target..." | tee -a "$LOG_FILE"
TARGET_DB_EXISTS=$(mongosh "$TARGET_URI" --quiet --eval "db.adminCommand('listDatabases').databases.map(function(d) { return d.name; }).indexOf('$DB_NAME') !== -1 ? 'exists' : 'not_exists'")

if [ "$TARGET_DB_EXISTS" == "exists" ]; then
    echo "Warning: Database '$DB_NAME' already exists in target" | tee -a "$LOG_FILE"
    read -p "Do you want to drop the existing database? (y/n): " CONFIRM
    
    if [[ "$CONFIRM" == "y" || "$CONFIRM" == "Y" ]]; then
        echo "Dropping existing database '$DB_NAME' from target..." | tee -a "$LOG_FILE"
        mongosh "$TARGET_URI" --quiet --eval "db.getSiblingDB('$DB_NAME').dropDatabase()" 2>&1 | tee -a "$LOG_FILE"
    else
        echo "Aborting import to prevent overwriting existing database" | tee -a "$LOG_FILE"
        exit 1
    fi
fi

# Run mongorestore to import the data
echo "Running mongorestore to import data into target..." | tee -a "$LOG_FILE"
mongorestore --uri="$TARGET_URI" \
    --nsInclude="$DB_NAME.*" \
    --gzip \
    "$WORK_DIR" \
    2>&1 | tee -a "$LOG_FILE"

# Check if the import was successful
if [ $? -eq 0 ]; then
    echo "Import completed successfully" | tee -a "$LOG_FILE"
    
    # Verify the import by counting collections
    COLLECTION_COUNT=$(mongosh "$TARGET_URI" --quiet --eval "db.getSiblingDB('$DB_NAME').getCollectionNames().length")
    echo "Verified $COLLECTION_COUNT collections in target database" | tee -a "$LOG_FILE"
else
    echo "Import failed. Check the log file for details: $LOG_FILE" | tee -a "$LOG_FILE"
    exit 1
fi

echo "" | tee -a "$LOG_FILE"
echo "==== Transfer completed successfully ====" | tee -a "$LOG_FILE"
echo "Source: $SOURCE_URI (Database: $DB_NAME)" | tee -a "$LOG_FILE"
echo "Target: $TARGET_URI (Database: $DB_NAME)" | tee -a "$LOG_FILE"
echo "Temporary data: $WORK_DIR" | tee -a "$LOG_FILE"
echo "Log file: $LOG_FILE" | tee -a "$LOG_FILE"
echo "Completed at: $(date)" | tee -a "$LOG_FILE"

# Clean up (optional - comment out to keep the dump files)
# echo "Cleaning up temporary files..." | tee -a "$LOG_FILE"
# rm -rf "$WORK_DIR/$DB_NAME"

echo "Process completed. Data transferred from source to target successfully."