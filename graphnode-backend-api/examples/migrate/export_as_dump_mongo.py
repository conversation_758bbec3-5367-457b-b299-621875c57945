#!/usr/bin/env python3
import os
import sys
import subprocess
import logging
import argparse
from datetime import datetime
from pathlib import Path
from typing import List, Set, Dict, Any, Optional
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, OperationFailure

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def get_source_collections(host: str, port: int, username: str, password: str, 
                           auth_db: str = "admin") -> Dict[str, Set[str]]:
    """Get all databases and their collections from the source MongoDB server using pymongo."""
    try:
        # Create connection URI
        uri = f"mongodb://{username}:{password}@{host}:{port}/{auth_db}"
        
        # Connect to MongoDB
        client = MongoClient(uri)
        
        # Test connection
        client.admin.command('ping')
        
        # Get all database names
        database_names = client.list_database_names()
        
        # Filter out system databases
        databases = [db for db in database_names if db not in ['admin', 'local', 'config']]
        
        db_collections = {}
        
        # For each database, get all collections
        for db_name in databases:
            db = client[db_name]
            collections = set(db.list_collection_names())
            db_collections[db_name] = collections
            
        client.close()
        return db_collections
    
    except ConnectionFailure as e:
        logger.error(f"Failed to connect to MongoDB: {e}")
        return {}
    except OperationFailure as e:
        logger.error(f"Authentication failed: {e}")
        return {}
    except Exception as e:
        logger.error(f"Error getting source collections: {e}")
        return {}

def get_backup_collections(backup_path: Path) -> Dict[str, Set[str]]:
    """Get all databases and their collections from the backup directory."""
    backup_collections = {}
    
    try:
        # Check if backup path exists
        if not backup_path.exists():
            logger.error(f"Backup path {backup_path} does not exist")
            return {}
            
        # Get all database directories
        for db_dir in backup_path.iterdir():
            if db_dir.is_dir() and not db_dir.name.startswith('.'):
                # Skip system databases
                if db_dir.name in ['admin', 'local', 'config']:
                    continue
                    
                collections = set()
                # Get all collection files (bson files)
                for file_path in db_dir.glob('*.bson'):
                    collections.add(file_path.stem)
                    
                backup_collections[db_dir.name] = collections
                
        return backup_collections
    except Exception as e:
        logger.error(f"Error getting backup collections: {e}")
        return {}

def update_backup(host: str, port: int, username: str, password: str, 
                 backup_path: Path, missing_collections: Dict[str, List[str]],
                 auth_db: str = "admin") -> bool:
    """Update the backup with missing collections."""
    try:
        for db, collections in missing_collections.items():
            for collection in collections:
                logger.info(f"Backing up missing collection: {db}.{collection}")
                
                # Create command to dump only this collection
                cmd = [
                    "mongodump",
                    f"--host={host}",
                    f"--port={port}",
                    f"--username={username}",
                    f"--password={password}",
                    f"--authenticationDatabase={auth_db}",
                    f"--db={db}",
                    f"--collection={collection}",
                    f"--out={backup_path}"
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True)
                if result.returncode != 0:
                    logger.error(f"Failed to backup {db}.{collection}: {result.stderr}")
                    return False
                    
                logger.info(f"Successfully backed up {db}.{collection}")
                
        return True
    except Exception as e:
        logger.error(f"Error updating backup: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Check and update MongoDB backup")
    parser.add_argument("--host", default="*************", help="MongoDB server host")
    parser.add_argument("--port", type=int, default=27017, help="MongoDB server port")
    parser.add_argument("--username", default="dev", help="MongoDB username")
    parser.add_argument("--password", default="kaviadevsecureai", help="MongoDB password")
    parser.add_argument("--auth-db", default="admin", help="Authentication database")
    parser.add_argument("--backup-path", default="./backups/mongodb_backup", help="Backup directory path")
    
    args = parser.parse_args()
    
    backup_path = Path(args.backup_path)
    
    # Get source collections
    logger.info("Getting source database collections...")
    source_collections = get_source_collections(
        args.host, args.port, args.username, args.password, args.auth_db
    )
    
    if not source_collections:
        logger.error("Failed to get source collections. Exiting.")
        sys.exit(1)
    
    # Get backup collections
    logger.info(f"Getting backup collections from {backup_path}...")
    backup_collections = get_backup_collections(backup_path)
    
    # Find missing collections
    missing_collections = {}
    for db, collections in source_collections.items():
        if db not in backup_collections:
            missing_collections[db] = list(collections)
            logger.warning(f"Missing entire database in backup: {db}")
        else:
            missing = collections - backup_collections[db]
            if missing:
                missing_collections[db] = list(missing)
                logger.warning(f"Missing collections in {db}: {', '.join(missing)}")
    
    # If there are missing collections, update the backup
    if missing_collections:
        logger.info(f"Found {sum(len(cols) for cols in missing_collections.values())} missing collections")
        logger.info("Updating backup...")
        success = update_backup(
            args.host, args.port, args.username, args.password,
            backup_path, missing_collections, args.auth_db
        )
        
        if success:
            logger.info("Backup successfully updated")
        else:
            logger.error("Failed to update backup")
            sys.exit(1)
    else:
        logger.info("Backup is complete, no missing collections found")

if __name__ == "__main__":
    main()