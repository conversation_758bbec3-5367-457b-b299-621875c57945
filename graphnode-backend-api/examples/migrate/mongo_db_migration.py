#!/usr/bin/env python3
"""
MongoDB Migration Script

This script copies all databases, collections, and documents from a source MongoDB
instance to a target MongoDB instance, preserving indexes and other metadata.

Usage:
    python mongodb_migration.py <source_uri> <target_uri>

Example:
    python mongodb_migration.py "mongodb://user:<EMAIL>:27017/" "mongodb://user:<EMAIL>:27017/"

Requirements:
    - pymongo
    - tqdm (for progress bars)
"""

import sys
import time
import logging
from concurrent.futures import ThreadPoolExecutor
from typing import List, Dict, Any

import pymongo
from pymongo.database import Database
from pymongo.collection import Collection
from tqdm import tqdm

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("mongodb_migration.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("mongodb_migration")

# Constants
BATCH_SIZE = 1000  # Number of documents to insert in a single batch
MAX_WORKERS = 4    # Maximum number of concurrent threads for collection migration
EXCLUDED_DBS = ['admin', 'local', 'config']  # Databases to exclude from migration


def get_connection(uri: str) -> pymongo.MongoClient:
    """Establish connection to MongoDB instance."""
    try:
        client = pymongo.MongoClient(
            uri,
            connectTimeoutMS=30000,
            socketTimeoutMS=None,
            connect=True,
            maxPoolSize=50
        )
        # Verify connection
        client.admin.command('ping')
        return client
    except pymongo.errors.ConnectionFailure as e:
        logger.error(f"Could not connect to MongoDB: {e}")
        sys.exit(1)


def get_database_names(client: pymongo.MongoClient) -> List[str]:
    """Get list of database names, excluding system databases."""
    try:
        db_names = client.list_database_names()
        return [db for db in db_names if db not in EXCLUDED_DBS]
    except Exception as e:
        logger.error(f"Error listing databases: {e}")
        return []


def copy_collection_indexes(source_collection: Collection, target_collection: Collection) -> None:
    """Copy indexes from source collection to target collection."""
    try:
        indexes = source_collection.index_information()
        # Skip the default _id index
        for index_name, index_info in indexes.items():
            if index_name != '_id_':
                keys = index_info['key']
                options = {k: v for k, v in index_info.items() 
                           if k not in ('key', 'ns', 'v')}
                
                # Handle unique constraint
                if 'unique' in index_info:
                    options['unique'] = index_info['unique']
                
                # Handle TTL indexes
                if 'expireAfterSeconds' in index_info:
                    options['expireAfterSeconds'] = index_info['expireAfterSeconds']
                
                target_collection.create_index(keys, **options)
                logger.info(f"Created index {index_name} on {target_collection.full_name}")
    except Exception as e:
        logger.error(f"Error copying indexes for {source_collection.full_name}: {e}")


def copy_collection_data(source_collection: Collection, target_collection: Collection) -> int:
    """Copy all documents from source collection to target collection."""
    docs_count = source_collection.count_documents({})
    
    if docs_count == 0:
        logger.info(f"Collection {source_collection.full_name} is empty, skipping data copy")
        return 0
    
    try:
        copied = 0
        with tqdm(total=docs_count, desc=f"Copying {source_collection.full_name}", unit="docs") as pbar:
            cursor = source_collection.find({}, batch_size=BATCH_SIZE)
            batch = []
            
            for doc in cursor:
                batch.append(doc)
                
                if len(batch) >= BATCH_SIZE:
                    # Insert the batch
                    target_collection.insert_many(batch, ordered=False)
                    copied += len(batch)
                    pbar.update(len(batch))
                    batch = []
            
            # Insert any remaining documents
            if batch:
                target_collection.insert_many(batch, ordered=False)
                copied += len(batch)
                pbar.update(len(batch))
                
        return copied
    except Exception as e:
        logger.error(f"Error copying data for {source_collection.full_name}: {e}")
        return copied


def migrate_collection(args: Dict[str, Any]) -> Dict[str, Any]:
    """Migrate a single collection with its indexes and data."""
    source_db = args['source_client'][args['db_name']]
    target_db = args['target_client'][args['db_name']]
    collection_name = args['collection_name']
    
    start_time = time.time()
    result = {
        'collection': f"{args['db_name']}.{collection_name}",
        'documents_copied': 0,
        'success': False,
        'time': 0
    }
    
    try:
        source_collection = source_db[collection_name]
        
        # Create collection in target database
        if collection_name not in target_db.list_collection_names():
            target_db.create_collection(collection_name)
        
        target_collection = target_db[collection_name]
        
        # Copy indexes first (better to have indexes before data for efficiency)
        copy_collection_indexes(source_collection, target_collection)
        
        # Copy collection data
        docs_copied = copy_collection_data(source_collection, target_collection)
        
        result['documents_copied'] = docs_copied
        result['success'] = True
    except Exception as e:
        logger.error(f"Failed to migrate collection {args['db_name']}.{collection_name}: {e}")
    
    result['time'] = time.time() - start_time
    return result


def migrate_database(source_client: pymongo.MongoClient, 
                     target_client: pymongo.MongoClient, 
                     db_name: str) -> Dict[str, Any]:
    """Migrate a single database with all its collections."""
    logger.info(f"Migrating database: {db_name}")
    start_time = time.time()
    
    source_db = source_client[db_name]
    
    # Create database in target if it doesn't exist
    # MongoDB creates databases implicitly when collections are created
    target_db = target_client[db_name]
    
    # Copy collections
    collection_names = source_db.list_collection_names()
    logger.info(f"Found {len(collection_names)} collections in database {db_name}")
    
    results = {
        'database': db_name,
        'collections_total': len(collection_names),
        'collections_migrated': 0,
        'documents_copied': 0,
        'time': 0,
        'collection_results': []
    }
    
    if not collection_names:
        results['time'] = time.time() - start_time
        return results
    
    # Prepare arguments for each collection migration
    collection_args = [
        {
            'source_client': source_client,
            'target_client': target_client,
            'db_name': db_name,
            'collection_name': coll_name
        }
        for coll_name in collection_names
    ]
    
    # Migrate collections in parallel
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        collection_results = list(executor.map(migrate_collection, collection_args))
    
    # Process results
    for result in collection_results:
        if result['success']:
            results['collections_migrated'] += 1
            results['documents_copied'] += result['documents_copied']
        results['collection_results'].append(result)
    
    results['time'] = time.time() - start_time
    return results


def main() -> None:
    """Main function to execute the migration process."""
    if len(sys.argv) != 3:
        print(f"Usage: {sys.argv[0]} <source_uri> <target_uri>")
        sys.exit(1)
    
    source_uri = sys.argv[1]
    target_uri = sys.argv[2]
    
    logger.info("MongoDB Migration started")
    logger.info(f"Source URI: {source_uri.split('@')[-1] if '@' in source_uri else source_uri}")
    logger.info(f"Target URI: {target_uri.split('@')[-1] if '@' in target_uri else target_uri}")
    
    overall_start_time = time.time()
    
    # Connect to source and target MongoDB instances
    logger.info("Connecting to source MongoDB...")
    source_client = get_connection(source_uri)
    
    logger.info("Connecting to target MongoDB...")
    target_client = get_connection(target_uri)
    
    # Get list of databases to migrate
    db_names = get_database_names(source_client)
    logger.info(f"Found {len(db_names)} databases to migrate: {', '.join(db_names)}")
    
    # Migrate each database
    migration_results = []
    for db_name in db_names:
        db_result = migrate_database(source_client, target_client, db_name)
        migration_results.append(db_result)
        
        logger.info(f"Migrated database {db_name}: "
                    f"{db_result['collections_migrated']}/{db_result['collections_total']} collections, "
                    f"{db_result['documents_copied']} documents, "
                    f"{db_result['time']:.2f} seconds")
    
    # Calculate and report overall statistics
    total_time = time.time() - overall_start_time
    total_dbs = len(db_names)
    total_collections = sum(r['collections_total'] for r in migration_results)
    migrated_collections = sum(r['collections_migrated'] for r in migration_results)
    total_documents = sum(r['documents_copied'] for r in migration_results)
    
    logger.info("MongoDB Migration completed")
    logger.info(f"Total time: {total_time:.2f} seconds")
    logger.info(f"Databases: {total_dbs}")
    logger.info(f"Collections: {migrated_collections}/{total_collections}")
    logger.info(f"Documents: {total_documents}")
    
    # Close connections
    source_client.close()
    target_client.close()


if __name__ == "__main__":
    main()