# test_websocket_connection.py

from app.core.websocket.client import WebSocketClient
import time
from app.core.Settings import settings

def main():
    # Create WebSocket client instance with a task ID
    task_id = "test_task_123"
    ws_client = WebSocketClient(task_id, settings.WEBSOCKET_URI)

    try:
        print("Attempting to connect to WebSocket server...")
        
        # Attempt to connect
        connection_successful = ws_client.connect()

        if connection_successful:
            print("Successfully connected to WebSocket server!")
            
            if ws_client._offline_mode:
                print("Note: Running in offline mode")
            else:
                print("Connected in online mode")

            # Start the message handler
            ws_client.start_message_handler()

            # Keep the connection alive for a while to test
            print("Maintaining connection for 30 seconds...")
            time.sleep(30)

            # Test sending a message
            test_message = {
                "message": "Hello WebSocket Server!",
                "timestamp": time.time()
            }
            ws_client.send_message("test_message", test_message)
            print("Test message sent!")

        else:
            print("Failed to connect to WebSocket server")

    except Exception as e:
        print(f"An error occurred: {str(e)}")

    finally:
        # Cleanup
        print("Disconnecting...")
        ws_client.stop_message_handler()
        ws_client.disconnect()
        print("Disconnected")

if __name__ == "__main__":
    main()
