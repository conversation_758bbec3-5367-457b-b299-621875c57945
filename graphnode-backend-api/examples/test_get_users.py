from pymongo import MongoClient
import re

def get_user_ids_by_query(db_name):
    # Connect to MongoDB
    client = MongoClient("******************************************/")  # change URI if remote
    db = client[db_name]
    
    # Use the collection (adjust if your user collection has a different name)
    users_collection = db["users"]

    # Define your query
    query = {
        "email": {"$regex": re.compile(r"vitstudent\.ac\.in$")},
        "status": "active",
        "organization_id": "b2c"
    }

    # Perform the query and fetch IDs
    results = users_collection.find(query, {"_id": 1})
    user_ids = [str(doc["_id"]) for doc in results]

    return user_ids

# Example usage
if __name__ == "__main__":
    db_name = "pre_prod_kaviaroot"
    ids = get_user_ids_by_query(db_name)
    print("Matching user IDs:")
    for uid in ids:
        print(uid)