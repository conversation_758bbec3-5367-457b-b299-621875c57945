from pymongo import MongoC<PERSON>
from typing import List, Dict, Optional

# MongoDB connection URI
MONGO_URI = "***************************************"

# List of user_ids to lookup
user_ids = [
    "841864c8-50d1-7087-95f5-212ef6412419",
    "b428d4c8-f0f1-7017-fe81-d254a3bfad01",
    "14d8d4e8-8031-70b5-5bce-de73c7987faa",
    "44d8c418-e071-70c5-3d1d-ebdc58333911",
    "74f8c4c8-90b1-7064-5b9e-68093711a329",
    "c4f8f478-c031-7091-d7b2-955937616a2e",
    "9488e498-a0f1-70ba-f986-5da03631f0dc",
    "84688488-d001-701c-86fa-98dfa428c9e1",
    "04d81458-0041-703b-9c5c-c9ebdfd3f97a",
    "8478c4a8-f041-7047-0757-051f4067af39",
    "d4d884a8-70d1-7068-4f63-c961bf254e42",
    "2418c4a8-00f1-700f-79e9-4b7629528225",
    "e488c488-e091-70fa-b315-cc431e13d57b",
    "44f81488-9011-70e0-873b-29bd42d83b7d",
    "44c8c4e8-2071-7021-9dc7-b27c697f768a",
    "148884d8-4081-70ea-7893-6c97b3e79b23",
    "04f864f8-1031-70bd-9a88-1005af95846e",
    "54e854c8-1071-7053-c79e-9e515cb2d928",
    "94c82438-40b1-70c9-e0bd-8248f46fed6c",
    "64f8f428-e0f1-70e2-92f3-13396dacc786",
    "e40814d8-40a1-70b1-0cac-c62cd7656334",
    "24787498-5011-7081-e1e4-7b406e0fe2db",
    "54b8d468-20b1-7035-0a2a-01f05c287e36",
    "b428a408-0051-70b8-c7ed-d99b3154848d",
    "94b8f4a8-6051-707a-c126-fca2b9e5644e",
    "a49844e8-6081-70cb-fb62-58d87c74316a",
    "14e8c4b8-6031-70b1-fedb-fe666fc67e4d",
    "b4f85478-e0f1-704e-d2cb-bb885dd11fdf",
    "84d8d4a8-b0a1-701b-901a-0b7cf0c4988c",
    "f4c8c4a8-20a1-7071-1aa7-debc86be05e5",
    "d4583468-30d1-7045-72f0-95ee03c2a235",
    "54b89498-b031-70bc-a8d6-c8823674467b"
]

def get_names_batch(collection, user_ids: List[str]) -> Dict[str, Optional[str]]:
    """
    Efficiently fetch names for multiple user_ids in a single query.
    
    Args:
        collection: MongoDB collection object
        user_ids: List of user IDs to lookup
        
    Returns:
        Dictionary mapping user_id to name (or None if not found)
    """
    # Single query to fetch all users at once
    users = collection.find(
        {"_id": {"$in": user_ids}},
        {"_id": 1, "name": 1}  # Only fetch _id and name fields
    )
    
    # Create mapping of user_id to name
    user_mapping = {user["_id"]: user.get("name") for user in users}
    
    # Ensure all requested user_ids are in the result (with None for missing users)
    return {user_id: user_mapping.get(user_id) for user_id in user_ids}

def get_names_individual(collection, user_ids: List[str]) -> None:
    """
    Fetch and print names one by one (less efficient but more granular).
    
    Args:
        collection: MongoDB collection object
        user_ids: List of user IDs to lookup
    """
    print("Fetching names individually:")
    print("-" * 50)
    
    found_count = 0
    for i, user_id in enumerate(user_ids, 1):
        user = collection.find_one(
            {"_id": user_id},
            {"name": 1}
        )
        
        if user and "name" in user:
            name = user["name"]
            print(f"{i:2d}. {user_id} -> {name}")
            found_count += 1
        else:
            print(f"{i:2d}. {user_id} -> [NOT FOUND]")
    
    print(f"\nSummary: {found_count}/{len(user_ids)} users found")

def main():
    """Main function to demonstrate both batch and individual lookup methods."""
    try:
        print("Connecting to MongoDB...")
        client = MongoClient(MONGO_URI)
        db = client["pre_prod_kaviaroot"]
        users_collection = db["users"]  # Assuming users collection name
        
        print(f"Looking up names for {len(user_ids)} user IDs...\n")
        
        # Method 1: Batch lookup (more efficient)
        print("=" * 60)
        print("BATCH LOOKUP (Recommended - Single Query)")
        print("=" * 60)
        
        user_mapping = get_names_batch(users_collection, user_ids)
        
        found_count = 0
        for i, (user_id, name) in enumerate(user_mapping.items(), 1):
            if name:
                print(f"{i:2d}. {user_id} -> {name}")
                found_count += 1
            else:
                print(f"{i:2d}. {user_id} -> [NOT FOUND]")
        
        print(f"\nBatch Summary: {found_count}/{len(user_ids)} users found")
        
        # Optional: Method 2 (uncomment if needed)
        # print("\n" + "=" * 60)
        # print("INDIVIDUAL LOOKUP (Alternative Method)")
        # print("=" * 60)
        # get_names_individual(users_collection, user_ids)
        
    except Exception as e:
        print(f"Error occurred: {e}")
    finally:
        try:
            client.close()
            print("\nMongoDB connection closed.")
        except:
            pass

if __name__ == "__main__":
    main()