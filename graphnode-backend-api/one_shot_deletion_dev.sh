all_pods=`kubectl get pods -l service=codegen -n duploservices-k-qa01 --no-headers | awk '{print $1}'`
echo $all_pods
for i in $all_pods
do
  echo "inside the loop"
  echo $i
  deployment_name=`echo $i | cut -d "-" -f1,2`
  echo "INFO: Deleting deployment: $deployment_name"
  kubectl delete deployment $deployment_name -n duploservices-k-qa01 --ignore-not-found
  echo "INFO: Deleting the related services"
  service_name="internal-$deployment_name"
  service_cluster_name="internal-clusterip-$deployment_name"
  kubectl delete service $service_cluster_name -n duploservices-k-qa01 --ignore-not-found
  kubectl delete service $service_name -n duploservices-k-qa01 --ignore-not-found
  echo "INFO: Deleting the pvc: pvc-$deployment_name"
  pvc_name="pvc-$deployment_name"
  kubectl delete pvc $pvc_name -n duploservices-k-qa01 --ignore-not-found
  config_name="pod-status-$deployment_name"
  echo $config_name
  kubectl delete configmap $config_name -n duploservices-k-qa01 --ignore-not-found
done