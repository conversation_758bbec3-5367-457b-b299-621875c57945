import { getHeaders } from "@/utils/api";

const backend_base_url: string = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

interface FigmaResponse {
  // Add specific response type properties based on your API
  [key: string]: any;
}

interface FigmaV2Response {
  message: string;
  status: string;
  id: string;
}

interface FigmaProcessingStatus {
  id: string;
  status: string;
  total_frames: number;
  completed_frames: number;
  failed_frames: number;
  error_message: string | null;
  time_updated: string;
}

interface DeleteFigmaDesignResponse {
  message: string;
  id: string;
  results: {
    s3_deleted: boolean;
    mongodb_deleted: boolean;
    project_ref_removed: boolean;
  }
}

interface FigmaFileData {
  // You can expand this interface based on the actual response structure
  [key: string]: any;
}
interface FigmaExtImage {
  project_id: string;
  figma_ext_id: string;
  // Add other fields that might be in your image object
  [key: string]: any;
}

interface GetExtImageResponse {
  image: FigmaExtImage | null;
}
export async function addFigmaFile(
  projectId: string | number,
  name: string,
  url: string
): Promise<FigmaResponse> {
  const base_url = backend_base_url;
  const endpoint = `${base_url}/figma/add_figma_file`;

  try {
    const response = await fetch(`${endpoint}?project_id=${projectId}`, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify({
        name: name,
        url: url
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail);
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function updateFigmaFile(
  projectId: string | number,
  name: string,
  figmaLink: string
): Promise<FigmaResponse> {
  const base_url = backend_base_url;
  const endpoint = `${base_url}/figma/update_figma_file`;

  try {
    const response = await fetch(`${endpoint}?project_id=${projectId}`, {
      method: 'PUT',
      headers: await getHeaders(),
      body: JSON.stringify({
        name: name,
        url: figmaLink
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail);
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function getFigmaFiles(
  projectId: string | number
): Promise<FigmaResponse[]> {
  const base_url = backend_base_url;
  const endpoint = `${base_url}/figma/get_figma_files`;

  try {
    const response = await fetch(`${endpoint}?project_id=${projectId}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (!response.ok) {
      throw new Error('Failed to get Figma files');
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
}

export async function deleteFigmaFile(
  projectId: string | number,
  name: string,
  url: string
): Promise<FigmaResponse> {
  const base_url = backend_base_url;
  const endpoint = `${base_url}/figma/delete_figma_file`;

  try {
    const response = await fetch(`${endpoint}?project_id=${projectId}`, {
      method: 'DELETE',
      headers: await getHeaders(),
      body: JSON.stringify({
        name: name,
        url: url
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail);
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function addFigmaFileV2(
  projectId: string | number,
  name: string,
  url: string
): Promise<FigmaV2Response> {
  const base_url = backend_base_url;
  const endpoint = `${base_url}/figma/add_figma_file_v2`;

  try {
    const response = await fetch(`${endpoint}?project_id=${projectId}`, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify({
        name: name,
        url: url
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message);
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function getFigmaProcessingStatus(
  projectId: string | number,
  taskId: string,
  stream: boolean = false
): Promise<FigmaProcessingStatus> {
  const base_url = backend_base_url;
  const endpoint = `${base_url}/figma/stream-status/${projectId}/${taskId}`;

  try {
    const response = await fetch(`${endpoint}?stream=${stream}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail);
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function reloadFigmaDesign(
  projectId: string | number,
  taskId: string
): Promise<FigmaV2Response> {
  const base_url = backend_base_url;
  const endpoint = `${base_url}/figma/reload_design/${projectId}/${taskId}`;

  try {
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: await getHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail);
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function deleteFigmaDesign(
  projectId: string | number,
  taskId: string
): Promise<DeleteFigmaDesignResponse> {
  const base_url = backend_base_url;
  const endpoint = `${base_url}/figma/delete_design/${projectId}/${taskId}`;

  try {
    const response = await fetch(endpoint, {
      method: 'DELETE',
      headers: await getHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail);
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function getFigmaFileData(
  figmaId: string
): Promise<FigmaFileData> {
  const base_url = backend_base_url;
  const endpoint = `${base_url}/figma/get_figma_file_data`;

  try {
    const response = await fetch(`${endpoint}?figma_id=${figmaId}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail);
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}
export async function getFigmaExtImage(
  projectId: string | number,
  imageExtId: string
): Promise<GetExtImageResponse> {
  const base_url = backend_base_url;
  const endpoint = `${base_url}/figma/get_ext_images/${projectId}/${imageExtId}`;

  try {
    const response = await fetch(endpoint, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to get external image');
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
}
export async function deleteExtImages(
  figmaExtId: string
): Promise<{ message: string }> {
  const base_url = backend_base_url;
  const endpoint = `${base_url}/figma/delete_ext_images/${figmaExtId}`;

  try {
    const response = await fetch(endpoint, {
      method: 'DELETE',
      headers: await getHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to delete external images');
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
}