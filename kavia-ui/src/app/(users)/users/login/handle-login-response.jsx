"use client";

import React, { useState, useEffect, useContext } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { setCookie, decrypt } from "@/utils/auth";
import { encryptTenantId, getRootTenantId } from "@/utils/hash";
import Link from "next/link";
import <PERSON><PERSON><PERSON>ong<PERSON><PERSON> from "@/components/KaviaLongLogo";
import { getUser } from "@/utils/api";

const HandleLoginResponsePage = () => {
  const { showAlert } = useContext(AlertContext);
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isProcessing, setIsProcessing] = useState(true);
  const [status, setStatus] = useState("processing"); // processing, success, error
  const [tenant_name, setTenant_name] = useState(null);
  const [tenant_id, setTenant_id] = useState(null);
  const [isLoadingTenant, setIsLoadingTenant] = useState(true);
  const [errorShown, setErrorShown] = useState(false);

  const handleLogoClick = () => {
    const tenantId = encodeURIComponent(getRootTenantId());
    router.push({
      pathname: '/users/login',
      query: tenantId !== process.env.NEXT_PUBLIC_ROOT_TENANT_ID
        ? { tenant_id: tenantId }
        : {}
    });
  };

  useEffect(() => {
    // Get the login response data from localStorage
    const loginResponseData = localStorage.getItem('loginResponseData');

    if (!loginResponseData && !errorShown) {
      setErrorShown(true);
      showAlert("No login data received", "danger");
      setStatus("error");
      setIsProcessing(false);
      return;
    }

    // Get redirect URI if provided
    const redirectUri = searchParams.get("redirect_uri") || "/dashboard";

    async function processLoginResponse() {
      try {
        setIsProcessing(true);

        // Parse the login response data
        const response = JSON.parse(loginResponseData);

        if (!response || response.error) {
          setErrorShown(true);
          showAlert(response?.message || "Authentication failed", "danger");
          setStatus("error");
          return;
        }

        // Set tenant ID
        if (response.tenant_id) {
          const processedTenantId = encryptTenantId(response.tenant_id);
          setTenant_id(processedTenantId);
          setTenant_name(response.tenant_name || "Organization");
        }

        try {
          // 7-day expiration for all authentication cookies
          const cookieOptions = { expires: 7 }; // 7 days

          // Set cookies from the response data with 7-day expiration
          if (response.id_token) await setCookie("idToken", response.id_token, cookieOptions);
          if (response.refresh_token) await setCookie("refreshToken", response.refresh_token, cookieOptions);
          if (response.tenant_id) {
            await setCookie("encrypted_tenant_id", encryptTenantId(response.tenant_id), cookieOptions);
            await setCookie("tenant_id", response.tenant_id, cookieOptions);
          }

          // Handle user information from the JWT token
          if (response.id_token) {
            try {
              const decodedToken = await decrypt(response.id_token);
              if (decodedToken.sub) await setCookie("userId", decodedToken.sub, cookieOptions);
              if (decodedToken["custom:Name"]) await setCookie("username", decodedToken["custom:Name"], cookieOptions);
              if (decodedToken.email) await setCookie("email", decodedToken.email, cookieOptions);
            } catch (tokenError) {

              // Fall back to direct values if available
              if (response.user_id) await setCookie("userId", response.user_id, cookieOptions);
              if (response.username) await setCookie("username", response.username, cookieOptions);
              if (response.email) await setCookie("email", response.email, cookieOptions);
            }
          } else {
            // Fall back to direct values if available
            if (response.user_id) await setCookie("userId", response.user_id, cookieOptions);
            if (response.username) await setCookie("username", response.username, cookieOptions);
            if (response.email) await setCookie("email", response.email, cookieOptions);
          }

          // Set additional cookies for the specific response format with 7-day expiration
          if (response.is_admin !== undefined) await setCookie("is_admin", response.is_admin.toString(), cookieOptions);
          if (response.is_super_admin !== undefined) await setCookie("is_super_admin", response.is_super_admin.toString(), cookieOptions);
          if (response.AccessToken) await setCookie("accessToken", response.AccessToken, cookieOptions);

          // Check tenant status with getUser
          try {
            const userData = await getUser();

            // Check for inactive tenant
            if (userData.error && userData.errorType === "INACTIVE_TENANT" &&
              response.tenant_id !== process.env.NEXT_PUBLIC_ROOT_TENANT_ID) {
              // Set the inactive_tenant cookie and redirect to warning page
              document.cookie = "inactive_tenant=true; path=/";
              router.push("/warning?signin=true");
              return;
            }
          } catch (error) {
            // For other errors, continue with the normal flow

          }

          showAlert("Successfully authenticated", "success");
          setStatus("success");

          // Clear the login response data from localStorage
          localStorage.removeItem('loginResponseData');

          // Redirect to dashboard or specified redirect URI after a short delay
          setTimeout(() => {
            router.push(redirectUri, { forceRefresh: true });
          }, 2000);
        } catch (error) {

          setErrorShown(true);
          showAlert("Failed to process authentication", "danger");
          setStatus("error");
        }
      } catch (error) {

        setErrorShown(true);
        showAlert("Failed to process authentication", "danger");
        setStatus("error");
      } finally {
        setIsProcessing(false);
        setIsLoadingTenant(false);
      }
    }

    // Process the login response
    if (loginResponseData) {
      processLoginResponse();
    }
  }, [router, showAlert, errorShown, searchParams]);

  return (
    <div className="flex flex-col justify-center items-center h-screen bg-gray-100 relative">
      <div className="fixed top-0 left-0 w-full h-auto z-0 overflow-hidden">
        <svg
          width="1339"
          height="630"
          viewBox="0 0 1339 630"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M-29.8639 36.4947C60.2712 -118.158 189.994 -146.671 347.037 -142.805C481.412 -139.5 626.714 -103.648 750.733 -156.591C874.369 -209.373 959.936 -304.228 1093.19 -298.775C1271.86 -291.462 1348.82 -146.32 1338 -3.83823C1323.03 193.712 1155.57 294.342 989.469 283.675C852.007 274.851 706.468 262.402 583.689 353.952C470.601 438.27 393.707 612.104 237.31 627.97C-71.2614 659.273 -196.578 322.53 -29.8639 36.4947Z"
            fill="url(#paint0_linear_344_19883)"
          />
          <defs>
            <linearGradient
              id="paint0_linear_344_19883"
              x1="1181.75"
              y1="-424.8"
              x2="-43.2764"
              y2="746.157"
              gradientUnits="userSpaceOnUse"
            >
              <stop stopColor="#9D3ADF" />
              <stop offset="0.22" stopColor="#8542D8" />
              <stop offset="0.69" stopColor="#4857C6" />
              <stop offset="1" stopColor="#1F6FEB" />
            </linearGradient>
          </defs>
        </svg>
      </div>
      <header
        className="fixed top-10 left-8 text-white typography-heading-2 font-weight-semibold z-20 cursor-pointer"
        onClick={handleLogoClick}
      >
        <KaviaLongLogo />
      </header>

      <div className="bg-custom-bg-primary rounded-lg shadow-xl p-7 max-w-md w-full z-20 text-center">
        <h1 className="project-panel-heading mb-2 -mt-2 typography-heading-4 font-weight-semibold">Authentication</h1>

        {isLoadingTenant ? (
          <div className="inline-flex items-center px-3 py-1 mb-4 rounded-full bg-gradient-to-r from-purple-100 to-primary-100 border border-primary-200">
            <div className="w-2 h-2 rounded-full bg-primary mr-2 animate-pulse"></div>
            <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
          </div>
        ) : (tenant_name && tenant_id) && (
          <div className="inline-flex items-center px-3 py-1 mb-4 rounded-full bg-gradient-to-r from-purple-100 to-primary-100 border border-primary-200">
            <span className="w-2 h-2 rounded-full bg-primary mr-2"></span>
            <span className="font-weight-medium text-gray-800">{tenant_name}</span>
          </div>
        )}

        <div className="flex flex-col items-center justify-center py-6">
          {isProcessing ? (
            <>
              <div className="inline-block h-12 w-12 animate-spin rounded-full border-4 border-solid border-[#1F6FEB] border-e-transparent align-[-0.125em] mb-4"></div>
              <p className="text-gray-700 mb-2">Processing your authentication...</p>
              <p className="text-gray-500 typography-body-sm">Please wait while we verify your credentials</p>
            </>
          ) : status === "success" ? (
            <>
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <p className="text-gray-700 mb-2">Authentication successful!</p>
              <p className="text-gray-500 typography-body-sm">Redirecting to dashboard...</p>
            </>
          ) : (
            <>
              <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <p className="text-gray-700 mb-4">
                Authentication failed. Please try again.
              </p>
              <div className="flex space-x-4">
                <button
                  onClick={() => {
                    const loginPath = '/users/login';
                    const params = new URLSearchParams();

                    if (tenant_id) {
                      params.append('tenant_id', encodeURIComponent(tenant_id));
                    }

                    if (searchParams.get("redirect_uri")) {
                      params.append('redirect_uri', searchParams.get("redirect_uri"));
                    }

                    const queryString = params.toString();
                    router.push(`${loginPath}${queryString ? `?${queryString}` : ''}`);
                  }}
                  className="px-4 py-2 bg-[#1F6FEB] text-white rounded-md hover:bg-primary-600 transition-colors"
                >
                  Back to Login
                </button>
              </div>
            </>
          )}
        </div>
      </div>

      <div className="flex mt-6 gap-3 typography-body-sm">
        <div className="flex items-center justify-center text-gray-600 text-md">
          © 2025 Kavia AI
        </div>
        <div className="flex items-center justify-center text-gray-600 text-md space-x-4 z-50">
          {/* <span>|</span>
          <Link href="#" className="text-black">
            Support
          </Link> */}
          <span>|</span>
          <Link href="https://kavia.ai/privacy" target="_blank"
            rel="noopener noreferrer" className="text-black" >
            Privacy
          </Link>
          <span>|</span>
          <Link href="https://kavia.ai/terms" target="_blank"
            rel="noopener noreferrer" className="text-black">
            Terms
          </Link>
        </div>
      </div>
    </div>
  );
};

export default HandleLoginResponsePage; 