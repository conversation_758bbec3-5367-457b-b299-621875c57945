"use client";

import React, { useState, useEffect, useContext, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { getTenantName, getUser } from "@/utils/api";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { decryptTenantId, encryptTenantId, getRootTenantId } from "@/utils/hash";
import { setCookie } from "@/utils/auth";
import { googleCallback } from "@/utils/api";
import LoginSignupContainer from "@/components/LoginSignupContainer";

const GoogleCallbackPage = () => {
  const { showAlert } = useContext(AlertContext);
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isProcessing, setIsProcessing] = useState(true);
  const [status, setStatus] = useState("processing"); // processing, success, error, signup
  const [tenant_id, setTenant_id] = useState(null);
  const [tenant_name, setTenant_name] = useState(null);
  const [isLoadingTenant, setIsLoadingTenant] = useState(false);
  // Remove errorShown state to prevent flickering
  // Add a ref to track if the callback has been processed
  const callbackProcessed = useRef(false);

  const handleLogoClick = () => {
    const tenantId = encodeURIComponent(getRootTenantId());
    router.push({
      pathname: '/users/login',
      query: tenantId !== process.env.NEXT_PUBLIC_ROOT_TENANT_ID
        ? { tenant_id: tenantId }
        : {}
    });
  };

  useEffect(() => {
    // Prevent multiple callback processing
    if (callbackProcessed.current) {
      return;
    }

    // Check for code parameter immediately to avoid processing without it
    const code = searchParams.get("code");
    if (!code) {
      setStatus("error");
      setIsProcessing(false);
      // Don't show alert here to avoid flickering, only set the status
      // No automatic redirect - let the user click the button instead
      return;
    }

    // Get redirect URI if provided
    const redirectUri = searchParams.get("redirect_uri") || "/";

    async function fetchTenantName(tenant_id) {
      if (decryptTenantId(tenant_id) == process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID) {
        return;
      }
      try {
        setIsLoadingTenant(true);
        const tenant_details = await getTenantName(tenant_id);
        setTenant_name(tenant_details.name);
      } finally {
        setIsLoadingTenant(false);
      }
    }

    async function processCallback() {
      // Only run if we haven't processed the callback
      if (callbackProcessed.current) return;

      // Mark the callback as processed to prevent multiple calls
      callbackProcessed.current = true;

      try {
        const code = searchParams.get("code");
        const state = searchParams.get("state");
        const error = searchParams.get("error");
        const tenantIdParam = searchParams.get("tenant_id");

        if (error) {
          showAlert("Authentication failed: " + error, "danger");
          setStatus("error");
          setIsProcessing(false);
          return;
        }

        // Set tenant ID
        let processedTenantId;
        if (tenantIdParam) {
          if (decryptTenantId(tenantIdParam)) {
            processedTenantId = tenantIdParam;
          } else {
            processedTenantId = encryptTenantId(tenantIdParam);
          }
          setTenant_id(processedTenantId);
          fetchTenantName(processedTenantId);
        } else {
          // Use default B2C tenant ID
          const b2cTenantId = process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID;
          processedTenantId = encryptTenantId(b2cTenantId);
          setTenant_id(processedTenantId);
          fetchTenantName(processedTenantId);
        }

        try {
          // Process the callback with backend
          const response = await googleCallback(code, state, processedTenantId);

          // Check if the response is an error
          if (response.error) {
            showAlert(response.message || "Authentication failed", "danger");
            
            // Redirect to signup page if user doesn't exist
            if (response.error === "user_not_found") {
              showAlert("User does not exist. Redirecting to signup page...", "info");
              setStatus("signup");
              setIsProcessing(false);

              // Redirect to signup page after a short delay
              setTimeout(() => {
                const signupPath = '/users/sign_up';
                const params = new URLSearchParams();

                if (processedTenantId) {
                  params.append('tenant_id', encodeURIComponent(decryptTenantId(processedTenantId)));
                }

                if (searchParams.get("redirect_uri")) {
                  params.append('redirect_uri', searchParams.get("redirect_uri"));
                }

                // If we have the email from the Google response, pass it to the signup form
                if (response.email) {
                  params.append('email', response.email);
                }

                const queryString = params.toString();
                router.push(`${signupPath}${queryString ? `?${queryString}` : ''}`);
              }, 3000);
            } else {
              // For other errors, redirect to login page after a short delay
              setStatus("error");
              setIsProcessing(false);
              
              setTimeout(() => {
                const loginPath = '/users/login';
                const params = new URLSearchParams();

                if (processedTenantId) {
                  params.append('tenant_id', encodeURIComponent(decryptTenantId(processedTenantId)));
                }

                if (searchParams.get("redirect_uri")) {
                  params.append('redirect_uri', searchParams.get("redirect_uri"));
                }

                const queryString = params.toString();
                router.push(`${loginPath}${queryString ? `?${queryString}` : ''}`);
              }, 3000);
            }
            return;
          }

          // Set cookies if they're returned from the API
          if (response.id_token) {
            await setCookie("idToken", response.id_token);
            await setCookie("userId", response.userId);
            await setCookie("username", response.username);
          }
          if (response.refresh_token) {
            await setCookie("refreshToken", response.refresh_token);
          }
          
          // Handle tenant_id: check for custom:tenant_id in token first, then fallback to response.tenant_id
          let tenant_id_to_use = null;
          
          // Check for custom:tenant_id in the ID token if available
          if (response.id_token) {
            try {
              // Parse the JWT token (split by dots and decode the payload)
              const tokenParts = response.id_token.split('.');
              if (tokenParts.length >= 2) {
                const payload = JSON.parse(atob(tokenParts[1]));
                if (payload['custom:tenant_id']) {
                  tenant_id_to_use = payload['custom:tenant_id'];
                }
              }
            } catch (error) {
              
            }
          }
          
          // Fallback to response.tenant_id if custom:tenant_id wasn't found
          if (!tenant_id_to_use && response.tenant_id) {
            tenant_id_to_use = response.tenant_id;
          }
          
          // Save tenant_id if we have one from either source
          if (tenant_id_to_use) {
            await setCookie("encrypted_tenant_id", encryptTenantId(tenant_id_to_use));
            await setCookie("tenant_id", tenant_id_to_use);
          }
          
          if (response.user_id) {
            await setCookie("userId", response.user_id);
          }
          if (response.username) {
            await setCookie("username", response.username);
          }
          if (response.email) {
            await setCookie("email", response.email);
          }

          // Add the missing fields from the response
          if (response.is_admin !== undefined) {
            await setCookie("isAdmin", response.is_admin.toString());
          }
          if (response.is_super_admin !== undefined) {
            await setCookie("isSuperAdmin", response.is_super_admin.toString());
          }
          if (response.AccessToken) {
            await setCookie("accessToken", response.AccessToken);
          }
          if (response.ExpiresIn) {
            await setCookie("expiresIn", response.ExpiresIn.toString());
          }
          if (response.TokenType) {
            await setCookie("tokenType", response.TokenType);
          }
          if (response.provider) {
            await setCookie("authProvider", response.provider);
          }

          // Force a context refresh by triggering a storage event
          // This ensures UserContext picks up the new cookies immediately
          window.dispatchEvent(new Event('storage'));
          
          // First, update the status to success and show the alert
          setStatus("success");
          setIsProcessing(false);
          showAlert("Successfully authenticated with Google", "success");
          
          // Set a timer to check tenant status with getUser after showing success message
          setTimeout(async () => {
            try {
              const userData = await getUser();
              
              // Check for inactive tenant
              if (userData.error && userData.errorType === "INACTIVE_TENANT" &&
                  response.tenant_id !== process.env.NEXT_PUBLIC_ROOT_TENANT_ID) {
                // Set the inactive_tenant cookie and redirect to warning page
                document.cookie = "inactive_tenant=true; path=/";
                router.push("/warning?signin=true");
                return;
              }
              
              // Only redirect if we haven't redirected to warning page
              router.push(redirectUri, { forceRefresh: true });
            } catch (error) {
              // For other errors, just log and continue with normal redirect
              
              router.push(redirectUri, { forceRefresh: true });
            }
          }, 1000); // Wait 1 second after success message before checking user status
        } catch (error) {
          

          // Check if the error message contains "user_not_found"
          const isUserNotFound = error.message && (
            error.message.includes("user_not_found") ||
            error.message.includes("User does not exist")
          );

          // Provide more specific error messages based on the error
          if (isUserNotFound) {
            showAlert("User does not exist. Please sign up first.", "info");
            setStatus("signup");
            setIsProcessing(false);

            // Redirect to signup page after a short delay
            setTimeout(() => {
              const signupPath = '/users/sign_up';
              const params = new URLSearchParams();

              if (processedTenantId) {
                params.append('tenant_id', encodeURIComponent(decryptTenantId(processedTenantId)));
              }

              if (searchParams.get("redirect_uri")) {
                params.append('redirect_uri', searchParams.get("redirect_uri"));
              }

              const queryString = params.toString();
              router.push(`${signupPath}${queryString ? `?${queryString}` : ''}`);
            }, 3000);
          } else if (error.message && error.message.includes("Network error")) {
            setStatus("error");
            setIsProcessing(false);
            showAlert("Unable to connect to authentication service. Please check if the backend server is running.", "danger");

            // Redirect to login page after a short delay
            setTimeout(() => {
              const loginPath = '/users/login';
              const params = new URLSearchParams();

              if (processedTenantId) {
                params.append('tenant_id', encodeURIComponent(decryptTenantId(processedTenantId)));
              }

              if (searchParams.get("redirect_uri")) {
                params.append('redirect_uri', searchParams.get("redirect_uri"));
              }

              const queryString = params.toString();
              router.push(`${loginPath}${queryString ? `?${queryString}` : ''}`);
            }, 3000);
          } else if (error.message && error.message.includes("Failed to fetch")) {
            setStatus("error");
            setIsProcessing(false);
            showAlert("Network error: Unable to connect to the authentication server. This could be due to CORS issues or the server being unavailable.", "danger");

            // Redirect to login page after a short delay
            setTimeout(() => {
              const loginPath = '/users/login';
              const params = new URLSearchParams();

              if (processedTenantId) {
                params.append('tenant_id', encodeURIComponent(decryptTenantId(processedTenantId)));
              }

              if (searchParams.get("redirect_uri")) {
                params.append('redirect_uri', searchParams.get("redirect_uri"));
              }

              const queryString = params.toString();
              router.push(`${loginPath}${queryString ? `?${queryString}` : ''}`);
            }, 3000);
          } else if (error.message) {
            setStatus("error");
            setIsProcessing(false);
            showAlert(error.message, "danger");

            // Redirect to login page after a short delay
            setTimeout(() => {
              const loginPath = '/users/login';
              const params = new URLSearchParams();

              if (processedTenantId) {
                params.append('tenant_id', encodeURIComponent(decryptTenantId(processedTenantId)));
              }

              if (searchParams.get("redirect_uri")) {
                params.append('redirect_uri', searchParams.get("redirect_uri"));
              }

              const queryString = params.toString();
              router.push(`${loginPath}${queryString ? `?${queryString}` : ''}`);
            }, 3000);
          } else {
            setStatus("error");
            setIsProcessing(false);
            showAlert("Google authentication service is currently unavailable", "danger");

            // Redirect to login page after a short delay
            setTimeout(() => {
              const loginPath = '/users/login';
              const params = new URLSearchParams();

              if (processedTenantId) {
                params.append('tenant_id', encodeURIComponent(decryptTenantId(processedTenantId)));
              }

              if (searchParams.get("redirect_uri")) {
                params.append('redirect_uri', searchParams.get("redirect_uri"));
              }

              const queryString = params.toString();
              router.push(`${loginPath}${queryString ? `?${queryString}` : ''}`);
            }, 3000);
          }
        }
      } catch (error) {
        
        showAlert("Failed to process authentication", "danger");
        setStatus("error");
        setIsProcessing(false);
      }
    }

    // Only proceed with callback processing if we have a code
    if (code) {
      processCallback();
    }
  }, [searchParams, router, showAlert]);

  return (
    <LoginSignupContainer>
      <div className="bg-black/80 rounded-lg shadow-xl p-7 max-w-md w-full z-20 text-center border border-[hsl(var(--primary))]/30 backdrop-blur-sm">
        <h1 className="mb-2 -mt-2 typography-heading-4 font-weight-semibold text-white">Google Authentication</h1>

        {isLoadingTenant ? (
          <div className="inline-flex items-center px-3 py-1 mb-4 rounded-full bg-black/50 border border-[hsl(var(--primary))]/50">
            <div className="w-2 h-2 rounded-full bg-[hsl(var(--primary))] mr-2 animate-pulse"></div>
            <div className="h-4 w-24 bg-gray-700 rounded animate-pulse"></div>
          </div>
        ) : (tenant_name && decryptTenantId(tenant_id) !== process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID) && (
          <div className={`inline-flex items-center px-3 py-1 mb-4 rounded-full ${decryptTenantId(tenant_id) === process.env.NEXT_PUBLIC_ROOT_TENANT_ID
              ? 'bg-gradient-to-r from-[hsl(var(--primary))] to-[#FF9358] border border-[hsl(var(--primary))]'
              : 'bg-black/50 border border-[hsl(var(--primary))]/50'
            }`}>
            <span className={`w-2 h-2 rounded-full ${decryptTenantId(tenant_id) === process.env.NEXT_PUBLIC_ROOT_TENANT_ID ? 'bg-white' : 'bg-[hsl(var(--primary))]'
              } mr-2`}></span>
            <span className="font-weight-medium text-white">{tenant_name}</span>
          </div>
        )}

        <div className="flex flex-col items-center justify-center py-6">
          {isProcessing ? (
            <>
              <div className="inline-block h-12 w-12 animate-spin rounded-full border-4 border-solid border-[hsl(var(--primary))] border-e-transparent align-[-0.125em] mb-4"></div>
              <p className="text-gray-300 mb-2">Processing your Google authentication...</p>
              <p className="text-gray-400 typography-body-sm">Please wait while we verify your credentials</p>
            </>
          ) : status === "success" ? (
            <>
              <div className="w-12 h-12 bg-[hsl(var(--primary))]/20 rounded-full flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[hsl(var(--primary))]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <p className="text-gray-300 mb-2">Authentication successful!</p>
              <p className="text-gray-400 typography-body-sm">Redirecting to dashboard...</p>
            </>
          ) : status === "signup" ? (
            <>
              <div className="w-12 h-12 bg-[hsl(var(--primary))]/20 rounded-full flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[hsl(var(--primary))]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                </svg>
              </div>
              <p className="text-gray-300 mb-2">User not found</p>
              <p className="text-gray-400 typography-body-sm mb-4">Redirecting to signup page...</p>
              <div className="flex space-x-4">
                <button
                  onClick={() => {
                    const signupPath = '/users/sign_up';
                    const params = new URLSearchParams();

                    if (tenant_id) {
                      params.append('tenant_id', encodeURIComponent(decryptTenantId(tenant_id)));
                    }

                    if (searchParams.get("redirect_uri")) {
                      params.append('redirect_uri', searchParams.get("redirect_uri"));
                    }

                    const queryString = params.toString();
                    router.push(`${signupPath}${queryString ? `?${queryString}` : ''}`);
                  }}
                  className="px-4 py-2 bg-gradient-to-r from-[hsl(var(--primary))] to-[#FF9358] text-white rounded-md hover:from-[#FF9358] hover:to-[hsl(var(--primary))] transition-all"
                >
                  Go to Signup
                </button>
              </div>
            </>
          ) : (
            <>
              <div className="w-12 h-12 bg-[hsl(var(--primary))]/20 rounded-full flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[hsl(var(--primary))]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <p className="text-gray-300 mb-4">
                {!searchParams.get("code")
                  ? "No authentication code received. This may happen if you accessed this page directly or the authentication process was interrupted."
                  : "Authentication failed"}
              </p>
              <div className="flex space-x-4">
                <button
                  onClick={() => {
                    const loginPath = '/users/login';
                    const params = new URLSearchParams();

                    if (searchParams.get("redirect_uri")) {
                      params.append('redirect_uri', searchParams.get("redirect_uri"));
                    }

                    const queryString = params.toString();
                    router.push(`${loginPath}${queryString ? `?${queryString}` : ''}`);
                  }}
                  className="px-4 py-2 bg-gradient-to-r from-[hsl(var(--primary))] to-[#FF9358] text-white rounded-md hover:from-[#FF9358] hover:to-[hsl(var(--primary))] transition-all"
                >
                  Back to Login
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </LoginSignupContainer>
  );
};

export default GoogleCallbackPage; 