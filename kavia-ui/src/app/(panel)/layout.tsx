"use client";
import "../globals.css";
import React, { useState, useEffect, useCallback } from "react";
import { useSearchParams } from "next/navigation";
import Sidebar from "@/components/Sidebar";
import Header from "@/components/Header";
import SplitPanel from "@/components/SplitPanel";

import { PanelProvider } from "../../components/Context/PanelContext";
import { PastTasksModalProvider } from "@/components/Context/PastTasksModalContext";
import { DiscussionChatProvider } from "@/components/Context/DiscussionChatContext";
import StatusPanel from "@/components/StatusPanel/StatusPanel";
import { ExecutionProvider } from "@/components/Context/ExecutionContext";
import { CodeGenerationExecutionProvider } from "@/components/Context/CodeGenerationExecutionContext";
import { ArchitectureProvider } from "@/components/Context/ArchitectureContext";
import DiscussionModal from "../modal/DiscussionModal";
import DiscussionDrawer from "@/components/Drawer/DiscussionDrawer";
import HeaderDrawer from "@/components/Drawer/HeaderDrawer";
import { TabProvider } from "@/components/Context/TabContext";
import { StateProvider } from "@/components/Context/StateContext";
import TabInitializer from "@/components/Tabs/TabInitializer";
import { getCurrentDiscussions } from "@/utils/api";
import Head from "next/head";
import dynamic from "next/dynamic";
import useAutoLogout from "@/hooks/useAutoLogout";
import { CodeGenerationProvider } from "@/components/Context/CodeGenerationContext";
import { DiscussionTabProvider } from "@/components/Context/DiscussionTabContext";
import { DeploymentProvider } from "@/components/Context/DeploymentContext";
import { ProjectAssetProvider } from "@/components/Context/ProjectAssetContext";
import { BuildProgressProvider } from "@/components/Context/BuildProgressContext";
import { WebSocketProvider } from "@/components/Context/WebsocketContext";
import CollapseViewer from "@/components/CollapseViewer";
import { ModalProvider } from "@/components/Context/ModalContext";
import { ToggleGraphProvider } from "@/components/Context/ToggleGraphContext";
import TermsAcceptanceWrapper from '@/components/TermsAcceptanceModal/TermsAcceptanceWrapper';

const Topbar = dynamic(() => import("../../components/Topbar"), {
  ssr: false,
});

type DrawerType = "discussion" | "header" | null;

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [drawerType, setDrawerType] = useState<DrawerType>(null);
  const [discussions, setDiscussions] = useState<any[]>([]);
  const [codeGenKey, setCodeGenKey] = useState(0);
  
  const searchParams = useSearchParams();
  const taskId = searchParams?.get('task_id');

  const handleDrawerToggle = useCallback((type: DrawerType) => {
    setDrawerType(prev => prev === type ? null : type);
  }, []);

  // Reset CodeGenerationProvider when task_id changes
  useEffect(() => {
    setCodeGenKey(prev => prev + 1);
  }, [taskId]);

  useEffect(() => {
    async function fetchDiscussions() {
      try {
        const response = await getCurrentDiscussions();
        setDiscussions(response);
      } catch (error) {
        // Handle error silently or add logging
      }
    }

    fetchDiscussions();
  }, []);

  useAutoLogout();

  return (
    <TermsAcceptanceWrapper>
      <div className="flex h-screen overflow-hidden relative">
        <Head>
          <title>Project</title>
        </Head>
        <Sidebar />
        <PanelProvider>
          <BuildProgressProvider>
            <WebSocketProvider>
              <DiscussionTabProvider>
                <TabInitializer />
                <DeploymentProvider>
                  <ExecutionProvider>
                    <CodeGenerationExecutionProvider>
                      <PastTasksModalProvider>
                        <ArchitectureProvider>
                          <DiscussionChatProvider>
                            <ProjectAssetProvider>
                              <StateProvider>
                                <CodeGenerationProvider key={codeGenKey}>
                                  <TabProvider>
                                    <ToggleGraphProvider>
                                      <div className="flex flex-col w-full h-full">
                                        <div className="flex-none">
                                          <Topbar />
                                          <Header
                                            onCommentsIconClick={() =>
                                              handleDrawerToggle("discussion")
                                            }
                                            onPlayIconClick={() =>
                                              handleDrawerToggle("header")
                                            }
                                          />
                                        </div>
                                        <div className="flex-grow overflow-hidden">
                                          <DiscussionDrawer
                                            discussions={discussions}
                                            isOpen={drawerType === "discussion"}
                                            onClose={() => handleDrawerToggle("discussion")}
                                          />
                                          <HeaderDrawer
                                            isOpen={drawerType === "header"}
                                            onClose={() => handleDrawerToggle("header")}
                                          />
                                          <div className="flex-grow overflow-hidden h-full">
                                            <ModalProvider>
                                              <SplitPanel>
                                                <div className="h-full overflow-hidden">
                                                  <CollapseViewer />
                                                </div>
                                                {children}
                                                <div className="h-full overflow-hidden">
                                                  <StatusPanel />
                                                </div>
                                              </SplitPanel>
                                            </ModalProvider>
                                          </div>
                                        </div>
                                      </div>
                                      <DiscussionModal />
                                    </ToggleGraphProvider>
                                  </TabProvider>
                                </CodeGenerationProvider>
                              </StateProvider>
                            </ProjectAssetProvider>
                          </DiscussionChatProvider>
                        </ArchitectureProvider>
                      </PastTasksModalProvider>
                    </CodeGenerationExecutionProvider>
                  </ExecutionProvider>
                </DeploymentProvider>
              </DiscussionTabProvider>
            </WebSocketProvider>
          </BuildProgressProvider>
        </PanelProvider>
      </div>
    </TermsAcceptanceWrapper>
  );
}