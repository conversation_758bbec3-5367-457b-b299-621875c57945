import React, { useEffect, useState } from "react";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import { listBranches } from "@/utils/api";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { CloudUpload } from "lucide-react";
import { DeploymentModal } from "@/components/Modal/DeploymentModal";

interface Branch {
  name: string;
  lastCommitId: string;
  lastCommitMessage: string;
}

interface Repository {
  repositoryName: string;
  repositoryId: string;
  creationDate: string;
  lastModifiedDate: string;
  cloneUrlHttp: string;
}

const SkeletonCard = () => (
  <div className="p-4">
    <h3 className="typography-body-lg font-weight-medium text-semantic-gray-800 mb-6">Repository Details</h3>
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((i) => (
        <div key={i} className="bg-custom-bg-primary rounded-lg shadow-sm border border-custom-border p-4 animate-pulse">
          <div className="flex items-center gap-3 mb-4">
            <div className="h-4 bg-semantic-gray-200 rounded w-16"></div>
            <div className="h-6 bg-semantic-gray-200 rounded-full w-32"></div>
          </div>
          <div className="space-y-2 bg-semantic-gray-50 p-3 rounded-md">
            <div className="h-4 bg-semantic-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-semantic-gray-200 rounded w-full"></div>
          </div>
        </div>
      ))}
    </div>
  </div>
);

const truncateMessage = (message: string) => {
  const words = message.split(" ");
  return words.slice(0, 7).join(" ") + (words.length > 7 ? "..." : "");
};

const RepositoryDetails = ({ repository }: { repository: Repository }) => (
  <div className="bg-white p-1 mb-1">
    <div>
      <div className="flex justify-between">
        <p className="typography-body-sm text-gray-600">
          <span className="project-panel-heading">Repository Name:</span>{" "}
          <span>{repository.repositoryName}</span>
        </p>
        <span className="inline-flex items-center px-3 py-1 typography-body-sm rounded-full font-weight-medium text-primary-700 bg-primary-50 border border-primary-200">
          {repository.repositoryId}
        </span>
      </div>
    </div>
  </div>
);

const BranchList = ({ 
  branches, 
  projectId, 
  containerId 
}: { 
  branches: Branch[];
  projectId: string;
  containerId: string;
}) => {
  const [isDeploymentModalOpen, setIsDeploymentModalOpen] = useState(false);
  const [selectedBranch, setSelectedBranch] = useState<string | null>(null);

  const handleDeploy = (branchName: string) => {
    setSelectedBranch(branchName);
    setIsDeploymentModalOpen(true);
  };

  return (
    <>
      <div className="grid grid-cols-1 gap-4">
        {branches.map((branch) => (
          <div
            key={branch.name}
            className="bg-white rounded-lg shadow-sm border border-[#cfcfd2] p-3"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <span className="typography-body-sm font-weight-semibold text-gray-600 ml-1.5">
                  Branch
                </span>
                <span className="inline-flex items-center px-3 py-1 typography-body-sm rounded-full font-weight-medium text-primary-700 bg-primary-50 border border-primary-200">
                  {branch.name}
                </span>
              </div>
              <DynamicButton
                variant="primary"
                size="small"
                icon={CloudUpload}
                text="Deploy"
                tooltip="Deploy this branch"
                onClick={() => handleDeploy(branch.name)}
              />
            </div>
            <div className="space-y-2 bg-gray-50 p-2 -mt-2 rounded-md">
              <p className="typography-body-sm text-gray-600">
                <span className="project-panel-heading">Last Commit:</span>{" "}
                <span className="">{branch.lastCommitId.slice(0, 7)}</span>
              </p>
              <p className="typography-body-sm text-gray-600">
                <span className="project-panel-heading">Message:</span>{" "}
                <span>{truncateMessage(branch.lastCommitMessage)}</span>
              </p>
            </div>
          </div>
        ))}
      </div>

      {isDeploymentModalOpen && selectedBranch && (
        <DeploymentModal
          isOpen={isDeploymentModalOpen}
          onClose={() => {
            setIsDeploymentModalOpen(false);
            setSelectedBranch(null);
          }}
          projectId={projectId}
          containerId={containerId}
          branch="conflict-resolution-8d71f9cd"
        />
      )}
    </>
  );
};

const EnvironmentCard = () => {
  const [branches, setBranches] = useState<Branch[]>([]);
  const [repository, setRepository] = useState<Repository | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const projectId = window.location.pathname.split("/")[3];
  const containerId = window.location.pathname.split("/")[5];

  // const projectId = "37635"
  // const containerId = "37909"

  useEffect(() => {
    const fetchBranches = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const data = await listBranches(projectId, containerId);
        setBranches(data.branches);
        setRepository(data.repository);
      } catch (error) {
        setError("Failed to fetch repository details");
        
      } finally {
        setIsLoading(false);
      }
    };
    fetchBranches();
  }, [projectId, containerId]);

  if (isLoading) {
    return <SkeletonCard />;
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-red-200 p-6">
        <p className="text-red-600">{error}</p>
      </div>
    );
  }

  if (!branches.length) {
    return (
      <EmptyStateView
        type="noBranchesFound"
        onClick={() => {
          setBranches([]);
        }}
      />
    );
  }

  return (
    <div className="p-4">
      <div>
        {/* {repository && <RepositoryDetails repository={repository} />} */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <BranchList 
            branches={branches} 
            projectId={projectId}
            containerId={containerId}
          />
        </div>
      </div>
    </div>
  );
};

export default EnvironmentCard;