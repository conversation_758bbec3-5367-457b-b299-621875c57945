// index.tsx
import React from 'react';
import EnvironmentCard from './EnvironmentCard';

const DeploymentEnvironment = () => {
  return (
    <div className="h-70vh overflow-auto custom-scrollbar  p-2 mx-1">
      <div className="mb-1">
      {/* <h3 className="project-panel-heading">Repository Details</h3> */}
        {/* <h2 className="project-panel-heading">Deployment Environments</h2>
        <p className="typography-body-sm text-gray-500">Configure and manage your deployment environments</p> */}
      </div>
      <div className="">
        <EnvironmentCard />
      </div>
    </div>
  );
};

export default DeploymentEnvironment;