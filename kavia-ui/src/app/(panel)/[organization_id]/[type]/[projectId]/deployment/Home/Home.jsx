'use client';

import React from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Card } from '../components/ui/card';
import { Button } from '../components/ui/button';

const Home = () => {
  const router = useRouter();
  const pathname = usePathname();

  const resources = [
    {
      code: 'FE',
      title: 'Frontend Stack',
      description: 'Deploy scalable web applications',
      subtext: 'with CDN and load balancing',
      route: 'frontend'
    },
    {
      code: 'BE',
      title: 'Backend Stack',
      description: 'Configure containerized services',
      subtext: 'with API management',
      route: 'backend'
    }
  ];

  const handleCardClick = (route) => {
    router.push(`${pathname}/${route}`);
  };

  return (
    <div className="min-h-screen bg-white">
      <div className="mx-auto">
        {/* Container with consistent padding */}
        <div className="px-[16px] py-5">
          {/* Header Section */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="typography-heading-5 text-[#2A3439]">
                Infrastructure Resources
              </h1>
              <p className="text-[13px] text-[#2A3439] font-weight-normal mt-1.5">
                Select a resource to configure and deploy your application
              </p>
            </div>
            <Button
              className="h-[30px] px-4 bg-[#F5F6F7] text-[#2A3439] text-[14px] font-weight-medium rounded-md border border-[#F0F6FC]/10"
            >
              Auto Configure
            </Button>
          </div>

          {/* Resource Cards */}
          <div className="flex gap-[51px] mt-6">
            {resources.map((resource) => (
              <Card
                key={resource.code}
                className="group"
                onClick={() => handleCardClick(resource.route)}
              >
                <div className="w-[326px] h-28 cursor-pointer relative bg-white rounded-md border border-[#EFF2F5] group-hover:border-[#E5E9EB] group-hover:shadow-sm transition-all duration-200">
                  {/* Code Section */}
                  <div className="w-[50.50px] h-[52.50px] left-[24px] top-[27px] absolute">
                    {/* Border Box */}
                    <div className="w-[50.50px] h-[52.50px] left-0 top-0 absolute border border-[#EFF2F5] rounded-[4px] flex items-center justify-center">
                      {/* Centered Text */}
                      <div className="text-[#2a3439] typography-heading-1 font-weight-semibold  -mt-1">
                        {resource.code}
                      </div>
                    </div>
                  </div>

                  {/* Title */}
                  <div className="left-[97px] top-[32px] absolute text-black text-md font-weight-semibold">
                    {resource.title}
                  </div>

                  {/* Description */}
                  <div className="left-[97px] top-[52px] absolute text-[#2a3439] text-[12px] font-weight-medium">
                    {resource.description}   {resource.subtext}
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;