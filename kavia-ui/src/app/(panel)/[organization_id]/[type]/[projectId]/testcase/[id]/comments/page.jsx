"use client";

import React, { useState } from "react";

const CommentsTab = () => {
  const [newComment, setNewComment] = useState("");
  const [comments, setComments] = useState([
    {
      id: 1,
      author: "<PERSON>",
      content: "Comment by <PERSON>",
      timestamp: "07/05/2024 8:11 am",
    },
  ]);

  const handleAddComment = (e) => {
    e.preventDefault();
    if (newComment.trim()) {
      const newCommentObj = {
        id: comments.length + 1,
        author: "Current User", // This would typically come from a user context or prop
        content: newComment,
        timestamp: new Date().toLocaleString(),
      };
      setComments([newCommentObj, ...comments]);
      setNewComment("");
    }
  };

  return (
    <div className="pb-16">
      <form onSubmit={handleAddComment} className="mb-8">
        <input
          type="text"
          placeholder="Add comment"
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
        />
      </form>

      <div className="space-y-6">
        {comments.map((comment) => (
          <div key={comment.id} className="flex space-x-3">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 rounded-full bg-primary flex items-center justify-center text-white font-weight-semibold">
                {comment.author[0]}
              </div>
            </div>
            <div className="flex-grow">
              <div className="flex items-center space-x-2">
                <span className="font-weight-medium">{comment.author}</span>
                <span className="text-gray-500 typography-body-sm">
                  {comment.timestamp}
                </span>
              </div>
              <p className="text-gray-700 mt-1">{comment.content}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CommentsTab;
