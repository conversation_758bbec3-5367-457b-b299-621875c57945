"use client";
import React, { useState } from "react";
import { Avatar } from "@radix-ui/react-avatar";
import { ChevronDown, ChevronUp } from "lucide-react";

const HistoryItem = ({ user, date, description, previousDescription }) => {
  const [showPrevious, setShowPrevious] = useState(false);

  return (
    <div className="pb-16">
      <div className="flex items-start space-x-3">
        <Avatar className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
          <span className="font-weight-semibold text-white typography-body-sm">{user[0]}</span>
        </Avatar>
        <div className="flex-grow">
          <div className="flex items-center mt-2 mb-2">
            <span className="font-weight-semibold typography-body-sm mr-2">{user}</span>
            <span className="typography-caption text-gray-500">{date}</span>
          </div>
          <div className="bg-gray-50 p-4 rounded-md">
            <h3 className="font-weight-semibold typography-body-sm mb-1">Description</h3>
            <p className="typography-body-sm text-gray-700 mb-2">{description}</p>
            <button
              className="text-primary flex items-center typography-caption font-weight-medium"
              onClick={() => setShowPrevious(!showPrevious)}
            >
              {showPrevious ? (
                <ChevronUp className="mr-1 h-3 w-3" />
              ) : (
                <ChevronDown className="mr-1 h-3 w-3" />
              )}
              {showPrevious ? "Hide" : "Show"} previous value
            </button>
            {showPrevious && (
              <div className="mt-2 pt-2 border-t border-gray-200">
                <p className="typography-body-sm text-gray-600">{previousDescription}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

const HistoryTab = () => {
  const historyItems = [
    {
      id: 1,
      user: "John",
      date: "07/05/2024 8:11 am",
      description:
        "This is the updated description - given set of locations over a specific time period.",
      previousDescription:
        "This is the previous description - given set of locations over a specific time period.",
    },
  ];

  return (
    <div className="overflow-y-auto custom-scrollbar max-h-[70vh]">
      {historyItems.map((item) => (
        <HistoryItem key={item.id} {...item} />
      ))}
    </div>
  );
};

export default HistoryTab;
