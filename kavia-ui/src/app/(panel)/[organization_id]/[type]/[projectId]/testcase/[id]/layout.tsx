"use client"
import React from "react";

interface TestCaseItem {
  id: string;
  properties?: {
    Title?: string;
  };
}

const TestCaseLayout = ({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) => {
  return (
    <div className="flex flex-col h-screen overflow-hidden">
      {/* Main content - with improved overflow handling */}
      <div className="flex-1 overflow-y-auto min-h-0">
        {children}
      </div>
    </div>
  );
};

export default TestCaseLayout;