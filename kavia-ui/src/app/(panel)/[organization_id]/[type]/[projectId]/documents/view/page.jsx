// app/documents/view/page.jsx
'use client';

import { useSearchParams, useRouter } from 'next/navigation';
import DocumentViewer from '../DocumentViewer/DocumentViewer'

export default function ViewerPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const document = {
    projectId: searchParams.get('projectId'),
    docType: searchParams.get('docType'),
    version: searchParams.get('version'),
    folder_path: searchParams.get('folder_path'),
    fileName: searchParams.get('fileName'),
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <DocumentViewer 
      document={document} 
      onBack={handleBack}
    />
  );
}