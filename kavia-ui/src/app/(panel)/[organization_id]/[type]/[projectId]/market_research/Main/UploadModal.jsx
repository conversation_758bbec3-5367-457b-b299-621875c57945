import React, { useState, useEffect, useRef, useContext } from "react";
import { FaUpload } from "react-icons/fa6";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { X, CloudUpload } from 'lucide-react';

const UploadModal = ({ showModal, onClose }) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const modalRef = useRef(null);
  const { showAlert } = useContext(AlertContext);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose();
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [onClose]);


  if (!showModal) return null;

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    setSelectedFile(file);
  };

  const handleFileDrop = (event) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    setSelectedFile(file);
  };

  const handleDragOver = (event) => {
    event.preventDefault();
  };

  const handleUpload = () => {
    
    showAlert("File handling functionality was not implemented","info")
    setSelectedFile(null);
    onClose();
  };

  return (
    <>
      <div className="fixed inset-0 bg-gray-900 bg-opacity-50 z-40"></div>

      <div className="fixed inset-0 flex items-center justify-center z-50">
        <div className="bg-white w-full max-w-lg p-6 rounded-lg shadow-lg relative" ref={modalRef}>
          <div className="flex justify-end">
            <DynamicButton
              variant="ghost"
              size="sqSmall"
              icon={X}
              onClick={onClose}
              tooltip="Close"
            />
          </div>

          <h2 className="project-panel-title mb-4 border-b-2 border-gray-200 pb-2">
            Upload Research Document
          </h2>

          <div
            className="flex flex-col items-center border-2 border-dashed border-gray-300 p-8 rounded-lg mb-6"
            onDrop={handleFileDrop}
            onDragOver={handleDragOver}
          >
            <FaUpload size={30} className="text-gray-500 mb-4" />
            <p className="text-gray-600 mb-4">
              Drag and drop a file here or click below to upload
            </p>
            <input
              type="file"
              id="filePicker"
              className="hidden"
              onChange={handleFileChange}
            />
            <label
              htmlFor="filePicker"
              className="bg-gray-100 text-black px-4 py-2 rounded-lg hover:bg-gray-300 transition cursor-pointer"
            >
              {selectedFile ? selectedFile.name : "Choose a File"}
            </label>
          </div>
          <div className="flex justify-end">
            <DynamicButton
              type="submit"
              variant="primary"
              disabled={!selectedFile}
              icon={CloudUpload}
              onClick={handleUpload}
              text={selectedFile ? "Proceed to Upload" : "Upload"}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default UploadModal;
