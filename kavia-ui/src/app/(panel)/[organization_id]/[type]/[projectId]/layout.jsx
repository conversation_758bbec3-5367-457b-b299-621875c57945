"use client";

import React, { useState, useEffect, useContext, useRef, useCallback } from "react";
import { useRouter, usePathname } from "next/navigation";
import Head from "next/head";
import "@/styles/components/LockedTab.css";
import { TopBarContext } from "@/components/Context/TopBarContext";
import GraphModal from "@/components/Modal/GraphModal";
import GraphContent from "@/components/GraphContent";
import { LayoutGrid, FileIcon, ClipboardPen, Box, CodeXml, Files, Palette, FileCog, FileText } from 'lucide-react';
import { useUser } from "@/components/Context/UserContext";
import { LOCKED_TABS } from "@/components/FreePlanRestriction";
import { ChatContext } from "@/components/Context/ChatContext";
import { useToggleGraph } from "@/components/Context/ToggleGraphContext";
import { TOOLTIP_CONTENT } from "@/utils/constant/tooltip";
import ProfessionalTabsWithOverflow from '@/components/UIComponents/Tabs/ProfessionalTabsWithOverflow';

const TABS = [
  { name: "overview", icon: <LayoutGrid className="w-4 h-4" />, label: "Overview", tooltip: TOOLTIP_CONTENT.overview.title },
  { name: "query", icon: <FileIcon className="w-4 h-4" />, label: "Query", tooltip: TOOLTIP_CONTENT.codeQuery.title },
  { name: "code", icon: <CodeXml className="w-4 h-4" />, label: "Code", tooltip: TOOLTIP_CONTENT.codeMaintenance.title },
  { name: "requirements", icon: <ClipboardPen className="w-4 h-4" />, label: "Requirements", tooltip: TOOLTIP_CONTENT.Requirements.title },
  { name: "architecture", icon: <Box className="w-4 h-4" />, label: "Architecture", tooltip: TOOLTIP_CONTENT.Architecture.title },
  { name: "ui_ux", icon: <Palette className="w-4 h-4" />, label: "UI/UX", tooltip: TOOLTIP_CONTENT.Figma.title },
  { name: "documents", icon: <Files className="w-4 h-4" />, label: "Documents", tooltip: TOOLTIP_CONTENT.Document.title },
  { name: "testcase", icon: <FileCog className="w-4 h-4" />, label: "Test Case", tooltip: TOOLTIP_CONTENT.testCase.title },
  { name: "test_execution", icon: <FileText className="w-4 h-4" />, label: "Test Execution", tooltip: TOOLTIP_CONTENT.testExecution.title },
  { name: "deployment", icon: <FileCog className="w-4 h-4" />, label: "Deployment", tooltip: TOOLTIP_CONTENT.Deployement.title },
];


const isGraphPath = (path) => {
  if (!path) return false;
  const graphPaths = ['graph', 'newgraph2d', 'newgraph3d'];
  return graphPaths.some(graphPath => path.includes(graphPath));
};


const TabLayout = ({ children }) => {
  const [transition, setTransition] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const { is_free_user } = useUser();
  const { discussionId } = useContext(ChatContext);
  const { updateBrowseTabHref } = useContext(TopBarContext);
  const [isGraphModalOpen, setIsGraphModalOpen] = useState(false);
  const [lastGraphPath, setLastGraphPath] = useState('graph');
  const currentTab = pathname.split("/")[4];
  const organizationId = pathname.split("/")[1];
  const type = pathname.split("/")[2];
  const projectId = pathname.split("/")[3];
  const { setShowToggleButton, setIsGraphView, setToggleGraphFunction } = useToggleGraph();
  const shouldShowToggleButton = currentTab === "overview" || isGraphPath(currentTab);


  const handleTabChange = (value) => {
    const { buildProjectUrl } = require('@/utils/navigationHelpers');
    let url = buildProjectUrl(projectId, value, type, organizationId);
    if (discussionId) {
      url += `?discussion_id=${discussionId}`;
    }
    updateBrowseTabHref(projectId, value);
    router.push(url);
  };

  const toggleGraph = useCallback(() => {
    const { buildProjectUrl } = require('@/utils/navigationHelpers');
    const currentPath = pathname.split('/')[4];

    if (!isGraphPath(currentPath)) {
      router.push(buildProjectUrl(projectId, lastGraphPath, type, organizationId));
    } else {
      router.push(buildProjectUrl(projectId, 'overview', type, organizationId));
    }
  }, [pathname, projectId, lastGraphPath, router, type, organizationId]);

  const closeGraphModal = () => {
    setIsGraphModalOpen(false);
  };

  useEffect(() => {
    const currentPath = pathname.split('/')[4];
    if (isGraphPath(currentPath)) {
      setLastGraphPath(currentPath);
    }
  }, [pathname]);

  useEffect(() => {
    const timer = setTimeout(() => setTransition(false), 300);
    return () => clearTimeout(timer);
  }, [pathname]);

  const isFirstRender = useRef(true);

  useEffect(() => {
    setToggleGraphFunction(toggleGraph);
  }, [toggleGraph, setToggleGraphFunction]);

  useEffect(() => {
    setShowToggleButton(shouldShowToggleButton);
    setIsGraphView(isGraphPath(currentTab));

    if (isFirstRender.current) {
      isFirstRender.current = false;
    }
  }, [currentTab, pathname, shouldShowToggleButton, setShowToggleButton, setIsGraphView]);

  useEffect(() => {
    const path = pathname.split("/")[1];
    document.title = `${path.charAt(0).toUpperCase() + path.slice(1)}`;
  }, [pathname]);


  return (
    <div className="flex flex-col">
      <Head>
        <title>
          {TABS.find((tab) => tab.name === currentTab)?.label || "Project"}
        </title>
      </Head>

      <header className="sticky top-0 z-10 bg-white shadow-sm">
        <div className="w-full">
          <ProfessionalTabsWithOverflow
            tabs={TABS}
            currentTab={currentTab}
            handleTabChange={handleTabChange}
            isFreeUser={is_free_user}
            lockedTabs={LOCKED_TABS}
           
          />
        </div>
      </header>

      <main className={`
        flex-grow transition-all duration-300 justify-center
        ${transition ? "opacity-0" : "opacity-100"}
        bg-white
        px-4
      `}>
        <GraphModal
          isOpen={isGraphModalOpen}
          onClose={closeGraphModal}
          fullScreen={true}
        >
          <GraphContent />
        </GraphModal>
        {children}
      </main>
    </div>
  );
};

export default TabLayout;