"use client";
import React, { useEffect, useState, useContext } from "react";
import "@/styles/tabs/workItem.css"
import {
  useParams,
  usePathname,
  useSearchParams,
  useRouter,
} from "next/navigation";
import { Loading2 } from "@/components/Loaders/Loading";
import {
  fetchChildNodes,
  fetchNodeBasedOnDataModelById,
  fetchNodeById,
} from "@/utils/api";
import TableComponent from "@/components/SimpleTable/table";
import Search from "@/components/BrowsePanel/TableComponents/Search";
import PropertiesRenderer from "@/components/UiMetadata/PropertiesRenderer";
import { buildProjectUrl } from "@/utils/navigationHelpers";

import en from "@/en.json";
import ErrorView from "@/components/Modal/ErrorViewModal";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import ConfigureButtons from "@/components/ConfigureButtons/ConfigureButtons"
import CustomDropdown from '@/components/UIComponents/Dropdowns/CustomDropdown';
import { IconButton } from '@/components/UIComponents/Buttons/IconButton';
import {
  BookOpen,
  ArrowLeft,
  ArrowRight,
  X,
} from "lucide-react";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { updateNodeByPriority } from "@/utils/api";
import RequirementCard from '@/components/UIComponents/DetailCards/DetailCard';
import "@/styles/tabs/requirements.css"
import { CardLoadingSkeleton } from "@/components/UIComponents/Loaders/LoaderGroup"
import { TOOLTIP_CONTENT } from "@/utils/constant/tooltip";

export default function Page() {
  const params = useParams();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const workitemId = params.workitemId;
  const projectId = params.projectId;
  const [nodeLoading, setNodeLoading] = useState(true);
  const [nodeDetail, setNodeDetail] = useState(null);
  const [workItemNodeId, setWorkItemNodeId] = useState(null);
  const [uniqueIds, setUniquIds] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(-1);
  const [error, setError] = useState(null)
  const { showAlert } = useContext(AlertContext);


  // for child nodes
  const [nodeDetails, setNodeDetails] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [nodeBased, setNodeBased] = useState(null);

  const handleViewPastDiscussion = async () => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("view_past_discussions", "true");
    newSearchParams.set("node_id", workitemId);
    newSearchParams.set("discussion_type", `workItem`);
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const fetchData = async () => {
    try {
      const dataWorkItem = await fetchNodeById(workitemId, "WorkItem");
      const childNodeId = await fetchChildNodes(projectId, 'Project', 'WorkItemRoot');
      const nodes = await fetchChildNodes(childNodeId[0].id, 'WorkItem', 'WorkItem');
      const childNodes = await fetchChildNodes(workitemId, "WorkItem", "WorkItem");
      setNodeDetails(childNodes);
      setWorkItemNodeId(childNodeId[0].id);
      setUniquIds(nodes.map(item => item.id));
      setCurrentIndex(nodes.findIndex(dataItem => dataItem.id == workitemId));
      setNodeDetail(dataWorkItem);
      setNodeBased(await fetchNodeBasedOnDataModelById(workitemId, 'WorkItem'));
    } catch (e) {
      setError(e)
    } finally {
      setNodeLoading(false);
      setLoading(false);
    }
  }

  useEffect(() => {
    fetchData()
  }, [projectId, workitemId, searchParams]);

  const onRowClick = (id) => {
    router.push(buildProjectUrl(projectId, `workitems/${id}`));
  };

  const transformedNodeDetails = nodeDetails.map((item) => ({
    id: item.id,
    Title: item.properties.Title,
    Type: item.properties.Type,
    Assignee: item.properties.Assignee,
    Description: item.properties.Description,
  }));

  const filteredData = transformedNodeDetails.filter(
    (item) =>
      item.Title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.Assignee.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.Description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const headers = [
    { key: "id", label: "Id" },
    { key: "Title", label: "Title" },
    //   { key: 'Type', label: 'Type' },
    { key: "Assignee", label: "Assignee" },
    { key: "Description", label: "Description" },
  ];

  const updateProps = {
    onUpdateClick: () => {
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set("discussion", "new");
      newSearchParams.set("node_id", workitemId);
      newSearchParams.set("node_type", "WorkItem");
      router.push(`${pathname}?${newSearchParams.toString()}`);
    },
    buttonText: "Update WorkItem",
    tooltip: TOOLTIP_CONTENT.WorkItems.update,
  };

  const menuOptions = [
    {
      label: "History",
      icon: BookOpen,
      onClick: handleViewPastDiscussion,
      tooltip: TOOLTIP_CONTENT.WorkItems.viewPastDiscussion,
    },
  ]


  if (error) {
    return (
      <ErrorView
        title="Unable to Load Workitems"
        message={en.UnableToLoadWorkitems}
        onRetry={() => fetchData()}
        panelType='main'
      />
    );
  }

  const handlePropertyUpdate = async (key, value) => {
    try {
      const response = await updateNodeByPriority(workitemId, key, value);

      if (response === "success") {
        // Update local state
        setNodeBased(prev => ({
          ...prev,
          node: {
            ...prev.node,
            [key]: value
          }
        }));
        showAlert("Content updated successfully", "success"); // Add showAlert from context if you're using it
      } else {
        throw new Error('Update failed');
      }
    } catch (error) {

      showAlert && showAlert("Failed to update content", "error"); // Add showAlert from context if you're using it
    }
  };


  return (
    <div className="requirementContainerWrapper  custom-scrollbar">
      {/* Fixed Header */}
      <div className="flex items-center justify-between px-4 py-2 border-b shrink-0">
        <div className="flex items-center space-x-2">
          <IconButton
            icon={<X strokeWidth={3} className="h-4 w-4 text-gray-600" />}
            tooltip="Go to work item table"
            onClick={() => router.push(buildProjectUrl(projectId, 'workitems'))}
            className="hover:bg-gray-100"
            variant="small"
          />
          <div className="w-px h-4 bg-gray-100" />
          <IconButton
            icon={<ArrowLeft strokeWidth={3} className="h-4 w-4 text-gray-600" />}
            tooltip="Go to previous work item"
            onClick={() => {
              if (currentIndex - 1 >= 0) {
                router.push(
                  buildProjectUrl(projectId, `workitems/${uniqueIds[currentIndex - 1]}`)
                );
              }
            }}
            className="hover:bg-gray-100"
            variant="small"
          />
          <IconButton
            icon={<ArrowRight strokeWidth={3} className="h-4 w-4 text-gray-600" />}
            tooltip="Go to next work item"
            onClick={() => {
              if (currentIndex >= 0 && currentIndex + 1 < uniqueIds.length) {
                router.push(
                  buildProjectUrl(projectId, `workitems/${uniqueIds[currentIndex + 1]}`)
                );
              }
            }}
            className="hover:bg-gray-100"
            variant="small"
          />
        </div>

        <div className="flex items-center space-x-2">
          <ConfigureButtons updateProps={updateProps} />
          <CustomDropdown options={menuOptions} align="right" />
        </div>
      </div>

      {/* Scrollable Content */}
      <div>
        {nodeLoading ? (
          <CardLoadingSkeleton />
        ) : (
          <div className="min-h-full">
            {nodeDetail ? (
              <div className="p-4 pr-4 space-y-4">
                <RequirementCard
                  nodeDetails={nodeDetail}
                  onKeyUpdate={handlePropertyUpdate}
                />
                <div className="space-y-4">
                  {nodeBased && (
                    <PropertiesRenderer
                      properties={nodeBased.node}
                      metadata={nodeBased.ui_metadata.WorkItem}
                      to_skip={[
                        "Assignee",
                        "Title",
                        "Id",
                        "Type",
                        "Priority",
                        "Description",
                        "EstimatedDuration",
                      ]}
                      onUpdate={handlePropertyUpdate}
                    />
                  )}
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-full p-4">
                <EmptyStateView type="workItems" />
              </div>
            )}

            {loading ? (
              <div className="flex items-center justify-center p-4">
                <Loading2 />
              </div>
            ) : (
              <div className="border-t">
                {nodeDetails.length !== 0 && (
                  <div className="p-4 space-y-4">
                    <div className="flex items-center max-w-md">
                      <Search
                        searchTerm={searchTerm}
                        setSearchTerm={setSearchTerm}
                      />
                    </div>
                    {filteredData.length === 0 ? (
                      <div className="flex items-center justify-center p-8">
                        <EmptyStateView type="noSearchResult" />
                      </div>
                    ) : (
                      <TableComponent
                        data={filteredData}
                        onRowClick={onRowClick}
                        headers={headers}
                        sortableColumns={{ id: true, Title: true, Assignee: true }}
                        itemsPerPage={20}
                      />
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
