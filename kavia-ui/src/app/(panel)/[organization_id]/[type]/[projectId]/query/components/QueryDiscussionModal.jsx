'use client';
import React, { useState, useEffect, useRef } from 'react';
import { X, Save, Trash } from 'lucide-react';
import { useSearchParams, usePathname } from 'next/navigation';
import DiscussionChatPanel from '@/components/DiscussionChatPanel';
import EmptyState from './content/EmptyState';
import DocumentView from './content/DocumentView';
import CodeView from './content/CodeView';
import MermaidChart from './content/MermaidChart';
import FileStructure from './content/FileStructure';
import { FaCode, FaSearch, FaMagic, FaCheck } from 'react-icons/fa';
import { getCookie } from '@/utils/auth';
import { useContext } from 'react';
import { AlertContext } from '@/components/NotificationAlertService/AlertList';
import { initializeCodeQueryAPI, checkCodeQueryStatusAPI, deleteCodeQuerySessionAPI } from "@/utils/api";
import { useRouter } from 'next/navigation';
import { useRightContent } from '@/components/Context/RightContentContext';
import { updateNodeByPriority } from '@/utils/api';
import { fetchSavedDocuments } from '@/utils/documentationAPI';
import { deleteQueryDiscussion } from '@/utils/api';

const LoadingStep = ({ label, status, icon: Icon, progress, customProgress }) => (
  <div className="flex items-center space-x-4 py-3">
    <div className={`
      rounded-full p-3 transition-all duration-300
      ${status === 'completed' ? 'bg-green-100' :
        status === 'loading' ? 'bg-primary-100' :
          'bg-gray-100'}
    `}>
      {status === 'completed' ? (
        <FaCheck className="h-5 w-5 text-green-600" />
      ) : status === 'loading' ? (
        <Icon className={`h-5 w-5 ${status === 'loading' ? 'text-primary animate-pulse' : 'text-gray-400'}`} />
      ) : (
        <Icon className="h-5 w-5 text-gray-400" />
      )}
    </div>
    <div className="flex-1">
      <p className={`font-weight-medium ${status === 'completed' ? 'text-green-600' :
        status === 'loading' ? 'text-primary' :
          'text-gray-500'
        }`}>
        {label}
        {progress && (
          <span className="ml-2 typography-body-sm text-gray-500">
            ({progress.filesProcessed}/{progress.totalFiles} files)
          </span>
        )}
      </p>
      <div className="mt-1 h-1 w-full rounded-full bg-gray-100">
        {(status === 'completed' || status === 'loading') && (
          <div
            className={`h-1 rounded-full transition-all ${status === 'completed' ? 'bg-green-500 w-full' : 'bg-primary'}`}
            style={{
              width: status === 'loading' && customProgress !== undefined
                ? `${customProgress}%`
                : status === 'loading'
                  ? '75%'
                  : '100%',
              transition: 'width 1s ease'
            }}
          />
        )}
      </div>
    </div>
  </div>
);

const CloseConfirmationModal = React.memo(({ isVisible, onClose, sessionId, discussionId, onTerminate, closeDiscussion, showAlert, setInstantCloseStatus, closeWebSocket }) => {
  const [newSessionName, setNewSessionName] = useState('Untitled Session');
  const [isUpdating, setIsUpdating] = useState(false);
  const sessionTextBox = useRef(null);
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    if (searchParams.get('session_name')) {
      setNewSessionName(searchParams.get('session_name'));
    }
  }, [searchParams]);

  const handleConfirm = async () => {
    try {
      setInstantCloseStatus(); //instantly set the close status to true to avoid updating search params in discussionChatContext
      closeWebSocket();
      setIsUpdating(true);
      router.push(pathname);
      closeDiscussion(); 
      onClose(); // close the confirm modal

      // Update session name if discussionId exists
      if (discussionId) {
        try {
          await updateNodeByPriority(discussionId, 'session_name', newSessionName);
        } catch (error) {
          showAlert('Failed to update session name: ' + error, 'error');
        }
      }
      await deleteCodeQuerySessionAPI(sessionId, discussionId, 'stop');
      onTerminate();
    } catch (error) {
      router.push(pathname);
      showAlert('Error processing your request: ' + error, 'error');
      closeDiscussion(); 
      onClose(); // close the confirm modal
      onTerminate();
    } finally {
      setIsUpdating(false);
    }
  };
  
  const handleDirectClose = async () => {
    try {
      setIsUpdating(true);
      setInstantCloseStatus();
      closeWebSocket();
      router.push(pathname);
      closeDiscussion();
      onClose();
      
      // Call the deleteQueryDiscussion API directly
      if (discussionId) {
        try {
          await deleteQueryDiscussion(discussionId);
        } catch (error) {
          showAlert('Failed to delete discussion: ' + error, 'error');
        }
      }
      
      await deleteCodeQuerySessionAPI(sessionId, discussionId, 'stop');
      onTerminate();
    } catch (error) {
      showAlert('Error processing your request: ' + error, 'error');
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    isVisible && (
      <div className="fixed inset-0 flex items-center justify-center z-[60]">
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={onClose} />
        <div className="bg-white rounded-xl shadow-lg p-6 max-w-md mx-auto z-[70] relative">
          <div className="flex justify-between items-center mb-5">
            <h2 className="typography-body-lg font-weight-semibold text-gray-800">Close Session</h2>
            <button className="text-gray-400 hover:text-gray-600" onClick={onClose}>
              <X size={20} />
            </button>
          </div>
          
          {/* Confirmation message */}
          <p className="mt-2 mb-6 typography-body-sm text-gray-500">
            Are you sure you want to close the code query panel?
          </p>
          
          {/* Session Name Input - Only show if discussionId exists */}
          {discussionId && (
            <div className="mb-6">
              <label className="block typography-body-sm font-weight-medium text-gray-600 mb-2">
                Name your session
              </label>
              <input
                ref={sessionTextBox}
                type="text"
                value={newSessionName}
                onChange={(e) => setNewSessionName(e.target.value)}
                placeholder="Untitled Session"
                className="w-full py-3 px-4 bg-white border border-primary rounded-lg outline-none ring-1 ring-primary-500 text-gray-800"
              />
            </div>
          )}
          
          {discussionId ? (
            // If discussionId exists, show the new styled buttons
            <div className="grid grid-cols-2 gap-4">
              <button 
                onClick={handleDirectClose}
                disabled={isUpdating}
                className="py-2 px-4 flex items-center justify-center gap-2 bg-gray-100 hover:bg-gray-200 text-gray-700 font-weight-medium rounded-lg border border-gray-300"
              >
                <Trash size={18} />
                <span>{isUpdating ? 'Closing...' : 'Discard'}</span>
              </button>
              
              <button
                onClick={handleConfirm}
                disabled={isUpdating}
                className="py-2 px-4 flex items-center justify-center gap-2 bg-primary hover:bg-primary-600 text-white font-weight-medium rounded-lg"
              >
                <Save size={18} />
                <span>{isUpdating ? 'Saving...' : 'Save'}</span>
              </button>
            </div>
          ) : (
            // If no discussionId, keep the original Cancel and Close buttons
            <div className="grid grid-cols-2 gap-4 mt-6">
              <button
                onClick={onClose}
                className="py-2 px-4 flex items-center justify-center gap-2 bg-gray-100 hover:bg-gray-200 text-gray-700 font-weight-medium rounded-lg border border-gray-300"
              >
                <span>Cancel</span>
              </button>
              
              <button
                onClick={handleConfirm}
                disabled={isUpdating}
                className="py-2 px-4 flex items-center justify-center gap-2 bg-primary hover:bg-primary-600 text-white font-weight-medium rounded-lg"
              >
                <span>{isUpdating ? 'Closing...' : 'Close'}</span>
              </button>
            </div>
          )}
        </div>
      </div>
    )
  );
}, (prevProps, nextProps) => {
  // Proper equality check for memoization
  return prevProps.isVisible === nextProps.isVisible && 
         prevProps.sessionName === nextProps.sessionName &&
         prevProps.discussionId === nextProps.discussionId;
});
CloseConfirmationModal.displayName = 'CloseConfirmationModal';




const QueryDiscussionModal = ({ isOpen, setIsOpen, onClose, setInstantCloseStatus, wsConnection }) => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const buildIdsParam = searchParams.get('buildIds');
  const discussion_id = searchParams.get('query_discussion_id');
  const buildIds = buildIdsParam ? [buildIdsParam] : [];
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const { rightContent, setRightContent } = useRightContent();
  const [, setCurrentIndex] = useState(0);
  const [, setIsInitializing] = useState(true);
  const [currentStep, setCurrentStep] = useState(0);
  const [sessionId, setSessionId] = useState(null);
  const { showAlert } = useContext(AlertContext);
  const [hasInitialized, setHasInitialized] = useState(false);
  const [isRequestInProgress, setIsRequestInProgress] = useState(false);
  const [progress, setProgress] = useState({ filesProcessed: 0, totalFiles: 0 });
  const [pollCount, setPollCount] = useState(0);
  const initializationSteps = [
    { label: 'Initializing session for code query', id: 'init', icon: FaCode },
    { label: 'Analyzing your codebase...', id: 'analyze', icon: FaSearch },
    { label: 'Almost there! Your code analysis will be ready for queries in a few moments...', id: 'finalize', icon: FaMagic }
  ];
  const initializationAttempted = React.useRef(false);
  const [accordionKeys, setAccordionKeys] = useState(new Date().toISOString());
  const [initProgress, setInitProgress] = useState(0);
  const initInterval = useRef(null);
  const isOpenRef = useRef(isOpen);
  const projectId = pathname.split('/')[3];
  const [savedDocuments, setSavedDocuments] = useState([]); 

  const handleSetIsOpen = (value) => {
    isOpenRef.current = value;
    setIsOpen(value);
  }

  useEffect(() => {
    isOpenRef.current = isOpen;
  }, [isOpen]); 

  const closeWebSocket = () => {
    if(wsConnection) {
      wsConnection.close();
    }
  }


  useEffect(() => {
    const sessionIdFromUrl = searchParams.get('sessionId');
    const discussion = searchParams.get('discussion');
    if (sessionIdFromUrl && discussion === 'New') {
      setSessionId(sessionIdFromUrl);
      setIsInitializing(false);
      return;
    }

    const initialize = async () => {
      const existingSessionId = searchParams.get('sessionId');
      if (!existingSessionId && !initializationAttempted.current && isOpen && buildIds.length > 0) {
        initializationAttempted.current = true;
        await handleInitialization();
      }
    };
    initialize();
    return () => {
      if (!isOpen) {
        setIsRequestInProgress(false);
        setCurrentStep(0);
        setSessionId(null);
        setHasInitialized(false);
        initializationAttempted.current = false;
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, buildIds, searchParams]);

  useEffect(() => {
    const fetchData = async () => {
      const savedDocs = await fetchSavedDocuments(projectId, "SAVED", "", discussion_id);
      if(savedDocs){
        setSavedDocuments(savedDocs.files);
      }
    }

    if(isOpenRef.current){
      fetchData();
    }
  }, [discussion_id]);

  useEffect(() => {
    setAccordionKeys(new Date().toISOString());
  }, [rightContent])

  const checkStatus = async (sessionId) => {
    try {
      if(isOpenRef.current){
        const statusResponse = await checkCodeQueryStatusAPI(sessionId);
        const statusData = await statusResponse.json();

        if(isOpenRef.current){
          setProgress({
            filesProcessed: statusData.files_processed,
            totalFiles: statusData.total_files
          });

          if (statusData.is_ready) {
            if (pollCount === 0) {
              setCurrentStep(2);
              await new Promise(resolve => setTimeout(resolve, 1000));
              setCurrentStep(3);
              setTimeout(() => {
                setIsInitializing(false);
                setIsRequestInProgress(false);
              }, 1000);
            } else {
              setCurrentStep(3);
              setTimeout(() => {
                setIsInitializing(false);
                setIsRequestInProgress(false);
              }, 1000);
            }
            return true;
          }
          if (pollCount === 2) {
            setCurrentStep(2);
          }
          setPollCount(prev => prev + 1);
          return false;
        }
      }
    } catch (error) {
      
      showAlert('Error checking session status', 'error');
      return false;
    }
  };



  const pollStatus = async (sessionId) => {
    while (true && isOpen) {
      const isReady = await checkStatus(sessionId);
      if (isReady) break;
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  };

  const getBuildIdsArray = (buildIdsParam) => {
    if (!buildIdsParam) return [];
    return buildIdsParam.split(',').map(id => id.trim());
  };


  const handleInitialization = async () => {
    if (isRequestInProgress || hasInitialized) return;
    setIsRequestInProgress(true);
    setHasInitialized(true);
    setInitProgress(0); // Start at 0%

    try {
      const userId = await getCookie('userId');
      const tenantId = await getCookie('tenant_id');
      const buildIdsParam = searchParams.get('buildIds');
      const session_name = searchParams.get('sessionName');
      const description = searchParams.get('sessionDescription')
      const buildIds = getBuildIdsArray(buildIdsParam);
      // Initial progress increment
      setInitProgress(10);

      if (!userId || !tenantId || !projectId || !buildIds.length) {
        throw new Error('Missing required parameters');
      }

      // Increment progress gradually while API call is in progress
      initInterval.current = setInterval(() => {
        setInitProgress(prev => prev < 70 ? prev + 10 : prev); // Cap at 70%
      }, 1200); // Roughly ~12 seconds to reach 70%

      const data = await initializeCodeQueryAPI(userId, tenantId, projectId, buildIds, session_name, description);

      // Clear the interval once API call completes
      clearInterval(initInterval.current);

      if (data.status === 'success' && data.session_id && isOpenRef.current) {
        setInitProgress(75);
        setSessionId(data.session_id);
        setCurrentStep(1);
        await new Promise(resolve => setTimeout(resolve, 1000));
        await pollStatus(data.session_id);
        
        if(isOpenRef.current && typeof window !== "undefined"){ //only change url if modal is open. Using ref so that the value of isOpen changes even if the function is mid-execution.
          const searchParams = new URLSearchParams(window.location.search);
          searchParams.set('sessionId', data.session_id);
          const newUrl = `${window.location.pathname}?${searchParams.toString()}`;
          window.history.pushState({}, '', newUrl);
        }
        
      } else {
        if(isOpenRef.current){
          throw new Error(data.message || 'Failed to initialize code query');
        }
      }

    } catch (error) {
      
      showAlert(error.message || 'Error during initialization', 'error');
      setIsRequestInProgress(false);
      setHasInitialized(false);
      setCurrentStep(0);
    }
  };

  if (!isOpen) return null;

  const handleConfirmClose = () => {
    setShowConfirmModal(true);
  };


  const confirmCloseModal = async (action, discussion_id, sessionName) => {
    try {
      if (sessionId) {
        try {

          if (action === 'stop') {
            const response = await deleteCodeQuerySessionAPI(sessionId, discussion_id, action);
            const data = await response.json();

            if (data.status === 'success') {
              sessionStorage.removeItem(`message_status_${sessionId}`);
            }
          } else {
            const projectId = pathname.split('/')[3];
            sessionStorage.removeItem(`message_status_${sessionId}`);
            sessionStorage.setItem(`${projectId}_${discussion_id}_${sessionId}`, 'archive');
          }
        } catch (error) {
          
        }
      }

      resetStates();
      onClose();
    } catch (error) {
      
      router.push(pathname);
      resetStates();
      onClose();
    }
  };

  const resetStates = () => {
    if(initInterval.current){
      clearInterval(initInterval.current);
    }
    setInitProgress(0);
    setProgress({ filesProcessed: 0, totalFiles: 0 });
    setCurrentStep(0);
    setPollCount(0);
    setShowConfirmModal(false);
    // setRightContent({ type: 'empty', data: null });
    setCurrentIndex(0);
    setIsInitializing(true);
    setSessionId(null);
    setHasInitialized(false);
    setIsRequestInProgress(false);
    initializationAttempted.current = false;
  };

  const handleClose = () => {
    onClose();
  };

  const renderRightContent = () => {
    if (!rightContent || !rightContent.data) {
      return <EmptyState />;
    }
    if (rightContent.data != undefined) {
      switch (rightContent.type) {
        case 'document':
          return (
            <div className="h-full overflow-y-auto">
              <DocumentView key={accordionKeys} documentData={rightContent.data} accordionKey={accordionKeys} projectId={projectId} folderId={discussion_id} savedDocuments={savedDocuments} setSavedDocuments={setSavedDocuments}/>
            </div>
          );
        case 'code':
          return <CodeView code={rightContent.data?.code} language={rightContent.data?.language} />;
        case 'mermaid':
          if (rightContent.data != undefined) {
            return <MermaidChart chart={rightContent.data} />;
          }
        case 'fileStructure':
          return <FileStructure structure={rightContent.data} />;
        default:
          return <EmptyState />;
      }
    }
  };

  const updateContent = (type, data) => {
    setRightContent({ type, data });
  };

  const handleModalClose = () => {
    router.replace(pathname);
    resetStates();
    isOpenRef.current=false;
    onClose();
  };

  return (
<>
  {!searchParams.get('sessionId') && isOpen ? (
    <div className="fixed inset-0 bg-gray-900 bg-opacity-60 flex justify-center items-center z-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-xl mx-4 p-6">
        {/* Modal Header */}
        <div className="flex justify-end">
          <button
            onClick={handleModalClose}
            className="p-2 rounded bg-gray-100 hover:bg-gray-100 text-gray-600 hover:text-red-500 transition-colors"
            aria-label="Close modal"
          >
            <X size={16} />
          </button>
        </div>

        {/* Modal Content */}
        <div className="text-center mb-6">
          <h2 className="typography-heading-4 font-weight-bold text-gray-900">Initializing Query Assistant</h2>
          <p className="mt-2 text-gray-600">Please wait while we prepare your environment...</p>
        </div>

        {/* Steps Section */}
        <div className="space-y-4">
          {initializationSteps.map((step, index) => (
            <LoadingStep
              key={step.id}
              label={step.label}
              icon={step.icon}
              status={
                index < currentStep
                  ? 'completed'
                  : index === currentStep
                  ? 'loading'
                  : 'pending'
              }
              progress={index === 1 ? progress : null}
              customProgress={index === 0 && currentStep === 0 ? initProgress : undefined}
            />
          ))}
        </div>
      </div>
    </div>
  ) : isOpen ? (
    <>
      {/* Background Overlay */}
      <div
        className="fixed inset-0 bg-gray-900 bg-opacity-40 z-40"
        onClick={handleClose}
      />

      {/* Session Overview Modal */}
      <div className="fixed inset-0 bg-gray-900 bg-opacity-60 flex justify-center items-center z-50">
        <div className="bg-white rounded-lg w-[90vw] h-[90vh] flex flex-col relative shadow-xl">
          {/* Modal Header */}
          <div className="px-4 py-2 border-b border-gray-100 flex justify-between items-center">
            <h2 className="typography-body-lg font-weight-medium text-gray-800">
              {searchParams.get('sessionName')}
            </h2>
            <button
              onClick={handleConfirmClose}
              className="p-1.5 text-gray-500 hover:text-gray-700 bg-gray-100 rounded hover:bg-gray-100 transition-colors"
            >
              <X size={18} />
            </button>
          </div>


          {/* Main Content */}
          <div className="flex flex-grow overflow-hidden px-4 py-2 gap-3">
            {/* Left Panel */}
            <div className="min-w-[30%] max-w-[30%] border border-gray-200 rounded-md overflow-hidden">
              <DiscussionChatPanel
                maxFiles={5}
                acceptedFileTypes={['.pdf', '.png']}
                attachmentEnabled={true}
                onResponse={(response) => {
                  
                  if (response.content_type) {
                    updateContent(response.content_type, response.content);
                  }
                }}
              />
            </div>

            {/* Right Panel */}
            <div className="flex-grow border border-gray-300 rounded-lg overflow-auto">
              {renderRightContent()}
            </div>
          </div>
        </div>
      </div>
    </>
  ) : (
    <></>
  )}

  {/* Confirmation Modal */}

  <CloseConfirmationModal
    isVisible={showConfirmModal}
    sessionId={sessionId}
    onClose={() => {
      setShowConfirmModal(false)}
    }
    onConfirm={(action, sessionName) =>
      confirmCloseModal(action, discussion_id, sessionName)
    }
    discussionId={discussion_id}
    closeDiscussion={() => {handleSetIsOpen(false)}}
    onTerminate={() => {
      resetStates();
      onClose();
    }}
    
    showAlert={showAlert}
    setInstantCloseStatus={setInstantCloseStatus}
    closeWebSocket={closeWebSocket}
  />
</>
  );
};

export default QueryDiscussionModal;