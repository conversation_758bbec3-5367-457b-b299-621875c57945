import { useContext, useEffect } from "react";
import { useParams, useRouter, useSearchParams, usePathname } from "next/navigation";
import TableComponent from "@/components/SimpleTable/ArchitectureTable";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { formatUTCToLocal } from "@/utils/datetime";
import EditableCell from "@/components/SimpleTable/EditableCell";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import { updateTask } from "@/utils/batchAPI";

const PastDeepAnalysis = ({onClose, pastAnalysis, onLimitChange, onPageChange, isLoading, setPastAnalysis, pageSize, totalCount, customPage}) => {
    const router = useRouter();
    const { showAlert } = useContext(AlertContext);
    const searchParams = useSearchParams();
    const params = useParams();
    const pathname = usePathname();
    const projectId = params.projectId;
    let tableData = [];
    const {setIsVisible} = useCodeGeneration();
    

    const calculateDuration = (startTime, endTime) => {
        try {
            const start = new Date(startTime);
            const end = new Date(endTime);
        
            // Calculate difference in milliseconds
            const diffMs = end.getTime() - start.getTime();
        
            // Convert to various units
            const seconds = Math.floor(diffMs / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);
            const days = Math.floor(hours / 24);
            const weeks = Math.floor(days / 7);
            const years = Math.floor(days / 365.25);
        
            // Calculate remaining values
            const remainingWeeks = Math.floor((days % 365.25) / 7);
            const remainingDays = Math.floor(days % 7);
            const remainingHours = Math.floor(hours % 24);
            const remainingMinutes = Math.floor(minutes % 60);
            const remainingSeconds = Math.floor(seconds % 60);
        
            // Return appropriate unit based on duration with granular details
            if (years >= 1) {
            return `${years}y ${remainingWeeks}w`;
            } else if (weeks >= 1) {
            return `${weeks}w ${remainingDays}d`;
            } else if (days >= 1) {
            return `${days}d ${remainingHours}h`;
            } else if (hours >= 1) {
            return `${hours}h ${remainingMinutes}m`;
            } else if (minutes >= 1) {
            return `${minutes}m ${remainingSeconds}s`;
            } else {
            return `<1m`;
            }
        } catch (error) {
            
            return '0';
        }
    };

    const handleFieldUpdate = async (rowId, field, value) => {
        try {
            await updateTask(rowId, { [field]: value });
            const updatedData = pastAnalysis.map(item => 
                item._id === rowId ? {...item, [field]: value} : item
            );
            setPastAnalysis(updatedData);
            return Promise.resolve();
        } catch (error) {
            return Promise.reject(error);
        }
    };
     
    const analysisHeaders = [
        { 
            key: 'session_name',
            label: 'Session Name',
            render: (value, row) => (
                <EditableCell
                    value={value || 'Untitled Session'}
                    field="session_name"
                    rowId={row.fullId}
                    onUpdate={handleFieldUpdate}
                    onError={(error) => showAlert("Failed to update session name", "error")}
                    onSuccess={(message) => showAlert(message, "success")}
                />
            )
        },
    //     { 
    //       key: 'description',
    //       label: 'Description',
    //       render: (value, row) => (
    //           <div className="flex items-center justify-between gap-2">
    //               <div className="flex-1 truncate" title={value || 'No description'}>
    //                   <EditableCell
    //                       value={value || 'No description'}
    //                       field="description"
    //                       rowId={row.fullId}
    //                       onUpdate={handleFieldUpdate}
    //                       onError={(error) => showAlert("Failed to update description", "error")}
    //                       onSuccess={(message) => showAlert(message, "success")}
    //                   />
    //               </div>
    //           </div>
    //       )
    //   },
        {
            key: 'start_time',
            label: 'Created at',
            render: (value) => value
        },
        {
            key: 'duration',
            label: 'Duration',
            render: (value) => value || '-'
        },
        {
            key: 'status',
            label: 'Status'
        }
    ];

    const format_analysis = (data) => {
        const tableData = data.map(task => {
          const startTime = task.start_time;
          const endTime = task.messages && task.messages.length > 0
            ? task.messages[task.messages.length - 1].timestamp
            : (task.status === 'RUNNING' ? new Date().toISOString() : startTime);
    
          return {
            session_name: task.session_name || 'Untitled Session',
            description: task.description || 'No description',
            messages_length: Array.isArray(task.messages) ? task.messages.length : 0,
            status: task.status,
            fullId: task._id,
            start_time: formatUTCToLocal(task.start_time),
            duration: `${calculateDuration(startTime, endTime)}`
          };
        });
        
        return tableData;
    }
      
    useEffect(() => {
        tableData = format_analysis(pastAnalysis);
    }, [pastAnalysis]);

    tableData = format_analysis(pastAnalysis);

    const handleRowClick = (row) => {
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.set("task_id", row.fullId);
        newSearchParams.set("session_name", row.session_name);
        router.push(`${pathname}?${newSearchParams.toString()}`);
        onClose();
        setIsVisible(true);
    };

    return(
        <div className="flex-1 overflow-auto px-4 max-h-[100%] relative">
            <TableComponent
                data={tableData}
                headers={analysisHeaders}
                onRowClick={handleRowClick}
                sortableColumns={{ status: true, start_time: true }}
                totalCount={totalCount}
                onPageChange={onPageChange}
                onPageSizeChange={onLimitChange}
                component="deep-analysis"
                isLoading={isLoading}
                itemsPerPage={pageSize}
                customPage={customPage}
                emptyStateType="query_sessions"
            />
        </div>
    )
}

export default PastDeepAnalysis;