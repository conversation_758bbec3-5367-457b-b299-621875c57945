import React, { ReactNode } from "react";
import type { Metada<PERSON> } from "next";
import Sidebar from "@/components/Sidebar";
import DiscussionModal from "../modal/DiscussionModal";
import { DiscussionChatProvider } from "@/components/Context/DiscussionChatContext";
import { ArchitectureProvider } from "@/components/Context/ArchitectureContext";
import { PanelProvider } from "@/components/Context/PanelContext";
import { DiscussionTabProvider } from "@/components/Context/DiscussionTabContext";

export const dynamic = "force-dynamic";

interface LayoutProps {
  children: ReactNode;
}
export const metadata: Metadata = {
  title: "Home",
  description:
    "Kavia AI is a pioneering startup based in San Francisco, dedicated to revolutionizing workflow management. Our cutting-edge AI tools leverage Large language models and deep learning techniques to dramatically accelerate time-to-market for new products and features, ensuring our clients lead in innovation. At Kavia AI, we are committed to transforming businesses by making workflow automation more efficient and effective.",
};

export default function Layout({ children }: LayoutProps) {
  return (
    <div className="flex">
      <Sidebar />
      <div className="children-wrapper w-full content-right-padding">
        <PanelProvider>
          <DiscussionTabProvider>
            <DiscussionChatProvider>
              <ArchitectureProvider>
                <DiscussionModal />
              </ArchitectureProvider>
            </DiscussionChatProvider>
          </DiscussionTabProvider>
        </PanelProvider>
        {children}
      </div>
    </div>
  );
}
